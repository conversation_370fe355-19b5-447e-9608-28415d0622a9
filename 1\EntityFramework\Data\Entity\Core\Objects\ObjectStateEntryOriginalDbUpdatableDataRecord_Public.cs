﻿using System;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Objects
{
	// Token: 0x02000423 RID: 1059
	internal sealed class ObjectStateEntryOriginalDbUpdatableDataRecord_Public : ObjectStateEntryOriginalDbUpdatableDataRecord_Internal
	{
		// Token: 0x06003318 RID: 13080 RVA: 0x000A2086 File Offset: 0x000A0286
		internal ObjectStateEntryOriginalDbUpdatableDataRecord_Public(EntityEntry cacheEntry, StateManagerTypeMetadata metadata, object userObject, int parentEntityPropertyIndex)
			: base(cacheEntry, metadata, userObject)
		{
			this._parentEntityPropertyIndex = parentEntityPropertyIndex;
		}

		// Token: 0x06003319 RID: 13081 RVA: 0x000A2099 File Offset: 0x000A0299
		protected override object GetRecordValue(int ordinal)
		{
			return (this._cacheEntry as EntityEntry).GetOriginalEntityValue(this._metadata, ordinal, this._userObject, ObjectStateValueRecord.OriginalUpdatablePublic, this.GetPropertyIndex(ordinal));
		}

		// Token: 0x0600331A RID: 13082 RVA: 0x000A20C0 File Offset: 0x000A02C0
		protected override void SetRecordValue(int ordinal, object value)
		{
			StateManagerMemberMetadata stateManagerMemberMetadata = this._metadata.Member(ordinal);
			if (stateManagerMemberMetadata.IsComplex)
			{
				throw new InvalidOperationException(Strings.ObjectStateEntry_SetOriginalComplexProperties(stateManagerMemberMetadata.CLayerName));
			}
			object obj = value ?? DBNull.Value;
			EntityEntry entityEntry = this._cacheEntry as EntityEntry;
			EntityState state = entityEntry.State;
			if (entityEntry.HasRecordValueChanged(this, ordinal, obj))
			{
				if (stateManagerMemberMetadata.IsPartOfKey)
				{
					throw new InvalidOperationException(Strings.ObjectStateEntry_SetOriginalPrimaryKey(stateManagerMemberMetadata.CLayerName));
				}
				Type clrType = stateManagerMemberMetadata.ClrType;
				if (DBNull.Value == obj && clrType.IsValueType() && !stateManagerMemberMetadata.CdmMetadata.Nullable)
				{
					throw new InvalidOperationException(Strings.ObjectStateEntry_NullOriginalValueForNonNullableProperty(stateManagerMemberMetadata.CLayerName, stateManagerMemberMetadata.ClrMetadata.Name, stateManagerMemberMetadata.ClrMetadata.DeclaringType.FullName));
				}
				base.SetRecordValue(ordinal, value);
				if (state == EntityState.Unchanged && entityEntry.State == EntityState.Modified)
				{
					entityEntry.ObjectStateManager.ChangeState(entityEntry, state, EntityState.Modified);
				}
				entityEntry.SetModifiedPropertyInternal(this.GetPropertyIndex(ordinal));
			}
		}

		// Token: 0x0600331B RID: 13083 RVA: 0x000A21BC File Offset: 0x000A03BC
		private int GetPropertyIndex(int ordinal)
		{
			if (this._parentEntityPropertyIndex != -1)
			{
				return this._parentEntityPropertyIndex;
			}
			return ordinal;
		}

		// Token: 0x04001098 RID: 4248
		private readonly int _parentEntityPropertyIndex;
	}
}
