﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004EB RID: 1259
	internal abstract class Perspective
	{
		// Token: 0x06003EBE RID: 16062 RVA: 0x000D00AE File Offset: 0x000CE2AE
		internal Perspective(MetadataWorkspace metadataWorkspace, DataSpace targetDataspace)
		{
			this._metadataWorkspace = metadataWorkspace;
			this._targetDataspace = targetDataspace;
		}

		// Token: 0x06003EBF RID: 16063 RVA: 0x000D00C4 File Offset: 0x000CE2C4
		internal virtual bool TryGetMember(StructuralType type, string memberName, bool ignoreCase, out EdmMember outMember)
		{
			Check.NotEmpty(memberName, "memberName");
			outMember = null;
			return type.Members.TryGetValue(memberName, ignoreCase, out outMember);
		}

		// Token: 0x06003EC0 RID: 16064 RVA: 0x000D00E5 File Offset: 0x000CE2E5
		internal virtual bool TryGetEnumMember(EnumType type, string memberName, bool ignoreCase, out EnumMember outMember)
		{
			Check.NotEmpty(memberName, "memberName");
			outMember = null;
			return type.Members.TryGetValue(memberName, ignoreCase, out outMember);
		}

		// Token: 0x06003EC1 RID: 16065 RVA: 0x000D0106 File Offset: 0x000CE306
		internal virtual bool TryGetExtent(EntityContainer entityContainer, string extentName, bool ignoreCase, out EntitySetBase outSet)
		{
			return entityContainer.BaseEntitySets.TryGetValue(extentName, ignoreCase, out outSet);
		}

		// Token: 0x06003EC2 RID: 16066 RVA: 0x000D0118 File Offset: 0x000CE318
		internal virtual bool TryGetFunctionImport(EntityContainer entityContainer, string functionImportName, bool ignoreCase, out EdmFunction functionImport)
		{
			functionImport = null;
			if (ignoreCase)
			{
				functionImport = entityContainer.FunctionImports.Where((EdmFunction fi) => string.Equals(fi.Name, functionImportName, StringComparison.OrdinalIgnoreCase)).SingleOrDefault<EdmFunction>();
			}
			else
			{
				functionImport = entityContainer.FunctionImports.Where((EdmFunction fi) => fi.Name == functionImportName).SingleOrDefault<EdmFunction>();
			}
			return functionImport != null;
		}

		// Token: 0x06003EC3 RID: 16067 RVA: 0x000D017F File Offset: 0x000CE37F
		internal virtual EntityContainer GetDefaultContainer()
		{
			return null;
		}

		// Token: 0x06003EC4 RID: 16068 RVA: 0x000D0182 File Offset: 0x000CE382
		internal virtual bool TryGetEntityContainer(string name, bool ignoreCase, out EntityContainer entityContainer)
		{
			return this.MetadataWorkspace.TryGetEntityContainer(name, ignoreCase, this.TargetDataspace, out entityContainer);
		}

		// Token: 0x06003EC5 RID: 16069
		internal abstract bool TryGetTypeByName(string fullName, bool ignoreCase, out TypeUsage typeUsage);

		// Token: 0x06003EC6 RID: 16070 RVA: 0x000D0198 File Offset: 0x000CE398
		internal bool TryGetFunctionByName(string namespaceName, string functionName, bool ignoreCase, out IList<EdmFunction> functionOverloads)
		{
			Check.NotEmpty(namespaceName, "namespaceName");
			Check.NotEmpty(functionName, "functionName");
			string text = namespaceName + "." + functionName;
			ItemCollection itemCollection = this._metadataWorkspace.GetItemCollection(this._targetDataspace);
			IList<EdmFunction> list = ((this._targetDataspace == DataSpace.SSpace) ? ((StoreItemCollection)itemCollection).GetCTypeFunctions(text, ignoreCase) : itemCollection.GetFunctions(text, ignoreCase));
			if (this._targetDataspace == DataSpace.CSpace)
			{
				EntityContainer entityContainer;
				EdmFunction edmFunction;
				if ((list == null || list.Count == 0) && this.TryGetEntityContainer(namespaceName, false, out entityContainer) && this.TryGetFunctionImport(entityContainer, functionName, false, out edmFunction))
				{
					list = new EdmFunction[] { edmFunction };
				}
				ItemCollection itemCollection2;
				if ((list == null || list.Count == 0) && this._metadataWorkspace.TryGetItemCollection(DataSpace.SSpace, out itemCollection2))
				{
					list = ((StoreItemCollection)itemCollection2).GetCTypeFunctions(text, ignoreCase);
				}
			}
			functionOverloads = ((list != null && list.Count > 0) ? list : null);
			return functionOverloads != null;
		}

		// Token: 0x17000C49 RID: 3145
		// (get) Token: 0x06003EC7 RID: 16071 RVA: 0x000D0279 File Offset: 0x000CE479
		internal MetadataWorkspace MetadataWorkspace
		{
			get
			{
				return this._metadataWorkspace;
			}
		}

		// Token: 0x06003EC8 RID: 16072 RVA: 0x000D0281 File Offset: 0x000CE481
		internal virtual bool TryGetMappedPrimitiveType(PrimitiveTypeKind primitiveTypeKind, out PrimitiveType primitiveType)
		{
			primitiveType = this._metadataWorkspace.GetMappedPrimitiveType(primitiveTypeKind, DataSpace.CSpace);
			return primitiveType != null;
		}

		// Token: 0x17000C4A RID: 3146
		// (get) Token: 0x06003EC9 RID: 16073 RVA: 0x000D0297 File Offset: 0x000CE497
		internal DataSpace TargetDataspace
		{
			get
			{
				return this._targetDataspace;
			}
		}

		// Token: 0x0400154E RID: 5454
		private readonly MetadataWorkspace _metadataWorkspace;

		// Token: 0x0400154F RID: 5455
		private readonly DataSpace _targetDataspace;
	}
}
