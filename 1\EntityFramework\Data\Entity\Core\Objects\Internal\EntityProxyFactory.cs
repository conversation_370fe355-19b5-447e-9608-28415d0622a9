﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects.DataClasses;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Reflection.Emit;
using System.Runtime.Serialization;
using System.Threading;
using System.Xml.Serialization;

namespace System.Data.Entity.Core.Objects.Internal
{
	// Token: 0x0200043B RID: 1083
	internal class EntityProxyFactory
	{
		// Token: 0x060034E7 RID: 13543 RVA: 0x000A901C File Offset: 0x000A721C
		private static ModuleBuilder GetDynamicModule(EntityType ospaceEntityType)
		{
			Assembly assembly = ospaceEntityType.ClrType.Assembly();
			ModuleBuilder moduleBuilder;
			if (!EntityProxyFactory._moduleBuilders.TryGetValue(assembly, out moduleBuilder))
			{
				moduleBuilder = AssemblyBuilder.DefineDynamicAssembly(new AssemblyName(string.Format(CultureInfo.InvariantCulture, "EntityFrameworkDynamicProxies-{0}", new object[] { assembly.FullName }))
				{
					Version = new Version(1, 0, 0, 0)
				}, AssemblyBuilderAccess.Run).DefineDynamicModule("EntityProxyModule");
				EntityProxyFactory._moduleBuilders.Add(assembly, moduleBuilder);
			}
			return moduleBuilder;
		}

		// Token: 0x060034E8 RID: 13544 RVA: 0x000A9094 File Offset: 0x000A7294
		private static void DiscardDynamicModule(EntityType ospaceEntityType)
		{
			EntityProxyFactory._moduleBuilders.Remove(ospaceEntityType.ClrType.Assembly());
		}

		// Token: 0x060034E9 RID: 13545 RVA: 0x000A90AC File Offset: 0x000A72AC
		internal static bool TryGetProxyType(Type clrType, string entityTypeName, out EntityProxyTypeInfo proxyTypeInfo)
		{
			EntityProxyFactory._typeMapLock.EnterReadLock();
			bool flag;
			try
			{
				flag = EntityProxyFactory._proxyNameMap.TryGetValue(new Tuple<Type, string>(clrType, entityTypeName), out proxyTypeInfo);
			}
			finally
			{
				EntityProxyFactory._typeMapLock.ExitReadLock();
			}
			return flag;
		}

		// Token: 0x060034EA RID: 13546 RVA: 0x000A90F4 File Offset: 0x000A72F4
		internal static bool TryGetProxyType(Type proxyType, out EntityProxyTypeInfo proxyTypeInfo)
		{
			EntityProxyFactory._typeMapLock.EnterReadLock();
			bool flag;
			try
			{
				flag = EntityProxyFactory._proxyTypeMap.TryGetValue(proxyType, out proxyTypeInfo);
			}
			finally
			{
				EntityProxyFactory._typeMapLock.ExitReadLock();
			}
			return flag;
		}

		// Token: 0x060034EB RID: 13547 RVA: 0x000A9138 File Offset: 0x000A7338
		internal static bool TryGetProxyWrapper(object instance, out IEntityWrapper wrapper)
		{
			wrapper = null;
			EntityProxyTypeInfo entityProxyTypeInfo;
			if (EntityProxyFactory.IsProxyType(instance.GetType()) && EntityProxyFactory.TryGetProxyType(instance.GetType(), out entityProxyTypeInfo))
			{
				wrapper = entityProxyTypeInfo.GetEntityWrapper(instance);
			}
			return wrapper != null;
		}

		// Token: 0x060034EC RID: 13548 RVA: 0x000A9174 File Offset: 0x000A7374
		internal static EntityProxyTypeInfo GetProxyType(ClrEntityType ospaceEntityType, MetadataWorkspace workspace)
		{
			EntityProxyTypeInfo entityProxyTypeInfo = null;
			if (EntityProxyFactory.TryGetProxyType(ospaceEntityType.ClrType, ospaceEntityType.CSpaceTypeName, out entityProxyTypeInfo))
			{
				if (entityProxyTypeInfo != null)
				{
					entityProxyTypeInfo.ValidateType(ospaceEntityType);
				}
				return entityProxyTypeInfo;
			}
			EntityProxyFactory._typeMapLock.EnterUpgradeableReadLock();
			EntityProxyTypeInfo entityProxyTypeInfo2;
			try
			{
				entityProxyTypeInfo2 = EntityProxyFactory.TryCreateProxyType(ospaceEntityType, workspace);
			}
			finally
			{
				EntityProxyFactory._typeMapLock.ExitUpgradeableReadLock();
			}
			return entityProxyTypeInfo2;
		}

		// Token: 0x060034ED RID: 13549 RVA: 0x000A91D4 File Offset: 0x000A73D4
		internal static bool TryGetAssociationTypeFromProxyInfo(IEntityWrapper wrappedEntity, string relationshipName, out AssociationType associationType)
		{
			associationType = null;
			EntityProxyTypeInfo entityProxyTypeInfo;
			return EntityProxyFactory.TryGetProxyType(wrappedEntity.Entity.GetType(), out entityProxyTypeInfo) && entityProxyTypeInfo != null && entityProxyTypeInfo.TryGetNavigationPropertyAssociationType(relationshipName, out associationType);
		}

		// Token: 0x060034EE RID: 13550 RVA: 0x000A9208 File Offset: 0x000A7408
		internal static IEnumerable<AssociationType> TryGetAllAssociationTypesFromProxyInfo(IEntityWrapper wrappedEntity)
		{
			EntityProxyTypeInfo entityProxyTypeInfo;
			if (!EntityProxyFactory.TryGetProxyType(wrappedEntity.Entity.GetType(), out entityProxyTypeInfo))
			{
				return null;
			}
			return entityProxyTypeInfo.GetAllAssociationTypes();
		}

		// Token: 0x060034EF RID: 13551 RVA: 0x000A9234 File Offset: 0x000A7434
		internal static void TryCreateProxyTypes(IEnumerable<EntityType> ospaceEntityTypes, MetadataWorkspace workspace)
		{
			EntityProxyFactory._typeMapLock.EnterUpgradeableReadLock();
			try
			{
				foreach (EntityType entityType in ospaceEntityTypes)
				{
					EntityProxyFactory.TryCreateProxyType(entityType, workspace);
				}
			}
			finally
			{
				EntityProxyFactory._typeMapLock.ExitUpgradeableReadLock();
			}
		}

		// Token: 0x060034F0 RID: 13552 RVA: 0x000A929C File Offset: 0x000A749C
		private static EntityProxyTypeInfo TryCreateProxyType(EntityType ospaceEntityType, MetadataWorkspace workspace)
		{
			ClrEntityType clrEntityType = (ClrEntityType)ospaceEntityType;
			Tuple<Type, string> tuple = new Tuple<Type, string>(clrEntityType.ClrType, clrEntityType.HashedDescription);
			EntityProxyTypeInfo entityProxyTypeInfo;
			if (!EntityProxyFactory._proxyNameMap.TryGetValue(tuple, out entityProxyTypeInfo) && EntityProxyFactory.CanProxyType(ospaceEntityType))
			{
				try
				{
					entityProxyTypeInfo = EntityProxyFactory.BuildType(EntityProxyFactory.GetDynamicModule(ospaceEntityType), clrEntityType, workspace);
					EntityProxyFactory._typeMapLock.EnterWriteLock();
					try
					{
						EntityProxyFactory._proxyNameMap[tuple] = entityProxyTypeInfo;
						if (entityProxyTypeInfo != null)
						{
							EntityProxyFactory._proxyTypeMap[entityProxyTypeInfo.ProxyType] = entityProxyTypeInfo;
						}
					}
					finally
					{
						EntityProxyFactory._typeMapLock.ExitWriteLock();
					}
				}
				catch
				{
					EntityProxyFactory.DiscardDynamicModule(ospaceEntityType);
					throw;
				}
			}
			return entityProxyTypeInfo;
		}

		// Token: 0x060034F1 RID: 13553 RVA: 0x000A9348 File Offset: 0x000A7548
		internal static bool IsProxyType(Type type)
		{
			return type != null && EntityProxyFactory._proxyRuntimeAssemblies.Contains(type.Assembly());
		}

		// Token: 0x060034F2 RID: 13554 RVA: 0x000A9368 File Offset: 0x000A7568
		internal static IEnumerable<Type> GetKnownProxyTypes()
		{
			EntityProxyFactory._typeMapLock.EnterReadLock();
			IEnumerable<Type> enumerable;
			try
			{
				enumerable = (from info in EntityProxyFactory._proxyNameMap.Values
					where info != null
					select info.ProxyType).ToArray<Type>();
			}
			finally
			{
				EntityProxyFactory._typeMapLock.ExitReadLock();
			}
			return enumerable;
		}

		// Token: 0x060034F3 RID: 13555 RVA: 0x000A93F8 File Offset: 0x000A75F8
		public virtual Func<object, object> CreateBaseGetter(Type declaringType, PropertyInfo propertyInfo)
		{
			ParameterExpression parameterExpression;
			Func<object, object> nonProxyGetter = Expression.Lambda<Func<object, object>>(Expression.Property(Expression.Convert(parameterExpression, declaringType), propertyInfo), new ParameterExpression[] { parameterExpression }).Compile();
			string propertyName = propertyInfo.Name;
			return delegate(object entity)
			{
				Type type = entity.GetType();
				object obj;
				if (EntityProxyFactory.IsProxyType(type) && EntityProxyFactory.TryGetBasePropertyValue(type, propertyName, entity, out obj))
				{
					return obj;
				}
				return nonProxyGetter(entity);
			};
		}

		// Token: 0x060034F4 RID: 13556 RVA: 0x000A9460 File Offset: 0x000A7660
		private static bool TryGetBasePropertyValue(Type proxyType, string propertyName, object entity, out object value)
		{
			value = null;
			EntityProxyTypeInfo entityProxyTypeInfo;
			if (EntityProxyFactory.TryGetProxyType(proxyType, out entityProxyTypeInfo) && entityProxyTypeInfo.ContainsBaseGetter(propertyName))
			{
				value = entityProxyTypeInfo.BaseGetter(entity, propertyName);
				return true;
			}
			return false;
		}

		// Token: 0x060034F5 RID: 13557 RVA: 0x000A9495 File Offset: 0x000A7695
		public virtual Action<object, object> CreateBaseSetter(Type declaringType, PropertyInfo propertyInfo)
		{
			Action<object, object> nonProxySetter = DelegateFactory.CreateNavigationPropertySetter(declaringType, propertyInfo);
			string propertyName = propertyInfo.Name;
			return delegate(object entity, object value)
			{
				Type type = entity.GetType();
				if (EntityProxyFactory.IsProxyType(type) && EntityProxyFactory.TrySetBasePropertyValue(type, propertyName, entity, value))
				{
					return;
				}
				nonProxySetter(entity, value);
			};
		}

		// Token: 0x060034F6 RID: 13558 RVA: 0x000A94C0 File Offset: 0x000A76C0
		private static bool TrySetBasePropertyValue(Type proxyType, string propertyName, object entity, object value)
		{
			EntityProxyTypeInfo entityProxyTypeInfo;
			if (EntityProxyFactory.TryGetProxyType(proxyType, out entityProxyTypeInfo) && entityProxyTypeInfo.ContainsBaseSetter(propertyName))
			{
				entityProxyTypeInfo.BaseSetter(entity, propertyName, value);
				return true;
			}
			return false;
		}

		// Token: 0x060034F7 RID: 13559 RVA: 0x000A94F4 File Offset: 0x000A76F4
		private static EntityProxyTypeInfo BuildType(ModuleBuilder moduleBuilder, ClrEntityType ospaceEntityType, MetadataWorkspace workspace)
		{
			EntityProxyFactory.ProxyTypeBuilder proxyTypeBuilder = new EntityProxyFactory.ProxyTypeBuilder(ospaceEntityType);
			Type type = proxyTypeBuilder.CreateType(moduleBuilder);
			EntityProxyTypeInfo entityProxyTypeInfo;
			if (type != null)
			{
				Assembly assembly = type.Assembly();
				if (!EntityProxyFactory._proxyRuntimeAssemblies.Contains(assembly))
				{
					EntityProxyFactory._proxyRuntimeAssemblies.Add(assembly);
					EntityProxyFactory.AddAssemblyToResolveList(assembly);
				}
				entityProxyTypeInfo = new EntityProxyTypeInfo(type, ospaceEntityType, proxyTypeBuilder.CreateInitializeCollectionMethod(type), proxyTypeBuilder.BaseGetters, proxyTypeBuilder.BaseSetters, workspace);
				foreach (EdmMember edmMember in proxyTypeBuilder.LazyLoadMembers)
				{
					EntityProxyFactory.InterceptMember(edmMember, type, entityProxyTypeInfo);
				}
				EntityProxyFactory.SetResetFKSetterFlagDelegate(type, entityProxyTypeInfo);
				EntityProxyFactory.SetCompareByteArraysDelegate(type);
			}
			else
			{
				entityProxyTypeInfo = null;
			}
			return entityProxyTypeInfo;
		}

		// Token: 0x060034F8 RID: 13560 RVA: 0x000A95B4 File Offset: 0x000A77B4
		private static void AddAssemblyToResolveList(Assembly assembly)
		{
			try
			{
				AppDomain.CurrentDomain.AssemblyResolve += delegate(object _, ResolveEventArgs args)
				{
					if (!(args.Name == assembly.FullName))
					{
						return null;
					}
					return assembly;
				};
			}
			catch (MethodAccessException)
			{
			}
		}

		// Token: 0x060034F9 RID: 13561 RVA: 0x000A95FC File Offset: 0x000A77FC
		private static void InterceptMember(EdmMember member, Type proxyType, EntityProxyTypeInfo proxyTypeInfo)
		{
			PropertyInfo topProperty = proxyType.GetTopProperty(member.Name);
			FieldInfo field = proxyType.GetField(LazyLoadImplementor.GetInterceptorFieldName(member.Name), BindingFlags.DeclaredOnly | BindingFlags.Static | BindingFlags.NonPublic);
			EntityProxyFactory.AssignInterceptionDelegate(EntityProxyFactory.GetInterceptorDelegateMethod.MakeGenericMethod(new Type[] { proxyType, topProperty.PropertyType }).Invoke(null, new object[] { member, proxyTypeInfo.EntityWrapperDelegate }) as Delegate, field);
		}

		// Token: 0x060034FA RID: 13562 RVA: 0x000A966B File Offset: 0x000A786B
		private static void AssignInterceptionDelegate(Delegate interceptorDelegate, FieldInfo interceptorField)
		{
			interceptorField.SetValue(null, interceptorDelegate);
		}

		// Token: 0x060034FB RID: 13563 RVA: 0x000A9678 File Offset: 0x000A7878
		private static void SetResetFKSetterFlagDelegate(Type proxyType, EntityProxyTypeInfo proxyTypeInfo)
		{
			FieldInfo field = proxyType.GetField("_resetFKSetterFlag", BindingFlags.DeclaredOnly | BindingFlags.Static | BindingFlags.NonPublic);
			EntityProxyFactory.AssignInterceptionDelegate(EntityProxyFactory.GetResetFKSetterFlagDelegate(proxyTypeInfo.EntityWrapperDelegate), field);
		}

		// Token: 0x060034FC RID: 13564 RVA: 0x000A96A4 File Offset: 0x000A78A4
		private static Action<object> GetResetFKSetterFlagDelegate(Func<object, object> getEntityWrapperDelegate)
		{
			return delegate(object proxy)
			{
				EntityProxyFactory.ResetFKSetterFlag(getEntityWrapperDelegate(proxy));
			};
		}

		// Token: 0x060034FD RID: 13565 RVA: 0x000A96C0 File Offset: 0x000A78C0
		private static void ResetFKSetterFlag(object wrappedEntityAsObject)
		{
			IEntityWrapper entityWrapper = (IEntityWrapper)wrappedEntityAsObject;
			if (entityWrapper != null && entityWrapper.Context != null)
			{
				entityWrapper.Context.ObjectStateManager.EntityInvokingFKSetter = null;
			}
		}

		// Token: 0x060034FE RID: 13566 RVA: 0x000A96F0 File Offset: 0x000A78F0
		private static void SetCompareByteArraysDelegate(Type proxyType)
		{
			FieldInfo field = proxyType.GetField("_compareByteArrays", BindingFlags.DeclaredOnly | BindingFlags.Static | BindingFlags.NonPublic);
			EntityProxyFactory.AssignInterceptionDelegate(new Func<object, object, bool>(ByValueEqualityComparer.Default.Equals), field);
		}

		// Token: 0x060034FF RID: 13567 RVA: 0x000A9724 File Offset: 0x000A7924
		private static bool CanProxyType(EntityType ospaceEntityType)
		{
			Type clrType = ospaceEntityType.ClrType;
			if (!clrType.IsPublic() || clrType.IsSealed() || typeof(IEntityWithRelationships).IsAssignableFrom(clrType) || ospaceEntityType.Abstract)
			{
				return false;
			}
			ConstructorInfo declaredConstructor = clrType.GetDeclaredConstructor(new Type[0]);
			return declaredConstructor != null && ((declaredConstructor.Attributes & MethodAttributes.MemberAccessMask) == MethodAttributes.Public || (declaredConstructor.Attributes & MethodAttributes.MemberAccessMask) == MethodAttributes.Family || (declaredConstructor.Attributes & MethodAttributes.MemberAccessMask) == MethodAttributes.FamORAssem);
		}

		// Token: 0x06003500 RID: 13568 RVA: 0x000A97A0 File Offset: 0x000A79A0
		private static bool CanProxyMethod(MethodInfo method)
		{
			bool flag = false;
			if (method != null)
			{
				MethodAttributes methodAttributes = method.Attributes & MethodAttributes.MemberAccessMask;
				flag = method.IsVirtual && !method.IsFinal && (methodAttributes == MethodAttributes.Public || methodAttributes == MethodAttributes.Family || methodAttributes == MethodAttributes.FamORAssem);
			}
			return flag;
		}

		// Token: 0x06003501 RID: 13569 RVA: 0x000A97E5 File Offset: 0x000A79E5
		internal static bool CanProxyGetter(PropertyInfo clrProperty)
		{
			return EntityProxyFactory.CanProxyMethod(clrProperty.Getter());
		}

		// Token: 0x06003502 RID: 13570 RVA: 0x000A97F2 File Offset: 0x000A79F2
		internal static bool CanProxySetter(PropertyInfo clrProperty)
		{
			return EntityProxyFactory.CanProxyMethod(clrProperty.Setter());
		}

		// Token: 0x0400110B RID: 4363
		internal const string ResetFKSetterFlagFieldName = "_resetFKSetterFlag";

		// Token: 0x0400110C RID: 4364
		internal const string CompareByteArraysFieldName = "_compareByteArrays";

		// Token: 0x0400110D RID: 4365
		private static readonly Dictionary<Tuple<Type, string>, EntityProxyTypeInfo> _proxyNameMap = new Dictionary<Tuple<Type, string>, EntityProxyTypeInfo>();

		// Token: 0x0400110E RID: 4366
		private static readonly Dictionary<Type, EntityProxyTypeInfo> _proxyTypeMap = new Dictionary<Type, EntityProxyTypeInfo>();

		// Token: 0x0400110F RID: 4367
		private static readonly Dictionary<Assembly, ModuleBuilder> _moduleBuilders = new Dictionary<Assembly, ModuleBuilder>();

		// Token: 0x04001110 RID: 4368
		private static readonly ReaderWriterLockSlim _typeMapLock = new ReaderWriterLockSlim();

		// Token: 0x04001111 RID: 4369
		private static readonly HashSet<Assembly> _proxyRuntimeAssemblies = new HashSet<Assembly>();

		// Token: 0x04001112 RID: 4370
		internal static readonly MethodInfo GetInterceptorDelegateMethod = typeof(LazyLoadBehavior).GetOnlyDeclaredMethod("GetInterceptorDelegate");

		// Token: 0x02000A45 RID: 2629
		internal class ProxyTypeBuilder
		{
			// Token: 0x06006188 RID: 24968 RVA: 0x0014F058 File Offset: 0x0014D258
			public ProxyTypeBuilder(ClrEntityType ospaceEntityType)
			{
				this._ospaceEntityType = ospaceEntityType;
				this._baseImplementor = new BaseProxyImplementor();
				this._ipocoImplementor = new IPocoImplementor(ospaceEntityType);
				this._lazyLoadImplementor = new LazyLoadImplementor(ospaceEntityType);
				this._dataContractImplementor = new DataContractImplementor(ospaceEntityType);
				this._iserializableImplementor = new SerializableImplementor(ospaceEntityType);
			}

			// Token: 0x170010BA RID: 4282
			// (get) Token: 0x06006189 RID: 24969 RVA: 0x0014F0B9 File Offset: 0x0014D2B9
			public Type BaseType
			{
				get
				{
					return this._ospaceEntityType.ClrType;
				}
			}

			// Token: 0x0600618A RID: 24970 RVA: 0x0014F0C6 File Offset: 0x0014D2C6
			public DynamicMethod CreateInitializeCollectionMethod(Type proxyType)
			{
				return this._ipocoImplementor.CreateInitializeCollectionMethod(proxyType);
			}

			// Token: 0x170010BB RID: 4283
			// (get) Token: 0x0600618B RID: 24971 RVA: 0x0014F0D4 File Offset: 0x0014D2D4
			public List<PropertyInfo> BaseGetters
			{
				get
				{
					return this._baseImplementor.BaseGetters;
				}
			}

			// Token: 0x170010BC RID: 4284
			// (get) Token: 0x0600618C RID: 24972 RVA: 0x0014F0E1 File Offset: 0x0014D2E1
			public List<PropertyInfo> BaseSetters
			{
				get
				{
					return this._baseImplementor.BaseSetters;
				}
			}

			// Token: 0x170010BD RID: 4285
			// (get) Token: 0x0600618D RID: 24973 RVA: 0x0014F0EE File Offset: 0x0014D2EE
			public IEnumerable<EdmMember> LazyLoadMembers
			{
				get
				{
					return this._lazyLoadImplementor.Members;
				}
			}

			// Token: 0x0600618E RID: 24974 RVA: 0x0014F0FC File Offset: 0x0014D2FC
			public Type CreateType(ModuleBuilder moduleBuilder)
			{
				this._moduleBuilder = moduleBuilder;
				bool flag = false;
				if (this._iserializableImplementor.TypeIsSuitable)
				{
					foreach (EdmMember edmMember in this._ospaceEntityType.Members)
					{
						if (this._ipocoImplementor.CanProxyMember(edmMember) || this._lazyLoadImplementor.CanProxyMember(edmMember))
						{
							PropertyInfo topProperty = this.BaseType.GetTopProperty(edmMember.Name);
							PropertyBuilder propertyBuilder = this.TypeBuilder.DefineProperty(edmMember.Name, PropertyAttributes.None, topProperty.PropertyType, Type.EmptyTypes);
							if (!this._ipocoImplementor.EmitMember(this.TypeBuilder, edmMember, propertyBuilder, topProperty, this._baseImplementor))
							{
								EntityProxyFactory.ProxyTypeBuilder.EmitBaseSetter(this.TypeBuilder, propertyBuilder, topProperty);
							}
							if (!this._lazyLoadImplementor.EmitMember(this.TypeBuilder, edmMember, propertyBuilder, topProperty, this._baseImplementor))
							{
								EntityProxyFactory.ProxyTypeBuilder.EmitBaseGetter(this.TypeBuilder, propertyBuilder, topProperty);
							}
							flag = true;
						}
					}
					if (this._typeBuilder != null)
					{
						this._baseImplementor.Implement(this.TypeBuilder);
						this._iserializableImplementor.Implement(this.TypeBuilder, this._serializedFields);
					}
				}
				if (!flag)
				{
					return null;
				}
				return this.TypeBuilder.CreateType();
			}

			// Token: 0x170010BE RID: 4286
			// (get) Token: 0x0600618F RID: 24975 RVA: 0x0014F25C File Offset: 0x0014D45C
			private TypeBuilder TypeBuilder
			{
				get
				{
					if (this._typeBuilder == null)
					{
						TypeAttributes typeAttributes = TypeAttributes.Public | TypeAttributes.Sealed;
						if ((this.BaseType.Attributes() & TypeAttributes.Serializable) == TypeAttributes.Serializable)
						{
							typeAttributes |= TypeAttributes.Serializable;
						}
						string text = ((this.BaseType.Name.Length <= 20) ? this.BaseType.Name : this.BaseType.Name.Substring(0, 20));
						string text2 = string.Format(CultureInfo.InvariantCulture, "System.Data.Entity.DynamicProxies.{0}_{1}", new object[]
						{
							text,
							this._ospaceEntityType.HashedDescription
						});
						this._typeBuilder = this._moduleBuilder.DefineType(text2, typeAttributes, this.BaseType, this._ipocoImplementor.Interfaces);
						this._typeBuilder.DefineDefaultConstructor(MethodAttributes.FamANDAssem | MethodAttributes.Family | MethodAttributes.HideBySig | MethodAttributes.SpecialName | MethodAttributes.RTSpecialName);
						Action<FieldBuilder, bool> action = new Action<FieldBuilder, bool>(this.RegisterInstanceField);
						this._ipocoImplementor.Implement(this._typeBuilder, action);
						this._lazyLoadImplementor.Implement(this._typeBuilder, action);
						if (!this._iserializableImplementor.TypeImplementsISerializable)
						{
							this._dataContractImplementor.Implement(this._typeBuilder);
						}
					}
					return this._typeBuilder;
				}
			}

			// Token: 0x06006190 RID: 24976 RVA: 0x0014F388 File Offset: 0x0014D588
			private static void EmitBaseGetter(TypeBuilder typeBuilder, PropertyBuilder propertyBuilder, PropertyInfo baseProperty)
			{
				if (EntityProxyFactory.CanProxyGetter(baseProperty))
				{
					MethodInfo methodInfo = baseProperty.Getter();
					MethodAttributes methodAttributes = methodInfo.Attributes & MethodAttributes.MemberAccessMask;
					MethodBuilder methodBuilder = typeBuilder.DefineMethod("get_" + baseProperty.Name, methodAttributes | (MethodAttributes.Virtual | MethodAttributes.HideBySig | MethodAttributes.SpecialName), baseProperty.PropertyType, Type.EmptyTypes);
					ILGenerator ilgenerator = methodBuilder.GetILGenerator();
					ilgenerator.Emit(OpCodes.Ldarg_0);
					ilgenerator.Emit(OpCodes.Call, methodInfo);
					ilgenerator.Emit(OpCodes.Ret);
					propertyBuilder.SetGetMethod(methodBuilder);
				}
			}

			// Token: 0x06006191 RID: 24977 RVA: 0x0014F404 File Offset: 0x0014D604
			private static void EmitBaseSetter(TypeBuilder typeBuilder, PropertyBuilder propertyBuilder, PropertyInfo baseProperty)
			{
				if (EntityProxyFactory.CanProxySetter(baseProperty))
				{
					MethodInfo methodInfo = baseProperty.Setter();
					MethodAttributes methodAttributes = methodInfo.Attributes & MethodAttributes.MemberAccessMask;
					MethodBuilder methodBuilder = typeBuilder.DefineMethod("set_" + baseProperty.Name, methodAttributes | (MethodAttributes.Virtual | MethodAttributes.HideBySig | MethodAttributes.SpecialName), null, new Type[] { baseProperty.PropertyType });
					ILGenerator ilgenerator = methodBuilder.GetILGenerator();
					ilgenerator.Emit(OpCodes.Ldarg_0);
					ilgenerator.Emit(OpCodes.Ldarg_1);
					ilgenerator.Emit(OpCodes.Call, methodInfo);
					ilgenerator.Emit(OpCodes.Ret);
					propertyBuilder.SetSetMethod(methodBuilder);
				}
			}

			// Token: 0x06006192 RID: 24978 RVA: 0x0014F490 File Offset: 0x0014D690
			private void RegisterInstanceField(FieldBuilder field, bool serializable)
			{
				if (serializable)
				{
					this._serializedFields.Add(field);
					return;
				}
				EntityProxyFactory.ProxyTypeBuilder.MarkAsNotSerializable(field);
			}

			// Token: 0x06006193 RID: 24979 RVA: 0x0014F4A8 File Offset: 0x0014D6A8
			private static ConstructorInfo TryGetScriptIgnoreAttributeConstructor()
			{
				try
				{
					if (AspProxy.IsSystemWebLoaded())
					{
						Type type = Assembly.Load("System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35").GetType("System.Web.Script.Serialization.ScriptIgnoreAttribute");
						if (type != null)
						{
							return type.GetDeclaredConstructor(new Type[0]);
						}
					}
				}
				catch
				{
				}
				return null;
			}

			// Token: 0x06006194 RID: 24980 RVA: 0x0014F504 File Offset: 0x0014D704
			public static void MarkAsNotSerializable(FieldBuilder field)
			{
				object[] array = new object[0];
				field.SetCustomAttribute(new CustomAttributeBuilder(EntityProxyFactory.ProxyTypeBuilder._nonSerializedAttributeConstructor, array));
				if (field.IsPublic)
				{
					field.SetCustomAttribute(new CustomAttributeBuilder(EntityProxyFactory.ProxyTypeBuilder._ignoreDataMemberAttributeConstructor, array));
					field.SetCustomAttribute(new CustomAttributeBuilder(EntityProxyFactory.ProxyTypeBuilder._xmlIgnoreAttributeConstructor, array));
					if (EntityProxyFactory.ProxyTypeBuilder._scriptIgnoreAttributeConstructor.Value != null)
					{
						field.SetCustomAttribute(new CustomAttributeBuilder(EntityProxyFactory.ProxyTypeBuilder._scriptIgnoreAttributeConstructor.Value, array));
					}
				}
			}

			// Token: 0x04002A44 RID: 10820
			private TypeBuilder _typeBuilder;

			// Token: 0x04002A45 RID: 10821
			private readonly BaseProxyImplementor _baseImplementor;

			// Token: 0x04002A46 RID: 10822
			private readonly IPocoImplementor _ipocoImplementor;

			// Token: 0x04002A47 RID: 10823
			private readonly LazyLoadImplementor _lazyLoadImplementor;

			// Token: 0x04002A48 RID: 10824
			private readonly DataContractImplementor _dataContractImplementor;

			// Token: 0x04002A49 RID: 10825
			private readonly SerializableImplementor _iserializableImplementor;

			// Token: 0x04002A4A RID: 10826
			private readonly ClrEntityType _ospaceEntityType;

			// Token: 0x04002A4B RID: 10827
			private ModuleBuilder _moduleBuilder;

			// Token: 0x04002A4C RID: 10828
			private readonly List<FieldBuilder> _serializedFields = new List<FieldBuilder>(3);

			// Token: 0x04002A4D RID: 10829
			private static readonly ConstructorInfo _nonSerializedAttributeConstructor = typeof(NonSerializedAttribute).GetDeclaredConstructor(new Type[0]);

			// Token: 0x04002A4E RID: 10830
			private static readonly ConstructorInfo _ignoreDataMemberAttributeConstructor = typeof(IgnoreDataMemberAttribute).GetDeclaredConstructor(new Type[0]);

			// Token: 0x04002A4F RID: 10831
			private static readonly ConstructorInfo _xmlIgnoreAttributeConstructor = typeof(XmlIgnoreAttribute).GetDeclaredConstructor(new Type[0]);

			// Token: 0x04002A50 RID: 10832
			private static readonly Lazy<ConstructorInfo> _scriptIgnoreAttributeConstructor = new Lazy<ConstructorInfo>(new Func<ConstructorInfo>(EntityProxyFactory.ProxyTypeBuilder.TryGetScriptIgnoreAttributeConstructor));
		}
	}
}
