﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Validation;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Diagnostics;
using System.Linq;

namespace System.Data.Entity.Core.Mapping.ViewGeneration
{
	// Token: 0x0200056C RID: 1388
	internal class CellGroupValidator
	{
		// Token: 0x060043B1 RID: 17329 RVA: 0x000EA04F File Offset: 0x000E824F
		internal CellGroupValidator(IEnumerable<Cell> cells, ConfigViewGenerator config)
		{
			this.m_cells = cells;
			this.m_config = config;
			this.m_errorLog = new ErrorLog();
		}

		// Token: 0x060043B2 RID: 17330 RVA: 0x000EA070 File Offset: 0x000E8270
		internal ErrorLog Validate()
		{
			if (this.m_config.IsValidationEnabled)
			{
				if (!this.PerformSingleCellChecks())
				{
					return this.m_errorLog;
				}
			}
			else if (!this.CheckCellsWithDistinctFlag())
			{
				return this.m_errorLog;
			}
			SchemaConstraints<BasicKeyConstraint> schemaConstraints = new SchemaConstraints<BasicKeyConstraint>();
			SchemaConstraints<BasicKeyConstraint> schemaConstraints2 = new SchemaConstraints<BasicKeyConstraint>();
			this.ConstructCellRelationsWithConstraints(schemaConstraints, schemaConstraints2);
			if (this.m_config.IsVerboseTracing)
			{
				Trace.WriteLine(string.Empty);
				Trace.WriteLine("C-Level Basic Constraints");
				Trace.WriteLine(schemaConstraints);
				Trace.WriteLine("S-Level Basic Constraints");
				Trace.WriteLine(schemaConstraints2);
			}
			this.m_cViewConstraints = CellGroupValidator.PropagateConstraints(schemaConstraints);
			this.m_sViewConstraints = CellGroupValidator.PropagateConstraints(schemaConstraints2);
			if (this.m_config.IsVerboseTracing)
			{
				Trace.WriteLine(string.Empty);
				Trace.WriteLine("C-Level View Constraints");
				Trace.WriteLine(this.m_cViewConstraints);
				Trace.WriteLine("S-Level View Constraints");
				Trace.WriteLine(this.m_sViewConstraints);
			}
			if (this.m_config.IsValidationEnabled)
			{
				this.CheckImplication(this.m_cViewConstraints, this.m_sViewConstraints);
			}
			return this.m_errorLog;
		}

		// Token: 0x060043B3 RID: 17331 RVA: 0x000EA174 File Offset: 0x000E8374
		private void ConstructCellRelationsWithConstraints(SchemaConstraints<BasicKeyConstraint> cConstraints, SchemaConstraints<BasicKeyConstraint> sConstraints)
		{
			int num = 0;
			foreach (Cell cell in this.m_cells)
			{
				cell.CreateViewCellRelation(num);
				BasicCellRelation basicCellRelation = cell.CQuery.BasicCellRelation;
				BasicCellRelation basicCellRelation2 = cell.SQuery.BasicCellRelation;
				CellGroupValidator.PopulateBaseConstraints(basicCellRelation, cConstraints);
				CellGroupValidator.PopulateBaseConstraints(basicCellRelation2, sConstraints);
				num++;
			}
			foreach (Cell cell2 in this.m_cells)
			{
				foreach (Cell cell3 in this.m_cells)
				{
				}
			}
		}

		// Token: 0x060043B4 RID: 17332 RVA: 0x000EA260 File Offset: 0x000E8460
		private static void PopulateBaseConstraints(BasicCellRelation baseRelation, SchemaConstraints<BasicKeyConstraint> constraints)
		{
			baseRelation.PopulateKeyConstraints(constraints);
		}

		// Token: 0x060043B5 RID: 17333 RVA: 0x000EA26C File Offset: 0x000E846C
		private static SchemaConstraints<ViewKeyConstraint> PropagateConstraints(SchemaConstraints<BasicKeyConstraint> baseConstraints)
		{
			SchemaConstraints<ViewKeyConstraint> schemaConstraints = new SchemaConstraints<ViewKeyConstraint>();
			foreach (BasicKeyConstraint basicKeyConstraint in baseConstraints.KeyConstraints)
			{
				ViewKeyConstraint viewKeyConstraint = basicKeyConstraint.Propagate();
				if (viewKeyConstraint != null)
				{
					schemaConstraints.Add(viewKeyConstraint);
				}
			}
			return schemaConstraints;
		}

		// Token: 0x060043B6 RID: 17334 RVA: 0x000EA2C8 File Offset: 0x000E84C8
		private void CheckImplication(SchemaConstraints<ViewKeyConstraint> cViewConstraints, SchemaConstraints<ViewKeyConstraint> sViewConstraints)
		{
			this.CheckImplicationKeyConstraints(cViewConstraints, sViewConstraints);
			KeyToListMap<CellGroupValidator.ExtentPair, ViewKeyConstraint> keyToListMap = new KeyToListMap<CellGroupValidator.ExtentPair, ViewKeyConstraint>(EqualityComparer<CellGroupValidator.ExtentPair>.Default);
			foreach (ViewKeyConstraint viewKeyConstraint in cViewConstraints.KeyConstraints)
			{
				CellGroupValidator.ExtentPair extentPair = new CellGroupValidator.ExtentPair(viewKeyConstraint.Cell.CQuery.Extent, viewKeyConstraint.Cell.SQuery.Extent);
				keyToListMap.Add(extentPair, viewKeyConstraint);
			}
			foreach (CellGroupValidator.ExtentPair extentPair2 in keyToListMap.Keys)
			{
				ReadOnlyCollection<ViewKeyConstraint> readOnlyCollection = keyToListMap.ListForKey(extentPair2);
				bool flag = false;
				foreach (ViewKeyConstraint viewKeyConstraint2 in readOnlyCollection)
				{
					using (IEnumerator<ViewKeyConstraint> enumerator3 = sViewConstraints.KeyConstraints.GetEnumerator())
					{
						while (enumerator3.MoveNext())
						{
							if (enumerator3.Current.Implies(viewKeyConstraint2))
							{
								flag = true;
								break;
							}
						}
					}
				}
				if (!flag)
				{
					this.m_errorLog.AddEntry(ViewKeyConstraint.GetErrorRecord(readOnlyCollection));
				}
			}
		}

		// Token: 0x060043B7 RID: 17335 RVA: 0x000EA42C File Offset: 0x000E862C
		private void CheckImplicationKeyConstraints(SchemaConstraints<ViewKeyConstraint> leftViewConstraints, SchemaConstraints<ViewKeyConstraint> rightViewConstraints)
		{
			foreach (ViewKeyConstraint viewKeyConstraint in rightViewConstraints.KeyConstraints)
			{
				bool flag = false;
				using (IEnumerator<ViewKeyConstraint> enumerator2 = leftViewConstraints.KeyConstraints.GetEnumerator())
				{
					while (enumerator2.MoveNext())
					{
						if (enumerator2.Current.Implies(viewKeyConstraint))
						{
							flag = true;
							break;
						}
					}
				}
				if (!flag)
				{
					this.m_errorLog.AddEntry(ViewKeyConstraint.GetErrorRecord(viewKeyConstraint));
				}
			}
		}

		// Token: 0x060043B8 RID: 17336 RVA: 0x000EA4C8 File Offset: 0x000E86C8
		private bool CheckCellsWithDistinctFlag()
		{
			int count = this.m_errorLog.Count;
			using (IEnumerator<Cell> enumerator = this.m_cells.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					Cell cell = enumerator.Current;
					if (cell.SQuery.SelectDistinctFlag == CellQuery.SelectDistinct.Yes)
					{
						EntitySetBase cExtent = cell.CQuery.Extent;
						EntitySetBase sExtent = cell.SQuery.Extent;
						IEnumerable<Cell> enumerable = from otherCell in this.m_cells
							where otherCell != cell
							where otherCell.CQuery.Extent == cExtent && otherCell.SQuery.Extent == sExtent
							select otherCell;
						if (enumerable.Any<Cell>())
						{
							IEnumerable<Cell> enumerable2 = Enumerable.Repeat<Cell>(cell, 1).Union(enumerable);
							ErrorLog.Record record = new ErrorLog.Record(ViewGenErrorCode.MultipleFragmentsBetweenCandSExtentWithDistinct, Strings.Viewgen_MultipleFragmentsBetweenCandSExtentWithDistinct(cExtent.Name, sExtent.Name), enumerable2, string.Empty);
							this.m_errorLog.AddEntry(record);
						}
					}
				}
			}
			return this.m_errorLog.Count == count;
		}

		// Token: 0x060043B9 RID: 17337 RVA: 0x000EA604 File Offset: 0x000E8804
		private bool PerformSingleCellChecks()
		{
			int count = this.m_errorLog.Count;
			foreach (Cell cell in this.m_cells)
			{
				ErrorLog.Record record = cell.SQuery.CheckForDuplicateFields(cell.CQuery, cell);
				if (record != null)
				{
					this.m_errorLog.AddEntry(record);
				}
				record = cell.CQuery.VerifyKeysPresent(cell, new Func<object, object, string>(Strings.ViewGen_EntitySetKey_Missing), new Func<object, object, object, string>(Strings.ViewGen_AssociationSetKey_Missing), ViewGenErrorCode.KeyNotMappedForCSideExtent);
				if (record != null)
				{
					this.m_errorLog.AddEntry(record);
				}
				record = cell.SQuery.VerifyKeysPresent(cell, new Func<object, object, string>(Strings.ViewGen_TableKey_Missing), null, ViewGenErrorCode.KeyNotMappedForTable);
				if (record != null)
				{
					this.m_errorLog.AddEntry(record);
				}
				record = cell.CQuery.CheckForProjectedNotNullSlots(cell, this.m_cells.Where((Cell c) => c.SQuery.Extent is AssociationSet));
				if (record != null)
				{
					this.m_errorLog.AddEntry(record);
				}
				record = cell.SQuery.CheckForProjectedNotNullSlots(cell, this.m_cells.Where((Cell c) => c.CQuery.Extent is AssociationSet));
				if (record != null)
				{
					this.m_errorLog.AddEntry(record);
				}
			}
			return this.m_errorLog.Count == count;
		}

		// Token: 0x060043BA RID: 17338 RVA: 0x000EA78C File Offset: 0x000E898C
		[Conditional("DEBUG")]
		private static void CheckConstraintSanity(SchemaConstraints<BasicKeyConstraint> cConstraints, SchemaConstraints<BasicKeyConstraint> sConstraints, SchemaConstraints<ViewKeyConstraint> cViewConstraints, SchemaConstraints<ViewKeyConstraint> sViewConstraints)
		{
		}

		// Token: 0x04001840 RID: 6208
		private readonly IEnumerable<Cell> m_cells;

		// Token: 0x04001841 RID: 6209
		private readonly ConfigViewGenerator m_config;

		// Token: 0x04001842 RID: 6210
		private readonly ErrorLog m_errorLog;

		// Token: 0x04001843 RID: 6211
		private SchemaConstraints<ViewKeyConstraint> m_cViewConstraints;

		// Token: 0x04001844 RID: 6212
		private SchemaConstraints<ViewKeyConstraint> m_sViewConstraints;

		// Token: 0x02000B7B RID: 2939
		private class ExtentPair
		{
			// Token: 0x0600667C RID: 26236 RVA: 0x0015F01F File Offset: 0x0015D21F
			internal ExtentPair(EntitySetBase acExtent, EntitySetBase asExtent)
			{
				this.cExtent = acExtent;
				this.sExtent = asExtent;
			}

			// Token: 0x0600667D RID: 26237 RVA: 0x0015F038 File Offset: 0x0015D238
			public override bool Equals(object obj)
			{
				if (this == obj)
				{
					return true;
				}
				CellGroupValidator.ExtentPair extentPair = obj as CellGroupValidator.ExtentPair;
				return extentPair != null && extentPair.cExtent.Equals(this.cExtent) && extentPair.sExtent.Equals(this.sExtent);
			}

			// Token: 0x0600667E RID: 26238 RVA: 0x0015F07D File Offset: 0x0015D27D
			public override int GetHashCode()
			{
				return this.cExtent.GetHashCode() ^ this.sExtent.GetHashCode();
			}

			// Token: 0x04002DEF RID: 11759
			internal readonly EntitySetBase cExtent;

			// Token: 0x04002DF0 RID: 11760
			internal readonly EntitySetBase sExtent;
		}
	}
}
