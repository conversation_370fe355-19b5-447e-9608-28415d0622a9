﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000021 RID: 33
	public class Sys_FirstKill_Data
	{
		// Token: 0x17000100 RID: 256
		// (get) Token: 0x06000220 RID: 544 RVA: 0x00003283 File Offset: 0x00001483
		// (set) Token: 0x06000221 RID: 545 RVA: 0x0000328B File Offset: 0x0000148B
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000101 RID: 257
		// (get) Token: 0x06000222 RID: 546 RVA: 0x00003294 File Offset: 0x00001494
		// (set) Token: 0x06000223 RID: 547 RVA: 0x0000329C File Offset: 0x0000149C
		public int MissionID { get; set; }

		// Token: 0x17000102 RID: 258
		// (get) Token: 0x06000224 RID: 548 RVA: 0x000032A5 File Offset: 0x000014A5
		// (set) Token: 0x06000225 RID: 549 RVA: 0x000032AD File Offset: 0x000014AD
		public int BossID { get; set; }

		// Token: 0x17000103 RID: 259
		// (get) Token: 0x06000226 RID: 550 RVA: 0x000032B6 File Offset: 0x000014B6
		// (set) Token: 0x06000227 RID: 551 RVA: 0x000032BE File Offset: 0x000014BE
		public string AreaName { get; set; }

		// Token: 0x17000104 RID: 260
		// (get) Token: 0x06000228 RID: 552 RVA: 0x000032C7 File Offset: 0x000014C7
		// (set) Token: 0x06000229 RID: 553 RVA: 0x000032CF File Offset: 0x000014CF
		public string NickName1 { get; set; }

		// Token: 0x17000105 RID: 261
		// (get) Token: 0x0600022A RID: 554 RVA: 0x000032D8 File Offset: 0x000014D8
		// (set) Token: 0x0600022B RID: 555 RVA: 0x000032E0 File Offset: 0x000014E0
		public string NickName2 { get; set; }

		// Token: 0x17000106 RID: 262
		// (get) Token: 0x0600022C RID: 556 RVA: 0x000032E9 File Offset: 0x000014E9
		// (set) Token: 0x0600022D RID: 557 RVA: 0x000032F1 File Offset: 0x000014F1
		public string NickName3 { get; set; }

		// Token: 0x17000107 RID: 263
		// (get) Token: 0x0600022E RID: 558 RVA: 0x000032FA File Offset: 0x000014FA
		// (set) Token: 0x0600022F RID: 559 RVA: 0x00003302 File Offset: 0x00001502
		public string NickName4 { get; set; }

		// Token: 0x17000108 RID: 264
		// (get) Token: 0x06000230 RID: 560 RVA: 0x0000330B File Offset: 0x0000150B
		// (set) Token: 0x06000231 RID: 561 RVA: 0x00003313 File Offset: 0x00001513
		public DateTime PassMissonTime { get; set; }
	}
}
