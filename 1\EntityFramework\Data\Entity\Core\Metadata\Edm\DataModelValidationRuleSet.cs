﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x0200049A RID: 1178
	internal abstract class DataModelValidationRuleSet
	{
		// Token: 0x06003A30 RID: 14896 RVA: 0x000BFA4F File Offset: 0x000BDC4F
		protected void AddRule(DataModelValidationRule rule)
		{
			this._rules.Add(rule);
		}

		// Token: 0x06003A31 RID: 14897 RVA: 0x000BFA5D File Offset: 0x000BDC5D
		protected void RemoveRule(DataModelValidationRule rule)
		{
			this._rules.Remove(rule);
		}

		// Token: 0x06003A32 RID: 14898 RVA: 0x000BFA6C File Offset: 0x000BDC6C
		internal IEnumerable<DataModelValidationRule> GetRules(MetadataItem itemToValidate)
		{
			return this._rules.Where((DataModelValidationRule r) => r.ValidatedType.IsInstanceOfType(itemToValidate));
		}

		// Token: 0x04001368 RID: 4968
		private readonly List<DataModelValidationRule> _rules = new List<DataModelValidationRule>();
	}
}
