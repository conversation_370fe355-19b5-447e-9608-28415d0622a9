﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Mapping.ViewGeneration.CqlGeneration;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x0200059B RID: 1435
	internal class CellIdBoolean : TrueFalseLiteral
	{
		// Token: 0x06004588 RID: 17800 RVA: 0x000F4797 File Offset: 0x000F2997
		internal CellIdBoolean(CqlIdentifiers identifiers, int index)
		{
			this.m_index = index;
			this.m_slotName = identifiers.GetFromVariable(index);
		}

		// Token: 0x17000DB8 RID: 3512
		// (get) Token: 0x06004589 RID: 17801 RVA: 0x000F47B3 File Offset: 0x000F29B3
		internal string SlotName
		{
			get
			{
				return this.m_slotName;
			}
		}

		// Token: 0x0600458A RID: 17802 RVA: 0x000F47BC File Offset: 0x000F29BC
		internal override StringBuilder AsEsql(StringBuilder builder, string blockAlias, bool skipIsNotNull)
		{
			string qualifiedName = CqlWriter.GetQualifiedName(blockAlias, this.SlotName);
			builder.Append(qualifiedName);
			return builder;
		}

		// Token: 0x0600458B RID: 17803 RVA: 0x000F47DF File Offset: 0x000F29DF
		internal override DbExpression AsCqt(DbExpression row, bool skipIsNotNull)
		{
			return row.Property(this.SlotName);
		}

		// Token: 0x0600458C RID: 17804 RVA: 0x000F47ED File Offset: 0x000F29ED
		internal override StringBuilder AsUserString(StringBuilder builder, string blockAlias, bool skipIsNotNull)
		{
			return this.AsEsql(builder, blockAlias, skipIsNotNull);
		}

		// Token: 0x0600458D RID: 17805 RVA: 0x000F47F8 File Offset: 0x000F29F8
		internal override StringBuilder AsNegatedUserString(StringBuilder builder, string blockAlias, bool skipIsNotNull)
		{
			builder.Append("NOT(");
			builder = this.AsUserString(builder, blockAlias, skipIsNotNull);
			builder.Append(")");
			return builder;
		}

		// Token: 0x0600458E RID: 17806 RVA: 0x000F4820 File Offset: 0x000F2A20
		internal override void GetRequiredSlots(MemberProjectionIndex projectedSlotMap, bool[] requiredSlots)
		{
			int num = requiredSlots.Length - projectedSlotMap.Count;
			int num2 = projectedSlotMap.BoolIndexToSlot(this.m_index, num);
			requiredSlots[num2] = true;
		}

		// Token: 0x0600458F RID: 17807 RVA: 0x000F484C File Offset: 0x000F2A4C
		protected override bool IsEqualTo(BoolLiteral right)
		{
			CellIdBoolean cellIdBoolean = right as CellIdBoolean;
			return cellIdBoolean != null && this.m_index == cellIdBoolean.m_index;
		}

		// Token: 0x06004590 RID: 17808 RVA: 0x000F4874 File Offset: 0x000F2A74
		public override int GetHashCode()
		{
			return this.m_index.GetHashCode();
		}

		// Token: 0x06004591 RID: 17809 RVA: 0x000F488F File Offset: 0x000F2A8F
		internal override BoolLiteral RemapBool(Dictionary<MemberPath, MemberPath> remap)
		{
			return this;
		}

		// Token: 0x06004592 RID: 17810 RVA: 0x000F4892 File Offset: 0x000F2A92
		internal override void ToCompactString(StringBuilder builder)
		{
			builder.Append(this.SlotName);
		}

		// Token: 0x040018F0 RID: 6384
		private readonly int m_index;

		// Token: 0x040018F1 RID: 6385
		private readonly string m_slotName;
	}
}
