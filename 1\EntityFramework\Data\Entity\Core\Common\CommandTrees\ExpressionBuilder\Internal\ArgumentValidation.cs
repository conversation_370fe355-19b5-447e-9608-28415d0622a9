﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.CommandTrees.Internal;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;

namespace System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder.Internal
{
	// Token: 0x020006FB RID: 1787
	internal static class ArgumentValidation
	{
		// Token: 0x06005448 RID: 21576 RVA: 0x0012DCF9 File Offset: 0x0012BEF9
		internal static ReadOnlyCollection<TElement> NewReadOnlyCollection<TElement>(IList<TElement> list)
		{
			return new ReadOnlyCollection<TElement>(list);
		}

		// Token: 0x06005449 RID: 21577 RVA: 0x0012DD01 File Offset: 0x0012BF01
		internal static void RequirePolymorphicType(TypeUsage type)
		{
			if (!TypeSemantics.IsPolymorphicType(type))
			{
				throw new ArgumentException(Strings.Cqt_General_PolymorphicTypeRequired(type.ToString()), "type");
			}
		}

		// Token: 0x0600544A RID: 21578 RVA: 0x0012DD21 File Offset: 0x0012BF21
		internal static void RequireCompatibleType(DbExpression expression, TypeUsage requiredResultType, string argumentName)
		{
			ArgumentValidation.RequireCompatibleType(expression, requiredResultType, argumentName, -1);
		}

		// Token: 0x0600544B RID: 21579 RVA: 0x0012DD2C File Offset: 0x0012BF2C
		private static void RequireCompatibleType(DbExpression expression, TypeUsage requiredResultType, string argumentName, int argumentIndex)
		{
			if (!TypeSemantics.IsStructurallyEqualOrPromotableTo(expression.ResultType, requiredResultType))
			{
				if (argumentIndex != -1)
				{
					argumentName = StringUtil.FormatIndex(argumentName, argumentIndex);
				}
				throw new ArgumentException(Strings.Cqt_ExpressionLink_TypeMismatch(expression.ResultType.ToString(), requiredResultType.ToString()), argumentName);
			}
		}

		// Token: 0x0600544C RID: 21580 RVA: 0x0012DD66 File Offset: 0x0012BF66
		internal static void RequireCompatibleType(DbExpression expression, PrimitiveTypeKind requiredResultType, string argumentName)
		{
			ArgumentValidation.RequireCompatibleType(expression, requiredResultType, argumentName, -1);
		}

		// Token: 0x0600544D RID: 21581 RVA: 0x0012DD74 File Offset: 0x0012BF74
		private static void RequireCompatibleType(DbExpression expression, PrimitiveTypeKind requiredResultType, string argumentName, int index)
		{
			PrimitiveTypeKind primitiveTypeKind;
			bool flag = TypeHelpers.TryGetPrimitiveTypeKind(expression.ResultType, out primitiveTypeKind);
			if (!flag || primitiveTypeKind != requiredResultType)
			{
				if (index != -1)
				{
					argumentName = StringUtil.FormatIndex(argumentName, index);
				}
				throw new ArgumentException(Strings.Cqt_ExpressionLink_TypeMismatch(flag ? Enum.GetName(typeof(PrimitiveTypeKind), primitiveTypeKind) : expression.ResultType.ToString(), Enum.GetName(typeof(PrimitiveTypeKind), requiredResultType)), argumentName);
			}
		}

		// Token: 0x0600544E RID: 21582 RVA: 0x0012DDEC File Offset: 0x0012BFEC
		private static void RequireCompatibleType(DbExpression from, RelationshipEndMember end, bool allowAllRelationshipsInSameTypeHierarchy)
		{
			TypeUsage typeUsage = end.TypeUsage;
			if (!TypeSemantics.IsReferenceType(typeUsage))
			{
				typeUsage = TypeHelpers.CreateReferenceTypeUsage(TypeHelpers.GetEdmType<EntityType>(typeUsage));
			}
			if (allowAllRelationshipsInSameTypeHierarchy)
			{
				if (TypeHelpers.GetCommonTypeUsage(typeUsage, from.ResultType) == null)
				{
					throw new ArgumentException(Strings.Cqt_RelNav_WrongSourceType(typeUsage.ToString()), "from");
				}
			}
			else if (!TypeSemantics.IsStructurallyEqualOrPromotableTo(from.ResultType.EdmType, typeUsage.EdmType))
			{
				throw new ArgumentException(Strings.Cqt_RelNav_WrongSourceType(typeUsage.ToString()), "from");
			}
		}

		// Token: 0x0600544F RID: 21583 RVA: 0x0012DE69 File Offset: 0x0012C069
		internal static void RequireCollectionArgument<TExpressionType>(DbExpression argument)
		{
			if (!TypeSemantics.IsCollectionType(argument.ResultType))
			{
				throw new ArgumentException(Strings.Cqt_Unary_CollectionRequired(typeof(TExpressionType).Name), "argument");
			}
		}

		// Token: 0x06005450 RID: 21584 RVA: 0x0012DE98 File Offset: 0x0012C098
		internal static TypeUsage RequireCollectionArguments<TExpressionType>(DbExpression left, DbExpression right)
		{
			if (!TypeSemantics.IsCollectionType(left.ResultType) || !TypeSemantics.IsCollectionType(right.ResultType))
			{
				throw new ArgumentException(Strings.Cqt_Binary_CollectionsRequired(typeof(TExpressionType).Name));
			}
			TypeUsage commonTypeUsage = TypeHelpers.GetCommonTypeUsage(left.ResultType, right.ResultType);
			if (commonTypeUsage == null)
			{
				throw new ArgumentException(Strings.Cqt_Binary_CollectionsRequired(typeof(TExpressionType).Name));
			}
			return commonTypeUsage;
		}

		// Token: 0x06005451 RID: 21585 RVA: 0x0012DF0C File Offset: 0x0012C10C
		internal static TypeUsage RequireComparableCollectionArguments<TExpressionType>(DbExpression left, DbExpression right)
		{
			TypeUsage typeUsage = ArgumentValidation.RequireCollectionArguments<TExpressionType>(left, right);
			if (!TypeHelpers.IsSetComparableOpType(TypeHelpers.GetElementTypeUsage(left.ResultType)))
			{
				throw new ArgumentException(Strings.Cqt_InvalidTypeForSetOperation(TypeHelpers.GetElementTypeUsage(left.ResultType).Identity, typeof(TExpressionType).Name), "left");
			}
			if (!TypeHelpers.IsSetComparableOpType(TypeHelpers.GetElementTypeUsage(right.ResultType)))
			{
				throw new ArgumentException(Strings.Cqt_InvalidTypeForSetOperation(TypeHelpers.GetElementTypeUsage(right.ResultType).Identity, typeof(TExpressionType).Name), "right");
			}
			return typeUsage;
		}

		// Token: 0x06005452 RID: 21586 RVA: 0x0012DFA2 File Offset: 0x0012C1A2
		private static EnumerableValidator<TElementIn, TElementOut, TResult> CreateValidator<TElementIn, TElementOut, TResult>(IEnumerable<TElementIn> argument, string argumentName, Func<TElementIn, int, TElementOut> convertElement, Func<List<TElementOut>, TResult> createResult)
		{
			return new EnumerableValidator<TElementIn, TElementOut, TResult>(argument, argumentName)
			{
				ConvertElement = convertElement,
				CreateResult = createResult
			};
		}

		// Token: 0x06005453 RID: 21587 RVA: 0x0012DFB9 File Offset: 0x0012C1B9
		internal static DbExpressionList CreateExpressionList(IEnumerable<DbExpression> arguments, string argumentName, Action<DbExpression, int> validationCallback)
		{
			return ArgumentValidation.CreateExpressionList(arguments, argumentName, false, validationCallback);
		}

		// Token: 0x06005454 RID: 21588 RVA: 0x0012DFC4 File Offset: 0x0012C1C4
		private static DbExpressionList CreateExpressionList(IEnumerable<DbExpression> arguments, string argumentName, bool allowEmpty, Action<DbExpression, int> validationCallback)
		{
			EnumerableValidator<DbExpression, DbExpression, DbExpressionList> enumerableValidator = ArgumentValidation.CreateValidator<DbExpression, DbExpression, DbExpressionList>(arguments, argumentName, delegate(DbExpression exp, int idx)
			{
				if (validationCallback != null)
				{
					validationCallback(exp, idx);
				}
				return exp;
			}, (List<DbExpression> expList) => new DbExpressionList(expList));
			enumerableValidator.AllowEmpty = allowEmpty;
			return enumerableValidator.Validate();
		}

		// Token: 0x06005455 RID: 21589 RVA: 0x0012E01C File Offset: 0x0012C21C
		private static DbExpressionList CreateExpressionList(IEnumerable<DbExpression> arguments, string argumentName, int expectedElementCount, Action<DbExpression, int> validationCallback)
		{
			EnumerableValidator<DbExpression, DbExpression, DbExpressionList> enumerableValidator = ArgumentValidation.CreateValidator<DbExpression, DbExpression, DbExpressionList>(arguments, argumentName, delegate(DbExpression exp, int idx)
			{
				if (validationCallback != null)
				{
					validationCallback(exp, idx);
				}
				return exp;
			}, (List<DbExpression> expList) => new DbExpressionList(expList));
			enumerableValidator.ExpectedElementCount = expectedElementCount;
			enumerableValidator.AllowEmpty = false;
			return enumerableValidator.Validate();
		}

		// Token: 0x06005456 RID: 21590 RVA: 0x0012E07B File Offset: 0x0012C27B
		private static FunctionParameter[] GetExpectedParameters(EdmFunction function)
		{
			return function.Parameters.Where((FunctionParameter p) => p.Mode == ParameterMode.In || p.Mode == ParameterMode.InOut).ToArray<FunctionParameter>();
		}

		// Token: 0x06005457 RID: 21591 RVA: 0x0012E0AC File Offset: 0x0012C2AC
		internal static DbExpressionList ValidateFunctionAggregate(EdmFunction function, IEnumerable<DbExpression> args)
		{
			ArgumentValidation.CheckFunction(function);
			if (!TypeSemantics.IsAggregateFunction(function) || function.ReturnParameter == null)
			{
				throw new ArgumentException(Strings.Cqt_Aggregate_InvalidFunction, "function");
			}
			FunctionParameter[] expectedParams = ArgumentValidation.GetExpectedParameters(function);
			return ArgumentValidation.CreateExpressionList(args, "argument", expectedParams.Length, delegate(DbExpression exp, int idx)
			{
				TypeUsage typeUsage = expectedParams[idx].TypeUsage;
				TypeUsage typeUsage2 = null;
				if (TypeHelpers.TryGetCollectionElementType(typeUsage, out typeUsage2))
				{
					typeUsage = typeUsage2;
				}
				ArgumentValidation.RequireCompatibleType(exp, typeUsage, "argument");
			});
		}

		// Token: 0x06005458 RID: 21592 RVA: 0x0012E110 File Offset: 0x0012C310
		internal static void ValidateSortClause(DbExpression key)
		{
			if (!TypeHelpers.IsValidSortOpKeyType(key.ResultType))
			{
				throw new ArgumentException(Strings.Cqt_Sort_OrderComparable, "key");
			}
		}

		// Token: 0x06005459 RID: 21593 RVA: 0x0012E12F File Offset: 0x0012C32F
		internal static void ValidateSortClause(DbExpression key, string collation)
		{
			ArgumentValidation.ValidateSortClause(key);
			Check.NotEmpty(collation, "collation");
			if (!TypeSemantics.IsPrimitiveType(key.ResultType, PrimitiveTypeKind.String))
			{
				throw new ArgumentException(Strings.Cqt_Sort_NonStringCollationInvalid, "collation");
			}
		}

		// Token: 0x0600545A RID: 21594 RVA: 0x0012E164 File Offset: 0x0012C364
		internal static ReadOnlyCollection<DbVariableReferenceExpression> ValidateLambda(IEnumerable<DbVariableReferenceExpression> variables)
		{
			EnumerableValidator<DbVariableReferenceExpression, DbVariableReferenceExpression, ReadOnlyCollection<DbVariableReferenceExpression>> enumerableValidator = ArgumentValidation.CreateValidator<DbVariableReferenceExpression, DbVariableReferenceExpression, ReadOnlyCollection<DbVariableReferenceExpression>>(variables, "variables", delegate(DbVariableReferenceExpression varExp, int idx)
			{
				if (varExp == null)
				{
					throw new ArgumentNullException(StringUtil.FormatIndex("variables", idx));
				}
				return varExp;
			}, (List<DbVariableReferenceExpression> varList) => new ReadOnlyCollection<DbVariableReferenceExpression>(varList));
			enumerableValidator.AllowEmpty = true;
			enumerableValidator.GetName = (DbVariableReferenceExpression varDef, int idx) => varDef.VariableName;
			return enumerableValidator.Validate();
		}

		// Token: 0x0600545B RID: 21595 RVA: 0x0012E1EB File Offset: 0x0012C3EB
		internal static TypeUsage ValidateQuantifier(DbExpression predicate)
		{
			ArgumentValidation.RequireCompatibleType(predicate, PrimitiveTypeKind.Boolean, "predicate");
			return predicate.ResultType;
		}

		// Token: 0x0600545C RID: 21596 RVA: 0x0012E200 File Offset: 0x0012C400
		internal static TypeUsage ValidateApply(DbExpressionBinding input, DbExpressionBinding apply)
		{
			if (input.VariableName.Equals(apply.VariableName, StringComparison.Ordinal))
			{
				throw new ArgumentException(Strings.Cqt_Apply_DuplicateVariableNames);
			}
			return ArgumentValidation.CreateCollectionOfRowResultType(new List<KeyValuePair<string, TypeUsage>>
			{
				new KeyValuePair<string, TypeUsage>(input.VariableName, input.VariableType),
				new KeyValuePair<string, TypeUsage>(apply.VariableName, apply.VariableType)
			});
		}

		// Token: 0x0600545D RID: 21597 RVA: 0x0012E264 File Offset: 0x0012C464
		internal static ReadOnlyCollection<DbExpressionBinding> ValidateCrossJoin(IEnumerable<DbExpressionBinding> inputs, out TypeUsage resultType)
		{
			List<DbExpressionBinding> list = new List<DbExpressionBinding>();
			List<KeyValuePair<string, TypeUsage>> list2 = new List<KeyValuePair<string, TypeUsage>>();
			Dictionary<string, int> dictionary = new Dictionary<string, int>();
			IEnumerator<DbExpressionBinding> enumerator = inputs.GetEnumerator();
			int num = 0;
			while (enumerator.MoveNext())
			{
				DbExpressionBinding dbExpressionBinding = enumerator.Current;
				string text = StringUtil.FormatIndex("inputs", num);
				if (dbExpressionBinding == null)
				{
					throw new ArgumentNullException(text);
				}
				int num2 = -1;
				if (dictionary.TryGetValue(dbExpressionBinding.VariableName, out num2))
				{
					throw new ArgumentException(Strings.Cqt_CrossJoin_DuplicateVariableNames(num2, num, dbExpressionBinding.VariableName));
				}
				list.Add(dbExpressionBinding);
				dictionary.Add(dbExpressionBinding.VariableName, num);
				list2.Add(new KeyValuePair<string, TypeUsage>(dbExpressionBinding.VariableName, dbExpressionBinding.VariableType));
				num++;
			}
			if (list.Count < 2)
			{
				throw new ArgumentException(Strings.Cqt_CrossJoin_AtLeastTwoInputs, "inputs");
			}
			resultType = ArgumentValidation.CreateCollectionOfRowResultType(list2);
			return new ReadOnlyCollection<DbExpressionBinding>(list);
		}

		// Token: 0x0600545E RID: 21598 RVA: 0x0012E350 File Offset: 0x0012C550
		internal static TypeUsage ValidateJoin(DbExpressionBinding left, DbExpressionBinding right, DbExpression joinCondition)
		{
			if (left.VariableName.Equals(right.VariableName, StringComparison.Ordinal))
			{
				throw new ArgumentException(Strings.Cqt_Join_DuplicateVariableNames);
			}
			ArgumentValidation.RequireCompatibleType(joinCondition, PrimitiveTypeKind.Boolean, "joinCondition");
			return ArgumentValidation.CreateCollectionOfRowResultType(new List<KeyValuePair<string, TypeUsage>>(2)
			{
				new KeyValuePair<string, TypeUsage>(left.VariableName, left.VariableType),
				new KeyValuePair<string, TypeUsage>(right.VariableName, right.VariableType)
			});
		}

		// Token: 0x0600545F RID: 21599 RVA: 0x0012E3C1 File Offset: 0x0012C5C1
		internal static TypeUsage ValidateFilter(DbExpressionBinding input, DbExpression predicate)
		{
			ArgumentValidation.RequireCompatibleType(predicate, PrimitiveTypeKind.Boolean, "predicate");
			return input.Expression.ResultType;
		}

		// Token: 0x06005460 RID: 21600 RVA: 0x0012E3DC File Offset: 0x0012C5DC
		internal static TypeUsage ValidateGroupBy(IEnumerable<KeyValuePair<string, DbExpression>> keys, IEnumerable<KeyValuePair<string, DbAggregate>> aggregates, out DbExpressionList validKeys, out ReadOnlyCollection<DbAggregate> validAggregates)
		{
			List<KeyValuePair<string, TypeUsage>> columns = new List<KeyValuePair<string, TypeUsage>>();
			HashSet<string> keyNames = new HashSet<string>();
			EnumerableValidator<KeyValuePair<string, DbExpression>, DbExpression, DbExpressionList> enumerableValidator = ArgumentValidation.CreateValidator<KeyValuePair<string, DbExpression>, DbExpression, DbExpressionList>(keys, "keys", delegate(KeyValuePair<string, DbExpression> keyInfo, int index)
			{
				ArgumentValidation.CheckNamed<DbExpression>(keyInfo, "keys", index);
				if (!TypeHelpers.IsValidGroupKeyType(keyInfo.Value.ResultType))
				{
					throw new ArgumentException(Strings.Cqt_GroupBy_KeyNotEqualityComparable(keyInfo.Key));
				}
				keyNames.Add(keyInfo.Key);
				columns.Add(new KeyValuePair<string, TypeUsage>(keyInfo.Key, keyInfo.Value.ResultType));
				return keyInfo.Value;
			}, (List<DbExpression> expList) => new DbExpressionList(expList));
			enumerableValidator.AllowEmpty = true;
			enumerableValidator.GetName = (KeyValuePair<string, DbExpression> keyInfo, int idx) => keyInfo.Key;
			validKeys = enumerableValidator.Validate();
			bool hasGroupAggregate = false;
			EnumerableValidator<KeyValuePair<string, DbAggregate>, DbAggregate, ReadOnlyCollection<DbAggregate>> enumerableValidator2 = ArgumentValidation.CreateValidator<KeyValuePair<string, DbAggregate>, DbAggregate, ReadOnlyCollection<DbAggregate>>(aggregates, "aggregates", delegate(KeyValuePair<string, DbAggregate> aggInfo, int idx)
			{
				ArgumentValidation.CheckNamed<DbAggregate>(aggInfo, "aggregates", idx);
				if (keyNames.Contains(aggInfo.Key))
				{
					throw new ArgumentException(Strings.Cqt_GroupBy_AggregateColumnExistsAsGroupColumn(aggInfo.Key));
				}
				if (aggInfo.Value is DbGroupAggregate)
				{
					if (hasGroupAggregate)
					{
						throw new ArgumentException(Strings.Cqt_GroupBy_MoreThanOneGroupAggregate);
					}
					hasGroupAggregate = true;
				}
				columns.Add(new KeyValuePair<string, TypeUsage>(aggInfo.Key, aggInfo.Value.ResultType));
				return aggInfo.Value;
			}, (List<DbAggregate> aggList) => ArgumentValidation.NewReadOnlyCollection<DbAggregate>(aggList));
			enumerableValidator2.AllowEmpty = true;
			enumerableValidator2.GetName = (KeyValuePair<string, DbAggregate> aggInfo, int idx) => aggInfo.Key;
			validAggregates = enumerableValidator2.Validate();
			if (validKeys.Count == 0 && validAggregates.Count == 0)
			{
				throw new ArgumentException(Strings.Cqt_GroupBy_AtLeastOneKeyOrAggregate);
			}
			return ArgumentValidation.CreateCollectionOfRowResultType(columns);
		}

		// Token: 0x06005461 RID: 21601 RVA: 0x0012E50C File Offset: 0x0012C70C
		internal static ReadOnlyCollection<DbSortClause> ValidateSortArguments(IEnumerable<DbSortClause> sortOrder)
		{
			EnumerableValidator<DbSortClause, DbSortClause, ReadOnlyCollection<DbSortClause>> enumerableValidator = ArgumentValidation.CreateValidator<DbSortClause, DbSortClause, ReadOnlyCollection<DbSortClause>>(sortOrder, "sortOrder", (DbSortClause key, int idx) => key, (List<DbSortClause> keyList) => ArgumentValidation.NewReadOnlyCollection<DbSortClause>(keyList));
			enumerableValidator.AllowEmpty = false;
			return enumerableValidator.Validate();
		}

		// Token: 0x06005462 RID: 21602 RVA: 0x0012E56E File Offset: 0x0012C76E
		internal static ReadOnlyCollection<DbSortClause> ValidateSort(IEnumerable<DbSortClause> sortOrder)
		{
			return ArgumentValidation.ValidateSortArguments(sortOrder);
		}

		// Token: 0x06005463 RID: 21603 RVA: 0x0012E578 File Offset: 0x0012C778
		internal static TypeUsage ValidateConstant(Type type)
		{
			PrimitiveTypeKind primitiveTypeKind;
			if (!ArgumentValidation.TryGetPrimitiveTypeKind(type, out primitiveTypeKind))
			{
				throw new ArgumentException(Strings.Cqt_Constant_InvalidType, "type");
			}
			return TypeHelpers.GetLiteralTypeUsage(primitiveTypeKind);
		}

		// Token: 0x06005464 RID: 21604 RVA: 0x0012E5A5 File Offset: 0x0012C7A5
		internal static TypeUsage ValidateConstant(object value)
		{
			return ArgumentValidation.ValidateConstant(value.GetType());
		}

		// Token: 0x06005465 RID: 21605 RVA: 0x0012E5B4 File Offset: 0x0012C7B4
		internal static void ValidateConstant(TypeUsage constantType, object value)
		{
			ArgumentValidation.CheckType(constantType, "constantType");
			EnumType enumType;
			if (TypeHelpers.TryGetEdmType<EnumType>(constantType, out enumType))
			{
				Type clrEquivalentType = enumType.UnderlyingType.ClrEquivalentType;
				if (clrEquivalentType != value.GetType() && (!value.GetType().IsEnum() || !ArgumentValidation.ClrEdmEnumTypesMatch(enumType, value.GetType())))
				{
					throw new ArgumentException(Strings.Cqt_Constant_ClrEnumTypeDoesNotMatchEdmEnumType(value.GetType().Name, enumType.Name, clrEquivalentType.Name), "value");
				}
			}
			else
			{
				PrimitiveType primitiveType;
				if (!TypeHelpers.TryGetEdmType<PrimitiveType>(constantType, out primitiveType))
				{
					throw new ArgumentException(Strings.Cqt_Constant_InvalidConstantType(constantType.ToString()), "constantType");
				}
				PrimitiveTypeKind primitiveTypeKind;
				if ((!ArgumentValidation.TryGetPrimitiveTypeKind(value.GetType(), out primitiveTypeKind) || primitiveType.PrimitiveTypeKind != primitiveTypeKind) && (!Helper.IsGeographicType(primitiveType) || primitiveTypeKind != PrimitiveTypeKind.Geography) && (!Helper.IsGeometricType(primitiveType) || primitiveTypeKind != PrimitiveTypeKind.Geometry))
				{
					throw new ArgumentException(Strings.Cqt_Constant_InvalidValueForType(constantType.ToString()), "value");
				}
			}
		}

		// Token: 0x06005466 RID: 21606 RVA: 0x0012E6A0 File Offset: 0x0012C8A0
		internal static TypeUsage ValidateCreateRef(EntitySet entitySet, EntityType entityType, IEnumerable<DbExpression> keyValues, out DbExpression keyConstructor)
		{
			ArgumentValidation.CheckEntitySet(entitySet, "entitySet");
			ArgumentValidation.CheckType(entityType, "entityType");
			if (!TypeSemantics.IsValidPolymorphicCast(entitySet.ElementType, entityType))
			{
				throw new ArgumentException(Strings.Cqt_Ref_PolymorphicArgRequired);
			}
			IList<EdmMember> keyMembers = entityType.KeyMembers;
			EnumerableValidator<DbExpression, KeyValuePair<string, DbExpression>, List<KeyValuePair<string, DbExpression>>> enumerableValidator = ArgumentValidation.CreateValidator<DbExpression, KeyValuePair<string, DbExpression>, List<KeyValuePair<string, DbExpression>>>(keyValues, "keyValues", delegate(DbExpression valueExp, int idx)
			{
				ArgumentValidation.RequireCompatibleType(valueExp, keyMembers[idx].TypeUsage, "keyValues", idx);
				return new KeyValuePair<string, DbExpression>(keyMembers[idx].Name, valueExp);
			}, (List<KeyValuePair<string, DbExpression>> columnList) => columnList);
			enumerableValidator.ExpectedElementCount = keyMembers.Count;
			List<KeyValuePair<string, DbExpression>> list = enumerableValidator.Validate();
			keyConstructor = DbExpressionBuilder.NewRow(list);
			return ArgumentValidation.CreateReferenceResultType(entityType);
		}

		// Token: 0x06005467 RID: 21607 RVA: 0x0012E74C File Offset: 0x0012C94C
		internal static TypeUsage ValidateRefFromKey(EntitySet entitySet, DbExpression keyValues, EntityType entityType)
		{
			ArgumentValidation.CheckEntitySet(entitySet, "entitySet");
			ArgumentValidation.CheckType(entityType);
			if (!TypeSemantics.IsValidPolymorphicCast(entitySet.ElementType, entityType))
			{
				throw new ArgumentException(Strings.Cqt_Ref_PolymorphicArgRequired);
			}
			TypeUsage typeUsage = ArgumentValidation.CreateResultType(TypeHelpers.CreateKeyRowType(entitySet.ElementType));
			ArgumentValidation.RequireCompatibleType(keyValues, typeUsage, "keyValues");
			return ArgumentValidation.CreateReferenceResultType(entityType);
		}

		// Token: 0x06005468 RID: 21608 RVA: 0x0012E7A8 File Offset: 0x0012C9A8
		internal static TypeUsage ValidateNavigate(DbExpression navigateFrom, RelationshipType type, string fromEndName, string toEndName, out RelationshipEndMember fromEnd, out RelationshipEndMember toEnd)
		{
			ArgumentValidation.CheckType(type);
			if (!type.RelationshipEndMembers.TryGetValue(fromEndName, false, out fromEnd))
			{
				throw new ArgumentOutOfRangeException(fromEndName, Strings.Cqt_Factory_NoSuchRelationEnd);
			}
			if (!type.RelationshipEndMembers.TryGetValue(toEndName, false, out toEnd))
			{
				throw new ArgumentOutOfRangeException(toEndName, Strings.Cqt_Factory_NoSuchRelationEnd);
			}
			ArgumentValidation.RequireCompatibleType(navigateFrom, fromEnd, false);
			return ArgumentValidation.CreateResultType(toEnd);
		}

		// Token: 0x06005469 RID: 21609 RVA: 0x0012E808 File Offset: 0x0012CA08
		internal static TypeUsage ValidateNavigate(DbExpression navigateFrom, RelationshipEndMember fromEnd, RelationshipEndMember toEnd, out RelationshipType relType, bool allowAllRelationshipsInSameTypeHierarchy)
		{
			ArgumentValidation.CheckMember(fromEnd, "fromEnd");
			ArgumentValidation.CheckMember(toEnd, "toEnd");
			relType = fromEnd.DeclaringType as RelationshipType;
			ArgumentValidation.CheckType(relType);
			if (!relType.Equals(toEnd.DeclaringType))
			{
				throw new ArgumentException(Strings.Cqt_Factory_IncompatibleRelationEnds, "toEnd");
			}
			ArgumentValidation.RequireCompatibleType(navigateFrom, fromEnd, allowAllRelationshipsInSameTypeHierarchy);
			return ArgumentValidation.CreateResultType(toEnd);
		}

		// Token: 0x0600546A RID: 21610 RVA: 0x0012E86D File Offset: 0x0012CA6D
		internal static TypeUsage ValidateElement(DbExpression argument)
		{
			ArgumentValidation.RequireCollectionArgument<DbElementExpression>(argument);
			return TypeHelpers.GetEdmType<CollectionType>(argument.ResultType).TypeUsage;
		}

		// Token: 0x0600546B RID: 21611 RVA: 0x0012E888 File Offset: 0x0012CA88
		internal static TypeUsage ValidateCase(IEnumerable<DbExpression> whenExpressions, IEnumerable<DbExpression> thenExpressions, DbExpression elseExpression, out DbExpressionList validWhens, out DbExpressionList validThens)
		{
			validWhens = ArgumentValidation.CreateExpressionList(whenExpressions, "whenExpressions", delegate(DbExpression exp, int idx)
			{
				ArgumentValidation.RequireCompatibleType(exp, PrimitiveTypeKind.Boolean, "whenExpressions", idx);
			});
			TypeUsage commonResultType = null;
			validThens = ArgumentValidation.CreateExpressionList(thenExpressions, "thenExpressions", delegate(DbExpression exp, int idx)
			{
				if (commonResultType == null)
				{
					commonResultType = exp.ResultType;
					return;
				}
				commonResultType = TypeHelpers.GetCommonTypeUsage(exp.ResultType, commonResultType);
				if (commonResultType == null)
				{
					throw new ArgumentException(Strings.Cqt_Case_InvalidResultType);
				}
			});
			commonResultType = TypeHelpers.GetCommonTypeUsage(elseExpression.ResultType, commonResultType);
			if (commonResultType == null)
			{
				throw new ArgumentException(Strings.Cqt_Case_InvalidResultType);
			}
			if (validWhens.Count != validThens.Count)
			{
				throw new ArgumentException(Strings.Cqt_Case_WhensMustEqualThens);
			}
			return commonResultType;
		}

		// Token: 0x0600546C RID: 21612 RVA: 0x0012E934 File Offset: 0x0012CB34
		internal static TypeUsage ValidateFunction(EdmFunction function, IEnumerable<DbExpression> arguments, out DbExpressionList validArgs)
		{
			ArgumentValidation.CheckFunction(function);
			if (!function.IsComposableAttribute)
			{
				throw new ArgumentException(Strings.Cqt_Function_NonComposableInExpression, "function");
			}
			if (!string.IsNullOrEmpty(function.CommandTextAttribute) && !function.HasUserDefinedBody)
			{
				throw new ArgumentException(Strings.Cqt_Function_CommandTextInExpression, "function");
			}
			if (function.ReturnParameter == null)
			{
				throw new ArgumentException(Strings.Cqt_Function_VoidResultInvalid, "function");
			}
			FunctionParameter[] expectedParams = ArgumentValidation.GetExpectedParameters(function);
			validArgs = ArgumentValidation.CreateExpressionList(arguments, "arguments", expectedParams.Length, delegate(DbExpression exp, int idx)
			{
				ArgumentValidation.RequireCompatibleType(exp, expectedParams[idx].TypeUsage, "arguments", idx);
			});
			return function.ReturnParameter.TypeUsage;
		}

		// Token: 0x0600546D RID: 21613 RVA: 0x0012E9DC File Offset: 0x0012CBDC
		internal static TypeUsage ValidateInvoke(DbLambda lambda, IEnumerable<DbExpression> arguments, out DbExpressionList validArguments)
		{
			validArguments = null;
			EnumerableValidator<DbExpression, DbExpression, DbExpressionList> enumerableValidator = ArgumentValidation.CreateValidator<DbExpression, DbExpression, DbExpressionList>(arguments, "arguments", delegate(DbExpression exp, int idx)
			{
				ArgumentValidation.RequireCompatibleType(exp, lambda.Variables[idx].ResultType, "arguments", idx);
				return exp;
			}, (List<DbExpression> expList) => new DbExpressionList(expList));
			enumerableValidator.ExpectedElementCount = lambda.Variables.Count;
			validArguments = enumerableValidator.Validate();
			return lambda.Body.ResultType;
		}

		// Token: 0x0600546E RID: 21614 RVA: 0x0012EA5E File Offset: 0x0012CC5E
		internal static TypeUsage ValidateNewEmptyCollection(TypeUsage collectionType, out DbExpressionList validElements)
		{
			ArgumentValidation.CheckType(collectionType, "collectionType");
			if (!TypeSemantics.IsCollectionType(collectionType))
			{
				throw new ArgumentException(Strings.Cqt_NewInstance_CollectionTypeRequired, "collectionType");
			}
			validElements = new DbExpressionList(new DbExpression[0]);
			return collectionType;
		}

		// Token: 0x0600546F RID: 21615 RVA: 0x0012EA94 File Offset: 0x0012CC94
		internal static TypeUsage ValidateNewRow(IEnumerable<KeyValuePair<string, DbExpression>> columnValues, out DbExpressionList validElements)
		{
			List<KeyValuePair<string, TypeUsage>> columnTypes = new List<KeyValuePair<string, TypeUsage>>();
			EnumerableValidator<KeyValuePair<string, DbExpression>, DbExpression, DbExpressionList> enumerableValidator = ArgumentValidation.CreateValidator<KeyValuePair<string, DbExpression>, DbExpression, DbExpressionList>(columnValues, "columnValues", delegate(KeyValuePair<string, DbExpression> columnValue, int idx)
			{
				ArgumentValidation.CheckNamed<DbExpression>(columnValue, "columnValues", idx);
				columnTypes.Add(new KeyValuePair<string, TypeUsage>(columnValue.Key, columnValue.Value.ResultType));
				return columnValue.Value;
			}, (List<DbExpression> expList) => new DbExpressionList(expList));
			enumerableValidator.GetName = (KeyValuePair<string, DbExpression> columnValue, int idx) => columnValue.Key;
			validElements = enumerableValidator.Validate();
			return ArgumentValidation.CreateResultType(TypeHelpers.CreateRowType(columnTypes));
		}

		// Token: 0x06005470 RID: 21616 RVA: 0x0012EB28 File Offset: 0x0012CD28
		internal static TypeUsage ValidateNew(TypeUsage instanceType, IEnumerable<DbExpression> arguments, out DbExpressionList validArguments)
		{
			ArgumentValidation.CheckType(instanceType, "instanceType");
			CollectionType collectionType = null;
			if (TypeHelpers.TryGetEdmType<CollectionType>(instanceType, out collectionType) && collectionType != null)
			{
				TypeUsage elementType = collectionType.TypeUsage;
				validArguments = ArgumentValidation.CreateExpressionList(arguments, "arguments", true, delegate(DbExpression exp, int idx)
				{
					ArgumentValidation.RequireCompatibleType(exp, elementType, "arguments", idx);
				});
			}
			else
			{
				List<TypeUsage> expectedTypes = ArgumentValidation.GetStructuralMemberTypes(instanceType);
				int pos = 0;
				validArguments = ArgumentValidation.CreateExpressionList(arguments, "arguments", expectedTypes.Count, delegate(DbExpression exp, int idx)
				{
					List<TypeUsage> expectedTypes2 = expectedTypes;
					int pos2 = pos;
					pos = pos2 + 1;
					ArgumentValidation.RequireCompatibleType(exp, expectedTypes2[pos2], "arguments", idx);
				});
			}
			return instanceType;
		}

		// Token: 0x06005471 RID: 21617 RVA: 0x0012EBBC File Offset: 0x0012CDBC
		private static List<TypeUsage> GetStructuralMemberTypes(TypeUsage instanceType)
		{
			StructuralType structuralType = instanceType.EdmType as StructuralType;
			if (structuralType == null)
			{
				throw new ArgumentException(Strings.Cqt_NewInstance_StructuralTypeRequired, "instanceType");
			}
			if (structuralType.Abstract)
			{
				throw new ArgumentException(Strings.Cqt_NewInstance_CannotInstantiateAbstractType(instanceType.ToString()), "instanceType");
			}
			IBaseList<EdmMember> allStructuralMembers = TypeHelpers.GetAllStructuralMembers(structuralType);
			if (allStructuralMembers == null || allStructuralMembers.Count < 1)
			{
				throw new ArgumentException(Strings.Cqt_NewInstance_CannotInstantiateMemberlessType(instanceType.ToString()), "instanceType");
			}
			List<TypeUsage> list = new List<TypeUsage>(allStructuralMembers.Count);
			for (int i = 0; i < allStructuralMembers.Count; i++)
			{
				list.Add(Helper.GetModelTypeUsage(allStructuralMembers[i]));
			}
			return list;
		}

		// Token: 0x06005472 RID: 21618 RVA: 0x0012EC60 File Offset: 0x0012CE60
		internal static TypeUsage ValidateNewEntityWithRelationships(EntityType entityType, IEnumerable<DbExpression> attributeValues, IList<DbRelatedEntityRef> relationships, out DbExpressionList validArguments, out ReadOnlyCollection<DbRelatedEntityRef> validRelatedRefs)
		{
			TypeUsage typeUsage = ArgumentValidation.CreateResultType(entityType);
			typeUsage = ArgumentValidation.ValidateNew(typeUsage, attributeValues, out validArguments);
			if (relationships.Count > 0)
			{
				List<DbRelatedEntityRef> list = new List<DbRelatedEntityRef>(relationships.Count);
				for (int i = 0; i < relationships.Count; i++)
				{
					DbRelatedEntityRef dbRelatedEntityRef = relationships[i];
					EntityTypeBase elementType = TypeHelpers.GetEdmType<RefType>(dbRelatedEntityRef.SourceEnd.TypeUsage).ElementType;
					if (!entityType.EdmEquals(elementType) && !entityType.IsSubtypeOf(elementType))
					{
						throw new ArgumentException(Strings.Cqt_NewInstance_IncompatibleRelatedEntity_SourceTypeNotValid, StringUtil.FormatIndex("relationships", i));
					}
					list.Add(dbRelatedEntityRef);
				}
				validRelatedRefs = new ReadOnlyCollection<DbRelatedEntityRef>(list);
			}
			else
			{
				validRelatedRefs = new ReadOnlyCollection<DbRelatedEntityRef>(new DbRelatedEntityRef[0]);
			}
			return typeUsage;
		}

		// Token: 0x06005473 RID: 21619 RVA: 0x0012ED10 File Offset: 0x0012CF10
		internal static TypeUsage ValidateProperty(DbExpression instance, string propertyName, bool ignoreCase, out EdmMember foundMember)
		{
			StructuralType structuralType;
			if (TypeHelpers.TryGetEdmType<StructuralType>(instance.ResultType, out structuralType) && structuralType.Members.TryGetValue(propertyName, ignoreCase, out foundMember) && foundMember != null && (Helper.IsRelationshipEndMember(foundMember) || Helper.IsEdmProperty(foundMember) || Helper.IsNavigationProperty(foundMember)))
			{
				return Helper.GetModelTypeUsage(foundMember);
			}
			throw new ArgumentOutOfRangeException("propertyName", Strings.NoSuchProperty(propertyName, instance.ResultType.ToString()));
		}

		// Token: 0x06005474 RID: 21620 RVA: 0x0012ED80 File Offset: 0x0012CF80
		private static void CheckNamed<T>(KeyValuePair<string, T> element, string argumentName, int index)
		{
			if (string.IsNullOrEmpty(element.Key))
			{
				if (index != -1)
				{
					argumentName = StringUtil.FormatIndex(argumentName, index);
				}
				throw new ArgumentNullException(string.Format(CultureInfo.InvariantCulture, "{0}.Key", new object[] { argumentName }));
			}
			if (element.Value == null)
			{
				if (index != -1)
				{
					argumentName = StringUtil.FormatIndex(argumentName, index);
				}
				throw new ArgumentNullException(string.Format(CultureInfo.InvariantCulture, "{0}.Value", new object[] { argumentName }));
			}
		}

		// Token: 0x06005475 RID: 21621 RVA: 0x0012EE01 File Offset: 0x0012D001
		private static void CheckReadOnly(GlobalItem item, string varName)
		{
			if (!item.IsReadOnly)
			{
				throw new ArgumentException(Strings.Cqt_General_MetadataNotReadOnly, varName);
			}
		}

		// Token: 0x06005476 RID: 21622 RVA: 0x0012EE17 File Offset: 0x0012D017
		private static void CheckReadOnly(TypeUsage item, string varName)
		{
			if (!item.IsReadOnly)
			{
				throw new ArgumentException(Strings.Cqt_General_MetadataNotReadOnly, varName);
			}
		}

		// Token: 0x06005477 RID: 21623 RVA: 0x0012EE2D File Offset: 0x0012D02D
		private static void CheckReadOnly(EntitySetBase item, string varName)
		{
			if (!item.IsReadOnly)
			{
				throw new ArgumentException(Strings.Cqt_General_MetadataNotReadOnly, varName);
			}
		}

		// Token: 0x06005478 RID: 21624 RVA: 0x0012EE43 File Offset: 0x0012D043
		private static void CheckType(EdmType type)
		{
			ArgumentValidation.CheckType(type, "type");
		}

		// Token: 0x06005479 RID: 21625 RVA: 0x0012EE50 File Offset: 0x0012D050
		private static void CheckType(EdmType type, string argumentName)
		{
			ArgumentValidation.CheckReadOnly(type, argumentName);
		}

		// Token: 0x0600547A RID: 21626 RVA: 0x0012EE59 File Offset: 0x0012D059
		internal static void CheckType(TypeUsage type)
		{
			ArgumentValidation.CheckType(type, "type");
		}

		// Token: 0x0600547B RID: 21627 RVA: 0x0012EE66 File Offset: 0x0012D066
		internal static void CheckType(TypeUsage type, string varName)
		{
			ArgumentValidation.CheckReadOnly(type, varName);
			if (!ArgumentValidation.CheckDataSpace(type))
			{
				throw new ArgumentException(Strings.Cqt_Metadata_TypeUsageIncorrectSpace, "type");
			}
		}

		// Token: 0x0600547C RID: 21628 RVA: 0x0012EE87 File Offset: 0x0012D087
		internal static void CheckMember(EdmMember memberMeta, string varName)
		{
			ArgumentValidation.CheckReadOnly(memberMeta.DeclaringType, varName);
			if (!ArgumentValidation.CheckDataSpace(memberMeta.TypeUsage) || !ArgumentValidation.CheckDataSpace(memberMeta.DeclaringType))
			{
				throw new ArgumentException(Strings.Cqt_Metadata_EdmMemberIncorrectSpace, varName);
			}
		}

		// Token: 0x0600547D RID: 21629 RVA: 0x0012EEBB File Offset: 0x0012D0BB
		private static void CheckParameter(FunctionParameter paramMeta, string varName)
		{
			ArgumentValidation.CheckReadOnly(paramMeta.DeclaringFunction, varName);
			if (!ArgumentValidation.CheckDataSpace(paramMeta.TypeUsage))
			{
				throw new ArgumentException(Strings.Cqt_Metadata_FunctionParameterIncorrectSpace, varName);
			}
		}

		// Token: 0x0600547E RID: 21630 RVA: 0x0012EEE4 File Offset: 0x0012D0E4
		private static void CheckFunction(EdmFunction function)
		{
			ArgumentValidation.CheckReadOnly(function, "function");
			if (!ArgumentValidation.CheckDataSpace(function))
			{
				throw new ArgumentException(Strings.Cqt_Metadata_FunctionIncorrectSpace, "function");
			}
			if (function.IsComposableAttribute && function.ReturnParameter == null)
			{
				throw new ArgumentException(Strings.Cqt_Metadata_FunctionReturnParameterNull, "function");
			}
			if (function.ReturnParameter != null && !ArgumentValidation.CheckDataSpace(function.ReturnParameter.TypeUsage))
			{
				throw new ArgumentException(Strings.Cqt_Metadata_FunctionParameterIncorrectSpace, "function.ReturnParameter");
			}
			IList<FunctionParameter> parameters = function.Parameters;
			for (int i = 0; i < parameters.Count; i++)
			{
				ArgumentValidation.CheckParameter(parameters[i], StringUtil.FormatIndex("function.Parameters", i));
			}
		}

		// Token: 0x0600547F RID: 21631 RVA: 0x0012EF90 File Offset: 0x0012D190
		internal static void CheckEntitySet(EntitySetBase entitySet, string varName)
		{
			ArgumentValidation.CheckReadOnly(entitySet, varName);
			if (entitySet.EntityContainer == null)
			{
				throw new ArgumentException(Strings.Cqt_Metadata_EntitySetEntityContainerNull, varName);
			}
			if (!ArgumentValidation.CheckDataSpace(entitySet.EntityContainer))
			{
				throw new ArgumentException(Strings.Cqt_Metadata_EntitySetIncorrectSpace, varName);
			}
			if (!ArgumentValidation.CheckDataSpace(entitySet.ElementType))
			{
				throw new ArgumentException(Strings.Cqt_Metadata_EntitySetIncorrectSpace, varName);
			}
		}

		// Token: 0x06005480 RID: 21632 RVA: 0x0012EFEA File Offset: 0x0012D1EA
		private static bool CheckDataSpace(TypeUsage type)
		{
			return ArgumentValidation.CheckDataSpace(type.EdmType);
		}

		// Token: 0x06005481 RID: 21633 RVA: 0x0012EFF8 File Offset: 0x0012D1F8
		private static bool CheckDataSpace(GlobalItem item)
		{
			if (BuiltInTypeKind.PrimitiveType == item.BuiltInTypeKind || (BuiltInTypeKind.EdmFunction == item.BuiltInTypeKind && DataSpace.CSpace == item.DataSpace))
			{
				return true;
			}
			if (Helper.IsRowType(item))
			{
				using (ReadOnlyMetadataCollection<EdmProperty>.Enumerator enumerator = ((RowType)item).Properties.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						if (!ArgumentValidation.CheckDataSpace(enumerator.Current.TypeUsage))
						{
							return false;
						}
					}
				}
				return true;
			}
			if (Helper.IsCollectionType(item))
			{
				return ArgumentValidation.CheckDataSpace(((CollectionType)item).TypeUsage);
			}
			if (Helper.IsRefType(item))
			{
				return ArgumentValidation.CheckDataSpace(((RefType)item).ElementType);
			}
			return item.DataSpace == DataSpace.SSpace || item.DataSpace == DataSpace.CSpace;
		}

		// Token: 0x06005482 RID: 21634 RVA: 0x0012F0CC File Offset: 0x0012D2CC
		internal static TypeUsage CreateCollectionOfRowResultType(List<KeyValuePair<string, TypeUsage>> columns)
		{
			return TypeUsage.Create(TypeHelpers.CreateCollectionType(TypeUsage.Create(TypeHelpers.CreateRowType(columns))));
		}

		// Token: 0x06005483 RID: 21635 RVA: 0x0012F0E3 File Offset: 0x0012D2E3
		private static TypeUsage CreateResultType(EdmType resultType)
		{
			return TypeUsage.Create(resultType);
		}

		// Token: 0x06005484 RID: 21636 RVA: 0x0012F0EC File Offset: 0x0012D2EC
		private static TypeUsage CreateResultType(RelationshipEndMember end)
		{
			TypeUsage typeUsage = end.TypeUsage;
			if (!TypeSemantics.IsReferenceType(typeUsage))
			{
				typeUsage = TypeHelpers.CreateReferenceTypeUsage(TypeHelpers.GetEdmType<EntityType>(typeUsage));
			}
			if (RelationshipMultiplicity.Many == end.RelationshipMultiplicity)
			{
				typeUsage = TypeHelpers.CreateCollectionTypeUsage(typeUsage);
			}
			return typeUsage;
		}

		// Token: 0x06005485 RID: 21637 RVA: 0x0012F125 File Offset: 0x0012D325
		internal static TypeUsage CreateReferenceResultType(EntityTypeBase referencedEntityType)
		{
			return TypeUsage.Create(TypeHelpers.CreateReferenceType(referencedEntityType));
		}

		// Token: 0x06005486 RID: 21638 RVA: 0x0012F132 File Offset: 0x0012D332
		private static bool TryGetPrimitiveTypeKind(Type clrType, out PrimitiveTypeKind primitiveTypeKind)
		{
			return ClrProviderManifest.TryGetPrimitiveTypeKind(clrType, out primitiveTypeKind);
		}

		// Token: 0x06005487 RID: 21639 RVA: 0x0012F13C File Offset: 0x0012D33C
		private static bool ClrEdmEnumTypesMatch(EnumType edmEnumType, Type clrEnumType)
		{
			if (clrEnumType.Name != edmEnumType.Name || clrEnumType.GetEnumNames().Length < edmEnumType.Members.Count)
			{
				return false;
			}
			PrimitiveTypeKind primitiveTypeKind;
			if (!ArgumentValidation.TryGetPrimitiveTypeKind(clrEnumType.GetEnumUnderlyingType(), out primitiveTypeKind) || primitiveTypeKind != edmEnumType.UnderlyingType.PrimitiveTypeKind)
			{
				return false;
			}
			foreach (EnumMember enumMember in edmEnumType.Members)
			{
				if (!clrEnumType.GetEnumNames().Contains(enumMember.Name) || !enumMember.Value.Equals(Convert.ChangeType(Enum.Parse(clrEnumType, enumMember.Name), clrEnumType.GetEnumUnderlyingType(), CultureInfo.InvariantCulture)))
				{
					return false;
				}
			}
			return true;
		}
	}
}
