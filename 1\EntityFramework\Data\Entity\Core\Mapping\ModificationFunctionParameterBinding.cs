﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;
using System.Globalization;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x02000550 RID: 1360
	public sealed class ModificationFunctionParameterBinding : MappingItem
	{
		// Token: 0x060042DE RID: 17118 RVA: 0x000E52E2 File Offset: 0x000E34E2
		public ModificationFunctionParameterBinding(FunctionParameter parameter, ModificationFunctionMemberPath memberPath, bool isCurrent)
		{
			Check.NotNull<FunctionParameter>(parameter, "parameter");
			Check.NotNull<ModificationFunctionMemberPath>(memberPath, "memberPath");
			this._parameter = parameter;
			this._memberPath = memberPath;
			this._isCurrent = isCurrent;
		}

		// Token: 0x17000D3D RID: 3389
		// (get) Token: 0x060042DF RID: 17119 RVA: 0x000E5317 File Offset: 0x000E3517
		public FunctionParameter Parameter
		{
			get
			{
				return this._parameter;
			}
		}

		// Token: 0x17000D3E RID: 3390
		// (get) Token: 0x060042E0 RID: 17120 RVA: 0x000E531F File Offset: 0x000E351F
		public ModificationFunctionMemberPath MemberPath
		{
			get
			{
				return this._memberPath;
			}
		}

		// Token: 0x17000D3F RID: 3391
		// (get) Token: 0x060042E1 RID: 17121 RVA: 0x000E5327 File Offset: 0x000E3527
		public bool IsCurrent
		{
			get
			{
				return this._isCurrent;
			}
		}

		// Token: 0x060042E2 RID: 17122 RVA: 0x000E532F File Offset: 0x000E352F
		public override string ToString()
		{
			return string.Format(CultureInfo.InvariantCulture, "@{0}->{1}{2}", new object[]
			{
				this.Parameter,
				this.IsCurrent ? "+" : "-",
				this.MemberPath
			});
		}

		// Token: 0x060042E3 RID: 17123 RVA: 0x000E536F File Offset: 0x000E356F
		internal override void SetReadOnly()
		{
			MappingItem.SetReadOnly(this._memberPath);
			base.SetReadOnly();
		}

		// Token: 0x04001789 RID: 6025
		private readonly FunctionParameter _parameter;

		// Token: 0x0400178A RID: 6026
		private readonly ModificationFunctionMemberPath _memberPath;

		// Token: 0x0400178B RID: 6027
		private readonly bool _isCurrent;
	}
}
