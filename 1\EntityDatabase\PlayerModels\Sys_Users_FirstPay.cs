﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x0200002A RID: 42
	public class Sys_Users_FirstPay
	{
		// Token: 0x17000146 RID: 326
		// (get) Token: 0x060002B5 RID: 693 RVA: 0x0000377A File Offset: 0x0000197A
		// (set) Token: 0x060002B6 RID: 694 RVA: 0x00003782 File Offset: 0x00001982
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000147 RID: 327
		// (get) Token: 0x060002B7 RID: 695 RVA: 0x0000378B File Offset: 0x0000198B
		// (set) Token: 0x060002B8 RID: 696 RVA: 0x00003793 File Offset: 0x00001993
		public int UserID { get; set; }

		// Token: 0x17000148 RID: 328
		// (get) Token: 0x060002B9 RID: 697 RVA: 0x0000379C File Offset: 0x0000199C
		// (set) Token: 0x060002BA RID: 698 RVA: 0x000037A4 File Offset: 0x000019A4
		public string IsComplate { get; set; }

		// Token: 0x17000149 RID: 329
		// (get) Token: 0x060002BB RID: 699 RVA: 0x000037AD File Offset: 0x000019AD
		// (set) Token: 0x060002BC RID: 700 RVA: 0x000037B5 File Offset: 0x000019B5
		public string IsGetReward { get; set; }

		// Token: 0x1700014A RID: 330
		// (get) Token: 0x060002BD RID: 701 RVA: 0x000037BE File Offset: 0x000019BE
		// (set) Token: 0x060002BE RID: 702 RVA: 0x000037C6 File Offset: 0x000019C6
		public bool IsUseCard { get; set; }

		// Token: 0x1700014B RID: 331
		// (get) Token: 0x060002BF RID: 703 RVA: 0x000037CF File Offset: 0x000019CF
		// (set) Token: 0x060002C0 RID: 704 RVA: 0x000037D7 File Offset: 0x000019D7
		public DateTime CardEndDate { get; set; }

		// Token: 0x1700014C RID: 332
		// (get) Token: 0x060002C1 RID: 705 RVA: 0x000037E0 File Offset: 0x000019E0
		// (set) Token: 0x060002C2 RID: 706 RVA: 0x000037E8 File Offset: 0x000019E8
		public string SendName { get; set; }

		// Token: 0x1700014D RID: 333
		// (get) Token: 0x060002C3 RID: 707 RVA: 0x000037F1 File Offset: 0x000019F1
		// (set) Token: 0x060002C4 RID: 708 RVA: 0x000037F9 File Offset: 0x000019F9
		public int GoldMoney { get; set; }

		// Token: 0x1700014E RID: 334
		// (get) Token: 0x060002C5 RID: 709 RVA: 0x00003802 File Offset: 0x00001A02
		// (set) Token: 0x060002C6 RID: 710 RVA: 0x0000380A File Offset: 0x00001A0A
		public string RechargeMoney { get; set; }
	}
}
