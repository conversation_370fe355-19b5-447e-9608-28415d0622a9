﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000022 RID: 34
	public class Sys_MonthCard_Info
	{
		// Token: 0x17000109 RID: 265
		// (get) Token: 0x06000233 RID: 563 RVA: 0x00003325 File Offset: 0x00001525
		// (set) Token: 0x06000234 RID: 564 RVA: 0x0000332D File Offset: 0x0000152D
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x1700010A RID: 266
		// (get) Token: 0x06000235 RID: 565 RVA: 0x00003336 File Offset: 0x00001536
		// (set) Token: 0x06000236 RID: 566 RVA: 0x0000333E File Offset: 0x0000153E
		public int UserID { get; set; }

		// Token: 0x1700010B RID: 267
		// (get) Token: 0x06000237 RID: 567 RVA: 0x00003347 File Offset: 0x00001547
		// (set) Token: 0x06000238 RID: 568 RVA: 0x0000334F File Offset: 0x0000154F
		public int RewardID1Days { get; set; }

		// Token: 0x1700010C RID: 268
		// (get) Token: 0x06000239 RID: 569 RVA: 0x00003358 File Offset: 0x00001558
		// (set) Token: 0x0600023A RID: 570 RVA: 0x00003360 File Offset: 0x00001560
		public int RewardID1Count { get; set; }

		// Token: 0x1700010D RID: 269
		// (get) Token: 0x0600023B RID: 571 RVA: 0x00003369 File Offset: 0x00001569
		// (set) Token: 0x0600023C RID: 572 RVA: 0x00003371 File Offset: 0x00001571
		public int RewardID2Days { get; set; }

		// Token: 0x1700010E RID: 270
		// (get) Token: 0x0600023D RID: 573 RVA: 0x0000337A File Offset: 0x0000157A
		// (set) Token: 0x0600023E RID: 574 RVA: 0x00003382 File Offset: 0x00001582
		public int RewardID2Count { get; set; }

		// Token: 0x1700010F RID: 271
		// (get) Token: 0x0600023F RID: 575 RVA: 0x0000338B File Offset: 0x0000158B
		// (set) Token: 0x06000240 RID: 576 RVA: 0x00003393 File Offset: 0x00001593
		public bool CanGetRewardID1 { get; set; }

		// Token: 0x17000110 RID: 272
		// (get) Token: 0x06000241 RID: 577 RVA: 0x0000339C File Offset: 0x0000159C
		// (set) Token: 0x06000242 RID: 578 RVA: 0x000033A4 File Offset: 0x000015A4
		public bool CanGetRewardID2 { get; set; }
	}
}
