﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000025 RID: 37
	public class Sys_Users_CaveLoot_Progress
	{
		// Token: 0x17000121 RID: 289
		// (get) Token: 0x06000266 RID: 614 RVA: 0x000034D8 File Offset: 0x000016D8
		// (set) Token: 0x06000267 RID: 615 RVA: 0x000034E0 File Offset: 0x000016E0
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000122 RID: 290
		// (get) Token: 0x06000268 RID: 616 RVA: 0x000034E9 File Offset: 0x000016E9
		// (set) Token: 0x06000269 RID: 617 RVA: 0x000034F1 File Offset: 0x000016F1
		public int UserID { get; set; }

		// Token: 0x17000123 RID: 291
		// (get) Token: 0x0600026A RID: 618 RVA: 0x000034FA File Offset: 0x000016FA
		// (set) Token: 0x0600026B RID: 619 RVA: 0x00003502 File Offset: 0x00001702
		public int ProgressID { get; set; }

		// Token: 0x17000124 RID: 292
		// (get) Token: 0x0600026C RID: 620 RVA: 0x0000350B File Offset: 0x0000170B
		// (set) Token: 0x0600026D RID: 621 RVA: 0x00003513 File Offset: 0x00001713
		public int CurrentNum { get; set; }

		// Token: 0x17000125 RID: 293
		// (get) Token: 0x0600026E RID: 622 RVA: 0x0000351C File Offset: 0x0000171C
		// (set) Token: 0x0600026F RID: 623 RVA: 0x00003524 File Offset: 0x00001724
		public bool IsRewardClaimed { get; set; }
	}
}
