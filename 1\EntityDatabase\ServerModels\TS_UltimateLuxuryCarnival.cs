﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000017 RID: 23
	public class TS_UltimateLuxuryCarnival
	{
		// Token: 0x17000098 RID: 152
		// (get) Token: 0x06000145 RID: 325 RVA: 0x00002B38 File Offset: 0x00000D38
		// (set) Token: 0x06000146 RID: 326 RVA: 0x00002B40 File Offset: 0x00000D40
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int ID { get; set; }

		// Token: 0x17000099 RID: 153
		// (get) Token: 0x06000147 RID: 327 RVA: 0x00002B49 File Offset: 0x00000D49
		// (set) Token: 0x06000148 RID: 328 RVA: 0x00002B51 File Offset: 0x00000D51
		public int Round { get; set; }

		// Token: 0x1700009A RID: 154
		// (get) Token: 0x06000149 RID: 329 RVA: 0x00002B5A File Offset: 0x00000D5A
		// (set) Token: 0x0600014A RID: 330 RVA: 0x00002B62 File Offset: 0x00000D62
		public int ItemID { get; set; }

		// Token: 0x1700009B RID: 155
		// (get) Token: 0x0600014B RID: 331 RVA: 0x00002B6B File Offset: 0x00000D6B
		// (set) Token: 0x0600014C RID: 332 RVA: 0x00002B73 File Offset: 0x00000D73
		public int Validay { get; set; }

		// Token: 0x1700009C RID: 156
		// (get) Token: 0x0600014D RID: 333 RVA: 0x00002B7C File Offset: 0x00000D7C
		// (set) Token: 0x0600014E RID: 334 RVA: 0x00002B84 File Offset: 0x00000D84
		public int Count { get; set; }

		// Token: 0x1700009D RID: 157
		// (get) Token: 0x0600014F RID: 335 RVA: 0x00002B8D File Offset: 0x00000D8D
		// (set) Token: 0x06000150 RID: 336 RVA: 0x00002B95 File Offset: 0x00000D95
		public bool IsBind { get; set; }

		// Token: 0x1700009E RID: 158
		// (get) Token: 0x06000151 RID: 337 RVA: 0x00002B9E File Offset: 0x00000D9E
		// (set) Token: 0x06000152 RID: 338 RVA: 0x00002BA6 File Offset: 0x00000DA6
		public int OriginalPrice { get; set; }

		// Token: 0x1700009F RID: 159
		// (get) Token: 0x06000153 RID: 339 RVA: 0x00002BAF File Offset: 0x00000DAF
		// (set) Token: 0x06000154 RID: 340 RVA: 0x00002BB7 File Offset: 0x00000DB7
		public int DiscountedPrice { get; set; }

		// Token: 0x170000A0 RID: 160
		// (get) Token: 0x06000155 RID: 341 RVA: 0x00002BC0 File Offset: 0x00000DC0
		// (set) Token: 0x06000156 RID: 342 RVA: 0x00002BC8 File Offset: 0x00000DC8
		public int LimitNum { get; set; }
	}
}
