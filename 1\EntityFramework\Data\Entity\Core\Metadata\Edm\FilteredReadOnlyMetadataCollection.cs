﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.Entity.Resources;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004C6 RID: 1222
	internal class FilteredReadOnlyMetadataCollection<TDerived, TBase> : ReadOnlyMetadataCollection<TDerived>, IBaseList<TBase>, IList, ICollection, IEnumerable where TDerived : TBase where TBase : MetadataItem
	{
		// Token: 0x06003C77 RID: 15479 RVA: 0x000C7B28 File Offset: 0x000C5D28
		internal FilteredReadOnlyMetadataCollection(ReadOnlyMetadataCollection<TBase> collection, Predicate<TBase> predicate)
			: base(FilteredReadOnlyMetadataCollection<TDerived, TBase>.FilterCollection(collection, predicate))
		{
			this._source = collection;
			this._predicate = predicate;
		}

		// Token: 0x17000BE8 RID: 3048
		public override TDerived this[string identity]
		{
			get
			{
				TBase tbase = this._source[identity];
				if (this._predicate(tbase))
				{
					return (TDerived)((object)tbase);
				}
				throw new ArgumentException(Strings.ItemInvalidIdentity(identity), "identity");
			}
		}

		// Token: 0x06003C79 RID: 15481 RVA: 0x000C7B8C File Offset: 0x000C5D8C
		public override TDerived GetValue(string identity, bool ignoreCase)
		{
			TBase value = this._source.GetValue(identity, ignoreCase);
			if (this._predicate(value))
			{
				return (TDerived)((object)value);
			}
			throw new ArgumentException(Strings.ItemInvalidIdentity(identity), "identity");
		}

		// Token: 0x06003C7A RID: 15482 RVA: 0x000C7BD4 File Offset: 0x000C5DD4
		public override bool Contains(string identity)
		{
			TBase tbase;
			return this._source.TryGetValue(identity, false, out tbase) && this._predicate(tbase);
		}

		// Token: 0x06003C7B RID: 15483 RVA: 0x000C7C00 File Offset: 0x000C5E00
		public override bool TryGetValue(string identity, bool ignoreCase, out TDerived item)
		{
			item = default(TDerived);
			TBase tbase;
			if (this._source.TryGetValue(identity, ignoreCase, out tbase) && this._predicate(tbase))
			{
				item = (TDerived)((object)tbase);
				return true;
			}
			return false;
		}

		// Token: 0x06003C7C RID: 15484 RVA: 0x000C7C48 File Offset: 0x000C5E48
		internal static List<TDerived> FilterCollection(ReadOnlyMetadataCollection<TBase> collection, Predicate<TBase> predicate)
		{
			List<TDerived> list = new List<TDerived>(collection.Count);
			for (int i = 0; i < collection.Count; i++)
			{
				TBase tbase = collection[i];
				if (predicate(tbase))
				{
					list.Add((TDerived)((object)tbase));
				}
			}
			return list;
		}

		// Token: 0x06003C7D RID: 15485 RVA: 0x000C7C98 File Offset: 0x000C5E98
		public override int IndexOf(TDerived value)
		{
			TBase tbase;
			if (this._source.TryGetValue(value.Identity, false, out tbase) && this._predicate(tbase))
			{
				return base.IndexOf((TDerived)((object)tbase));
			}
			return -1;
		}

		// Token: 0x17000BE9 RID: 3049
		TBase IBaseList<TBase>.this[string identity]
		{
			get
			{
				return (TBase)((object)this[identity]);
			}
		}

		// Token: 0x17000BEA RID: 3050
		TBase IBaseList<TBase>.this[int index]
		{
			get
			{
				return (TBase)((object)base[index]);
			}
		}

		// Token: 0x06003C80 RID: 15488 RVA: 0x000C7D07 File Offset: 0x000C5F07
		int IBaseList<TBase>.IndexOf(TBase item)
		{
			if (this._predicate(item))
			{
				return this.IndexOf((TDerived)((object)item));
			}
			return -1;
		}

		// Token: 0x06003C81 RID: 15489 RVA: 0x000C7D2A File Offset: 0x000C5F2A
		bool IList.get_IsReadOnly()
		{
			return base.IsReadOnly;
		}

		// Token: 0x040014D3 RID: 5331
		private readonly ReadOnlyMetadataCollection<TBase> _source;

		// Token: 0x040014D4 RID: 5332
		private readonly Predicate<TBase> _predicate;
	}
}
