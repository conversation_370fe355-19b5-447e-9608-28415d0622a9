﻿using System;
using System.Linq;

namespace System.Data.Entity.Core.Common.Utils.Boolean
{
	// Token: 0x02000623 RID: 1571
	internal class ToDecisionDiagramConverter<T_Identifier> : Visitor<T_Identifier, Vertex>
	{
		// Token: 0x06004C10 RID: 19472 RVA: 0x0010B180 File Offset: 0x00109380
		private ToDecisionDiagramConverter(ConversionContext<T_Identifier> context)
		{
			this._context = context;
		}

		// Token: 0x06004C11 RID: 19473 RVA: 0x0010B190 File Offset: 0x00109390
		internal static Vertex TranslateToRobdd(BoolExpr<T_Identifier> expr, ConversionContext<T_Identifier> context)
		{
			ToDecisionDiagramConverter<T_Identifier> toDecisionDiagramConverter = new ToDecisionDiagramConverter<T_Identifier>(context);
			return expr.Accept<Vertex>(toDecisionDiagramConverter);
		}

		// Token: 0x06004C12 RID: 19474 RVA: 0x0010B1AB File Offset: 0x001093AB
		internal override Vertex VisitTrue(TrueExpr<T_Identifier> expression)
		{
			return Vertex.One;
		}

		// Token: 0x06004C13 RID: 19475 RVA: 0x0010B1B2 File Offset: 0x001093B2
		internal override Vertex VisitFalse(FalseExpr<T_Identifier> expression)
		{
			return Vertex.Zero;
		}

		// Token: 0x06004C14 RID: 19476 RVA: 0x0010B1B9 File Offset: 0x001093B9
		internal override Vertex VisitTerm(TermExpr<T_Identifier> expression)
		{
			return this._context.TranslateTermToVertex(expression);
		}

		// Token: 0x06004C15 RID: 19477 RVA: 0x0010B1C7 File Offset: 0x001093C7
		internal override Vertex VisitNot(NotExpr<T_Identifier> expression)
		{
			return this._context.Solver.Not(expression.Child.Accept<Vertex>(this));
		}

		// Token: 0x06004C16 RID: 19478 RVA: 0x0010B1E5 File Offset: 0x001093E5
		internal override Vertex VisitAnd(AndExpr<T_Identifier> expression)
		{
			return this._context.Solver.And(expression.Children.Select((BoolExpr<T_Identifier> child) => child.Accept<Vertex>(this)));
		}

		// Token: 0x06004C17 RID: 19479 RVA: 0x0010B20E File Offset: 0x0010940E
		internal override Vertex VisitOr(OrExpr<T_Identifier> expression)
		{
			return this._context.Solver.Or(expression.Children.Select((BoolExpr<T_Identifier> child) => child.Accept<Vertex>(this)));
		}

		// Token: 0x04001A8D RID: 6797
		private readonly ConversionContext<T_Identifier> _context;
	}
}
