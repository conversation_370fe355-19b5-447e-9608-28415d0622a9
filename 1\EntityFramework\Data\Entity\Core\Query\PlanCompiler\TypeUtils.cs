﻿using System;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Metadata.Edm;

namespace System.Data.Entity.Core.Query.PlanCompiler
{
	// Token: 0x02000375 RID: 885
	internal static class TypeUtils
	{
		// Token: 0x06002ADC RID: 10972 RVA: 0x0008BE50 File Offset: 0x0008A050
		internal static bool IsStructuredType(TypeUsage type)
		{
			return TypeSemantics.IsReferenceType(type) || TypeSemantics.IsRowType(type) || TypeSemantics.IsEntityType(type) || TypeSemantics.IsRelationshipType(type) || TypeSemantics.IsComplexType(type);
		}

		// Token: 0x06002ADD RID: 10973 RVA: 0x0008BE7A File Offset: 0x0008A07A
		internal static bool IsCollectionType(TypeUsage type)
		{
			return TypeSemantics.IsCollectionType(type);
		}

		// Token: 0x06002ADE RID: 10974 RVA: 0x0008BE82 File Offset: 0x0008A082
		internal static bool IsEnumerationType(TypeUsage type)
		{
			return TypeSemantics.IsEnumerationType(type);
		}

		// Token: 0x06002ADF RID: 10975 RVA: 0x0008BE8A File Offset: 0x0008A08A
		internal static TypeUsage CreateCollectionType(TypeUsage elementType)
		{
			return TypeHelpers.CreateCollectionTypeUsage(elementType);
		}
	}
}
