﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;

namespace System.Data.Entity.Core.Mapping.Update.Internal
{
	// Token: 0x020005D7 RID: 1495
	internal sealed class UpdateCompiler
	{
		// Token: 0x0600481D RID: 18461 RVA: 0x000FF3C4 File Offset: 0x000FD5C4
		internal UpdateCompiler(UpdateTranslator translator)
		{
			this.m_translator = translator;
		}

		// Token: 0x0600481E RID: 18462 RVA: 0x000FF3D4 File Offset: 0x000FD5D4
		internal UpdateCommand BuildDeleteCommand(PropagatorResult oldRow, TableChangeProcessor processor)
		{
			bool flag = true;
			DbExpressionBinding target = UpdateCompiler.GetTarget(processor);
			DbExpression dbExpression = this.BuildPredicate(target, oldRow, null, processor, ref flag);
			DbDeleteCommandTree dbDeleteCommandTree = new DbDeleteCommandTree(this.m_translator.MetadataWorkspace, DataSpace.SSpace, target, dbExpression);
			return new DynamicUpdateCommand(processor, this.m_translator, ModificationOperator.Delete, oldRow, null, dbDeleteCommandTree, null);
		}

		// Token: 0x0600481F RID: 18463 RVA: 0x000FF41C File Offset: 0x000FD61C
		internal UpdateCommand BuildUpdateCommand(PropagatorResult oldRow, PropagatorResult newRow, TableChangeProcessor processor)
		{
			bool flag = false;
			DbExpressionBinding target = UpdateCompiler.GetTarget(processor);
			List<DbModificationClause> list = new List<DbModificationClause>();
			Dictionary<int, string> dictionary;
			DbExpression dbExpression;
			foreach (DbModificationClause dbModificationClause in this.BuildSetClauses(target, newRow, oldRow, processor, false, out dictionary, out dbExpression, ref flag))
			{
				list.Add(dbModificationClause);
			}
			DbExpression dbExpression2 = this.BuildPredicate(target, oldRow, newRow, processor, ref flag);
			if (list.Count == 0)
			{
				if (flag)
				{
					List<IEntityStateEntry> list2 = new List<IEntityStateEntry>();
					list2.AddRange(SourceInterpreter.GetAllStateEntries(oldRow, this.m_translator, processor.Table));
					list2.AddRange(SourceInterpreter.GetAllStateEntries(newRow, this.m_translator, processor.Table));
					if (list2.All((IEntityStateEntry it) => it.State == EntityState.Unchanged))
					{
						flag = false;
					}
				}
				if (!flag)
				{
					return null;
				}
			}
			DbUpdateCommandTree dbUpdateCommandTree = new DbUpdateCommandTree(this.m_translator.MetadataWorkspace, DataSpace.SSpace, target, dbExpression2, new ReadOnlyCollection<DbModificationClause>(list), dbExpression);
			return new DynamicUpdateCommand(processor, this.m_translator, ModificationOperator.Update, oldRow, newRow, dbUpdateCommandTree, dictionary);
		}

		// Token: 0x06004820 RID: 18464 RVA: 0x000FF538 File Offset: 0x000FD738
		internal UpdateCommand BuildInsertCommand(PropagatorResult newRow, TableChangeProcessor processor)
		{
			DbExpressionBinding target = UpdateCompiler.GetTarget(processor);
			bool flag = true;
			List<DbModificationClause> list = new List<DbModificationClause>();
			Dictionary<int, string> dictionary;
			DbExpression dbExpression;
			foreach (DbModificationClause dbModificationClause in this.BuildSetClauses(target, newRow, null, processor, true, out dictionary, out dbExpression, ref flag))
			{
				list.Add(dbModificationClause);
			}
			DbInsertCommandTree dbInsertCommandTree = new DbInsertCommandTree(this.m_translator.MetadataWorkspace, DataSpace.SSpace, target, new ReadOnlyCollection<DbModificationClause>(list), dbExpression);
			return new DynamicUpdateCommand(processor, this.m_translator, ModificationOperator.Insert, null, newRow, dbInsertCommandTree, dictionary);
		}

		// Token: 0x06004821 RID: 18465 RVA: 0x000FF5D8 File Offset: 0x000FD7D8
		private IEnumerable<DbModificationClause> BuildSetClauses(DbExpressionBinding target, PropagatorResult row, PropagatorResult originalRow, TableChangeProcessor processor, bool insertMode, out Dictionary<int, string> outputIdentifiers, out DbExpression returning, ref bool rowMustBeTouched)
		{
			Dictionary<EdmProperty, PropagatorResult> dictionary = new Dictionary<EdmProperty, PropagatorResult>();
			List<KeyValuePair<string, DbExpression>> list = new List<KeyValuePair<string, DbExpression>>();
			outputIdentifiers = new Dictionary<int, string>();
			PropagatorFlags propagatorFlags = (insertMode ? PropagatorFlags.NoFlags : (PropagatorFlags.Preserve | PropagatorFlags.Unknown));
			for (int i = 0; i < processor.Table.ElementType.Properties.Count; i++)
			{
				EdmProperty edmProperty = processor.Table.ElementType.Properties[i];
				PropagatorResult propagatorResult = row.GetMemberValue(i);
				if (-1 != propagatorResult.Identifier)
				{
					propagatorResult = propagatorResult.ReplicateResultWithNewValue(this.m_translator.KeyManager.GetPrincipalValue(propagatorResult));
				}
				bool flag = false;
				bool flag2 = false;
				for (int j = 0; j < processor.KeyOrdinals.Length; j++)
				{
					if (processor.KeyOrdinals[j] == i)
					{
						flag2 = true;
						break;
					}
				}
				PropagatorFlags propagatorFlags2 = PropagatorFlags.NoFlags;
				if (!insertMode && flag2)
				{
					flag = true;
				}
				else
				{
					propagatorFlags2 |= propagatorResult.PropagatorFlags;
				}
				StoreGeneratedPattern storeGeneratedPattern = MetadataHelper.GetStoreGeneratedPattern(edmProperty);
				bool flag3 = storeGeneratedPattern == StoreGeneratedPattern.Computed || (insertMode && storeGeneratedPattern == StoreGeneratedPattern.Identity);
				if (flag3)
				{
					DbPropertyExpression dbPropertyExpression = target.Variable.Property(edmProperty);
					list.Add(new KeyValuePair<string, DbExpression>(edmProperty.Name, dbPropertyExpression));
					int identifier = propagatorResult.Identifier;
					if (-1 != identifier)
					{
						if (this.m_translator.KeyManager.HasPrincipals(identifier))
						{
							throw new InvalidOperationException(Strings.Update_GeneratedDependent(edmProperty.Name));
						}
						outputIdentifiers.Add(identifier, edmProperty.Name);
						if (storeGeneratedPattern != StoreGeneratedPattern.Identity && processor.IsKeyProperty(i))
						{
							throw new NotSupportedException(Strings.Update_NotSupportedComputedKeyColumn("StoreGeneratedPattern", "Computed", "Identity", edmProperty.Name, edmProperty.DeclaringType.FullName));
						}
					}
				}
				if ((propagatorFlags2 & propagatorFlags) != PropagatorFlags.NoFlags)
				{
					flag = true;
				}
				else if (flag3)
				{
					flag = true;
					rowMustBeTouched = true;
				}
				if (!flag && !insertMode && storeGeneratedPattern == StoreGeneratedPattern.Identity)
				{
					PropagatorResult memberValue = originalRow.GetMemberValue(i);
					if (!ByValueEqualityComparer.Default.Equals(memberValue.GetSimpleValue(), propagatorResult.GetSimpleValue()))
					{
						throw new InvalidOperationException(Strings.Update_ModifyingIdentityColumn("Identity", edmProperty.Name, edmProperty.DeclaringType.FullName));
					}
					flag = true;
				}
				if (!flag)
				{
					dictionary.Add(edmProperty, propagatorResult);
				}
			}
			if (0 < list.Count)
			{
				returning = DbExpressionBuilder.NewRow(list);
			}
			else
			{
				returning = null;
			}
			List<DbModificationClause> list2 = new List<DbModificationClause>(dictionary.Count);
			foreach (KeyValuePair<EdmProperty, PropagatorResult> keyValuePair in dictionary)
			{
				list2.Add(new DbSetClause(UpdateCompiler.GeneratePropertyExpression(target, keyValuePair.Key), this.GenerateValueExpression(keyValuePair.Key, keyValuePair.Value)));
			}
			return list2;
		}

		// Token: 0x06004822 RID: 18466 RVA: 0x000FF89C File Offset: 0x000FDA9C
		private DbExpression BuildPredicate(DbExpressionBinding target, PropagatorResult referenceRow, PropagatorResult current, TableChangeProcessor processor, ref bool rowMustBeTouched)
		{
			Dictionary<EdmProperty, PropagatorResult> dictionary = new Dictionary<EdmProperty, PropagatorResult>();
			int num = 0;
			foreach (EdmProperty edmProperty in processor.Table.ElementType.Properties)
			{
				PropagatorResult memberValue = referenceRow.GetMemberValue(num);
				PropagatorResult propagatorResult = ((current == null) ? null : current.GetMemberValue(num));
				if (!rowMustBeTouched && (UpdateCompiler.HasFlag(memberValue, PropagatorFlags.ConcurrencyValue) || UpdateCompiler.HasFlag(propagatorResult, PropagatorFlags.ConcurrencyValue)))
				{
					rowMustBeTouched = true;
				}
				if (!dictionary.ContainsKey(edmProperty) && (UpdateCompiler.HasFlag(memberValue, PropagatorFlags.ConcurrencyValue | PropagatorFlags.Key) || UpdateCompiler.HasFlag(propagatorResult, PropagatorFlags.ConcurrencyValue | PropagatorFlags.Key)))
				{
					dictionary.Add(edmProperty, memberValue);
				}
				num++;
			}
			DbExpression dbExpression = null;
			foreach (KeyValuePair<EdmProperty, PropagatorResult> keyValuePair in dictionary)
			{
				DbExpression dbExpression2 = this.GenerateEqualityExpression(target, keyValuePair.Key, keyValuePair.Value);
				if (dbExpression == null)
				{
					dbExpression = dbExpression2;
				}
				else
				{
					dbExpression = dbExpression.And(dbExpression2);
				}
			}
			return dbExpression;
		}

		// Token: 0x06004823 RID: 18467 RVA: 0x000FF9C4 File Offset: 0x000FDBC4
		private DbExpression GenerateEqualityExpression(DbExpressionBinding target, EdmProperty property, PropagatorResult value)
		{
			DbExpression dbExpression = UpdateCompiler.GeneratePropertyExpression(target, property);
			DbExpression dbExpression2 = this.GenerateValueExpression(property, value);
			if (dbExpression2.ExpressionKind == DbExpressionKind.Null)
			{
				return dbExpression.IsNull();
			}
			return dbExpression.Equal(dbExpression2);
		}

		// Token: 0x06004824 RID: 18468 RVA: 0x000FF9FA File Offset: 0x000FDBFA
		private static DbExpression GeneratePropertyExpression(DbExpressionBinding target, EdmProperty property)
		{
			return target.Variable.Property(property);
		}

		// Token: 0x06004825 RID: 18469 RVA: 0x000FFA08 File Offset: 0x000FDC08
		private DbExpression GenerateValueExpression(EdmProperty property, PropagatorResult value)
		{
			if (value.IsNull)
			{
				return Helper.GetModelTypeUsage(property).Null();
			}
			object obj = this.m_translator.KeyManager.GetPrincipalValue(value);
			if (Convert.IsDBNull(obj))
			{
				return Helper.GetModelTypeUsage(property).Null();
			}
			TypeUsage modelTypeUsage = Helper.GetModelTypeUsage(property);
			Type type = obj.GetType();
			if (type.IsEnum())
			{
				obj = Convert.ChangeType(obj, type.GetEnumUnderlyingType(), CultureInfo.InvariantCulture);
			}
			Type clrEquivalentType = ((PrimitiveType)modelTypeUsage.EdmType).ClrEquivalentType;
			if (type != clrEquivalentType)
			{
				obj = Convert.ChangeType(obj, clrEquivalentType, CultureInfo.InvariantCulture);
			}
			return modelTypeUsage.Constant(obj);
		}

		// Token: 0x06004826 RID: 18470 RVA: 0x000FFAA3 File Offset: 0x000FDCA3
		private static bool HasFlag(PropagatorResult input, PropagatorFlags flags)
		{
			return input != null && (flags & input.PropagatorFlags) > PropagatorFlags.NoFlags;
		}

		// Token: 0x06004827 RID: 18471 RVA: 0x000FFAB5 File Offset: 0x000FDCB5
		private static DbExpressionBinding GetTarget(TableChangeProcessor processor)
		{
			return processor.Table.Scan().BindAs("target");
		}

		// Token: 0x040019A4 RID: 6564
		internal readonly UpdateTranslator m_translator;

		// Token: 0x040019A5 RID: 6565
		private const string s_targetVarName = "target";
	}
}
