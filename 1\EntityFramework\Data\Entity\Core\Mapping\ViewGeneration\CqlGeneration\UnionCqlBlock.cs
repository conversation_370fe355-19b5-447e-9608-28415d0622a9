﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.CqlGeneration
{
	// Token: 0x020005C0 RID: 1472
	internal sealed class UnionCqlBlock : CqlBlock
	{
		// Token: 0x0600476A RID: 18282 RVA: 0x000FB56E File Offset: 0x000F976E
		internal UnionCqlBlock(SlotInfo[] slotInfos, List<CqlBlock> children, CqlIdentifiers identifiers, int blockAliasNum)
			: base(slotInfos, children, BoolExpression.True, identifiers, blockAliasNum)
		{
		}

		// Token: 0x0600476B RID: 18283 RVA: 0x000FB580 File Offset: 0x000F9780
		internal override StringBuilder AsEsql(StringBuilder builder, bool isTopLevel, int indentLevel)
		{
			bool flag = true;
			foreach (CqlBlock cqlBlock in base.Children)
			{
				if (!flag)
				{
					StringUtil.IndentNewLine(builder, indentLevel + 1);
					builder.Append(OpCellTreeNode.OpToEsql(CellTreeOpType.Union));
				}
				flag = false;
				builder.Append(" (");
				cqlBlock.AsEsql(builder, isTopLevel, indentLevel + 1);
				builder.Append(')');
			}
			return builder;
		}

		// Token: 0x0600476C RID: 18284 RVA: 0x000FB604 File Offset: 0x000F9804
		internal override DbExpression AsCqt(bool isTopLevel)
		{
			DbExpression dbExpression = base.Children[0].AsCqt(isTopLevel);
			for (int i = 1; i < base.Children.Count; i++)
			{
				dbExpression = dbExpression.UnionAll(base.Children[i].AsCqt(isTopLevel));
			}
			return dbExpression;
		}
	}
}
