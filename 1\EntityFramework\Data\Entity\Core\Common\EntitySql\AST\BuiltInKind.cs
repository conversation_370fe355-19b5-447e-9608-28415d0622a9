﻿using System;

namespace System.Data.Entity.Core.Common.EntitySql.AST
{
	// Token: 0x02000678 RID: 1656
	internal enum BuiltInKind
	{
		// Token: 0x04001CA4 RID: 7332
		And,
		// Token: 0x04001CA5 RID: 7333
		Or,
		// Token: 0x04001CA6 RID: 7334
		Not,
		// Token: 0x04001CA7 RID: 7335
		Cast,
		// Token: 0x04001CA8 RID: 7336
		OfType,
		// Token: 0x04001CA9 RID: 7337
		Treat,
		// Token: 0x04001CAA RID: 7338
		IsOf,
		// Token: 0x04001CAB RID: 7339
		Union,
		// Token: 0x04001CAC RID: 7340
		UnionAll,
		// Token: 0x04001CAD RID: 7341
		Intersect,
		// Token: 0x04001CAE RID: 7342
		Overlaps,
		// Token: 0x04001CAF RID: 7343
		AnyElement,
		// Token: 0x04001CB0 RID: 7344
		Element,
		// Token: 0x04001CB1 RID: 7345
		Except,
		// Token: 0x04001CB2 RID: 7346
		Exists,
		// Token: 0x04001CB3 RID: 7347
		Flatten,
		// Token: 0x04001CB4 RID: 7348
		In,
		// Token: 0x04001CB5 RID: 7349
		NotIn,
		// Token: 0x04001CB6 RID: 7350
		Distinct,
		// Token: 0x04001CB7 RID: 7351
		IsNull,
		// Token: 0x04001CB8 RID: 7352
		IsNotNull,
		// Token: 0x04001CB9 RID: 7353
		Like,
		// Token: 0x04001CBA RID: 7354
		Equal,
		// Token: 0x04001CBB RID: 7355
		NotEqual,
		// Token: 0x04001CBC RID: 7356
		LessEqual,
		// Token: 0x04001CBD RID: 7357
		LessThan,
		// Token: 0x04001CBE RID: 7358
		GreaterThan,
		// Token: 0x04001CBF RID: 7359
		GreaterEqual,
		// Token: 0x04001CC0 RID: 7360
		Plus,
		// Token: 0x04001CC1 RID: 7361
		Minus,
		// Token: 0x04001CC2 RID: 7362
		Multiply,
		// Token: 0x04001CC3 RID: 7363
		Divide,
		// Token: 0x04001CC4 RID: 7364
		Modulus,
		// Token: 0x04001CC5 RID: 7365
		UnaryMinus,
		// Token: 0x04001CC6 RID: 7366
		UnaryPlus,
		// Token: 0x04001CC7 RID: 7367
		Between,
		// Token: 0x04001CC8 RID: 7368
		NotBetween
	}
}
