﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000009 RID: 9
	public class DevilturnRankAward
	{
		// Token: 0x17000015 RID: 21
		// (get) Token: 0x06000030 RID: 48 RVA: 0x000021EA File Offset: 0x000003EA
		// (set) Token: 0x06000031 RID: 49 RVA: 0x000021F2 File Offset: 0x000003F2
		[Key]
		public int Rank { get; set; }

		// Token: 0x17000016 RID: 22
		// (get) Token: 0x06000032 RID: 50 RVA: 0x000021FB File Offset: 0x000003FB
		// (set) Token: 0x06000033 RID: 51 RVA: 0x00002203 File Offset: 0x00000403
		public int TemplatedID { get; set; }

		// Token: 0x17000017 RID: 23
		// (get) Token: 0x06000034 RID: 52 RVA: 0x0000220C File Offset: 0x0000040C
		// (set) Token: 0x06000035 RID: 53 RVA: 0x00002214 File Offset: 0x00000414
		public string Des { get; set; }
	}
}
