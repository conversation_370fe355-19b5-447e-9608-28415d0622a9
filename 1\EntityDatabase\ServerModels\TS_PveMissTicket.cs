﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000011 RID: 17
	public class TS_PveMissTicket
	{
		// Token: 0x17000063 RID: 99
		// (get) Token: 0x060000D5 RID: 213 RVA: 0x0000277D File Offset: 0x0000097D
		// (set) Token: 0x060000D6 RID: 214 RVA: 0x00002785 File Offset: 0x00000985
		[Key]
		public int PveID { get; set; }

		// Token: 0x17000064 RID: 100
		// (get) Token: 0x060000D7 RID: 215 RVA: 0x0000278E File Offset: 0x0000098E
		// (set) Token: 0x060000D8 RID: 216 RVA: 0x00002796 File Offset: 0x00000996
		public int TicketID { get; set; }

		// Token: 0x17000065 RID: 101
		// (get) Token: 0x060000D9 RID: 217 RVA: 0x0000279F File Offset: 0x0000099F
		// (set) Token: 0x060000DA RID: 218 RVA: 0x000027A7 File Offset: 0x000009A7
		public int TicketCount { get; set; }

		// Token: 0x17000066 RID: 102
		// (get) Token: 0x060000DB RID: 219 RVA: 0x000027B0 File Offset: 0x000009B0
		// (set) Token: 0x060000DC RID: 220 RVA: 0x000027B8 File Offset: 0x000009B8
		public int Attack { get; set; }

		// Token: 0x17000067 RID: 103
		// (get) Token: 0x060000DD RID: 221 RVA: 0x000027C1 File Offset: 0x000009C1
		// (set) Token: 0x060000DE RID: 222 RVA: 0x000027C9 File Offset: 0x000009C9
		public int Defence { get; set; }

		// Token: 0x17000068 RID: 104
		// (get) Token: 0x060000DF RID: 223 RVA: 0x000027D2 File Offset: 0x000009D2
		// (set) Token: 0x060000E0 RID: 224 RVA: 0x000027DA File Offset: 0x000009DA
		public int Agility { get; set; }

		// Token: 0x17000069 RID: 105
		// (get) Token: 0x060000E1 RID: 225 RVA: 0x000027E3 File Offset: 0x000009E3
		// (set) Token: 0x060000E2 RID: 226 RVA: 0x000027EB File Offset: 0x000009EB
		public int Luck { get; set; }

		// Token: 0x1700006A RID: 106
		// (get) Token: 0x060000E3 RID: 227 RVA: 0x000027F4 File Offset: 0x000009F4
		// (set) Token: 0x060000E4 RID: 228 RVA: 0x000027FC File Offset: 0x000009FC
		public int MinFightPower { get; set; }

		// Token: 0x1700006B RID: 107
		// (get) Token: 0x060000E5 RID: 229 RVA: 0x00002805 File Offset: 0x00000A05
		// (set) Token: 0x060000E6 RID: 230 RVA: 0x0000280D File Offset: 0x00000A0D
		public int MaxFightPower { get; set; }

		// Token: 0x1700006C RID: 108
		// (get) Token: 0x060000E7 RID: 231 RVA: 0x00002816 File Offset: 0x00000A16
		// (set) Token: 0x060000E8 RID: 232 RVA: 0x0000281E File Offset: 0x00000A1E
		public int IsAcadeMy { get; set; }

		// Token: 0x1700006D RID: 109
		// (get) Token: 0x060000E9 RID: 233 RVA: 0x00002827 File Offset: 0x00000A27
		// (set) Token: 0x060000EA RID: 234 RVA: 0x0000282F File Offset: 0x00000A2F
		public int IsCouples { get; set; }

		// Token: 0x1700006E RID: 110
		// (get) Token: 0x060000EB RID: 235 RVA: 0x00002838 File Offset: 0x00000A38
		// (set) Token: 0x060000EC RID: 236 RVA: 0x00002840 File Offset: 0x00000A40
		public int IsGuild { get; set; }

		// Token: 0x1700006F RID: 111
		// (get) Token: 0x060000ED RID: 237 RVA: 0x00002849 File Offset: 0x00000A49
		// (set) Token: 0x060000EE RID: 238 RVA: 0x00002851 File Offset: 0x00000A51
		public int MinPeople { get; set; }

		// Token: 0x17000070 RID: 112
		// (get) Token: 0x060000EF RID: 239 RVA: 0x0000285A File Offset: 0x00000A5A
		// (set) Token: 0x060000F0 RID: 240 RVA: 0x00002862 File Offset: 0x00000A62
		public int MaxPeople { get; set; }

		// Token: 0x17000071 RID: 113
		// (get) Token: 0x060000F1 RID: 241 RVA: 0x0000286B File Offset: 0x00000A6B
		// (set) Token: 0x060000F2 RID: 242 RVA: 0x00002873 File Offset: 0x00000A73
		public bool Equilibrium { get; set; }

		// Token: 0x17000072 RID: 114
		// (get) Token: 0x060000F3 RID: 243 RVA: 0x0000287C File Offset: 0x00000A7C
		// (set) Token: 0x060000F4 RID: 244 RVA: 0x00002884 File Offset: 0x00000A84
		public bool IsSelect { get; set; }
	}
}
