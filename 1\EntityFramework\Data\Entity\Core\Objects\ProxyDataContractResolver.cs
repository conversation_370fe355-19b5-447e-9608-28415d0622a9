﻿using System;
using System.Data.Entity.Utilities;
using System.Runtime.Serialization;
using System.Xml;

namespace System.Data.Entity.Core.Objects
{
	// Token: 0x0200042C RID: 1068
	public class ProxyDataContractResolver : DataContractResolver
	{
		// Token: 0x06003402 RID: 13314 RVA: 0x000A71C3 File Offset: 0x000A53C3
		public override Type ResolveName(string typeName, string typeNamespace, Type declaredType, DataContractResolver knownTypeResolver)
		{
			Check.NotEmpty(typeName, "typeName");
			Check.NotEmpty(typeNamespace, "typeNamespace");
			Check.NotNull<Type>(declaredType, "declaredType");
			Check.NotNull<DataContractResolver>(knownTypeResolver, "knownTypeResolver");
			return knownTypeResolver.ResolveName(typeName, typeNamespace, declaredType, null);
		}

		// Token: 0x06003403 RID: 13315 RVA: 0x000A7204 File Offset: 0x000A5404
		public override bool TryResolveType(Type type, Type declaredType, DataContractResolver knownTypeResolver, out XmlDictionaryString typeName, out XmlDictionaryString typeNamespace)
		{
			Check.NotNull<Type>(type, "type");
			Check.NotNull<Type>(declaredType, "declaredType");
			Check.NotNull<DataContractResolver>(knownTypeResolver, "knownTypeResolver");
			Type objectType = ObjectContext.GetObjectType(type);
			if (objectType != type)
			{
				XmlQualifiedName schemaTypeName = this._exporter.GetSchemaTypeName(objectType);
				XmlDictionary xmlDictionary = new XmlDictionary(2);
				typeName = new XmlDictionaryString(xmlDictionary, schemaTypeName.Name, 0);
				typeNamespace = new XmlDictionaryString(xmlDictionary, schemaTypeName.Namespace, 1);
				return true;
			}
			return knownTypeResolver.TryResolveType(type, declaredType, null, out typeName, out typeNamespace);
		}

		// Token: 0x040010D0 RID: 4304
		private readonly XsdDataContractExporter _exporter = new XsdDataContractExporter();
	}
}
