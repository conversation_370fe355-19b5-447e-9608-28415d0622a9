﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;

namespace System.Data.Entity.Core.Common.EntitySql
{
	// Token: 0x0200064B RID: 1611
	internal sealed class EntityContainerExpression : ExpressionResolution
	{
		// Token: 0x06004DF2 RID: 19954 RVA: 0x001177C6 File Offset: 0x001159C6
		internal EntityContainerExpression(EntityContainer entityContainer)
			: base(ExpressionResolutionClass.EntityContainer)
		{
			this.EntityContainer = entityContainer;
		}

		// Token: 0x17000EFD RID: 3837
		// (get) Token: 0x06004DF3 RID: 19955 RVA: 0x001177D6 File Offset: 0x001159D6
		internal override string ExpressionClassName
		{
			get
			{
				return EntityContainerExpression.EntityContainerClassName;
			}
		}

		// Token: 0x17000EFE RID: 3838
		// (get) Token: 0x06004DF4 RID: 19956 RVA: 0x001177DD File Offset: 0x001159DD
		internal static string EntityContainerClassName
		{
			get
			{
				return Strings.LocalizedEntityContainerExpression;
			}
		}

		// Token: 0x04001C2B RID: 7211
		internal readonly EntityContainer EntityContainer;
	}
}
