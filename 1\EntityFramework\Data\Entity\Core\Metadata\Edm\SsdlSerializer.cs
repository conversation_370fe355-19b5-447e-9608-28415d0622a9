﻿using System;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Xml;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004FB RID: 1275
	public class SsdlSerializer
	{
		// Token: 0x14000011 RID: 17
		// (add) Token: 0x06003F1E RID: 16158 RVA: 0x000D0E68 File Offset: 0x000CF068
		// (remove) Token: 0x06003F1F RID: 16159 RVA: 0x000D0EA0 File Offset: 0x000CF0A0
		public event EventHandler<DataModelErrorEventArgs> OnError;

		// Token: 0x06003F20 RID: 16160 RVA: 0x000D0ED8 File Offset: 0x000CF0D8
		public virtual bool Serialize(EdmModel dbDatabase, string provider, string providerManifestToken, XmlWriter xmlWriter, bool serializeDefaultNullability = true)
		{
			Check.NotNull<EdmModel>(dbDatabase, "dbDatabase");
			Check.NotEmpty(provider, "provider");
			Check.NotEmpty(providerManifestToken, "providerManifestToken");
			Check.NotNull<XmlWriter>(xmlWriter, "xmlWriter");
			if (this.ValidateModel(dbDatabase))
			{
				SsdlSerializer.CreateVisitor(xmlWriter, dbDatabase, serializeDefaultNullability).Visit(dbDatabase, provider, providerManifestToken);
				return true;
			}
			return false;
		}

		// Token: 0x06003F21 RID: 16161 RVA: 0x000D0F34 File Offset: 0x000CF134
		public virtual bool Serialize(EdmModel dbDatabase, string namespaceName, string provider, string providerManifestToken, XmlWriter xmlWriter, bool serializeDefaultNullability = true)
		{
			Check.NotNull<EdmModel>(dbDatabase, "dbDatabase");
			Check.NotEmpty(namespaceName, "namespaceName");
			Check.NotEmpty(provider, "provider");
			Check.NotEmpty(providerManifestToken, "providerManifestToken");
			Check.NotNull<XmlWriter>(xmlWriter, "xmlWriter");
			if (this.ValidateModel(dbDatabase))
			{
				SsdlSerializer.CreateVisitor(xmlWriter, dbDatabase, serializeDefaultNullability).Visit(dbDatabase, namespaceName, provider, providerManifestToken);
				return true;
			}
			return false;
		}

		// Token: 0x06003F22 RID: 16162 RVA: 0x000D0FA0 File Offset: 0x000CF1A0
		private bool ValidateModel(EdmModel model)
		{
			bool modelIsValid = true;
			Action<DataModelErrorEventArgs> onErrorAction = delegate(DataModelErrorEventArgs e)
			{
				MetadataItem item = e.Item;
				if (item == null || !MetadataItemHelper.IsInvalid(item))
				{
					modelIsValid = false;
					if (this.OnError != null)
					{
						this.OnError(this, e);
					}
				}
			};
			if (model.NamespaceNames.Count<string>() > 1 || model.Containers.Count<EntityContainer>() != 1)
			{
				onErrorAction(new DataModelErrorEventArgs
				{
					ErrorMessage = Strings.Serializer_OneNamespaceAndOneContainer
				});
			}
			DataModelValidator dataModelValidator = new DataModelValidator();
			dataModelValidator.OnError += delegate(object _, DataModelErrorEventArgs e)
			{
				onErrorAction(e);
			};
			dataModelValidator.Validate(model, true);
			return modelIsValid;
		}

		// Token: 0x06003F23 RID: 16163 RVA: 0x000D102E File Offset: 0x000CF22E
		private static EdmSerializationVisitor CreateVisitor(XmlWriter xmlWriter, EdmModel dbDatabase, bool serializeDefaultNullability)
		{
			return new EdmSerializationVisitor(xmlWriter, dbDatabase.SchemaVersion, serializeDefaultNullability);
		}
	}
}
