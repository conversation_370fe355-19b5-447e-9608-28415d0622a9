﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000010 RID: 16
	public class TS_FirstPayShopTemp
	{
		// Token: 0x17000059 RID: 89
		// (get) Token: 0x060000C0 RID: 192 RVA: 0x000026CA File Offset: 0x000008CA
		// (set) Token: 0x060000C1 RID: 193 RVA: 0x000026D2 File Offset: 0x000008D2
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int ID { get; set; }

		// Token: 0x1700005A RID: 90
		// (get) Token: 0x060000C2 RID: 194 RVA: 0x000026DB File Offset: 0x000008DB
		// (set) Token: 0x060000C3 RID: 195 RVA: 0x000026E3 File Offset: 0x000008E3
		public int TemplateId { get; set; }

		// Token: 0x1700005B RID: 91
		// (get) Token: 0x060000C4 RID: 196 RVA: 0x000026EC File Offset: 0x000008EC
		// (set) Token: 0x060000C5 RID: 197 RVA: 0x000026F4 File Offset: 0x000008F4
		public int MstType { get; set; }

		// Token: 0x1700005C RID: 92
		// (get) Token: 0x060000C6 RID: 198 RVA: 0x000026FD File Offset: 0x000008FD
		// (set) Token: 0x060000C7 RID: 199 RVA: 0x00002705 File Offset: 0x00000905
		public int SonType { get; set; }

		// Token: 0x1700005D RID: 93
		// (get) Token: 0x060000C8 RID: 200 RVA: 0x0000270E File Offset: 0x0000090E
		// (set) Token: 0x060000C9 RID: 201 RVA: 0x00002716 File Offset: 0x00000916
		public int ItemTempId { get; set; }

		// Token: 0x1700005E RID: 94
		// (get) Token: 0x060000CA RID: 202 RVA: 0x0000271F File Offset: 0x0000091F
		// (set) Token: 0x060000CB RID: 203 RVA: 0x00002727 File Offset: 0x00000927
		public int ItemTempCount { get; set; }

		// Token: 0x1700005F RID: 95
		// (get) Token: 0x060000CC RID: 204 RVA: 0x00002730 File Offset: 0x00000930
		// (set) Token: 0x060000CD RID: 205 RVA: 0x00002738 File Offset: 0x00000938
		public int RefershRandom { get; set; }

		// Token: 0x17000060 RID: 96
		// (get) Token: 0x060000CE RID: 206 RVA: 0x00002741 File Offset: 0x00000941
		// (set) Token: 0x060000CF RID: 207 RVA: 0x00002749 File Offset: 0x00000949
		public int LimitBuyCount { get; set; }

		// Token: 0x17000061 RID: 97
		// (get) Token: 0x060000D0 RID: 208 RVA: 0x00002752 File Offset: 0x00000952
		// (set) Token: 0x060000D1 RID: 209 RVA: 0x0000275A File Offset: 0x0000095A
		public int NeedGoldBeans { get; set; }

		// Token: 0x17000062 RID: 98
		// (get) Token: 0x060000D2 RID: 210 RVA: 0x00002763 File Offset: 0x00000963
		// (set) Token: 0x060000D3 RID: 211 RVA: 0x0000276B File Offset: 0x0000096B
		public int ValidityDay { get; set; }
	}
}
