﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Security.Cryptography;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x02000525 RID: 1317
	internal class CompressingHashBuilder : StringHashBuilder
	{
		// Token: 0x060040FF RID: 16639 RVA: 0x000DAB0C File Offset: 0x000D8D0C
		internal CompressingHashBuilder(HashAlgorithm hashAlgorithm)
			: base(hashAlgorithm, 6144)
		{
		}

		// Token: 0x06004100 RID: 16640 RVA: 0x000DAB1A File Offset: 0x000D8D1A
		internal override void Append(string content)
		{
			base.Append(string.Empty.PadLeft(4 * this._indent, ' '));
			base.Append(content);
			this.CompressHash();
		}

		// Token: 0x06004101 RID: 16641 RVA: 0x000DAB43 File Offset: 0x000D8D43
		internal override void AppendLine(string content)
		{
			base.Append(string.Empty.PadLeft(4 * this._indent, ' '));
			base.AppendLine(content);
			this.CompressHash();
		}

		// Token: 0x06004102 RID: 16642 RVA: 0x000DAB6C File Offset: 0x000D8D6C
		private static Dictionary<Type, string> InitializeLegacyTypeNames()
		{
			return new Dictionary<Type, string>
			{
				{
					typeof(AssociationSetMapping),
					"System.Data.Entity.Core.Mapping.StorageAssociationSetMapping"
				},
				{
					typeof(AssociationSetModificationFunctionMapping),
					"System.Data.Entity.Core.Mapping.StorageAssociationSetModificationFunctionMapping"
				},
				{
					typeof(AssociationTypeMapping),
					"System.Data.Entity.Core.Mapping.StorageAssociationTypeMapping"
				},
				{
					typeof(ComplexPropertyMapping),
					"System.Data.Entity.Core.Mapping.StorageComplexPropertyMapping"
				},
				{
					typeof(ComplexTypeMapping),
					"System.Data.Entity.Core.Mapping.StorageComplexTypeMapping"
				},
				{
					typeof(ConditionPropertyMapping),
					"System.Data.Entity.Core.Mapping.StorageConditionPropertyMapping"
				},
				{
					typeof(EndPropertyMapping),
					"System.Data.Entity.Core.Mapping.StorageEndPropertyMapping"
				},
				{
					typeof(EntityContainerMapping),
					"System.Data.Entity.Core.Mapping.StorageEntityContainerMapping"
				},
				{
					typeof(EntitySetMapping),
					"System.Data.Entity.Core.Mapping.StorageEntitySetMapping"
				},
				{
					typeof(EntityTypeMapping),
					"System.Data.Entity.Core.Mapping.StorageEntityTypeMapping"
				},
				{
					typeof(EntityTypeModificationFunctionMapping),
					"System.Data.Entity.Core.Mapping.StorageEntityTypeModificationFunctionMapping"
				},
				{
					typeof(MappingFragment),
					"System.Data.Entity.Core.Mapping.StorageMappingFragment"
				},
				{
					typeof(ModificationFunctionMapping),
					"System.Data.Entity.Core.Mapping.StorageModificationFunctionMapping"
				},
				{
					typeof(ModificationFunctionMemberPath),
					"System.Data.Entity.Core.Mapping.StorageModificationFunctionMemberPath"
				},
				{
					typeof(ModificationFunctionParameterBinding),
					"System.Data.Entity.Core.Mapping.StorageModificationFunctionParameterBinding"
				},
				{
					typeof(ModificationFunctionResultBinding),
					"System.Data.Entity.Core.Mapping.StorageModificationFunctionResultBinding"
				},
				{
					typeof(PropertyMapping),
					"System.Data.Entity.Core.Mapping.StoragePropertyMapping"
				},
				{
					typeof(ScalarPropertyMapping),
					"System.Data.Entity.Core.Mapping.StorageScalarPropertyMapping"
				},
				{
					typeof(EntitySetBaseMapping),
					"System.Data.Entity.Core.Mapping.StorageSetMapping"
				},
				{
					typeof(TypeMapping),
					"System.Data.Entity.Core.Mapping.StorageTypeMapping"
				}
			};
		}

		// Token: 0x06004103 RID: 16643 RVA: 0x000DAD24 File Offset: 0x000D8F24
		internal void AppendObjectStartDump(object o, int objectIndex)
		{
			base.Append(string.Empty.PadLeft(4 * this._indent, ' '));
			string text;
			if (!CompressingHashBuilder._legacyTypeNames.TryGetValue(o.GetType(), out text))
			{
				text = o.GetType().ToString();
			}
			base.Append(text);
			base.Append(" Instance#");
			base.AppendLine(objectIndex.ToString(CultureInfo.InvariantCulture));
			this.CompressHash();
			this._indent++;
		}

		// Token: 0x06004104 RID: 16644 RVA: 0x000DADA3 File Offset: 0x000D8FA3
		internal void AppendObjectEndDump()
		{
			this._indent--;
		}

		// Token: 0x06004105 RID: 16645 RVA: 0x000DADB4 File Offset: 0x000D8FB4
		private void CompressHash()
		{
			if (base.CharCount >= 2048)
			{
				string text = base.ComputeHash();
				base.Clear();
				base.Append(text);
			}
		}

		// Token: 0x0400168B RID: 5771
		private const int HashCharacterCompressionThreshold = 2048;

		// Token: 0x0400168C RID: 5772
		private const int SpacesPerIndent = 4;

		// Token: 0x0400168D RID: 5773
		private int _indent;

		// Token: 0x0400168E RID: 5774
		private static readonly Dictionary<Type, string> _legacyTypeNames = CompressingHashBuilder.InitializeLegacyTypeNames();
	}
}
