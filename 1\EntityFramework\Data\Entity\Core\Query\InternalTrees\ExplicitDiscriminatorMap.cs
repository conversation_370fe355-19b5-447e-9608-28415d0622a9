﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Mapping.ViewGeneration;
using System.Data.Entity.Core.Metadata.Edm;
using System.Linq;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003A4 RID: 932
	internal class ExplicitDiscriminatorMap
	{
		// Token: 0x06002D44 RID: 11588 RVA: 0x00090BF0 File Offset: 0x0008EDF0
		internal ExplicitDiscriminatorMap(DiscriminatorMap template)
		{
			this.m_typeMap = template.TypeMap;
			this.m_discriminatorProperty = template.Discriminator.Property;
			this.m_properties = new ReadOnlyCollection<EdmProperty>(template.PropertyMap.Select((KeyValuePair<EdmProperty, DbExpression> propertyValuePair) => propertyValuePair.Key).ToList<EdmProperty>());
		}

		// Token: 0x170008DD RID: 2269
		// (get) Token: 0x06002D45 RID: 11589 RVA: 0x00090C5A File Offset: 0x0008EE5A
		internal ReadOnlyCollection<KeyValuePair<object, EntityType>> TypeMap
		{
			get
			{
				return this.m_typeMap;
			}
		}

		// Token: 0x170008DE RID: 2270
		// (get) Token: 0x06002D46 RID: 11590 RVA: 0x00090C62 File Offset: 0x0008EE62
		internal EdmMember DiscriminatorProperty
		{
			get
			{
				return this.m_discriminatorProperty;
			}
		}

		// Token: 0x170008DF RID: 2271
		// (get) Token: 0x06002D47 RID: 11591 RVA: 0x00090C6A File Offset: 0x0008EE6A
		internal ReadOnlyCollection<EdmProperty> Properties
		{
			get
			{
				return this.m_properties;
			}
		}

		// Token: 0x06002D48 RID: 11592 RVA: 0x00090C74 File Offset: 0x0008EE74
		internal object GetTypeId(EntityType entityType)
		{
			object obj = null;
			foreach (KeyValuePair<object, EntityType> keyValuePair in this.TypeMap)
			{
				if (keyValuePair.Value.EdmEquals(entityType))
				{
					obj = keyValuePair.Key;
					break;
				}
			}
			return obj;
		}

		// Token: 0x04000F25 RID: 3877
		private readonly ReadOnlyCollection<KeyValuePair<object, EntityType>> m_typeMap;

		// Token: 0x04000F26 RID: 3878
		private readonly EdmMember m_discriminatorProperty;

		// Token: 0x04000F27 RID: 3879
		private readonly ReadOnlyCollection<EdmProperty> m_properties;
	}
}
