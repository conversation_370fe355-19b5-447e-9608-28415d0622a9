﻿using System;
using System.Data.Entity;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x0200001D RID: 29
	public class PlayerData : DbContext
	{
		// Token: 0x170000CE RID: 206
		// (get) Token: 0x060001B7 RID: 439 RVA: 0x00002F04 File Offset: 0x00001104
		// (set) Token: 0x060001B8 RID: 440 RVA: 0x00002F0C File Offset: 0x0000110C
		public virtual DbSet<Sys_Carnival_Goods> Sys_Carnival_Goods { get; set; }

		// Token: 0x170000CF RID: 207
		// (get) Token: 0x060001B9 RID: 441 RVA: 0x00002F15 File Offset: 0x00001115
		// (set) Token: 0x060001BA RID: 442 RVA: 0x00002F1D File Offset: 0x0000111D
		public virtual DbSet<Sys_Carnival_Server> Sys_Carnival_Server { get; set; }

		// Token: 0x170000D0 RID: 208
		// (get) Token: 0x060001BB RID: 443 RVA: 0x00002F26 File Offset: 0x00001126
		// (set) Token: 0x060001BC RID: 444 RVA: 0x00002F2E File Offset: 0x0000112E
		public virtual DbSet<Sys_Carnival_Users> Sys_Carnival_Users { get; set; }

		// Token: 0x170000D1 RID: 209
		// (get) Token: 0x060001BD RID: 445 RVA: 0x00002F37 File Offset: 0x00001137
		// (set) Token: 0x060001BE RID: 446 RVA: 0x00002F3F File Offset: 0x0000113F
		public virtual DbSet<Sys_User_Info> Sys_User_Info { get; set; }

		// Token: 0x170000D2 RID: 210
		// (get) Token: 0x060001BF RID: 447 RVA: 0x00002F48 File Offset: 0x00001148
		// (set) Token: 0x060001C0 RID: 448 RVA: 0x00002F50 File Offset: 0x00001150
		public virtual DbSet<Sys_Users_FirstPay> Sys_Users_FirstPay { get; set; }

		// Token: 0x170000D3 RID: 211
		// (get) Token: 0x060001C1 RID: 449 RVA: 0x00002F59 File Offset: 0x00001159
		// (set) Token: 0x060001C2 RID: 450 RVA: 0x00002F61 File Offset: 0x00001161
		public virtual DbSet<Sys_Users_FirstPayTemp> Sys_Users_FirstPayTemp { get; set; }

		// Token: 0x170000D4 RID: 212
		// (get) Token: 0x060001C3 RID: 451 RVA: 0x00002F6A File Offset: 0x0000116A
		// (set) Token: 0x060001C4 RID: 452 RVA: 0x00002F72 File Offset: 0x00001172
		public virtual DbSet<Sys_MonthCard_Info> Sys_MonthCard_Info { get; set; }

		// Token: 0x170000D5 RID: 213
		// (get) Token: 0x060001C5 RID: 453 RVA: 0x00002F7B File Offset: 0x0000117B
		// (set) Token: 0x060001C6 RID: 454 RVA: 0x00002F83 File Offset: 0x00001183
		public virtual DbSet<Sys_Users_UiTimateLuxuryCarnival> Sys_Users_UiTimateLuxuryCarnival { get; set; }

		// Token: 0x170000D6 RID: 214
		// (get) Token: 0x060001C7 RID: 455 RVA: 0x00002F8C File Offset: 0x0000118C
		// (set) Token: 0x060001C8 RID: 456 RVA: 0x00002F94 File Offset: 0x00001194
		public virtual DbSet<Sys_Users_UltimateLuxuryPinTuan> Sys_Users_UltimateLuxuryPinTuan { get; set; }

		// Token: 0x170000D7 RID: 215
		// (get) Token: 0x060001C9 RID: 457 RVA: 0x00002F9D File Offset: 0x0000119D
		// (set) Token: 0x060001CA RID: 458 RVA: 0x00002FA5 File Offset: 0x000011A5
		public virtual DbSet<Sys_Users_UltimateLuxuryRecharge> Sys_Users_UltimateLuxuryRecharge { get; set; }

		// Token: 0x170000D8 RID: 216
		// (get) Token: 0x060001CB RID: 459 RVA: 0x00002FAE File Offset: 0x000011AE
		// (set) Token: 0x060001CC RID: 460 RVA: 0x00002FB6 File Offset: 0x000011B6
		public virtual DbSet<Sys_Users_UltimateLuxuryTurnTable> Sys_Users_UltimateLuxuryTurnTable { get; set; }

		// Token: 0x170000D9 RID: 217
		// (get) Token: 0x060001CD RID: 461 RVA: 0x00002FBF File Offset: 0x000011BF
		// (set) Token: 0x060001CE RID: 462 RVA: 0x00002FC7 File Offset: 0x000011C7
		public virtual DbSet<Sys_FirstKill_Data> Sys_FirstKill_Data { get; set; }

		// Token: 0x170000DA RID: 218
		// (get) Token: 0x060001CF RID: 463 RVA: 0x00002FD0 File Offset: 0x000011D0
		// (set) Token: 0x060001D0 RID: 464 RVA: 0x00002FD8 File Offset: 0x000011D8
		public virtual DbSet<Sys_Users_WarOrder_Info> Sys_Users_WarOrder_Info { get; set; }

		// Token: 0x170000DB RID: 219
		// (get) Token: 0x060001D1 RID: 465 RVA: 0x00002FE1 File Offset: 0x000011E1
		// (set) Token: 0x060001D2 RID: 466 RVA: 0x00002FE9 File Offset: 0x000011E9
		public virtual DbSet<Sys_Users_CaveLoot_PlayerInfo> Sys_Users_CaveLoot_PlayerInfo { get; set; }

		// Token: 0x170000DC RID: 220
		// (get) Token: 0x060001D3 RID: 467 RVA: 0x00002FF2 File Offset: 0x000011F2
		// (set) Token: 0x060001D4 RID: 468 RVA: 0x00002FFA File Offset: 0x000011FA
		public virtual DbSet<Sys_Users_CaveLoot_MineRecord> Sys_Users_CaveLoot_MineRecord { get; set; }

		// Token: 0x170000DD RID: 221
		// (get) Token: 0x060001D5 RID: 469 RVA: 0x00003003 File Offset: 0x00001203
		// (set) Token: 0x060001D6 RID: 470 RVA: 0x0000300B File Offset: 0x0000120B
		public virtual DbSet<Sys_Users_CaveLoot_Rank> Sys_Users_CaveLoot_Rank { get; set; }

		// Token: 0x170000DE RID: 222
		// (get) Token: 0x060001D7 RID: 471 RVA: 0x00003014 File Offset: 0x00001214
		// (set) Token: 0x060001D8 RID: 472 RVA: 0x0000301C File Offset: 0x0000121C
		public virtual DbSet<Sys_Users_CaveLoot_Progress> Sys_Users_CaveLoot_Progress { get; set; }

		// Token: 0x170000DF RID: 223
		// (get) Token: 0x060001D9 RID: 473 RVA: 0x00003025 File Offset: 0x00001225
		// (set) Token: 0x060001DA RID: 474 RVA: 0x0000302D File Offset: 0x0000122D
		public virtual DbSet<Sys_Users_CaveLoot_TempBag> Sys_Users_CaveLoot_TempBag { get; set; }

		// Token: 0x170000E0 RID: 224
		// (get) Token: 0x060001DB RID: 475 RVA: 0x00003036 File Offset: 0x00001236
		// (set) Token: 0x060001DC RID: 476 RVA: 0x0000303E File Offset: 0x0000123E
		public virtual DbSet<Sys_Users_DevilTurn> Sys_Users_DevilTurn { get; set; }

		// Token: 0x170000E1 RID: 225
		// (get) Token: 0x060001DD RID: 477 RVA: 0x00003047 File Offset: 0x00001247
		// (set) Token: 0x060001DE RID: 478 RVA: 0x0000304F File Offset: 0x0000124F
		public virtual DbSet<Sys_Users_DevilTurn_Rank> Sys_Users_DevilTurn_Rank { get; set; }

		// Token: 0x170000E2 RID: 226
		// (get) Token: 0x060001DF RID: 479 RVA: 0x00003058 File Offset: 0x00001258
		// (set) Token: 0x060001E0 RID: 480 RVA: 0x00003060 File Offset: 0x00001260
		public virtual DbSet<Sys_User_RelicItemTemplate> Sys_User_RelicItemTemplate { get; set; }

		// Token: 0x170000E3 RID: 227
		// (get) Token: 0x060001E1 RID: 481 RVA: 0x00003069 File Offset: 0x00001269
		// (set) Token: 0x060001E2 RID: 482 RVA: 0x00003071 File Offset: 0x00001271
		public virtual DbSet<Sys_User_RelicItemInfo> Sys_User_RelicItemInfo { get; set; }

		// Token: 0x060001E3 RID: 483 RVA: 0x0000307A File Offset: 0x0000127A
		public PlayerData()
			: base("name=PlayerData")
		{
		}

		// Token: 0x060001E4 RID: 484 RVA: 0x00003089 File Offset: 0x00001289
		protected override void OnModelCreating(DbModelBuilder modelBuilder)
		{
		}
	}
}
