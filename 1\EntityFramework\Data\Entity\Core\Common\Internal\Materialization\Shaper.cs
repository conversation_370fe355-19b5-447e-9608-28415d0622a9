﻿using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Core.Objects.DataClasses;
using System.Data.Entity.Core.Objects.Internal;
using System.Data.Entity.Hierarchy;
using System.Data.Entity.Resources;
using System.Data.Entity.Spatial;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Reflection;

namespace System.Data.Entity.Core.Common.Internal.Materialization
{
	// Token: 0x0200063F RID: 1599
	internal abstract class Shaper
	{
		// Token: 0x06004CFF RID: 19711 RVA: 0x0010EDC4 File Offset: 0x0010CFC4
		internal Shaper(DbDataReader reader, ObjectContext context, MetadataWorkspace workspace, MergeOption mergeOption, int stateCount, bool streaming)
		{
			this.Reader = reader;
			this.MergeOption = mergeOption;
			this.State = new object[stateCount];
			this.Context = context;
			this.Workspace = workspace;
			this._spatialReader = new Lazy<DbSpatialDataReader>(new Func<DbSpatialDataReader>(this.CreateSpatialDataReader));
			this.Streaming = streaming;
		}

		// Token: 0x06004D00 RID: 19712 RVA: 0x0010EE24 File Offset: 0x0010D024
		public TElement Discriminate<TElement>(object[] discriminatorValues, Func<object[], EntityType> discriminate, KeyValuePair<EntityType, Func<Shaper, TElement>>[] elementDelegates)
		{
			EntityType entityType = discriminate(discriminatorValues);
			Func<Shaper, TElement> func = null;
			foreach (KeyValuePair<EntityType, Func<Shaper, TElement>> keyValuePair in elementDelegates)
			{
				if (keyValuePair.Key == entityType)
				{
					func = keyValuePair.Value;
				}
			}
			return func(this);
		}

		// Token: 0x06004D01 RID: 19713 RVA: 0x0010EE6D File Offset: 0x0010D06D
		public IEntityWrapper HandleEntityNoTracking<TEntity>(IEntityWrapper wrappedEntity)
		{
			this.RegisterMaterializedEntityForEvent(wrappedEntity);
			return wrappedEntity;
		}

		// Token: 0x06004D02 RID: 19714 RVA: 0x0010EE78 File Offset: 0x0010D078
		public IEntityWrapper HandleEntity<TEntity>(IEntityWrapper wrappedEntity, EntityKey entityKey, EntitySet entitySet)
		{
			IEntityWrapper entityWrapper = wrappedEntity;
			if (entityKey != null)
			{
				EntityEntry entityEntry = this.Context.ObjectStateManager.FindEntityEntry(entityKey);
				if (entityEntry != null && !entityEntry.IsKeyEntry)
				{
					this.UpdateEntry<TEntity>(wrappedEntity, entityEntry);
					entityWrapper = entityEntry.WrappedEntity;
				}
				else
				{
					this.RegisterMaterializedEntityForEvent(entityWrapper);
					if (entityEntry == null)
					{
						this.Context.ObjectStateManager.AddEntry(wrappedEntity, entityKey, entitySet, "HandleEntity", false);
					}
					else
					{
						this.Context.ObjectStateManager.PromoteKeyEntry(entityEntry, wrappedEntity, false, true, false);
					}
				}
			}
			return entityWrapper;
		}

		// Token: 0x06004D03 RID: 19715 RVA: 0x0010EEF4 File Offset: 0x0010D0F4
		public IEntityWrapper HandleEntityAppendOnly<TEntity>(Func<Shaper, IEntityWrapper> constructEntityDelegate, EntityKey entityKey, EntitySet entitySet)
		{
			IEntityWrapper entityWrapper;
			if (entityKey == null)
			{
				entityWrapper = constructEntityDelegate(this);
				this.RegisterMaterializedEntityForEvent(entityWrapper);
			}
			else
			{
				EntityEntry entityEntry = this.Context.ObjectStateManager.FindEntityEntry(entityKey);
				if (entityEntry != null && !entityEntry.IsKeyEntry)
				{
					if (typeof(TEntity) != entityEntry.WrappedEntity.IdentityType)
					{
						EntityKey entityKey2 = entityEntry.EntityKey;
						throw new NotSupportedException(Strings.Materializer_RecyclingEntity(TypeHelpers.GetFullName(entityKey2.EntityContainerName, entityKey2.EntitySetName), typeof(TEntity).FullName, entityEntry.WrappedEntity.IdentityType.FullName));
					}
					if (EntityState.Added == entityEntry.State)
					{
						throw new InvalidOperationException(Strings.Materializer_AddedEntityAlreadyExists(typeof(TEntity).FullName));
					}
					entityWrapper = entityEntry.WrappedEntity;
				}
				else
				{
					entityWrapper = constructEntityDelegate(this);
					this.RegisterMaterializedEntityForEvent(entityWrapper);
					if (entityEntry == null)
					{
						this.Context.ObjectStateManager.AddEntry(entityWrapper, entityKey, entitySet, "HandleEntity", false);
					}
					else
					{
						this.Context.ObjectStateManager.PromoteKeyEntry(entityEntry, entityWrapper, false, true, false);
					}
				}
			}
			return entityWrapper;
		}

		// Token: 0x06004D04 RID: 19716 RVA: 0x0010F00C File Offset: 0x0010D20C
		public IEntityWrapper HandleFullSpanCollection<TTargetEntity>(IEntityWrapper wrappedEntity, Coordinator<TTargetEntity> coordinator, AssociationEndMember targetMember)
		{
			if (wrappedEntity.Entity != null)
			{
				coordinator.RegisterCloseHandler(delegate(Shaper state, List<IEntityWrapper> spannedEntities)
				{
					this.FullSpanAction<IEntityWrapper>(wrappedEntity, spannedEntities, targetMember);
				});
			}
			return wrappedEntity;
		}

		// Token: 0x06004D05 RID: 19717 RVA: 0x0010F05C File Offset: 0x0010D25C
		public IEntityWrapper HandleFullSpanElement(IEntityWrapper wrappedSource, IEntityWrapper wrappedSpannedEntity, AssociationEndMember targetMember)
		{
			if (wrappedSource.Entity == null)
			{
				return wrappedSource;
			}
			List<IEntityWrapper> list = null;
			if (wrappedSpannedEntity.Entity != null)
			{
				list = new List<IEntityWrapper>(1);
				list.Add(wrappedSpannedEntity);
			}
			else
			{
				EntityKey entityKey = wrappedSource.EntityKey;
				this.CheckClearedEntryOnSpan(null, wrappedSource, entityKey, targetMember);
			}
			this.FullSpanAction<IEntityWrapper>(wrappedSource, list, targetMember);
			return wrappedSource;
		}

		// Token: 0x06004D06 RID: 19718 RVA: 0x0010F0A8 File Offset: 0x0010D2A8
		public IEntityWrapper HandleRelationshipSpan(IEntityWrapper wrappedEntity, EntityKey targetKey, AssociationEndMember targetMember)
		{
			if (wrappedEntity.Entity == null)
			{
				return wrappedEntity;
			}
			EntityKey entityKey = wrappedEntity.EntityKey;
			AssociationEndMember otherAssociationEnd = MetadataHelper.GetOtherAssociationEnd(targetMember);
			this.CheckClearedEntryOnSpan(targetKey, wrappedEntity, entityKey, targetMember);
			RelatedEnd relatedEnd;
			if (targetKey != null)
			{
				EntitySet entitySet;
				AssociationSet associationSet = this.Context.MetadataWorkspace.MetadataOptimization.FindCSpaceAssociationSet((AssociationType)targetMember.DeclaringType, targetMember.Name, targetKey.EntitySetName, targetKey.EntityContainerName, out entitySet);
				ObjectStateManager objectStateManager = this.Context.ObjectStateManager;
				ILookup<EntityKey, RelationshipEntry> relationshipLookup = ObjectStateManager.GetRelationshipLookup(this.Context.ObjectStateManager, associationSet, otherAssociationEnd, entityKey);
				EntityState entityState;
				if (!ObjectStateManager.TryUpdateExistingRelationships(this.Context, this.MergeOption, associationSet, otherAssociationEnd, relationshipLookup, wrappedEntity, targetMember, targetKey, true, out entityState))
				{
					EntityEntry entityEntry = objectStateManager.GetOrAddKeyEntry(targetKey, entitySet);
					bool flag = true;
					RelationshipMultiplicity relationshipMultiplicity = otherAssociationEnd.RelationshipMultiplicity;
					if (relationshipMultiplicity > RelationshipMultiplicity.One)
					{
						if (relationshipMultiplicity != RelationshipMultiplicity.Many)
						{
						}
					}
					else
					{
						ILookup<EntityKey, RelationshipEntry> relationshipLookup2 = ObjectStateManager.GetRelationshipLookup(this.Context.ObjectStateManager, associationSet, targetMember, targetKey);
						flag = !ObjectStateManager.TryUpdateExistingRelationships(this.Context, this.MergeOption, associationSet, targetMember, relationshipLookup2, entityEntry.WrappedEntity, otherAssociationEnd, entityKey, true, out entityState);
						if (entityEntry.State == EntityState.Detached)
						{
							entityEntry = objectStateManager.AddKeyEntry(targetKey, entitySet);
						}
					}
					if (flag)
					{
						if (entityEntry.IsKeyEntry || entityState == EntityState.Deleted)
						{
							RelationshipWrapper relationshipWrapper = new RelationshipWrapper(associationSet, otherAssociationEnd.Name, entityKey, targetMember.Name, targetKey);
							objectStateManager.AddNewRelation(relationshipWrapper, entityState);
						}
						else if (entityEntry.State != EntityState.Deleted)
						{
							ObjectStateManager.AddEntityToCollectionOrReference(this.MergeOption, wrappedEntity, otherAssociationEnd, entityEntry.WrappedEntity, targetMember, true, false, false);
						}
						else
						{
							RelationshipWrapper relationshipWrapper2 = new RelationshipWrapper(associationSet, otherAssociationEnd.Name, entityKey, targetMember.Name, targetKey);
							objectStateManager.AddNewRelation(relationshipWrapper2, EntityState.Deleted);
						}
					}
				}
			}
			else if (this.TryGetRelatedEnd(wrappedEntity, (AssociationType)targetMember.DeclaringType, otherAssociationEnd.Name, targetMember.Name, out relatedEnd))
			{
				this.SetIsLoadedForSpan(relatedEnd, false);
			}
			return wrappedEntity;
		}

		// Token: 0x06004D07 RID: 19719 RVA: 0x0010F274 File Offset: 0x0010D474
		private bool TryGetRelatedEnd(IEntityWrapper wrappedEntity, AssociationType associationType, string sourceEndName, string targetEndName, out RelatedEnd relatedEnd)
		{
			AssociationType ospaceAssociationType = this.Workspace.MetadataOptimization.GetOSpaceAssociationType(associationType, () => this.Workspace.GetItemCollection(DataSpace.OSpace).GetItem<AssociationType>(associationType.FullName));
			AssociationEndMember associationEndMember = null;
			AssociationEndMember associationEndMember2 = null;
			foreach (AssociationEndMember associationEndMember3 in ospaceAssociationType.AssociationEndMembers)
			{
				if (associationEndMember3.Name == sourceEndName)
				{
					associationEndMember = associationEndMember3;
				}
				else if (associationEndMember3.Name == targetEndName)
				{
					associationEndMember2 = associationEndMember3;
				}
			}
			if (associationEndMember != null && associationEndMember2 != null)
			{
				bool flag = false;
				EntitySet entitySet;
				if (wrappedEntity.EntityKey == null)
				{
					flag = true;
				}
				else if (this.Workspace.MetadataOptimization.FindCSpaceAssociationSet(associationType, sourceEndName, wrappedEntity.EntityKey.EntitySetName, wrappedEntity.EntityKey.EntityContainerName, out entitySet) != null)
				{
					flag = true;
				}
				if (flag)
				{
					relatedEnd = DelegateFactory.GetRelatedEnd(wrappedEntity.RelationshipManager, associationEndMember, associationEndMember2, null);
					return true;
				}
			}
			relatedEnd = null;
			return false;
		}

		// Token: 0x06004D08 RID: 19720 RVA: 0x0010F390 File Offset: 0x0010D590
		private void SetIsLoadedForSpan(RelatedEnd relatedEnd, bool forceToTrue)
		{
			if (!forceToTrue)
			{
				forceToTrue = relatedEnd.IsEmpty();
				EntityReference entityReference = relatedEnd as EntityReference;
				if (entityReference != null)
				{
					forceToTrue &= entityReference.EntityKey == null;
				}
			}
			if (forceToTrue || this.MergeOption == MergeOption.OverwriteChanges)
			{
				relatedEnd.IsLoaded = true;
			}
		}

		// Token: 0x06004D09 RID: 19721 RVA: 0x0010F3D5 File Offset: 0x0010D5D5
		public IEntityWrapper HandleIEntityWithKey<TEntity>(IEntityWrapper wrappedEntity, EntitySet entitySet)
		{
			return this.HandleEntity<TEntity>(wrappedEntity, wrappedEntity.EntityKey, entitySet);
		}

		// Token: 0x06004D0A RID: 19722 RVA: 0x0010F3E5 File Offset: 0x0010D5E5
		public bool SetColumnValue(int recordStateSlotNumber, int ordinal, object value)
		{
			((RecordState)this.State[recordStateSlotNumber]).SetColumnValue(ordinal, value);
			return true;
		}

		// Token: 0x06004D0B RID: 19723 RVA: 0x0010F3FD File Offset: 0x0010D5FD
		public bool SetEntityRecordInfo(int recordStateSlotNumber, EntityKey entityKey, EntitySet entitySet)
		{
			((RecordState)this.State[recordStateSlotNumber]).SetEntityRecordInfo(entityKey, entitySet);
			return true;
		}

		// Token: 0x06004D0C RID: 19724 RVA: 0x0010F415 File Offset: 0x0010D615
		public bool SetState<T>(int ordinal, T value)
		{
			this.State[ordinal] = value;
			return true;
		}

		// Token: 0x06004D0D RID: 19725 RVA: 0x0010F426 File Offset: 0x0010D626
		public T SetStatePassthrough<T>(int ordinal, T value)
		{
			this.State[ordinal] = value;
			return value;
		}

		// Token: 0x06004D0E RID: 19726 RVA: 0x0010F437 File Offset: 0x0010D637
		public TProperty GetPropertyValueWithErrorHandling<TProperty>(int ordinal, string propertyName, string typeName)
		{
			return new Shaper.PropertyErrorHandlingValueReader<TProperty>(propertyName, typeName).GetValue(this.Reader, ordinal);
		}

		// Token: 0x06004D0F RID: 19727 RVA: 0x0010F44C File Offset: 0x0010D64C
		public TColumn GetColumnValueWithErrorHandling<TColumn>(int ordinal)
		{
			return new Shaper.ColumnErrorHandlingValueReader<TColumn>().GetValue(this.Reader, ordinal);
		}

		// Token: 0x06004D10 RID: 19728 RVA: 0x0010F45F File Offset: 0x0010D65F
		public HierarchyId GetHierarchyIdColumnValue(int ordinal)
		{
			return new HierarchyId(this.Reader.GetValue(ordinal).ToString());
		}

		// Token: 0x06004D11 RID: 19729 RVA: 0x0010F477 File Offset: 0x0010D677
		protected virtual DbSpatialDataReader CreateSpatialDataReader()
		{
			return SpatialHelpers.CreateSpatialDataReader(this.Workspace, this.Reader);
		}

		// Token: 0x06004D12 RID: 19730 RVA: 0x0010F48A File Offset: 0x0010D68A
		public DbGeography GetGeographyColumnValue(int ordinal)
		{
			if (this.Streaming)
			{
				return this._spatialReader.Value.GetGeography(ordinal);
			}
			return (DbGeography)this.Reader.GetValue(ordinal);
		}

		// Token: 0x06004D13 RID: 19731 RVA: 0x0010F4B7 File Offset: 0x0010D6B7
		public DbGeometry GetGeometryColumnValue(int ordinal)
		{
			if (this.Streaming)
			{
				return this._spatialReader.Value.GetGeometry(ordinal);
			}
			return (DbGeometry)this.Reader.GetValue(ordinal);
		}

		// Token: 0x06004D14 RID: 19732 RVA: 0x0010F4E4 File Offset: 0x0010D6E4
		public TColumn GetSpatialColumnValueWithErrorHandling<TColumn>(int ordinal, PrimitiveTypeKind spatialTypeKind)
		{
			TColumn tcolumn;
			if (spatialTypeKind == PrimitiveTypeKind.Geography)
			{
				if (this.Streaming)
				{
					tcolumn = new Shaper.ColumnErrorHandlingValueReader<TColumn>((DbDataReader reader, int column) => (TColumn)((object)this._spatialReader.Value.GetGeography(column)), (DbDataReader reader, int column) => this._spatialReader.Value.GetGeography(column)).GetValue(this.Reader, ordinal);
				}
				else
				{
					tcolumn = new Shaper.ColumnErrorHandlingValueReader<TColumn>((DbDataReader reader, int column) => (TColumn)((object)this.Reader.GetValue(column)), (DbDataReader reader, int column) => this.Reader.GetValue(column)).GetValue(this.Reader, ordinal);
				}
			}
			else if (this.Streaming)
			{
				tcolumn = new Shaper.ColumnErrorHandlingValueReader<TColumn>((DbDataReader reader, int column) => (TColumn)((object)this._spatialReader.Value.GetGeometry(column)), (DbDataReader reader, int column) => this._spatialReader.Value.GetGeometry(column)).GetValue(this.Reader, ordinal);
			}
			else
			{
				tcolumn = new Shaper.ColumnErrorHandlingValueReader<TColumn>((DbDataReader reader, int column) => (TColumn)((object)this.Reader.GetValue(column)), (DbDataReader reader, int column) => this.Reader.GetValue(column)).GetValue(this.Reader, ordinal);
			}
			return tcolumn;
		}

		// Token: 0x06004D15 RID: 19733 RVA: 0x0010F5B8 File Offset: 0x0010D7B8
		public TProperty GetSpatialPropertyValueWithErrorHandling<TProperty>(int ordinal, string propertyName, string typeName, PrimitiveTypeKind spatialTypeKind)
		{
			TProperty tproperty;
			if (Helper.IsGeographicTypeKind(spatialTypeKind))
			{
				if (this.Streaming)
				{
					tproperty = new Shaper.PropertyErrorHandlingValueReader<TProperty>(propertyName, typeName, (DbDataReader reader, int column) => (TProperty)((object)this._spatialReader.Value.GetGeography(column)), (DbDataReader reader, int column) => this._spatialReader.Value.GetGeography(column)).GetValue(this.Reader, ordinal);
				}
				else
				{
					tproperty = new Shaper.PropertyErrorHandlingValueReader<TProperty>(propertyName, typeName, (DbDataReader reader, int column) => (TProperty)((object)this.Reader.GetValue(column)), (DbDataReader reader, int column) => this.Reader.GetValue(column)).GetValue(this.Reader, ordinal);
				}
			}
			else if (this.Streaming)
			{
				tproperty = new Shaper.PropertyErrorHandlingValueReader<TProperty>(propertyName, typeName, (DbDataReader reader, int column) => (TProperty)((object)this._spatialReader.Value.GetGeometry(column)), (DbDataReader reader, int column) => this._spatialReader.Value.GetGeometry(column)).GetValue(this.Reader, ordinal);
			}
			else
			{
				tproperty = new Shaper.PropertyErrorHandlingValueReader<TProperty>(propertyName, typeName, (DbDataReader reader, int column) => (TProperty)((object)this.Reader.GetValue(column)), (DbDataReader reader, int column) => this.Reader.GetValue(column)).GetValue(this.Reader, ordinal);
			}
			return tproperty;
		}

		// Token: 0x06004D16 RID: 19734 RVA: 0x0010F698 File Offset: 0x0010D898
		private void CheckClearedEntryOnSpan(object targetValue, IEntityWrapper wrappedSource, EntityKey sourceKey, AssociationEndMember targetMember)
		{
			if (sourceKey != null && targetValue == null && (this.MergeOption == MergeOption.PreserveChanges || this.MergeOption == MergeOption.OverwriteChanges))
			{
				EdmType elementType = ((RefType)MetadataHelper.GetOtherAssociationEnd(targetMember).TypeUsage.EdmType).ElementType;
				TypeUsage typeUsage;
				if (!this.Context.Perspective.TryGetType(wrappedSource.IdentityType, out typeUsage) || typeUsage.EdmType.EdmEquals(elementType) || TypeSemantics.IsSubTypeOf(typeUsage.EdmType, elementType))
				{
					this.CheckClearedEntryOnSpan(sourceKey, targetMember);
				}
			}
		}

		// Token: 0x06004D17 RID: 19735 RVA: 0x0010F718 File Offset: 0x0010D918
		private void CheckClearedEntryOnSpan(EntityKey sourceKey, AssociationEndMember targetMember)
		{
			AssociationEndMember otherAssociationEnd = MetadataHelper.GetOtherAssociationEnd(targetMember);
			EntitySet entitySet;
			AssociationSet associationSet = this.Context.MetadataWorkspace.MetadataOptimization.FindCSpaceAssociationSet((AssociationType)otherAssociationEnd.DeclaringType, otherAssociationEnd.Name, sourceKey.EntitySetName, sourceKey.EntityContainerName, out entitySet);
			if (associationSet != null)
			{
				this.Context.ObjectStateManager.RemoveRelationships(this.MergeOption, associationSet, sourceKey, otherAssociationEnd);
			}
		}

		// Token: 0x06004D18 RID: 19736 RVA: 0x0010F780 File Offset: 0x0010D980
		private void FullSpanAction<TTargetEntity>(IEntityWrapper wrappedSource, IList<TTargetEntity> spannedEntities, AssociationEndMember targetMember)
		{
			if (wrappedSource.Entity != null)
			{
				AssociationEndMember otherAssociationEnd = MetadataHelper.GetOtherAssociationEnd(targetMember);
				RelatedEnd relatedEnd;
				if (this.TryGetRelatedEnd(wrappedSource, (AssociationType)targetMember.DeclaringType, otherAssociationEnd.Name, targetMember.Name, out relatedEnd))
				{
					int num = this.Context.ObjectStateManager.UpdateRelationships(this.Context, this.MergeOption, (AssociationSet)relatedEnd.RelationshipSet, otherAssociationEnd, wrappedSource, targetMember, (List<TTargetEntity>)spannedEntities, true);
					this.SetIsLoadedForSpan(relatedEnd, num > 0);
				}
			}
		}

		// Token: 0x06004D19 RID: 19737 RVA: 0x0010F7FC File Offset: 0x0010D9FC
		private void UpdateEntry<TEntity>(IEntityWrapper wrappedEntity, EntityEntry existingEntry)
		{
			Type typeFromHandle = typeof(TEntity);
			if (typeFromHandle != existingEntry.WrappedEntity.IdentityType)
			{
				EntityKey entityKey = existingEntry.EntityKey;
				throw new NotSupportedException(Strings.Materializer_RecyclingEntity(TypeHelpers.GetFullName(entityKey.EntityContainerName, entityKey.EntitySetName), typeFromHandle.FullName, existingEntry.WrappedEntity.IdentityType.FullName));
			}
			if (EntityState.Added == existingEntry.State)
			{
				throw new InvalidOperationException(Strings.Materializer_AddedEntityAlreadyExists(typeFromHandle.FullName));
			}
			if (this.MergeOption != MergeOption.AppendOnly)
			{
				if (MergeOption.OverwriteChanges == this.MergeOption)
				{
					if (EntityState.Deleted == existingEntry.State)
					{
						existingEntry.RevertDelete();
					}
					existingEntry.UpdateCurrentValueRecord(wrappedEntity.Entity);
					this.Context.ObjectStateManager.ForgetEntryWithConceptualNull(existingEntry, true);
					existingEntry.AcceptChanges();
					this.Context.ObjectStateManager.FixupReferencesByForeignKeys(existingEntry, true);
					return;
				}
				if (EntityState.Unchanged == existingEntry.State)
				{
					existingEntry.UpdateCurrentValueRecord(wrappedEntity.Entity);
					this.Context.ObjectStateManager.ForgetEntryWithConceptualNull(existingEntry, true);
					existingEntry.AcceptChanges();
					this.Context.ObjectStateManager.FixupReferencesByForeignKeys(existingEntry, true);
					return;
				}
				if (this.Context.ContextOptions.UseLegacyPreserveChangesBehavior)
				{
					existingEntry.UpdateRecordWithoutSetModified(wrappedEntity.Entity, existingEntry.EditableOriginalValues);
					return;
				}
				existingEntry.UpdateRecordWithSetModified(wrappedEntity.Entity, existingEntry.EditableOriginalValues);
			}
		}

		// Token: 0x06004D1A RID: 19738 RVA: 0x0010F94C File Offset: 0x0010DB4C
		public void RaiseMaterializedEvents()
		{
			if (this._materializedEntities != null)
			{
				foreach (IEntityWrapper entityWrapper in this._materializedEntities)
				{
					this.Context.OnObjectMaterialized(entityWrapper.Entity);
				}
				this._materializedEntities.Clear();
			}
		}

		// Token: 0x06004D1B RID: 19739 RVA: 0x0010F9B8 File Offset: 0x0010DBB8
		public void InitializeForOnMaterialize()
		{
			if (this.Context.OnMaterializedHasHandlers)
			{
				if (this._materializedEntities == null)
				{
					this._materializedEntities = new List<IEntityWrapper>();
					return;
				}
			}
			else if (this._materializedEntities != null)
			{
				this._materializedEntities = null;
			}
		}

		// Token: 0x06004D1C RID: 19740 RVA: 0x0010F9EA File Offset: 0x0010DBEA
		protected void RegisterMaterializedEntityForEvent(IEntityWrapper wrappedEntity)
		{
			if (this._materializedEntities != null)
			{
				this._materializedEntities.Add(wrappedEntity);
			}
		}

		// Token: 0x04001B5C RID: 7004
		private IList<IEntityWrapper> _materializedEntities;

		// Token: 0x04001B5D RID: 7005
		public readonly DbDataReader Reader;

		// Token: 0x04001B5E RID: 7006
		public readonly object[] State;

		// Token: 0x04001B5F RID: 7007
		public readonly ObjectContext Context;

		// Token: 0x04001B60 RID: 7008
		public readonly MetadataWorkspace Workspace;

		// Token: 0x04001B61 RID: 7009
		public readonly MergeOption MergeOption;

		// Token: 0x04001B62 RID: 7010
		protected readonly bool Streaming;

		// Token: 0x04001B63 RID: 7011
		private readonly Lazy<DbSpatialDataReader> _spatialReader;

		// Token: 0x02000C60 RID: 3168
		internal abstract class ErrorHandlingValueReader<T>
		{
			// Token: 0x06006AE0 RID: 27360 RVA: 0x0016C064 File Offset: 0x0016A264
			protected ErrorHandlingValueReader(Func<DbDataReader, int, T> typedValueAccessor, Func<DbDataReader, int, object> untypedValueAccessor)
			{
				this.getTypedValue = typedValueAccessor;
				this.getUntypedValue = untypedValueAccessor;
			}

			// Token: 0x06006AE1 RID: 27361 RVA: 0x0016C07A File Offset: 0x0016A27A
			protected ErrorHandlingValueReader()
				: this(new Func<DbDataReader, int, T>(Shaper.ErrorHandlingValueReader<T>.GetTypedValueDefault), new Func<DbDataReader, int, object>(Shaper.ErrorHandlingValueReader<T>.GetUntypedValueDefault))
			{
			}

			// Token: 0x06006AE2 RID: 27362 RVA: 0x0016C09C File Offset: 0x0016A29C
			private static T GetTypedValueDefault(DbDataReader reader, int ordinal)
			{
				Type underlyingType = Nullable.GetUnderlyingType(typeof(T));
				if (underlyingType != null && underlyingType.IsEnum())
				{
					return (T)((object)Shaper.ErrorHandlingValueReader<T>.GetGenericTypedValueDefaultMethod(underlyingType).Invoke(null, new object[] { reader, ordinal }));
				}
				bool flag;
				return (T)((object)CodeGenEmitter.GetReaderMethod(typeof(T), out flag).Invoke(reader, new object[] { ordinal }));
			}

			// Token: 0x06006AE3 RID: 27363 RVA: 0x0016C11A File Offset: 0x0016A31A
			public static MethodInfo GetGenericTypedValueDefaultMethod(Type underlyingType)
			{
				return typeof(Shaper.ErrorHandlingValueReader<>).MakeGenericType(new Type[] { underlyingType }).GetOnlyDeclaredMethod("GetTypedValueDefault");
			}

			// Token: 0x06006AE4 RID: 27364 RVA: 0x0016C13F File Offset: 0x0016A33F
			private static object GetUntypedValueDefault(DbDataReader reader, int ordinal)
			{
				return reader.GetValue(ordinal);
			}

			// Token: 0x06006AE5 RID: 27365 RVA: 0x0016C148 File Offset: 0x0016A348
			internal T GetValue(DbDataReader reader, int ordinal)
			{
				if (reader.IsDBNull(ordinal))
				{
					try
					{
						return (T)((object)null);
					}
					catch (NullReferenceException)
					{
						throw this.CreateNullValueException();
					}
				}
				T t;
				try
				{
					t = this.getTypedValue(reader, ordinal);
				}
				catch (Exception ex)
				{
					if (ex.IsCatchableExceptionType())
					{
						object obj = this.getUntypedValue(reader, ordinal);
						Type type = ((obj == null) ? null : obj.GetType());
						if (!typeof(T).IsAssignableFrom(type))
						{
							throw this.CreateWrongTypeException(type);
						}
					}
					throw;
				}
				return t;
			}

			// Token: 0x06006AE6 RID: 27366
			protected abstract Exception CreateNullValueException();

			// Token: 0x06006AE7 RID: 27367
			protected abstract Exception CreateWrongTypeException(Type resultType);

			// Token: 0x040030F8 RID: 12536
			private readonly Func<DbDataReader, int, T> getTypedValue;

			// Token: 0x040030F9 RID: 12537
			private readonly Func<DbDataReader, int, object> getUntypedValue;
		}

		// Token: 0x02000C61 RID: 3169
		private class ColumnErrorHandlingValueReader<TColumn> : Shaper.ErrorHandlingValueReader<TColumn>
		{
			// Token: 0x06006AE8 RID: 27368 RVA: 0x0016C1DC File Offset: 0x0016A3DC
			internal ColumnErrorHandlingValueReader()
			{
			}

			// Token: 0x06006AE9 RID: 27369 RVA: 0x0016C1E4 File Offset: 0x0016A3E4
			internal ColumnErrorHandlingValueReader(Func<DbDataReader, int, TColumn> typedAccessor, Func<DbDataReader, int, object> untypedAccessor)
				: base(typedAccessor, untypedAccessor)
			{
			}

			// Token: 0x06006AEA RID: 27370 RVA: 0x0016C1EE File Offset: 0x0016A3EE
			protected override Exception CreateNullValueException()
			{
				return new InvalidOperationException(Strings.Materializer_NullReferenceCast(typeof(TColumn)));
			}

			// Token: 0x06006AEB RID: 27371 RVA: 0x0016C204 File Offset: 0x0016A404
			protected override Exception CreateWrongTypeException(Type resultType)
			{
				return EntityUtil.ValueInvalidCast(resultType, typeof(TColumn));
			}
		}

		// Token: 0x02000C62 RID: 3170
		private class PropertyErrorHandlingValueReader<TProperty> : Shaper.ErrorHandlingValueReader<TProperty>
		{
			// Token: 0x06006AEC RID: 27372 RVA: 0x0016C216 File Offset: 0x0016A416
			internal PropertyErrorHandlingValueReader(string propertyName, string typeName)
			{
				this._propertyName = propertyName;
				this._typeName = typeName;
			}

			// Token: 0x06006AED RID: 27373 RVA: 0x0016C22C File Offset: 0x0016A42C
			internal PropertyErrorHandlingValueReader(string propertyName, string typeName, Func<DbDataReader, int, TProperty> typedAccessor, Func<DbDataReader, int, object> untypedAccessor)
				: base(typedAccessor, untypedAccessor)
			{
				this._propertyName = propertyName;
				this._typeName = typeName;
			}

			// Token: 0x06006AEE RID: 27374 RVA: 0x0016C245 File Offset: 0x0016A445
			protected override Exception CreateNullValueException()
			{
				return new ConstraintException(Strings.Materializer_SetInvalidValue(Nullable.GetUnderlyingType(typeof(TProperty)) ?? typeof(TProperty), this._typeName, this._propertyName, "null"));
			}

			// Token: 0x06006AEF RID: 27375 RVA: 0x0016C27F File Offset: 0x0016A47F
			protected override Exception CreateWrongTypeException(Type resultType)
			{
				return new InvalidOperationException(Strings.Materializer_SetInvalidValue(Nullable.GetUnderlyingType(typeof(TProperty)) ?? typeof(TProperty), this._typeName, this._propertyName, resultType));
			}

			// Token: 0x040030FA RID: 12538
			private readonly string _propertyName;

			// Token: 0x040030FB RID: 12539
			private readonly string _typeName;
		}
	}
}
