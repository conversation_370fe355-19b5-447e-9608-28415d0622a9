﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x0200002F RID: 47
	public class Sys_Users_UltimateLuxuryRecharge
	{
		// Token: 0x17000170 RID: 368
		// (get) Token: 0x0600030E RID: 782 RVA: 0x00003A71 File Offset: 0x00001C71
		// (set) Token: 0x0600030F RID: 783 RVA: 0x00003A79 File Offset: 0x00001C79
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000171 RID: 369
		// (get) Token: 0x06000310 RID: 784 RVA: 0x00003A82 File Offset: 0x00001C82
		// (set) Token: 0x06000311 RID: 785 RVA: 0x00003A8A File Offset: 0x00001C8A
		public int UserID { get; set; }

		// Token: 0x17000172 RID: 370
		// (get) Token: 0x06000312 RID: 786 RVA: 0x00003A93 File Offset: 0x00001C93
		// (set) Token: 0x06000313 RID: 787 RVA: 0x00003A9B File Offset: 0x00001C9B
		public int RechargeMoney { get; set; }

		// Token: 0x17000173 RID: 371
		// (get) Token: 0x06000314 RID: 788 RVA: 0x00003AA4 File Offset: 0x00001CA4
		// (set) Token: 0x06000315 RID: 789 RVA: 0x00003AAC File Offset: 0x00001CAC
		public int GetRechargeCount { get; set; }

		// Token: 0x17000174 RID: 372
		// (get) Token: 0x06000316 RID: 790 RVA: 0x00003AB5 File Offset: 0x00001CB5
		// (set) Token: 0x06000317 RID: 791 RVA: 0x00003ABD File Offset: 0x00001CBD
		public string GetRechargeArr { get; set; }
	}
}
