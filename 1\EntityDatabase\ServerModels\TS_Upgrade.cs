﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x0200001B RID: 27
	public class TS_Upgrade
	{
		// Token: 0x170000B6 RID: 182
		// (get) Token: 0x06000185 RID: 389 RVA: 0x00002D5A File Offset: 0x00000F5A
		// (set) Token: 0x06000186 RID: 390 RVA: 0x00002D62 File Offset: 0x00000F62
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int ID { get; set; }

		// Token: 0x170000B7 RID: 183
		// (get) Token: 0x06000187 RID: 391 RVA: 0x00002D6B File Offset: 0x00000F6B
		// (set) Token: 0x06000188 RID: 392 RVA: 0x00002D73 File Offset: 0x00000F73
		public int Types { get; set; }

		// Token: 0x170000B8 RID: 184
		// (get) Token: 0x06000189 RID: 393 RVA: 0x00002D7C File Offset: 0x00000F7C
		// (set) Token: 0x0600018A RID: 394 RVA: 0x00002D84 File Offset: 0x00000F84
		public int TemplateId { get; set; }

		// Token: 0x170000B9 RID: 185
		// (get) Token: 0x0600018B RID: 395 RVA: 0x00002D8D File Offset: 0x00000F8D
		// (set) Token: 0x0600018C RID: 396 RVA: 0x00002D95 File Offset: 0x00000F95
		public int Grades { get; set; }

		// Token: 0x170000BA RID: 186
		// (get) Token: 0x0600018D RID: 397 RVA: 0x00002D9E File Offset: 0x00000F9E
		// (set) Token: 0x0600018E RID: 398 RVA: 0x00002DA6 File Offset: 0x00000FA6
		public int Data { get; set; }

		// Token: 0x170000BB RID: 187
		// (get) Token: 0x0600018F RID: 399 RVA: 0x00002DAF File Offset: 0x00000FAF
		// (set) Token: 0x06000190 RID: 400 RVA: 0x00002DB7 File Offset: 0x00000FB7
		public string TemplateName { get; set; }

		// Token: 0x170000BC RID: 188
		// (get) Token: 0x06000191 RID: 401 RVA: 0x00002DC0 File Offset: 0x00000FC0
		// (set) Token: 0x06000192 RID: 402 RVA: 0x00002DC8 File Offset: 0x00000FC8
		public string ActiveObj { get; set; }

		// Token: 0x170000BD RID: 189
		// (get) Token: 0x06000193 RID: 403 RVA: 0x00002DD1 File Offset: 0x00000FD1
		// (set) Token: 0x06000194 RID: 404 RVA: 0x00002DD9 File Offset: 0x00000FD9
		public int ItemTempId1 { get; set; }

		// Token: 0x170000BE RID: 190
		// (get) Token: 0x06000195 RID: 405 RVA: 0x00002DE2 File Offset: 0x00000FE2
		// (set) Token: 0x06000196 RID: 406 RVA: 0x00002DEA File Offset: 0x00000FEA
		public int Param1 { get; set; }

		// Token: 0x170000BF RID: 191
		// (get) Token: 0x06000197 RID: 407 RVA: 0x00002DF3 File Offset: 0x00000FF3
		// (set) Token: 0x06000198 RID: 408 RVA: 0x00002DFB File Offset: 0x00000FFB
		public int ItemTempId2 { get; set; }

		// Token: 0x170000C0 RID: 192
		// (get) Token: 0x06000199 RID: 409 RVA: 0x00002E04 File Offset: 0x00001004
		// (set) Token: 0x0600019A RID: 410 RVA: 0x00002E0C File Offset: 0x0000100C
		public int Param2 { get; set; }

		// Token: 0x170000C1 RID: 193
		// (get) Token: 0x0600019B RID: 411 RVA: 0x00002E15 File Offset: 0x00001015
		// (set) Token: 0x0600019C RID: 412 RVA: 0x00002E1D File Offset: 0x0000101D
		public int ItemTempId3 { get; set; }

		// Token: 0x170000C2 RID: 194
		// (get) Token: 0x0600019D RID: 413 RVA: 0x00002E26 File Offset: 0x00001026
		// (set) Token: 0x0600019E RID: 414 RVA: 0x00002E2E File Offset: 0x0000102E
		public int Param3 { get; set; }

		// Token: 0x170000C3 RID: 195
		// (get) Token: 0x0600019F RID: 415 RVA: 0x00002E37 File Offset: 0x00001037
		// (set) Token: 0x060001A0 RID: 416 RVA: 0x00002E3F File Offset: 0x0000103F
		public int ItemTempId4 { get; set; }

		// Token: 0x170000C4 RID: 196
		// (get) Token: 0x060001A1 RID: 417 RVA: 0x00002E48 File Offset: 0x00001048
		// (set) Token: 0x060001A2 RID: 418 RVA: 0x00002E50 File Offset: 0x00001050
		public int Param4 { get; set; }
	}
}
