﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Text;
using System.Threading;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004DA RID: 1242
	public abstract class MetadataItem
	{
		// Token: 0x06003DC6 RID: 15814 RVA: 0x000CB76C File Offset: 0x000C996C
		internal MetadataItem()
		{
		}

		// Token: 0x06003DC7 RID: 15815 RVA: 0x000CB774 File Offset: 0x000C9974
		internal MetadataItem(MetadataItem.MetadataFlags flags)
		{
			this._flags = (int)flags;
		}

		// Token: 0x17000C20 RID: 3104
		// (get) Token: 0x06003DC8 RID: 15816 RVA: 0x000CB783 File Offset: 0x000C9983
		internal virtual IEnumerable<MetadataProperty> Annotations
		{
			get
			{
				return from p in this.GetMetadataProperties()
					where p.IsAnnotation
					select p;
			}
		}

		// Token: 0x17000C21 RID: 3105
		// (get) Token: 0x06003DC9 RID: 15817
		public abstract BuiltInTypeKind BuiltInTypeKind { get; }

		// Token: 0x17000C22 RID: 3106
		// (get) Token: 0x06003DCA RID: 15818 RVA: 0x000CB7AF File Offset: 0x000C99AF
		[MetadataProperty(BuiltInTypeKind.MetadataProperty, true)]
		public virtual ReadOnlyMetadataCollection<MetadataProperty> MetadataProperties
		{
			get
			{
				return this.GetMetadataProperties().AsReadOnlyMetadataCollection();
			}
		}

		// Token: 0x06003DCB RID: 15819 RVA: 0x000CB7BC File Offset: 0x000C99BC
		internal MetadataPropertyCollection GetMetadataProperties()
		{
			if (this._itemAttributes == null)
			{
				MetadataPropertyCollection metadataPropertyCollection = new MetadataPropertyCollection(this);
				if (this.IsReadOnly)
				{
					metadataPropertyCollection.SetReadOnly();
				}
				Interlocked.CompareExchange<MetadataPropertyCollection>(ref this._itemAttributes, metadataPropertyCollection, null);
			}
			return this._itemAttributes;
		}

		// Token: 0x06003DCC RID: 15820 RVA: 0x000CB7FC File Offset: 0x000C99FC
		public void AddAnnotation(string name, object value)
		{
			Check.NotEmpty(name, "name");
			MetadataProperty metadataProperty = this.Annotations.FirstOrDefault((MetadataProperty a) => a.Name == name);
			if (metadataProperty == null)
			{
				if (value != null)
				{
					this.GetMetadataProperties().Add(MetadataProperty.CreateAnnotation(name, value));
				}
				return;
			}
			if (value == null)
			{
				this.RemoveAnnotation(name);
				return;
			}
			metadataProperty.Value = value;
		}

		// Token: 0x06003DCD RID: 15821 RVA: 0x000CB878 File Offset: 0x000C9A78
		public bool RemoveAnnotation(string name)
		{
			Check.NotEmpty(name, "name");
			MetadataPropertyCollection metadataProperties = this.GetMetadataProperties();
			MetadataProperty metadataProperty;
			return metadataProperties.TryGetValue(name, false, out metadataProperty) && metadataProperties.Remove(metadataProperty);
		}

		// Token: 0x17000C23 RID: 3107
		// (get) Token: 0x06003DCE RID: 15822 RVA: 0x000CB8AD File Offset: 0x000C9AAD
		internal MetadataCollection<MetadataProperty> RawMetadataProperties
		{
			get
			{
				return this._itemAttributes;
			}
		}

		// Token: 0x17000C24 RID: 3108
		// (get) Token: 0x06003DCF RID: 15823 RVA: 0x000CB8B5 File Offset: 0x000C9AB5
		// (set) Token: 0x06003DD0 RID: 15824 RVA: 0x000CB8BD File Offset: 0x000C9ABD
		public Documentation Documentation { get; set; }

		// Token: 0x17000C25 RID: 3109
		// (get) Token: 0x06003DD1 RID: 15825
		internal abstract string Identity { get; }

		// Token: 0x06003DD2 RID: 15826 RVA: 0x000CB8C6 File Offset: 0x000C9AC6
		internal virtual bool EdmEquals(MetadataItem item)
		{
			return item != null && (this == item || (this.BuiltInTypeKind == item.BuiltInTypeKind && this.Identity == item.Identity));
		}

		// Token: 0x17000C26 RID: 3110
		// (get) Token: 0x06003DD3 RID: 15827 RVA: 0x000CB8F4 File Offset: 0x000C9AF4
		internal bool IsReadOnly
		{
			get
			{
				return this.GetFlag(MetadataItem.MetadataFlags.Readonly);
			}
		}

		// Token: 0x06003DD4 RID: 15828 RVA: 0x000CB8FD File Offset: 0x000C9AFD
		internal virtual void SetReadOnly()
		{
			if (!this.IsReadOnly)
			{
				if (this._itemAttributes != null)
				{
					this._itemAttributes.SetReadOnly();
				}
				this.SetFlag(MetadataItem.MetadataFlags.Readonly, true);
			}
		}

		// Token: 0x06003DD5 RID: 15829 RVA: 0x000CB923 File Offset: 0x000C9B23
		internal virtual void BuildIdentity(StringBuilder builder)
		{
			builder.Append(this.Identity);
		}

		// Token: 0x06003DD6 RID: 15830 RVA: 0x000CB932 File Offset: 0x000C9B32
		internal void AddMetadataProperties(IEnumerable<MetadataProperty> metadataProperties)
		{
			this.GetMetadataProperties().AddRange(metadataProperties);
		}

		// Token: 0x06003DD7 RID: 15831 RVA: 0x000CB940 File Offset: 0x000C9B40
		internal DataSpace GetDataSpace()
		{
			switch (this._flags & 7)
			{
			case 1:
				return DataSpace.CSpace;
			case 2:
				return DataSpace.OSpace;
			case 3:
				return DataSpace.OCSpace;
			case 4:
				return DataSpace.SSpace;
			case 5:
				return DataSpace.CSSpace;
			default:
				return (DataSpace)(-1);
			}
		}

		// Token: 0x06003DD8 RID: 15832 RVA: 0x000CB97D File Offset: 0x000C9B7D
		internal void SetDataSpace(DataSpace space)
		{
			this._flags = (this._flags & -8) | (int)(MetadataItem.MetadataFlags.DataSpace & MetadataItem.Convert(space));
		}

		// Token: 0x06003DD9 RID: 15833 RVA: 0x000CB997 File Offset: 0x000C9B97
		private static MetadataItem.MetadataFlags Convert(DataSpace space)
		{
			switch (space)
			{
			case DataSpace.OSpace:
				return MetadataItem.MetadataFlags.OSpace;
			case DataSpace.CSpace:
				return MetadataItem.MetadataFlags.CSpace;
			case DataSpace.SSpace:
				return MetadataItem.MetadataFlags.SSpace;
			case DataSpace.OCSpace:
				return MetadataItem.MetadataFlags.OCSpace;
			case DataSpace.CSSpace:
				return MetadataItem.MetadataFlags.CSSpace;
			default:
				return MetadataItem.MetadataFlags.None;
			}
		}

		// Token: 0x06003DDA RID: 15834 RVA: 0x000CB9C0 File Offset: 0x000C9BC0
		internal ParameterMode GetParameterMode()
		{
			MetadataItem.MetadataFlags metadataFlags = (MetadataItem.MetadataFlags)(this._flags & 3584);
			if (metadataFlags <= MetadataItem.MetadataFlags.Out)
			{
				if (metadataFlags == MetadataItem.MetadataFlags.In)
				{
					return ParameterMode.In;
				}
				if (metadataFlags == MetadataItem.MetadataFlags.Out)
				{
					return ParameterMode.Out;
				}
			}
			else
			{
				if (metadataFlags == MetadataItem.MetadataFlags.InOut)
				{
					return ParameterMode.InOut;
				}
				if (metadataFlags == MetadataItem.MetadataFlags.ReturnValue)
				{
					return ParameterMode.ReturnValue;
				}
			}
			return (ParameterMode)(-1);
		}

		// Token: 0x06003DDB RID: 15835 RVA: 0x000CBA0D File Offset: 0x000C9C0D
		internal void SetParameterMode(ParameterMode mode)
		{
			this._flags = (this._flags & -3585) | (int)(MetadataItem.MetadataFlags.ParameterMode & MetadataItem.Convert(mode));
		}

		// Token: 0x06003DDC RID: 15836 RVA: 0x000CBA2E File Offset: 0x000C9C2E
		private static MetadataItem.MetadataFlags Convert(ParameterMode mode)
		{
			switch (mode)
			{
			case ParameterMode.In:
				return MetadataItem.MetadataFlags.In;
			case ParameterMode.Out:
				return MetadataItem.MetadataFlags.Out;
			case ParameterMode.InOut:
				return MetadataItem.MetadataFlags.InOut;
			case ParameterMode.ReturnValue:
				return MetadataItem.MetadataFlags.ReturnValue;
			default:
				return MetadataItem.MetadataFlags.ParameterMode;
			}
		}

		// Token: 0x06003DDD RID: 15837 RVA: 0x000CBA63 File Offset: 0x000C9C63
		internal bool GetFlag(MetadataItem.MetadataFlags flag)
		{
			return flag == (MetadataItem.MetadataFlags)(this._flags & (int)flag);
		}

		// Token: 0x06003DDE RID: 15838 RVA: 0x000CBA70 File Offset: 0x000C9C70
		internal void SetFlag(MetadataItem.MetadataFlags flag, bool value)
		{
			SpinWait spinWait = default(SpinWait);
			for (;;)
			{
				int flags = this._flags;
				int num = (value ? (flags | (int)flag) : (flags & (int)(~(int)flag)));
				if ((flags & 8) == 8)
				{
					break;
				}
				if (flags == Interlocked.CompareExchange(ref this._flags, num, flags))
				{
					return;
				}
				spinWait.SpinOnce();
			}
			if ((flag & MetadataItem.MetadataFlags.Readonly) == MetadataItem.MetadataFlags.Readonly)
			{
				return;
			}
			throw new InvalidOperationException(Strings.OperationOnReadOnlyItem);
		}

		// Token: 0x06003DDF RID: 15839 RVA: 0x000CBACC File Offset: 0x000C9CCC
		static MetadataItem()
		{
			MetadataItem._builtInTypes[0] = new ComplexType();
			MetadataItem._builtInTypes[2] = new ComplexType();
			MetadataItem._builtInTypes[1] = new ComplexType();
			MetadataItem._builtInTypes[3] = new ComplexType();
			MetadataItem._builtInTypes[3] = new ComplexType();
			MetadataItem._builtInTypes[7] = new EnumType();
			MetadataItem._builtInTypes[6] = new ComplexType();
			MetadataItem._builtInTypes[8] = new ComplexType();
			MetadataItem._builtInTypes[9] = new ComplexType();
			MetadataItem._builtInTypes[10] = new EnumType();
			MetadataItem._builtInTypes[11] = new ComplexType();
			MetadataItem._builtInTypes[12] = new ComplexType();
			MetadataItem._builtInTypes[13] = new ComplexType();
			MetadataItem._builtInTypes[14] = new ComplexType();
			MetadataItem._builtInTypes[4] = new ComplexType();
			MetadataItem._builtInTypes[5] = new ComplexType();
			MetadataItem._builtInTypes[15] = new ComplexType();
			MetadataItem._builtInTypes[16] = new ComplexType();
			MetadataItem._builtInTypes[17] = new ComplexType();
			MetadataItem._builtInTypes[18] = new ComplexType();
			MetadataItem._builtInTypes[19] = new ComplexType();
			MetadataItem._builtInTypes[20] = new ComplexType();
			MetadataItem._builtInTypes[21] = new ComplexType();
			MetadataItem._builtInTypes[22] = new ComplexType();
			MetadataItem._builtInTypes[23] = new ComplexType();
			MetadataItem._builtInTypes[24] = new ComplexType();
			MetadataItem._builtInTypes[25] = new EnumType();
			MetadataItem._builtInTypes[26] = new ComplexType();
			MetadataItem._builtInTypes[27] = new EnumType();
			MetadataItem._builtInTypes[28] = new ComplexType();
			MetadataItem._builtInTypes[29] = new ComplexType();
			MetadataItem._builtInTypes[30] = new ComplexType();
			MetadataItem._builtInTypes[31] = new ComplexType();
			MetadataItem._builtInTypes[32] = new ComplexType();
			MetadataItem._builtInTypes[33] = new EnumType();
			MetadataItem._builtInTypes[34] = new ComplexType();
			MetadataItem._builtInTypes[35] = new ComplexType();
			MetadataItem._builtInTypes[36] = new ComplexType();
			MetadataItem._builtInTypes[37] = new ComplexType();
			MetadataItem._builtInTypes[38] = new ComplexType();
			MetadataItem._builtInTypes[39] = new ComplexType();
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataItem), "ItemType", false, null);
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataProperty), "MetadataProperty", true, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataItem));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.GlobalItem), "GlobalItem", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataItem));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.TypeUsage), "TypeUsage", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataItem));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmType), "EdmType", true, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.GlobalItem));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.SimpleType), "SimpleType", true, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmType));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EnumType), "EnumType", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.SimpleType));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.PrimitiveType), "PrimitiveType", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.SimpleType));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.CollectionType), "CollectionType", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmType));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.RefType), "RefType", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmType));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmMember), "EdmMember", true, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataItem));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmProperty), "EdmProperty", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmMember));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.NavigationProperty), "NavigationProperty", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmMember));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.ProviderManifest), "ProviderManifest", true, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataItem));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.RelationshipEndMember), "RelationshipEnd", true, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmMember));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.AssociationEndMember), "AssociationEnd", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.RelationshipEndMember));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EnumMember), "EnumMember", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataItem));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.ReferentialConstraint), "ReferentialConstraint", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataItem));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.StructuralType), "StructuralType", true, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmType));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.RowType), "RowType", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.StructuralType));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.ComplexType), "ComplexType", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.StructuralType));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EntityTypeBase), "ElementType", true, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.StructuralType));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EntityType), "EntityType", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EntityTypeBase));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.RelationshipType), "RelationshipType", true, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EntityTypeBase));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.AssociationType), "AssociationType", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.RelationshipType));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.Facet), "Facet", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataItem));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EntityContainer), "EntityContainerType", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.GlobalItem));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EntitySetBase), "BaseEntitySetType", true, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataItem));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EntitySet), "EntitySetType", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EntitySetBase));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.RelationshipSet), "RelationshipSet", true, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EntitySetBase));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.AssociationSet), "AssociationSetType", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.RelationshipSet));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.AssociationSetEnd), "AssociationSetEndType", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataItem));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.FunctionParameter), "FunctionParameter", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataItem));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmFunction), "EdmFunction", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmType));
			MetadataItem.InitializeBuiltInTypes((ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.Documentation), "Documentation", false, (ComplexType)MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataItem));
			MetadataItem.InitializeEnumType(BuiltInTypeKind.OperationAction, "DeleteAction", new string[] { "None", "Cascade" });
			MetadataItem.InitializeEnumType(BuiltInTypeKind.RelationshipMultiplicity, "RelationshipMultiplicity", new string[] { "One", "ZeroToOne", "Many" });
			MetadataItem.InitializeEnumType(BuiltInTypeKind.ParameterMode, "ParameterMode", new string[] { "In", "Out", "InOut" });
			MetadataItem.InitializeEnumType(BuiltInTypeKind.CollectionKind, "CollectionKind", new string[] { "None", "List", "Bag" });
			MetadataItem.InitializeEnumType(BuiltInTypeKind.PrimitiveTypeKind, "PrimitiveTypeKind", Enum.GetNames(typeof(PrimitiveTypeKind)));
			FacetDescription[] array = new FacetDescription[2];
			MetadataItem._nullableFacetDescription = new FacetDescription("Nullable", MetadataItem.EdmProviderManifest.GetPrimitiveType(PrimitiveTypeKind.Boolean), null, null, true);
			array[0] = MetadataItem._nullableFacetDescription;
			MetadataItem._defaultValueFacetDescription = new FacetDescription("DefaultValue", MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmType), null, null, null);
			array[1] = MetadataItem._defaultValueFacetDescription;
			MetadataItem._generalFacetDescriptions = new ReadOnlyCollection<FacetDescription>(array);
			MetadataItem._collectionKindFacetDescription = new FacetDescription("CollectionKind", MetadataItem.GetBuiltInType(BuiltInTypeKind.EnumType), null, null, null);
			TypeUsage typeUsage = TypeUsage.Create(MetadataItem.EdmProviderManifest.GetPrimitiveType(PrimitiveTypeKind.String));
			TypeUsage typeUsage2 = TypeUsage.Create(MetadataItem.EdmProviderManifest.GetPrimitiveType(PrimitiveTypeKind.Boolean));
			TypeUsage typeUsage3 = TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmType));
			TypeUsage typeUsage4 = TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.TypeUsage));
			TypeUsage typeUsage5 = TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.ComplexType));
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.MetadataProperty, new EdmProperty[]
			{
				new EdmProperty("Name", typeUsage),
				new EdmProperty("TypeUsage", typeUsage4),
				new EdmProperty("Value", typeUsage5)
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.MetadataItem, new EdmProperty[]
			{
				new EdmProperty("MetadataProperties", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.MetadataProperty).GetCollectionType())),
				new EdmProperty("Documentation", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.Documentation)))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.TypeUsage, new EdmProperty[]
			{
				new EdmProperty("EdmType", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmType))),
				new EdmProperty("Facets", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.Facet)))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.EdmType, new EdmProperty[]
			{
				new EdmProperty("Name", typeUsage),
				new EdmProperty("Namespace", typeUsage),
				new EdmProperty("Abstract", typeUsage2),
				new EdmProperty("Sealed", typeUsage2),
				new EdmProperty("BaseType", typeUsage5)
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.EnumType, new EdmProperty[]
			{
				new EdmProperty("EnumMembers", typeUsage)
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.CollectionType, new EdmProperty[]
			{
				new EdmProperty("TypeUsage", typeUsage4)
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.RefType, new EdmProperty[]
			{
				new EdmProperty("EntityType", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.EntityType)))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.EdmMember, new EdmProperty[]
			{
				new EdmProperty("Name", typeUsage),
				new EdmProperty("TypeUsage", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.TypeUsage)))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.EdmProperty, new EdmProperty[]
			{
				new EdmProperty("Nullable", typeUsage),
				new EdmProperty("DefaultValue", typeUsage5)
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.NavigationProperty, new EdmProperty[]
			{
				new EdmProperty("RelationshipTypeName", typeUsage),
				new EdmProperty("ToEndMemberName", typeUsage)
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.RelationshipEndMember, new EdmProperty[]
			{
				new EdmProperty("OperationBehaviors", typeUsage5),
				new EdmProperty("RelationshipMultiplicity", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.EnumType)))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.EnumMember, new EdmProperty[]
			{
				new EdmProperty("Name", typeUsage)
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.ReferentialConstraint, new EdmProperty[]
			{
				new EdmProperty("ToRole", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.RelationshipEndMember))),
				new EdmProperty("FromRole", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.RelationshipEndMember))),
				new EdmProperty("ToProperties", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmProperty).GetCollectionType())),
				new EdmProperty("FromProperties", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmProperty).GetCollectionType()))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.StructuralType, new EdmProperty[]
			{
				new EdmProperty("Members", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmMember)))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.EntityTypeBase, new EdmProperty[]
			{
				new EdmProperty("KeyMembers", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmMember)))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.Facet, new EdmProperty[]
			{
				new EdmProperty("Name", typeUsage),
				new EdmProperty("EdmType", typeUsage3),
				new EdmProperty("Value", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.EdmType)))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.EntityContainer, new EdmProperty[]
			{
				new EdmProperty("Name", typeUsage),
				new EdmProperty("EntitySets", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.EntitySet)))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.EntitySetBase, new EdmProperty[]
			{
				new EdmProperty("Name", typeUsage),
				new EdmProperty("EntityType", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.EntityType))),
				new EdmProperty("Schema", typeUsage),
				new EdmProperty("Table", typeUsage)
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.AssociationSet, new EdmProperty[]
			{
				new EdmProperty("AssociationSetEnds", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.AssociationSetEnd).GetCollectionType()))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.AssociationSetEnd, new EdmProperty[]
			{
				new EdmProperty("Role", typeUsage),
				new EdmProperty("EntitySetType", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.EntitySet)))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.FunctionParameter, new EdmProperty[]
			{
				new EdmProperty("Name", typeUsage),
				new EdmProperty("Mode", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.EnumType))),
				new EdmProperty("TypeUsage", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.TypeUsage)))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.EdmFunction, new EdmProperty[]
			{
				new EdmProperty("Name", typeUsage),
				new EdmProperty("Namespace", typeUsage),
				new EdmProperty("ReturnParameter", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.FunctionParameter))),
				new EdmProperty("Parameters", TypeUsage.Create(MetadataItem.GetBuiltInType(BuiltInTypeKind.FunctionParameter).GetCollectionType()))
			});
			MetadataItem.AddBuiltInTypeProperties(BuiltInTypeKind.Documentation, new EdmProperty[]
			{
				new EdmProperty("Summary", typeUsage),
				new EdmProperty("LongDescription", typeUsage)
			});
			for (int i = 0; i < MetadataItem._builtInTypes.Length; i++)
			{
				MetadataItem._builtInTypes[i].SetReadOnly();
			}
		}

		// Token: 0x17000C27 RID: 3111
		// (get) Token: 0x06003DE0 RID: 15840 RVA: 0x000CC871 File Offset: 0x000CAA71
		internal static FacetDescription DefaultValueFacetDescription
		{
			get
			{
				return MetadataItem._defaultValueFacetDescription;
			}
		}

		// Token: 0x17000C28 RID: 3112
		// (get) Token: 0x06003DE1 RID: 15841 RVA: 0x000CC878 File Offset: 0x000CAA78
		internal static FacetDescription CollectionKindFacetDescription
		{
			get
			{
				return MetadataItem._collectionKindFacetDescription;
			}
		}

		// Token: 0x17000C29 RID: 3113
		// (get) Token: 0x06003DE2 RID: 15842 RVA: 0x000CC87F File Offset: 0x000CAA7F
		internal static FacetDescription NullableFacetDescription
		{
			get
			{
				return MetadataItem._nullableFacetDescription;
			}
		}

		// Token: 0x17000C2A RID: 3114
		// (get) Token: 0x06003DE3 RID: 15843 RVA: 0x000CC886 File Offset: 0x000CAA86
		internal static EdmProviderManifest EdmProviderManifest
		{
			get
			{
				return EdmProviderManifest.Instance;
			}
		}

		// Token: 0x06003DE4 RID: 15844 RVA: 0x000CC88D File Offset: 0x000CAA8D
		public static EdmType GetBuiltInType(BuiltInTypeKind builtInTypeKind)
		{
			return MetadataItem._builtInTypes[(int)builtInTypeKind];
		}

		// Token: 0x06003DE5 RID: 15845 RVA: 0x000CC896 File Offset: 0x000CAA96
		public static ReadOnlyCollection<FacetDescription> GetGeneralFacetDescriptions()
		{
			return MetadataItem._generalFacetDescriptions;
		}

		// Token: 0x06003DE6 RID: 15846 RVA: 0x000CC89D File Offset: 0x000CAA9D
		private static void InitializeBuiltInTypes(ComplexType builtInType, string name, bool isAbstract, ComplexType baseType)
		{
			EdmType.Initialize(builtInType, name, "Edm", DataSpace.CSpace, isAbstract, baseType);
		}

		// Token: 0x06003DE7 RID: 15847 RVA: 0x000CC8B0 File Offset: 0x000CAAB0
		private static void AddBuiltInTypeProperties(BuiltInTypeKind builtInTypeKind, EdmProperty[] properties)
		{
			ComplexType complexType = (ComplexType)MetadataItem.GetBuiltInType(builtInTypeKind);
			if (properties != null)
			{
				for (int i = 0; i < properties.Length; i++)
				{
					complexType.AddMember(properties[i]);
				}
			}
		}

		// Token: 0x06003DE8 RID: 15848 RVA: 0x000CC8E4 File Offset: 0x000CAAE4
		private static void InitializeEnumType(BuiltInTypeKind builtInTypeKind, string name, string[] enumMemberNames)
		{
			EnumType enumType = (EnumType)MetadataItem.GetBuiltInType(builtInTypeKind);
			EdmType.Initialize(enumType, name, "Edm", DataSpace.CSpace, false, null);
			for (int i = 0; i < enumMemberNames.Length; i++)
			{
				enumType.AddMember(new EnumMember(enumMemberNames[i], i));
			}
		}

		// Token: 0x0400150A RID: 5386
		private int _flags;

		// Token: 0x0400150B RID: 5387
		private MetadataPropertyCollection _itemAttributes;

		// Token: 0x0400150D RID: 5389
		private static readonly EdmType[] _builtInTypes = new EdmType[40];

		// Token: 0x0400150E RID: 5390
		private static readonly ReadOnlyCollection<FacetDescription> _generalFacetDescriptions;

		// Token: 0x0400150F RID: 5391
		private static readonly FacetDescription _nullableFacetDescription;

		// Token: 0x04001510 RID: 5392
		private static readonly FacetDescription _defaultValueFacetDescription;

		// Token: 0x04001511 RID: 5393
		private static readonly FacetDescription _collectionKindFacetDescription;

		// Token: 0x02000AF8 RID: 2808
		[Flags]
		internal enum MetadataFlags
		{
			// Token: 0x04002C64 RID: 11364
			None = 0,
			// Token: 0x04002C65 RID: 11365
			CSpace = 1,
			// Token: 0x04002C66 RID: 11366
			OSpace = 2,
			// Token: 0x04002C67 RID: 11367
			OCSpace = 3,
			// Token: 0x04002C68 RID: 11368
			SSpace = 4,
			// Token: 0x04002C69 RID: 11369
			CSSpace = 5,
			// Token: 0x04002C6A RID: 11370
			DataSpace = 7,
			// Token: 0x04002C6B RID: 11371
			Readonly = 8,
			// Token: 0x04002C6C RID: 11372
			IsAbstract = 16,
			// Token: 0x04002C6D RID: 11373
			In = 512,
			// Token: 0x04002C6E RID: 11374
			Out = 1024,
			// Token: 0x04002C6F RID: 11375
			InOut = 1536,
			// Token: 0x04002C70 RID: 11376
			ReturnValue = 2048,
			// Token: 0x04002C71 RID: 11377
			ParameterMode = 3584
		}
	}
}
