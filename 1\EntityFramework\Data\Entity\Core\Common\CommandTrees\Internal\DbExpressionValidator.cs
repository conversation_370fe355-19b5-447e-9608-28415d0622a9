﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Common.CommandTrees.Internal
{
	// Token: 0x020006F4 RID: 1780
	internal sealed class DbExpressionValidator : DbExpressionRebinder
	{
		// Token: 0x060052D7 RID: 21207 RVA: 0x001285D4 File Offset: 0x001267D4
		internal DbExpressionValidator(MetadataWorkspace metadata, DataSpace expectedDataSpace)
			: base(metadata)
		{
			this.requiredSpace = expectedDataSpace;
			this.allowedFunctionSpaces = new DataSpace[]
			{
				DataSpace.CSpace,
				DataSpace.SSpace
			};
			if (expectedDataSpace == DataSpace.SSpace)
			{
				this.allowedMetadataSpaces = new DataSpace[]
				{
					DataSpace.SSpace,
					DataSpace.CSpace
				};
				return;
			}
			this.allowedMetadataSpaces = new DataSpace[] { DataSpace.CSpace };
		}

		// Token: 0x17001009 RID: 4105
		// (get) Token: 0x060052D8 RID: 21208 RVA: 0x00128642 File Offset: 0x00126842
		internal Dictionary<string, DbParameterReferenceExpression> Parameters
		{
			get
			{
				return this.paramMappings;
			}
		}

		// Token: 0x060052D9 RID: 21209 RVA: 0x0012864A File Offset: 0x0012684A
		internal void ValidateExpression(DbExpression expression, string argumentName)
		{
			this.expressionArgumentName = argumentName;
			this.VisitExpression(expression);
			this.expressionArgumentName = null;
		}

		// Token: 0x060052DA RID: 21210 RVA: 0x00128662 File Offset: 0x00126862
		protected override EntitySetBase VisitEntitySet(EntitySetBase entitySet)
		{
			return this.ValidateMetadata<EntitySetBase>(entitySet, new Func<EntitySetBase, EntitySetBase>(base.VisitEntitySet), (EntitySetBase es) => es.EntityContainer.DataSpace, this.allowedMetadataSpaces);
		}

		// Token: 0x060052DB RID: 21211 RVA: 0x0012869C File Offset: 0x0012689C
		protected override EdmFunction VisitFunction(EdmFunction function)
		{
			return this.ValidateMetadata<EdmFunction>(function, new Func<EdmFunction, EdmFunction>(base.VisitFunction), (EdmFunction func) => func.DataSpace, this.allowedFunctionSpaces);
		}

		// Token: 0x060052DC RID: 21212 RVA: 0x001286D6 File Offset: 0x001268D6
		protected override EdmType VisitType(EdmType type)
		{
			return this.ValidateMetadata<EdmType>(type, new Func<EdmType, EdmType>(base.VisitType), (EdmType et) => et.DataSpace, this.allowedMetadataSpaces);
		}

		// Token: 0x060052DD RID: 21213 RVA: 0x00128710 File Offset: 0x00126910
		protected override TypeUsage VisitTypeUsage(TypeUsage type)
		{
			return this.ValidateMetadata<TypeUsage>(type, new Func<TypeUsage, TypeUsage>(base.VisitTypeUsage), (TypeUsage tu) => tu.EdmType.DataSpace, this.allowedMetadataSpaces);
		}

		// Token: 0x060052DE RID: 21214 RVA: 0x0012874C File Offset: 0x0012694C
		protected override void OnEnterScope(IEnumerable<DbVariableReferenceExpression> scopeVariables)
		{
			Dictionary<string, TypeUsage> dictionary = scopeVariables.ToDictionary((DbVariableReferenceExpression var) => var.VariableName, (DbVariableReferenceExpression var) => var.ResultType, StringComparer.Ordinal);
			this.variableScopes.Push(dictionary);
		}

		// Token: 0x060052DF RID: 21215 RVA: 0x001287AF File Offset: 0x001269AF
		protected override void OnExitScope()
		{
			this.variableScopes.Pop();
		}

		// Token: 0x060052E0 RID: 21216 RVA: 0x001287C0 File Offset: 0x001269C0
		public override DbExpression Visit(DbVariableReferenceExpression expression)
		{
			Check.NotNull<DbVariableReferenceExpression>(expression, "expression");
			DbExpression dbExpression = base.Visit(expression);
			if (dbExpression.ExpressionKind == DbExpressionKind.VariableReference)
			{
				DbVariableReferenceExpression dbVariableReferenceExpression = (DbVariableReferenceExpression)dbExpression;
				TypeUsage typeUsage = null;
				using (Stack<Dictionary<string, TypeUsage>>.Enumerator enumerator = this.variableScopes.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						if (enumerator.Current.TryGetValue(dbVariableReferenceExpression.VariableName, out typeUsage))
						{
							break;
						}
					}
				}
				if (typeUsage == null)
				{
					this.ThrowInvalid(Strings.Cqt_Validator_VarRefInvalid(dbVariableReferenceExpression.VariableName));
				}
				if (!TypeSemantics.IsEqual(dbVariableReferenceExpression.ResultType, typeUsage))
				{
					this.ThrowInvalid(Strings.Cqt_Validator_VarRefTypeMismatch(dbVariableReferenceExpression.VariableName));
				}
			}
			return dbExpression;
		}

		// Token: 0x060052E1 RID: 21217 RVA: 0x00128878 File Offset: 0x00126A78
		public override DbExpression Visit(DbParameterReferenceExpression expression)
		{
			Check.NotNull<DbParameterReferenceExpression>(expression, "expression");
			DbExpression dbExpression = base.Visit(expression);
			if (dbExpression.ExpressionKind == DbExpressionKind.ParameterReference)
			{
				DbParameterReferenceExpression dbParameterReferenceExpression = dbExpression as DbParameterReferenceExpression;
				DbParameterReferenceExpression dbParameterReferenceExpression2;
				if (this.paramMappings.TryGetValue(dbParameterReferenceExpression.ParameterName, out dbParameterReferenceExpression2))
				{
					if (!TypeSemantics.IsEqual(dbParameterReferenceExpression.ResultType, dbParameterReferenceExpression2.ResultType))
					{
						this.ThrowInvalid(Strings.Cqt_Validator_InvalidIncompatibleParameterReferences(dbParameterReferenceExpression.ParameterName));
					}
				}
				else
				{
					this.paramMappings.Add(dbParameterReferenceExpression.ParameterName, dbParameterReferenceExpression);
				}
			}
			return dbExpression;
		}

		// Token: 0x060052E2 RID: 21218 RVA: 0x001288F8 File Offset: 0x00126AF8
		private TMetadata ValidateMetadata<TMetadata>(TMetadata metadata, Func<TMetadata, TMetadata> map, Func<TMetadata, DataSpace> getDataSpace, DataSpace[] allowedSpaces)
		{
			TMetadata tmetadata = map(metadata);
			if (metadata != tmetadata)
			{
				this.ThrowInvalidMetadata<TMetadata>();
			}
			DataSpace resultSpace = getDataSpace(tmetadata);
			if (!allowedSpaces.Any((DataSpace ds) => ds == resultSpace))
			{
				this.ThrowInvalidSpace<TMetadata>();
			}
			return tmetadata;
		}

		// Token: 0x060052E3 RID: 21219 RVA: 0x00128950 File Offset: 0x00126B50
		private void ThrowInvalidMetadata<TMetadata>()
		{
			this.ThrowInvalid(Strings.Cqt_Validator_InvalidOtherWorkspaceMetadata(typeof(TMetadata).Name));
		}

		// Token: 0x060052E4 RID: 21220 RVA: 0x0012896C File Offset: 0x00126B6C
		private void ThrowInvalidSpace<TMetadata>()
		{
			this.ThrowInvalid(Strings.Cqt_Validator_InvalidIncorrectDataSpaceMetadata(typeof(TMetadata).Name, Enum.GetName(typeof(DataSpace), this.requiredSpace)));
		}

		// Token: 0x060052E5 RID: 21221 RVA: 0x001289A2 File Offset: 0x00126BA2
		private void ThrowInvalid(string message)
		{
			throw new ArgumentException(message, this.expressionArgumentName);
		}

		// Token: 0x04001DEC RID: 7660
		private readonly DataSpace requiredSpace;

		// Token: 0x04001DED RID: 7661
		private readonly DataSpace[] allowedMetadataSpaces;

		// Token: 0x04001DEE RID: 7662
		private readonly DataSpace[] allowedFunctionSpaces;

		// Token: 0x04001DEF RID: 7663
		private readonly Dictionary<string, DbParameterReferenceExpression> paramMappings = new Dictionary<string, DbParameterReferenceExpression>();

		// Token: 0x04001DF0 RID: 7664
		private readonly Stack<Dictionary<string, TypeUsage>> variableScopes = new Stack<Dictionary<string, TypeUsage>>();

		// Token: 0x04001DF1 RID: 7665
		private string expressionArgumentName;
	}
}
