﻿using System;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003F6 RID: 1014
	internal sealed class UnionAllOp : SetOp
	{
		// Token: 0x06002F59 RID: 12121 RVA: 0x00094D15 File Offset: 0x00092F15
		private UnionAllOp()
			: base(OpType.UnionAll)
		{
		}

		// Token: 0x06002F5A RID: 12122 RVA: 0x00094D1F File Offset: 0x00092F1F
		internal UnionAllOp(VarVec outputs, VarMap left, VarMap right, Var branchDiscriminator)
			: base(OpType.UnionAll, outputs, left, right)
		{
			this.m_branchDiscriminator = branchDiscriminator;
		}

		// Token: 0x17000959 RID: 2393
		// (get) Token: 0x06002F5B RID: 12123 RVA: 0x00094D34 File Offset: 0x00092F34
		internal Var BranchDiscriminator
		{
			get
			{
				return this.m_branchDiscriminator;
			}
		}

		// Token: 0x06002F5C RID: 12124 RVA: 0x00094D3C File Offset: 0x00092F3C
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002F5D RID: 12125 RVA: 0x00094D46 File Offset: 0x00092F46
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000FFB RID: 4091
		private readonly Var m_branchDiscriminator;

		// Token: 0x04000FFC RID: 4092
		internal static readonly UnionAllOp Pattern = new UnionAllOp();
	}
}
