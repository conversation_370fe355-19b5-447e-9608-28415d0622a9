﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;

namespace System.Data.Entity.Core.Metadata.Edm.Provider
{
	// Token: 0x0200051B RID: 1307
	internal sealed class EdmProviderManifestFunctionBuilder
	{
		// Token: 0x06004084 RID: 16516 RVA: 0x000D82EC File Offset: 0x000D64EC
		internal EdmProviderManifestFunctionBuilder(ReadOnlyCollection<PrimitiveType> edmPrimitiveTypes)
		{
			TypeUsage[] array = new TypeUsage[edmPrimitiveTypes.Count];
			foreach (PrimitiveType primitiveType in edmPrimitiveTypes)
			{
				array[(int)primitiveType.PrimitiveTypeKind] = TypeUsage.Create(primitiveType);
			}
			this.primitiveTypes = array;
		}

		// Token: 0x06004085 RID: 16517 RVA: 0x000D8360 File Offset: 0x000D6560
		internal ReadOnlyCollection<EdmFunction> ToFunctionCollection()
		{
			return new ReadOnlyCollection<EdmFunction>(this.functions);
		}

		// Token: 0x06004086 RID: 16518 RVA: 0x000D8370 File Offset: 0x000D6570
		internal static void ForAllBasePrimitiveTypes(Action<PrimitiveTypeKind> forEachType)
		{
			for (int i = 0; i < 32; i++)
			{
				PrimitiveTypeKind primitiveTypeKind = (PrimitiveTypeKind)i;
				if (!Helper.IsStrongSpatialTypeKind(primitiveTypeKind))
				{
					forEachType(primitiveTypeKind);
				}
			}
		}

		// Token: 0x06004087 RID: 16519 RVA: 0x000D839C File Offset: 0x000D659C
		internal static void ForTypes(IEnumerable<PrimitiveTypeKind> typeKinds, Action<PrimitiveTypeKind> forEachType)
		{
			foreach (PrimitiveTypeKind primitiveTypeKind in typeKinds)
			{
				forEachType(primitiveTypeKind);
			}
		}

		// Token: 0x06004088 RID: 16520 RVA: 0x000D83E4 File Offset: 0x000D65E4
		internal void AddAggregate(string aggregateFunctionName, PrimitiveTypeKind collectionArgumentElementTypeKind)
		{
			this.AddAggregate(collectionArgumentElementTypeKind, aggregateFunctionName, collectionArgumentElementTypeKind);
		}

		// Token: 0x06004089 RID: 16521 RVA: 0x000D83F0 File Offset: 0x000D65F0
		internal void AddAggregate(PrimitiveTypeKind returnTypeKind, string aggregateFunctionName, PrimitiveTypeKind collectionArgumentElementTypeKind)
		{
			FunctionParameter functionParameter = this.CreateReturnParameter(returnTypeKind);
			FunctionParameter functionParameter2 = this.CreateAggregateParameter(collectionArgumentElementTypeKind);
			EdmFunction edmFunction = new EdmFunction(aggregateFunctionName, "Edm", DataSpace.CSpace, new EdmFunctionPayload
			{
				IsAggregate = new bool?(true),
				IsBuiltIn = new bool?(true),
				ReturnParameters = new FunctionParameter[] { functionParameter },
				Parameters = new FunctionParameter[] { functionParameter2 },
				IsFromProviderManifest = new bool?(true)
			});
			edmFunction.SetReadOnly();
			this.functions.Add(edmFunction);
		}

		// Token: 0x0600408A RID: 16522 RVA: 0x000D8477 File Offset: 0x000D6677
		internal void AddFunction(PrimitiveTypeKind returnType, string functionName)
		{
			this.AddFunction(returnType, functionName, new KeyValuePair<string, PrimitiveTypeKind>[0]);
		}

		// Token: 0x0600408B RID: 16523 RVA: 0x000D8487 File Offset: 0x000D6687
		internal void AddFunction(PrimitiveTypeKind returnType, string functionName, PrimitiveTypeKind argumentTypeKind, string argumentName)
		{
			this.AddFunction(returnType, functionName, new KeyValuePair<string, PrimitiveTypeKind>[]
			{
				new KeyValuePair<string, PrimitiveTypeKind>(argumentName, argumentTypeKind)
			});
		}

		// Token: 0x0600408C RID: 16524 RVA: 0x000D84A6 File Offset: 0x000D66A6
		internal void AddFunction(PrimitiveTypeKind returnType, string functionName, PrimitiveTypeKind argument1TypeKind, string argument1Name, PrimitiveTypeKind argument2TypeKind, string argument2Name)
		{
			this.AddFunction(returnType, functionName, new KeyValuePair<string, PrimitiveTypeKind>[]
			{
				new KeyValuePair<string, PrimitiveTypeKind>(argument1Name, argument1TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument2Name, argument2TypeKind)
			});
		}

		// Token: 0x0600408D RID: 16525 RVA: 0x000D84D5 File Offset: 0x000D66D5
		internal void AddFunction(PrimitiveTypeKind returnType, string functionName, PrimitiveTypeKind argument1TypeKind, string argument1Name, PrimitiveTypeKind argument2TypeKind, string argument2Name, PrimitiveTypeKind argument3TypeKind, string argument3Name)
		{
			this.AddFunction(returnType, functionName, new KeyValuePair<string, PrimitiveTypeKind>[]
			{
				new KeyValuePair<string, PrimitiveTypeKind>(argument1Name, argument1TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument2Name, argument2TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument3Name, argument3TypeKind)
			});
		}

		// Token: 0x0600408E RID: 16526 RVA: 0x000D8514 File Offset: 0x000D6714
		internal void AddFunction(PrimitiveTypeKind returnType, string functionName, PrimitiveTypeKind argument1TypeKind, string argument1Name, PrimitiveTypeKind argument2TypeKind, string argument2Name, PrimitiveTypeKind argument3TypeKind, string argument3Name, PrimitiveTypeKind argument4TypeKind, string argument4Name, PrimitiveTypeKind argument5TypeKind, string argument5Name, PrimitiveTypeKind argument6TypeKind, string argument6Name)
		{
			this.AddFunction(returnType, functionName, new KeyValuePair<string, PrimitiveTypeKind>[]
			{
				new KeyValuePair<string, PrimitiveTypeKind>(argument1Name, argument1TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument2Name, argument2TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument3Name, argument3TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument4Name, argument4TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument5Name, argument5TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument6Name, argument6TypeKind)
			});
		}

		// Token: 0x0600408F RID: 16527 RVA: 0x000D8590 File Offset: 0x000D6790
		internal void AddFunction(PrimitiveTypeKind returnType, string functionName, PrimitiveTypeKind argument1TypeKind, string argument1Name, PrimitiveTypeKind argument2TypeKind, string argument2Name, PrimitiveTypeKind argument3TypeKind, string argument3Name, PrimitiveTypeKind argument4TypeKind, string argument4Name, PrimitiveTypeKind argument5TypeKind, string argument5Name, PrimitiveTypeKind argument6TypeKind, string argument6Name, PrimitiveTypeKind argument7TypeKind, string argument7Name)
		{
			this.AddFunction(returnType, functionName, new KeyValuePair<string, PrimitiveTypeKind>[]
			{
				new KeyValuePair<string, PrimitiveTypeKind>(argument1Name, argument1TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument2Name, argument2TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument3Name, argument3TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument4Name, argument4TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument5Name, argument5TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument6Name, argument6TypeKind),
				new KeyValuePair<string, PrimitiveTypeKind>(argument7Name, argument7TypeKind)
			});
		}

		// Token: 0x06004090 RID: 16528 RVA: 0x000D861C File Offset: 0x000D681C
		private void AddFunction(PrimitiveTypeKind returnType, string functionName, KeyValuePair<string, PrimitiveTypeKind>[] parameterDefinitions)
		{
			FunctionParameter functionParameter = this.CreateReturnParameter(returnType);
			FunctionParameter[] array = parameterDefinitions.Select((KeyValuePair<string, PrimitiveTypeKind> paramDef) => this.CreateParameter(paramDef.Value, paramDef.Key)).ToArray<FunctionParameter>();
			EdmFunction edmFunction = new EdmFunction(functionName, "Edm", DataSpace.CSpace, new EdmFunctionPayload
			{
				IsBuiltIn = new bool?(true),
				ReturnParameters = new FunctionParameter[] { functionParameter },
				Parameters = array,
				IsFromProviderManifest = new bool?(true)
			});
			edmFunction.SetReadOnly();
			this.functions.Add(edmFunction);
		}

		// Token: 0x06004091 RID: 16529 RVA: 0x000D869E File Offset: 0x000D689E
		private FunctionParameter CreateParameter(PrimitiveTypeKind primitiveParameterType, string parameterName)
		{
			return new FunctionParameter(parameterName, this.primitiveTypes[(int)primitiveParameterType], ParameterMode.In);
		}

		// Token: 0x06004092 RID: 16530 RVA: 0x000D86AF File Offset: 0x000D68AF
		private FunctionParameter CreateAggregateParameter(PrimitiveTypeKind collectionParameterTypeElementTypeKind)
		{
			return new FunctionParameter("collection", TypeUsage.Create(this.primitiveTypes[(int)collectionParameterTypeElementTypeKind].EdmType.GetCollectionType()), ParameterMode.In);
		}

		// Token: 0x06004093 RID: 16531 RVA: 0x000D86D3 File Offset: 0x000D68D3
		private FunctionParameter CreateReturnParameter(PrimitiveTypeKind primitiveReturnType)
		{
			return new FunctionParameter("ReturnType", this.primitiveTypes[(int)primitiveReturnType], ParameterMode.ReturnValue);
		}

		// Token: 0x04001677 RID: 5751
		private readonly List<EdmFunction> functions = new List<EdmFunction>();

		// Token: 0x04001678 RID: 5752
		private readonly TypeUsage[] primitiveTypes;
	}
}
