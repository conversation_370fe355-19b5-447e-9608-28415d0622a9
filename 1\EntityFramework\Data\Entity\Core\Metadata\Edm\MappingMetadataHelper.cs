﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping;
using System.Data.Entity.Resources;
using System.Linq;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004CE RID: 1230
	internal static class MappingMetadataHelper
	{
		// Token: 0x06003D0E RID: 15630 RVA: 0x000C9300 File Offset: 0x000C7500
		internal static IEnumerable<TypeMapping> GetMappingsForEntitySetAndType(StorageMappingItemCollection mappingCollection, EntityContainer container, EntitySetBase entitySet, EntityTypeBase entityType)
		{
			EntitySetBaseMapping setMapping = MappingMetadataHelper.GetEntityContainerMap(mappingCollection, container).GetSetMapping(entitySet.Name);
			if (setMapping != null)
			{
				IEnumerable<TypeMapping> typeMappings = setMapping.TypeMappings;
				Func<TypeMapping, bool> <>9__0;
				Func<TypeMapping, bool> func;
				if ((func = <>9__0) == null)
				{
					func = (<>9__0 = (TypeMapping map) => map.Types.Union(map.IsOfTypes).Contains(entityType));
				}
				foreach (TypeMapping typeMapping in typeMappings.Where(func))
				{
					yield return typeMapping;
				}
				IEnumerator<TypeMapping> enumerator = null;
			}
			yield break;
			yield break;
		}

		// Token: 0x06003D0F RID: 15631 RVA: 0x000C9328 File Offset: 0x000C7528
		internal static IEnumerable<TypeMapping> GetMappingsForEntitySetAndSuperTypes(StorageMappingItemCollection mappingCollection, EntityContainer container, EntitySetBase entitySet, EntityTypeBase childEntityType)
		{
			return MetadataHelper.GetTypeAndParentTypesOf(childEntityType, true).SelectMany(delegate(EdmType edmType)
			{
				EntityTypeBase entityTypeBase = edmType as EntityTypeBase;
				if (!edmType.EdmEquals(childEntityType))
				{
					return MappingMetadataHelper.GetIsTypeOfMappingsForEntitySetAndType(mappingCollection, container, entitySet, entityTypeBase, childEntityType);
				}
				return MappingMetadataHelper.GetMappingsForEntitySetAndType(mappingCollection, container, entitySet, entityTypeBase);
			}).ToList<TypeMapping>();
		}

		// Token: 0x06003D10 RID: 15632 RVA: 0x000C9379 File Offset: 0x000C7579
		private static IEnumerable<TypeMapping> GetIsTypeOfMappingsForEntitySetAndType(StorageMappingItemCollection mappingCollection, EntityContainer container, EntitySetBase entitySet, EntityTypeBase entityType, EntityTypeBase childEntityType)
		{
			Func<EntityTypeBase, bool> <>9__0;
			foreach (TypeMapping typeMapping in MappingMetadataHelper.GetMappingsForEntitySetAndType(mappingCollection, container, entitySet, entityType))
			{
				IEnumerable<EntityTypeBase> isOfTypes = typeMapping.IsOfTypes;
				Func<EntityTypeBase, bool> func;
				if ((func = <>9__0) == null)
				{
					func = (<>9__0 = (EntityTypeBase parentType) => parentType.IsAssignableFrom(childEntityType));
				}
				if (isOfTypes.Any(func) || typeMapping.Types.Contains(childEntityType))
				{
					yield return typeMapping;
				}
			}
			IEnumerator<TypeMapping> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x06003D11 RID: 15633 RVA: 0x000C93A6 File Offset: 0x000C75A6
		internal static IEnumerable<EntityTypeModificationFunctionMapping> GetModificationFunctionMappingsForEntitySetAndType(StorageMappingItemCollection mappingCollection, EntityContainer container, EntitySetBase entitySet, EntityTypeBase entityType)
		{
			EntitySetMapping entitySetMapping = MappingMetadataHelper.GetEntityContainerMap(mappingCollection, container).GetSetMapping(entitySet.Name) as EntitySetMapping;
			if (entitySetMapping != null && entitySetMapping != null)
			{
				IEnumerable<EntityTypeModificationFunctionMapping> modificationFunctionMappings = entitySetMapping.ModificationFunctionMappings;
				Func<EntityTypeModificationFunctionMapping, bool> <>9__0;
				Func<EntityTypeModificationFunctionMapping, bool> func;
				if ((func = <>9__0) == null)
				{
					func = (<>9__0 = (EntityTypeModificationFunctionMapping functionMap) => functionMap.EntityType.Equals(entityType));
				}
				foreach (EntityTypeModificationFunctionMapping entityTypeModificationFunctionMapping in modificationFunctionMappings.Where(func))
				{
					yield return entityTypeModificationFunctionMapping;
				}
				IEnumerator<EntityTypeModificationFunctionMapping> enumerator = null;
			}
			yield break;
			yield break;
		}

		// Token: 0x06003D12 RID: 15634 RVA: 0x000C93CC File Offset: 0x000C75CC
		internal static EntityContainerMapping GetEntityContainerMap(StorageMappingItemCollection mappingCollection, EntityContainer entityContainer)
		{
			ReadOnlyCollection<EntityContainerMapping> items = mappingCollection.GetItems<EntityContainerMapping>();
			EntityContainerMapping entityContainerMapping = null;
			foreach (EntityContainerMapping entityContainerMapping2 in items)
			{
				if (entityContainer.Equals(entityContainerMapping2.EdmEntityContainer) || entityContainer.Equals(entityContainerMapping2.StorageEntityContainer))
				{
					entityContainerMapping = entityContainerMapping2;
					break;
				}
			}
			if (entityContainerMapping == null)
			{
				throw new MappingException(Strings.Mapping_NotFound_EntityContainer(entityContainer.Name));
			}
			return entityContainerMapping;
		}
	}
}
