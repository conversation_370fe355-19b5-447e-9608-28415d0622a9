﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects.Internal;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Runtime.Serialization;

namespace System.Data.Entity.Core.Objects.DataClasses
{
	// Token: 0x02000483 RID: 1155
	[Serializable]
	public class RelationshipManager
	{
		// Token: 0x060038F9 RID: 14585 RVA: 0x000BADE2 File Offset: 0x000B8FE2
		private RelationshipManager()
		{
			this._entityWrapperFactory = new EntityWrapperFactory();
			this._expensiveLoader = new ExpensiveOSpaceLoader();
		}

		// Token: 0x060038FA RID: 14586 RVA: 0x000BAE00 File Offset: 0x000B9000
		internal RelationshipManager(ExpensiveOSpaceLoader expensiveLoader)
		{
			this._entityWrapperFactory = new EntityWrapperFactory();
			this._expensiveLoader = expensiveLoader ?? new ExpensiveOSpaceLoader();
		}

		// Token: 0x060038FB RID: 14587 RVA: 0x000BAE23 File Offset: 0x000B9023
		internal void SetExpensiveLoader(ExpensiveOSpaceLoader loader)
		{
			this._expensiveLoader = loader;
		}

		// Token: 0x17000AE8 RID: 2792
		// (get) Token: 0x060038FC RID: 14588 RVA: 0x000BAE2C File Offset: 0x000B902C
		internal IEnumerable<RelatedEnd> Relationships
		{
			get
			{
				this.EnsureRelationshipsInitialized();
				return this._relationships.ToArray();
			}
		}

		// Token: 0x060038FD RID: 14589 RVA: 0x000BAE3F File Offset: 0x000B903F
		private void EnsureRelationshipsInitialized()
		{
			if (this._relationships == null)
			{
				this._relationships = new List<RelatedEnd>();
			}
		}

		// Token: 0x17000AE9 RID: 2793
		// (get) Token: 0x060038FE RID: 14590 RVA: 0x000BAE54 File Offset: 0x000B9054
		// (set) Token: 0x060038FF RID: 14591 RVA: 0x000BAE5C File Offset: 0x000B905C
		internal bool NodeVisited
		{
			get
			{
				return this._nodeVisited;
			}
			set
			{
				this._nodeVisited = value;
			}
		}

		// Token: 0x17000AEA RID: 2794
		// (get) Token: 0x06003900 RID: 14592 RVA: 0x000BAE65 File Offset: 0x000B9065
		internal IEntityWrapper WrappedOwner
		{
			get
			{
				if (this._wrappedOwner == null)
				{
					this._wrappedOwner = EntityWrapperFactory.CreateNewWrapper(this._owner, null);
				}
				return this._wrappedOwner;
			}
		}

		// Token: 0x17000AEB RID: 2795
		// (get) Token: 0x06003901 RID: 14593 RVA: 0x000BAE87 File Offset: 0x000B9087
		internal virtual EntityWrapperFactory EntityWrapperFactory
		{
			get
			{
				return this._entityWrapperFactory;
			}
		}

		// Token: 0x06003902 RID: 14594 RVA: 0x000BAE8F File Offset: 0x000B908F
		public static RelationshipManager Create(IEntityWithRelationships owner)
		{
			Check.NotNull<IEntityWithRelationships>(owner, "owner");
			return new RelationshipManager
			{
				_owner = owner
			};
		}

		// Token: 0x06003903 RID: 14595 RVA: 0x000BAEA9 File Offset: 0x000B90A9
		internal static RelationshipManager Create()
		{
			return new RelationshipManager();
		}

		// Token: 0x06003904 RID: 14596 RVA: 0x000BAEB0 File Offset: 0x000B90B0
		internal void SetWrappedOwner(IEntityWrapper wrappedOwner, object expectedOwner)
		{
			this._wrappedOwner = wrappedOwner;
			if (this._owner != null && expectedOwner != this._owner)
			{
				throw new InvalidOperationException(Strings.RelationshipManager_InvalidRelationshipManagerOwner);
			}
			if (this._relationships != null)
			{
				foreach (RelatedEnd relatedEnd in this._relationships)
				{
					relatedEnd.SetWrappedOwner(wrappedOwner);
				}
			}
		}

		// Token: 0x06003905 RID: 14597 RVA: 0x000BAF2C File Offset: 0x000B912C
		internal EntityCollection<TTargetEntity> GetRelatedCollection<TSourceEntity, TTargetEntity>(AssociationEndMember sourceMember, AssociationEndMember targetMember, NavigationPropertyAccessor sourceAccessor, NavigationPropertyAccessor targetAccessor, RelatedEnd existingRelatedEnd) where TSourceEntity : class where TTargetEntity : class
		{
			string fullName = sourceMember.DeclaringType.FullName;
			string name = targetMember.Name;
			RelationshipMultiplicity relationshipMultiplicity = sourceMember.RelationshipMultiplicity;
			RelatedEnd relatedEnd;
			this.TryGetCachedRelatedEnd(fullName, name, out relatedEnd);
			EntityCollection<TTargetEntity> entityCollection = relatedEnd as EntityCollection<TTargetEntity>;
			if (existingRelatedEnd != null)
			{
				if (relatedEnd != null)
				{
					this._relationships.Remove(relatedEnd);
				}
				RelationshipNavigation relationshipNavigation = new RelationshipNavigation((AssociationType)sourceMember.DeclaringType, sourceMember.Name, targetMember.Name, sourceAccessor, targetAccessor);
				EntityCollection<TTargetEntity> entityCollection2 = this.CreateRelatedEnd<TSourceEntity, TTargetEntity>(relationshipNavigation, relationshipMultiplicity, RelationshipMultiplicity.Many, existingRelatedEnd) as EntityCollection<TTargetEntity>;
				if (entityCollection2 != null)
				{
					bool flag = true;
					try
					{
						RelationshipManager.RemergeCollections<TTargetEntity>(entityCollection, entityCollection2);
						flag = false;
					}
					finally
					{
						if (flag && relatedEnd != null)
						{
							this._relationships.Remove(entityCollection2);
							this._relationships.Add(relatedEnd);
						}
					}
				}
				return entityCollection2;
			}
			if (relatedEnd != null)
			{
				return entityCollection;
			}
			RelationshipNavigation relationshipNavigation2 = new RelationshipNavigation((AssociationType)sourceMember.DeclaringType, sourceMember.Name, targetMember.Name, sourceAccessor, targetAccessor);
			return this.CreateRelatedEnd<TSourceEntity, TTargetEntity>(relationshipNavigation2, relationshipMultiplicity, RelationshipMultiplicity.Many, existingRelatedEnd) as EntityCollection<TTargetEntity>;
		}

		// Token: 0x06003906 RID: 14598 RVA: 0x000BB030 File Offset: 0x000B9230
		private static void RemergeCollections<TTargetEntity>(EntityCollection<TTargetEntity> previousCollection, EntityCollection<TTargetEntity> collection) where TTargetEntity : class
		{
			int num = 0;
			List<IEntityWrapper> list = new List<IEntityWrapper>(collection.CountInternal);
			foreach (IEntityWrapper entityWrapper in collection.GetWrappedEntities())
			{
				list.Add(entityWrapper);
			}
			foreach (IEntityWrapper entityWrapper2 in list)
			{
				bool flag = true;
				if (previousCollection != null && previousCollection.ContainsEntity(entityWrapper2))
				{
					num++;
					flag = false;
				}
				if (flag)
				{
					collection.Remove(entityWrapper2, false);
					collection.Add(entityWrapper2);
				}
			}
			if (previousCollection != null && num != previousCollection.CountInternal)
			{
				throw new InvalidOperationException(Strings.Collections_UnableToMergeCollections);
			}
		}

		// Token: 0x06003907 RID: 14599 RVA: 0x000BB108 File Offset: 0x000B9308
		internal EntityReference<TTargetEntity> GetRelatedReference<TSourceEntity, TTargetEntity>(AssociationEndMember sourceMember, AssociationEndMember targetMember, NavigationPropertyAccessor sourceAccessor, NavigationPropertyAccessor targetAccessor, RelatedEnd existingRelatedEnd) where TSourceEntity : class where TTargetEntity : class
		{
			string fullName = sourceMember.DeclaringType.FullName;
			string name = targetMember.Name;
			RelationshipMultiplicity relationshipMultiplicity = sourceMember.RelationshipMultiplicity;
			RelatedEnd relatedEnd;
			if (this.TryGetCachedRelatedEnd(fullName, name, out relatedEnd))
			{
				return relatedEnd as EntityReference<TTargetEntity>;
			}
			RelationshipNavigation relationshipNavigation = new RelationshipNavigation((AssociationType)sourceMember.DeclaringType, sourceMember.Name, targetMember.Name, sourceAccessor, targetAccessor);
			return this.CreateRelatedEnd<TSourceEntity, TTargetEntity>(relationshipNavigation, relationshipMultiplicity, RelationshipMultiplicity.One, existingRelatedEnd) as EntityReference<TTargetEntity>;
		}

		// Token: 0x06003908 RID: 14600 RVA: 0x000BB174 File Offset: 0x000B9374
		internal RelatedEnd GetRelatedEnd(string navigationProperty, bool throwArgumentException = false)
		{
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			EntityType item = wrappedOwner.Context.MetadataWorkspace.GetItem<EntityType>(wrappedOwner.IdentityType.FullNameWithNesting(), DataSpace.OSpace);
			EdmMember edmMember;
			if (!wrappedOwner.Context.Perspective.TryGetMember(item, navigationProperty, false, out edmMember) || !(edmMember is NavigationProperty))
			{
				string text = Strings.RelationshipManager_NavigationPropertyNotFound(navigationProperty);
				throw throwArgumentException ? new ArgumentException(text) : new InvalidOperationException(text);
			}
			NavigationProperty navigationProperty2 = (NavigationProperty)edmMember;
			return this.GetRelatedEndInternal(navigationProperty2.RelationshipType.FullName, navigationProperty2.ToEndMember.Name);
		}

		// Token: 0x06003909 RID: 14601 RVA: 0x000BB203 File Offset: 0x000B9403
		public IRelatedEnd GetRelatedEnd(string relationshipName, string targetRoleName)
		{
			return this.GetRelatedEndInternal(this.PrependNamespaceToRelationshipName(relationshipName), targetRoleName);
		}

		// Token: 0x0600390A RID: 14602 RVA: 0x000BB214 File Offset: 0x000B9414
		internal RelatedEnd GetRelatedEndInternal(string relationshipName, string targetRoleName)
		{
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			if (wrappedOwner.Context == null && wrappedOwner.RequiresRelationshipChangeTracking)
			{
				throw new InvalidOperationException(Strings.RelationshipManager_CannotGetRelatEndForDetachedPocoEntity);
			}
			AssociationType relationshipType = this.GetRelationshipType(relationshipName);
			return this.GetRelatedEndInternal(relationshipName, targetRoleName, null, relationshipType);
		}

		// Token: 0x0600390B RID: 14603 RVA: 0x000BB258 File Offset: 0x000B9458
		private RelatedEnd GetRelatedEndInternal(string relationshipName, string targetRoleName, RelatedEnd existingRelatedEnd, AssociationType relationship)
		{
			AssociationEndMember associationEndMember;
			AssociationEndMember associationEndMember2;
			RelationshipManager.GetAssociationEnds(relationship, targetRoleName, out associationEndMember, out associationEndMember2);
			Type clrType = MetadataHelper.GetEntityTypeForEnd(associationEndMember).ClrType;
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			if (!clrType.IsAssignableFrom(wrappedOwner.IdentityType))
			{
				throw new InvalidOperationException(Strings.RelationshipManager_OwnerIsNotSourceType(wrappedOwner.IdentityType.FullName, clrType.FullName, associationEndMember.Name, relationshipName));
			}
			if (!this.VerifyRelationship(relationship, associationEndMember.Name))
			{
				return null;
			}
			return DelegateFactory.GetRelatedEnd(this, associationEndMember, associationEndMember2, existingRelatedEnd);
		}

		// Token: 0x0600390C RID: 14604 RVA: 0x000BB2D0 File Offset: 0x000B94D0
		internal RelatedEnd GetRelatedEndInternal(AssociationType csAssociationType, AssociationEndMember csTargetEnd)
		{
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			if (wrappedOwner.Context == null && wrappedOwner.RequiresRelationshipChangeTracking)
			{
				throw new InvalidOperationException(Strings.RelationshipManager_CannotGetRelatEndForDetachedPocoEntity);
			}
			AssociationType relationshipType = this.GetRelationshipType(csAssociationType);
			AssociationEndMember associationEndMember;
			AssociationEndMember associationEndMember2;
			RelationshipManager.GetAssociationEnds(relationshipType, csTargetEnd.Name, out associationEndMember, out associationEndMember2);
			Type clrType = MetadataHelper.GetEntityTypeForEnd(associationEndMember).ClrType;
			if (!clrType.IsAssignableFrom(wrappedOwner.IdentityType))
			{
				throw new InvalidOperationException(Strings.RelationshipManager_OwnerIsNotSourceType(wrappedOwner.IdentityType.FullName, clrType.FullName, associationEndMember.Name, csAssociationType.FullName));
			}
			if (!this.VerifyRelationship(relationshipType, csAssociationType, associationEndMember.Name))
			{
				return null;
			}
			return DelegateFactory.GetRelatedEnd(this, associationEndMember, associationEndMember2, null);
		}

		// Token: 0x0600390D RID: 14605 RVA: 0x000BB378 File Offset: 0x000B9578
		private static void GetAssociationEnds(AssociationType associationType, string targetRoleName, out AssociationEndMember sourceEnd, out AssociationEndMember targetEnd)
		{
			targetEnd = associationType.TargetEnd;
			if (targetEnd.Identity != targetRoleName)
			{
				sourceEnd = targetEnd;
				targetEnd = associationType.SourceEnd;
				if (targetEnd.Identity != targetRoleName)
				{
					throw new InvalidOperationException(Strings.RelationshipManager_InvalidTargetRole(associationType.FullName, targetRoleName));
				}
			}
			else
			{
				sourceEnd = associationType.SourceEnd;
			}
		}

		// Token: 0x0600390E RID: 14606 RVA: 0x000BB3D4 File Offset: 0x000B95D4
		[Browsable(false)]
		[EditorBrowsable(EditorBrowsableState.Never)]
		public void InitializeRelatedReference<TTargetEntity>(string relationshipName, string targetRoleName, EntityReference<TTargetEntity> entityReference) where TTargetEntity : class
		{
			Check.NotNull<string>(relationshipName, "relationshipName");
			Check.NotNull<string>(targetRoleName, "targetRoleName");
			Check.NotNull<EntityReference<TTargetEntity>>(entityReference, "entityReference");
			if (entityReference.WrappedOwner.Entity != null)
			{
				throw new InvalidOperationException(Strings.RelationshipManager_ReferenceAlreadyInitialized(Strings.RelationshipManager_InitializeIsForDeserialization));
			}
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			if (wrappedOwner.Context != null && wrappedOwner.MergeOption != MergeOption.NoTracking)
			{
				throw new InvalidOperationException(Strings.RelationshipManager_RelationshipManagerAttached(Strings.RelationshipManager_InitializeIsForDeserialization));
			}
			relationshipName = this.PrependNamespaceToRelationshipName(relationshipName);
			AssociationType relationshipType = this.GetRelationshipType(relationshipName);
			RelatedEnd relatedEnd;
			if (this.TryGetCachedRelatedEnd(relationshipName, targetRoleName, out relatedEnd))
			{
				if (!relatedEnd.IsEmpty())
				{
					entityReference.InitializeWithValue(relatedEnd);
				}
				this._relationships.Remove(relatedEnd);
			}
			if (!(this.GetRelatedEndInternal(relationshipName, targetRoleName, entityReference, relationshipType) is EntityReference<TTargetEntity>))
			{
				throw new InvalidOperationException(Strings.EntityReference_ExpectedReferenceGotCollection(typeof(TTargetEntity).Name, targetRoleName, relationshipName));
			}
		}

		// Token: 0x0600390F RID: 14607 RVA: 0x000BB4B0 File Offset: 0x000B96B0
		[Browsable(false)]
		[EditorBrowsable(EditorBrowsableState.Never)]
		public void InitializeRelatedCollection<TTargetEntity>(string relationshipName, string targetRoleName, EntityCollection<TTargetEntity> entityCollection) where TTargetEntity : class
		{
			Check.NotNull<string>(relationshipName, "relationshipName");
			Check.NotNull<string>(targetRoleName, "targetRoleName");
			Check.NotNull<EntityCollection<TTargetEntity>>(entityCollection, "entityCollection");
			if (entityCollection.WrappedOwner.Entity != null)
			{
				throw new InvalidOperationException(Strings.RelationshipManager_CollectionAlreadyInitialized(Strings.RelationshipManager_CollectionInitializeIsForDeserialization));
			}
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			if (wrappedOwner.Context != null && wrappedOwner.MergeOption != MergeOption.NoTracking)
			{
				throw new InvalidOperationException(Strings.RelationshipManager_CollectionRelationshipManagerAttached(Strings.RelationshipManager_CollectionInitializeIsForDeserialization));
			}
			relationshipName = this.PrependNamespaceToRelationshipName(relationshipName);
			AssociationType relationshipType = this.GetRelationshipType(relationshipName);
			if (!(this.GetRelatedEndInternal(relationshipName, targetRoleName, entityCollection, relationshipType) is EntityCollection<TTargetEntity>))
			{
				throw new InvalidOperationException(Strings.Collections_ExpectedCollectionGotReference(typeof(TTargetEntity).Name, targetRoleName, relationshipName));
			}
		}

		// Token: 0x06003910 RID: 14608 RVA: 0x000BB564 File Offset: 0x000B9764
		internal string PrependNamespaceToRelationshipName(string relationshipName)
		{
			if (!relationshipName.Contains("."))
			{
				AssociationType associationType;
				if (EntityProxyFactory.TryGetAssociationTypeFromProxyInfo(this.WrappedOwner, relationshipName, out associationType))
				{
					return associationType.FullName;
				}
				if (this._relationships != null)
				{
					string text = this._relationships.Select((RelatedEnd r) => r.RelationshipName).FirstOrDefault((string n) => n.Substring(n.LastIndexOf('.') + 1) == relationshipName);
					if (text != null)
					{
						return text;
					}
				}
				string text2 = this.WrappedOwner.IdentityType.FullNameWithNesting();
				ObjectItemCollection objectItemCollection = RelationshipManager.GetObjectItemCollection(this.WrappedOwner);
				EdmType edmType = null;
				if (objectItemCollection != null)
				{
					objectItemCollection.TryGetItem<EdmType>(text2, out edmType);
				}
				else
				{
					Dictionary<string, EdmType> dictionary = this._expensiveLoader.LoadTypesExpensiveWay(this.WrappedOwner.IdentityType.Assembly());
					if (dictionary != null)
					{
						dictionary.TryGetValue(text2, out edmType);
					}
				}
				ClrEntityType clrEntityType = edmType as ClrEntityType;
				if (clrEntityType != null)
				{
					return clrEntityType.CSpaceNamespaceName + "." + relationshipName;
				}
			}
			return relationshipName;
		}

		// Token: 0x06003911 RID: 14609 RVA: 0x000BB681 File Offset: 0x000B9881
		private static ObjectItemCollection GetObjectItemCollection(IEntityWrapper wrappedOwner)
		{
			if (wrappedOwner.Context != null)
			{
				return (ObjectItemCollection)wrappedOwner.Context.MetadataWorkspace.GetItemCollection(DataSpace.OSpace);
			}
			return null;
		}

		// Token: 0x06003912 RID: 14610 RVA: 0x000BB6A4 File Offset: 0x000B98A4
		private bool TryGetOwnerEntityType(out EntityType entityType)
		{
			DefaultObjectMappingItemCollection defaultObjectMappingItemCollection;
			MappingBase mappingBase;
			if (RelationshipManager.TryGetObjectMappingItemCollection(this.WrappedOwner, out defaultObjectMappingItemCollection) && defaultObjectMappingItemCollection.TryGetMap(this.WrappedOwner.IdentityType.FullNameWithNesting(), DataSpace.OSpace, out mappingBase))
			{
				ObjectTypeMapping objectTypeMapping = (ObjectTypeMapping)mappingBase;
				if (Helper.IsEntityType(objectTypeMapping.EdmType))
				{
					entityType = (EntityType)objectTypeMapping.EdmType;
					return true;
				}
			}
			entityType = null;
			return false;
		}

		// Token: 0x06003913 RID: 14611 RVA: 0x000BB702 File Offset: 0x000B9902
		private static bool TryGetObjectMappingItemCollection(IEntityWrapper wrappedOwner, out DefaultObjectMappingItemCollection collection)
		{
			if (wrappedOwner.Context != null && wrappedOwner.Context.MetadataWorkspace != null)
			{
				collection = (DefaultObjectMappingItemCollection)wrappedOwner.Context.MetadataWorkspace.GetItemCollection(DataSpace.OCSpace);
				return collection != null;
			}
			collection = null;
			return false;
		}

		// Token: 0x06003914 RID: 14612 RVA: 0x000BB73C File Offset: 0x000B993C
		internal AssociationType GetRelationshipType(AssociationType csAssociationType)
		{
			MetadataWorkspace metadataWorkspace = this.WrappedOwner.Context.MetadataWorkspace;
			if (metadataWorkspace != null)
			{
				return metadataWorkspace.MetadataOptimization.GetOSpaceAssociationType(csAssociationType, () => this.GetRelationshipType(csAssociationType.FullName));
			}
			return this.GetRelationshipType(csAssociationType.FullName);
		}

		// Token: 0x06003915 RID: 14613 RVA: 0x000BB7A0 File Offset: 0x000B99A0
		internal AssociationType GetRelationshipType(string relationshipName)
		{
			AssociationType associationType = null;
			ObjectItemCollection objectItemCollection = RelationshipManager.GetObjectItemCollection(this.WrappedOwner);
			if (objectItemCollection != null)
			{
				associationType = objectItemCollection.GetRelationshipType(relationshipName);
			}
			if (associationType == null)
			{
				EntityProxyFactory.TryGetAssociationTypeFromProxyInfo(this.WrappedOwner, relationshipName, out associationType);
			}
			if (associationType == null && this._relationships != null)
			{
				associationType = (from e in this._relationships
					where e.RelationshipName == relationshipName
					select e.RelationMetadata).OfType<AssociationType>().FirstOrDefault<AssociationType>();
			}
			if (associationType == null)
			{
				associationType = this._expensiveLoader.GetRelationshipTypeExpensiveWay(this.WrappedOwner.IdentityType, relationshipName);
			}
			if (associationType == null)
			{
				throw RelationshipManager.UnableToGetMetadata(this.WrappedOwner, relationshipName);
			}
			return associationType;
		}

		// Token: 0x06003916 RID: 14614 RVA: 0x000BB878 File Offset: 0x000B9A78
		internal static Exception UnableToGetMetadata(IEntityWrapper wrappedOwner, string relationshipName)
		{
			ArgumentException ex = new ArgumentException(Strings.RelationshipManager_UnableToFindRelationshipTypeInMetadata(relationshipName), "relationshipName");
			if (EntityProxyFactory.IsProxyType(wrappedOwner.Entity.GetType()))
			{
				return new InvalidOperationException(Strings.EntityProxyTypeInfo_ProxyMetadataIsUnavailable(wrappedOwner.IdentityType.FullName), ex);
			}
			return ex;
		}

		// Token: 0x06003917 RID: 14615 RVA: 0x000BB8C0 File Offset: 0x000B9AC0
		private static IEnumerable<AssociationEndMember> GetAllTargetEnds(EntityType ownerEntityType, EntitySet ownerEntitySet)
		{
			foreach (AssociationSet assocSet in ownerEntitySet.AssociationSets)
			{
				if (assocSet.ElementType.AssociationEndMembers[1].GetEntityType().IsAssignableFrom(ownerEntityType))
				{
					yield return assocSet.ElementType.AssociationEndMembers[0];
				}
				if (assocSet.ElementType.AssociationEndMembers[0].GetEntityType().IsAssignableFrom(ownerEntityType))
				{
					yield return assocSet.ElementType.AssociationEndMembers[1];
				}
				assocSet = null;
			}
			IEnumerator<AssociationSet> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x06003918 RID: 14616 RVA: 0x000BB8D7 File Offset: 0x000B9AD7
		private IEnumerable<AssociationEndMember> GetAllTargetEnds(Type entityClrType)
		{
			ObjectItemCollection objectItemCollection = RelationshipManager.GetObjectItemCollection(this.WrappedOwner);
			IEnumerable<AssociationType> enumerable;
			if (objectItemCollection != null)
			{
				enumerable = objectItemCollection.GetItems<AssociationType>();
			}
			else
			{
				enumerable = EntityProxyFactory.TryGetAllAssociationTypesFromProxyInfo(this.WrappedOwner);
				if (enumerable == null)
				{
					enumerable = this._expensiveLoader.GetAllRelationshipTypesExpensiveWay(entityClrType.Assembly());
				}
			}
			foreach (AssociationType association in enumerable)
			{
				RefType refType = association.AssociationEndMembers[0].TypeUsage.EdmType as RefType;
				if (refType != null && refType.ElementType.ClrType.IsAssignableFrom(entityClrType))
				{
					yield return association.AssociationEndMembers[1];
				}
				refType = association.AssociationEndMembers[1].TypeUsage.EdmType as RefType;
				if (refType != null && refType.ElementType.ClrType.IsAssignableFrom(entityClrType))
				{
					yield return association.AssociationEndMembers[0];
				}
				association = null;
			}
			IEnumerator<AssociationType> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x06003919 RID: 14617 RVA: 0x000BB8F0 File Offset: 0x000B9AF0
		private bool VerifyRelationship(AssociationType relationship, string sourceEndName)
		{
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			if (wrappedOwner.Context == null)
			{
				return true;
			}
			EntityKey entityKey = wrappedOwner.EntityKey;
			return entityKey == null || RelationshipManager.VerifyRelationship(wrappedOwner, entityKey, relationship, sourceEndName);
		}

		// Token: 0x0600391A RID: 14618 RVA: 0x000BB92C File Offset: 0x000B9B2C
		private bool VerifyRelationship(AssociationType osAssociationType, AssociationType csAssociationType, string sourceEndName)
		{
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			if (wrappedOwner.Context == null)
			{
				return true;
			}
			EntityKey entityKey = wrappedOwner.EntityKey;
			if (entityKey == null)
			{
				return true;
			}
			if (osAssociationType.Index < 0)
			{
				return RelationshipManager.VerifyRelationship(wrappedOwner, entityKey, osAssociationType, sourceEndName);
			}
			EntitySet entitySet;
			if (wrappedOwner.Context.MetadataWorkspace.MetadataOptimization.FindCSpaceAssociationSet(csAssociationType, sourceEndName, entityKey.EntitySetName, entityKey.EntityContainerName, out entitySet) == null)
			{
				throw Error.Collections_NoRelationshipSetMatched(osAssociationType.FullName);
			}
			return true;
		}

		// Token: 0x0600391B RID: 14619 RVA: 0x000BB9A8 File Offset: 0x000B9BA8
		private static bool VerifyRelationship(IEntityWrapper wrappedOwner, EntityKey ownerKey, AssociationType relationship, string sourceEndName)
		{
			TypeUsage typeUsage;
			EntitySet entitySet;
			if (wrappedOwner.Context.Perspective.TryGetTypeByName(relationship.FullName, false, out typeUsage) && wrappedOwner.Context.MetadataWorkspace.MetadataOptimization.FindCSpaceAssociationSet((AssociationType)typeUsage.EdmType, sourceEndName, ownerKey.EntitySetName, ownerKey.EntityContainerName, out entitySet) == null)
			{
				throw Error.Collections_NoRelationshipSetMatched(relationship.FullName);
			}
			return true;
		}

		// Token: 0x0600391C RID: 14620 RVA: 0x000BBA0E File Offset: 0x000B9C0E
		public EntityCollection<TTargetEntity> GetRelatedCollection<TTargetEntity>(string relationshipName, string targetRoleName) where TTargetEntity : class
		{
			EntityCollection<TTargetEntity> entityCollection = this.GetRelatedEndInternal(this.PrependNamespaceToRelationshipName(relationshipName), targetRoleName) as EntityCollection<TTargetEntity>;
			if (entityCollection == null)
			{
				throw new InvalidOperationException(Strings.Collections_ExpectedCollectionGotReference(typeof(TTargetEntity).Name, targetRoleName, relationshipName));
			}
			return entityCollection;
		}

		// Token: 0x0600391D RID: 14621 RVA: 0x000BBA42 File Offset: 0x000B9C42
		public EntityReference<TTargetEntity> GetRelatedReference<TTargetEntity>(string relationshipName, string targetRoleName) where TTargetEntity : class
		{
			EntityReference<TTargetEntity> entityReference = this.GetRelatedEndInternal(this.PrependNamespaceToRelationshipName(relationshipName), targetRoleName) as EntityReference<TTargetEntity>;
			if (entityReference == null)
			{
				throw new InvalidOperationException(Strings.EntityReference_ExpectedReferenceGotCollection(typeof(TTargetEntity).Name, targetRoleName, relationshipName));
			}
			return entityReference;
		}

		// Token: 0x0600391E RID: 14622 RVA: 0x000BBA78 File Offset: 0x000B9C78
		internal RelatedEnd GetRelatedEnd(RelationshipNavigation navigation, IRelationshipFixer relationshipFixer)
		{
			RelatedEnd relatedEnd;
			if (this.TryGetCachedRelatedEnd(navigation.RelationshipName, navigation.To, out relatedEnd))
			{
				return relatedEnd;
			}
			relatedEnd = relationshipFixer.CreateSourceEnd(navigation, this);
			return relatedEnd;
		}

		// Token: 0x0600391F RID: 14623 RVA: 0x000BBAA8 File Offset: 0x000B9CA8
		internal RelatedEnd CreateRelatedEnd<TSourceEntity, TTargetEntity>(RelationshipNavigation navigation, RelationshipMultiplicity sourceRoleMultiplicity, RelationshipMultiplicity targetRoleMultiplicity, RelatedEnd existingRelatedEnd) where TSourceEntity : class where TTargetEntity : class
		{
			IRelationshipFixer relationshipFixer = new RelationshipFixer<TSourceEntity, TTargetEntity>(sourceRoleMultiplicity, targetRoleMultiplicity);
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			RelatedEnd relatedEnd;
			if (targetRoleMultiplicity > RelationshipMultiplicity.One)
			{
				if (targetRoleMultiplicity != RelationshipMultiplicity.Many)
				{
					Type typeFromHandle = typeof(RelationshipMultiplicity);
					string name = typeFromHandle.Name;
					object name2 = typeFromHandle.Name;
					int num = (int)targetRoleMultiplicity;
					throw new ArgumentOutOfRangeException(name, Strings.ADP_InvalidEnumerationValue(name2, num.ToString(CultureInfo.InvariantCulture)));
				}
				if (existingRelatedEnd != null)
				{
					existingRelatedEnd.InitializeRelatedEnd(wrappedOwner, navigation, relationshipFixer);
					relatedEnd = existingRelatedEnd;
				}
				else
				{
					relatedEnd = new EntityCollection<TTargetEntity>(wrappedOwner, navigation, relationshipFixer);
				}
			}
			else if (existingRelatedEnd != null)
			{
				existingRelatedEnd.InitializeRelatedEnd(wrappedOwner, navigation, relationshipFixer);
				relatedEnd = existingRelatedEnd;
			}
			else
			{
				relatedEnd = new EntityReference<TTargetEntity>(wrappedOwner, navigation, relationshipFixer);
			}
			if (wrappedOwner.Context != null)
			{
				relatedEnd.AttachContext(wrappedOwner.Context, wrappedOwner.MergeOption);
			}
			this.EnsureRelationshipsInitialized();
			this._relationships.Add(relatedEnd);
			return relatedEnd;
		}

		// Token: 0x06003920 RID: 14624 RVA: 0x000BBB6A File Offset: 0x000B9D6A
		public IEnumerable<IRelatedEnd> GetAllRelatedEnds()
		{
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			EntityType entityType;
			if (wrappedOwner.Context != null && wrappedOwner.Context.MetadataWorkspace != null && this.TryGetOwnerEntityType(out entityType))
			{
				EntitySet entitySet = wrappedOwner.Context.GetEntitySet(wrappedOwner.EntityKey.EntitySetName, wrappedOwner.EntityKey.EntityContainerName);
				foreach (AssociationEndMember associationEndMember in RelationshipManager.GetAllTargetEnds(entityType, entitySet))
				{
					yield return this.GetRelatedEnd(associationEndMember.DeclaringType.FullName, associationEndMember.Name);
				}
				IEnumerator<AssociationEndMember> enumerator = null;
			}
			else if (wrappedOwner.Entity != null)
			{
				foreach (AssociationEndMember associationEndMember2 in this.GetAllTargetEnds(wrappedOwner.IdentityType))
				{
					yield return this.GetRelatedEnd(associationEndMember2.DeclaringType.FullName, associationEndMember2.Name);
				}
				IEnumerator<AssociationEndMember> enumerator = null;
			}
			yield break;
			yield break;
		}

		// Token: 0x06003921 RID: 14625 RVA: 0x000BBB7C File Offset: 0x000B9D7C
		[EditorBrowsable(EditorBrowsableState.Never)]
		[Browsable(false)]
		[OnSerializing]
		public void OnSerializing(StreamingContext context)
		{
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			if (!(wrappedOwner.Entity is IEntityWithRelationships))
			{
				throw new InvalidOperationException(Strings.RelatedEnd_CannotSerialize("RelationshipManager"));
			}
			if (wrappedOwner.Context != null && wrappedOwner.MergeOption != MergeOption.NoTracking)
			{
				foreach (IRelatedEnd relatedEnd in this.GetAllRelatedEnds())
				{
					EntityReference entityReference = ((RelatedEnd)relatedEnd) as EntityReference;
					if (entityReference != null && entityReference.EntityKey != null)
					{
						entityReference.DetachedEntityKey = entityReference.EntityKey;
					}
				}
			}
		}

		// Token: 0x17000AEC RID: 2796
		// (get) Token: 0x06003922 RID: 14626 RVA: 0x000BBC20 File Offset: 0x000B9E20
		internal bool HasRelationships
		{
			get
			{
				return this._relationships != null;
			}
		}

		// Token: 0x06003923 RID: 14627 RVA: 0x000BBC2C File Offset: 0x000B9E2C
		internal void AddRelatedEntitiesToObjectStateManager(bool doAttach)
		{
			if (this._relationships != null)
			{
				bool flag = true;
				try
				{
					foreach (RelatedEnd relatedEnd in this.Relationships)
					{
						relatedEnd.Include(false, doAttach);
					}
					flag = false;
				}
				finally
				{
					if (flag)
					{
						IEntityWrapper wrappedOwner = this.WrappedOwner;
						TransactionManager transactionManager = wrappedOwner.Context.ObjectStateManager.TransactionManager;
						wrappedOwner.Context.ObjectStateManager.DegradePromotedRelationships();
						this.NodeVisited = true;
						RelationshipManager.RemoveRelatedEntitiesFromObjectStateManager(wrappedOwner);
						EntityEntry entityEntry;
						if (transactionManager.IsAttachTracking && transactionManager.PromotedKeyEntries.TryGetValue(wrappedOwner.Entity, out entityEntry))
						{
							entityEntry.DegradeEntry();
						}
						else
						{
							RelatedEnd.RemoveEntityFromObjectStateManager(wrappedOwner);
						}
					}
				}
			}
		}

		// Token: 0x06003924 RID: 14628 RVA: 0x000BBCFC File Offset: 0x000B9EFC
		internal static void RemoveRelatedEntitiesFromObjectStateManager(IEntityWrapper wrappedEntity)
		{
			foreach (RelatedEnd relatedEnd in wrappedEntity.RelationshipManager.Relationships)
			{
				if (relatedEnd.ObjectContext != null)
				{
					relatedEnd.Exclude();
					relatedEnd.DetachContext();
				}
			}
		}

		// Token: 0x06003925 RID: 14629 RVA: 0x000BBD5C File Offset: 0x000B9F5C
		internal void RemoveEntityFromRelationships()
		{
			if (this._relationships != null)
			{
				foreach (RelatedEnd relatedEnd in this.Relationships)
				{
					relatedEnd.RemoveAll();
				}
			}
		}

		// Token: 0x06003926 RID: 14630 RVA: 0x000BBDB0 File Offset: 0x000B9FB0
		internal void NullAllFKsInDependentsForWhichThisIsThePrincipal()
		{
			if (this._relationships != null)
			{
				List<EntityReference> list = new List<EntityReference>();
				foreach (RelatedEnd relatedEnd in this.Relationships)
				{
					if (relatedEnd.IsForeignKey)
					{
						foreach (IEntityWrapper entityWrapper in relatedEnd.GetWrappedEntities())
						{
							RelatedEnd otherEndOfRelationship = relatedEnd.GetOtherEndOfRelationship(entityWrapper);
							if (otherEndOfRelationship.IsDependentEndOfReferentialConstraint(false))
							{
								list.Add((EntityReference)otherEndOfRelationship);
							}
						}
					}
				}
				foreach (EntityReference entityReference in list)
				{
					entityReference.NullAllForeignKeys();
				}
			}
		}

		// Token: 0x06003927 RID: 14631 RVA: 0x000BBEA4 File Offset: 0x000BA0A4
		internal void DetachEntityFromRelationships(EntityState ownerEntityState)
		{
			if (this._relationships != null)
			{
				foreach (RelatedEnd relatedEnd in this.Relationships)
				{
					relatedEnd.DetachAll(ownerEntityState);
				}
			}
		}

		// Token: 0x06003928 RID: 14632 RVA: 0x000BBEF8 File Offset: 0x000BA0F8
		internal void RemoveEntity(string toRole, string relationshipName, IEntityWrapper wrappedEntity)
		{
			RelatedEnd relatedEnd;
			if (this.TryGetCachedRelatedEnd(relationshipName, toRole, out relatedEnd))
			{
				relatedEnd.Remove(wrappedEntity, false);
			}
		}

		// Token: 0x06003929 RID: 14633 RVA: 0x000BBF1C File Offset: 0x000BA11C
		internal void ClearRelatedEndWrappers()
		{
			if (this._relationships != null)
			{
				foreach (RelatedEnd relatedEnd in this.Relationships)
				{
					((RelatedEnd)relatedEnd).ClearWrappedValues();
				}
			}
		}

		// Token: 0x0600392A RID: 14634 RVA: 0x000BBF74 File Offset: 0x000BA174
		internal void RetrieveReferentialConstraintProperties(out Dictionary<string, KeyValuePair<object, IntBox>> properties, HashSet<object> visited, bool includeOwnValues)
		{
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			properties = new Dictionary<string, KeyValuePair<object, IntBox>>();
			EntityKey entityKey = wrappedOwner.EntityKey;
			if (entityKey.IsTemporary)
			{
				List<string> list;
				bool flag;
				this.FindNamesOfReferentialConstraintProperties(out list, out flag, false);
				if (list != null)
				{
					if (this._relationships != null)
					{
						foreach (RelatedEnd relatedEnd in this._relationships)
						{
							relatedEnd.RetrieveReferentialConstraintProperties(properties, visited);
						}
					}
					if (!RelationshipManager.CheckIfAllPropertiesWereRetrieved(properties, list))
					{
						wrappedOwner.Context.ObjectStateManager.FindEntityEntry(entityKey).RetrieveReferentialConstraintPropertiesFromKeyEntries(properties);
						if (!RelationshipManager.CheckIfAllPropertiesWereRetrieved(properties, list))
						{
							throw new InvalidOperationException(Strings.RelationshipManager_UnableToRetrieveReferentialConstraintProperties);
						}
					}
				}
			}
			if (!entityKey.IsTemporary || includeOwnValues)
			{
				wrappedOwner.Context.ObjectStateManager.FindEntityEntry(entityKey).GetOtherKeyProperties(properties);
			}
		}

		// Token: 0x0600392B RID: 14635 RVA: 0x000BC05C File Offset: 0x000BA25C
		private static bool CheckIfAllPropertiesWereRetrieved(Dictionary<string, KeyValuePair<object, IntBox>> properties, List<string> propertiesToRetrieve)
		{
			bool flag = true;
			List<int> list = new List<int>();
			ICollection<KeyValuePair<object, IntBox>> values = properties.Values;
			foreach (KeyValuePair<object, IntBox> keyValuePair in values)
			{
				list.Add(keyValuePair.Value.Value);
			}
			foreach (string text in propertiesToRetrieve)
			{
				if (!properties.ContainsKey(text))
				{
					flag = false;
					break;
				}
				KeyValuePair<object, IntBox> keyValuePair2 = properties[text];
				keyValuePair2.Value.Value = keyValuePair2.Value.Value - 1;
				if (keyValuePair2.Value.Value < 0)
				{
					flag = false;
					break;
				}
			}
			if (flag)
			{
				foreach (KeyValuePair<object, IntBox> keyValuePair3 in values)
				{
					if (keyValuePair3.Value.Value != 0)
					{
						flag = false;
						break;
					}
				}
			}
			if (!flag)
			{
				IEnumerator<int> enumerator3 = list.GetEnumerator();
				foreach (KeyValuePair<object, IntBox> keyValuePair4 in values)
				{
					enumerator3.MoveNext();
					keyValuePair4.Value.Value = enumerator3.Current;
				}
			}
			return flag;
		}

		// Token: 0x0600392C RID: 14636 RVA: 0x000BC1E0 File Offset: 0x000BA3E0
		internal void CheckReferentialConstraintProperties(EntityEntry ownerEntry)
		{
			if (this.HasReferentialConstraintPropertiesToCheck() && this._relationships != null)
			{
				foreach (RelatedEnd relatedEnd in this._relationships)
				{
					relatedEnd.CheckReferentialConstraintProperties(ownerEntry);
				}
			}
		}

		// Token: 0x0600392D RID: 14637 RVA: 0x000BC244 File Offset: 0x000BA444
		[EditorBrowsable(EditorBrowsableState.Never)]
		[Browsable(false)]
		[OnDeserialized]
		public void OnDeserialized(StreamingContext context)
		{
			this._entityWrapperFactory = new EntityWrapperFactory();
			this._expensiveLoader = new ExpensiveOSpaceLoader();
			this._wrappedOwner = this.EntityWrapperFactory.WrapEntityUsingContext(this._owner, null);
		}

		// Token: 0x0600392E RID: 14638 RVA: 0x000BC274 File Offset: 0x000BA474
		private bool TryGetCachedRelatedEnd(string relationshipName, string targetRoleName, out RelatedEnd relatedEnd)
		{
			relatedEnd = null;
			if (this._relationships != null)
			{
				foreach (RelatedEnd relatedEnd2 in this._relationships)
				{
					RelationshipNavigation relationshipNavigation = relatedEnd2.RelationshipNavigation;
					if (relationshipNavigation.RelationshipName == relationshipName && relationshipNavigation.To == targetRoleName)
					{
						relatedEnd = relatedEnd2;
						return true;
					}
				}
				return false;
			}
			return false;
		}

		// Token: 0x0600392F RID: 14639 RVA: 0x000BC2F8 File Offset: 0x000BA4F8
		internal bool FindNamesOfReferentialConstraintProperties(out List<string> propertiesToRetrieve, out bool propertiesToPropagateExist, bool skipFK)
		{
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			EntityKey entityKey = wrappedOwner.EntityKey;
			if (entityKey == null)
			{
				throw Error.EntityKey_UnexpectedNull();
			}
			propertiesToRetrieve = null;
			propertiesToPropagateExist = false;
			if (wrappedOwner.Context == null)
			{
				throw new InvalidOperationException(Strings.RelationshipManager_UnexpectedNullContext);
			}
			EntitySet entitySet = entityKey.GetEntitySet(wrappedOwner.Context.MetadataWorkspace);
			ReadOnlyCollection<AssociationSet> associationSets = entitySet.AssociationSets;
			bool flag = false;
			foreach (AssociationSet associationSet in associationSets)
			{
				if (skipFK && associationSet.ElementType.IsForeignKey)
				{
					flag = true;
				}
				else
				{
					foreach (ReferentialConstraint referentialConstraint in associationSet.ElementType.ReferentialConstraints)
					{
						if (referentialConstraint.ToRole.TypeUsage.EdmType == entitySet.ElementType.GetReferenceType())
						{
							propertiesToRetrieve = propertiesToRetrieve ?? new List<string>();
							foreach (EdmProperty edmProperty in referentialConstraint.ToProperties)
							{
								propertiesToRetrieve.Add(edmProperty.Name);
							}
						}
						if (referentialConstraint.FromRole.TypeUsage.EdmType == entitySet.ElementType.GetReferenceType())
						{
							propertiesToPropagateExist = true;
						}
					}
				}
			}
			return flag;
		}

		// Token: 0x06003930 RID: 14640 RVA: 0x000BC484 File Offset: 0x000BA684
		internal bool HasReferentialConstraintPropertiesToCheck()
		{
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			EntityKey entityKey = wrappedOwner.EntityKey;
			if (entityKey == null)
			{
				throw Error.EntityKey_UnexpectedNull();
			}
			if (wrappedOwner.Context == null)
			{
				throw new InvalidOperationException(Strings.RelationshipManager_UnexpectedNullContext);
			}
			EntitySet entitySet = entityKey.GetEntitySet(wrappedOwner.Context.MetadataWorkspace);
			foreach (AssociationSet associationSet in entitySet.AssociationSets)
			{
				foreach (ReferentialConstraint referentialConstraint in associationSet.ElementType.ReferentialConstraints)
				{
					if (referentialConstraint.ToRole.TypeUsage.EdmType == entitySet.ElementType.GetReferenceType())
					{
						return true;
					}
					if (referentialConstraint.FromRole.TypeUsage.EdmType == entitySet.ElementType.GetReferenceType())
					{
						return true;
					}
				}
			}
			return false;
		}

		// Token: 0x06003931 RID: 14641 RVA: 0x000BC594 File Offset: 0x000BA794
		internal bool IsOwner(IEntityWrapper wrappedEntity)
		{
			IEntityWrapper wrappedOwner = this.WrappedOwner;
			return wrappedEntity.Entity == wrappedOwner.Entity;
		}

		// Token: 0x06003932 RID: 14642 RVA: 0x000BC5B8 File Offset: 0x000BA7B8
		internal void AttachContextToRelatedEnds(ObjectContext context, EntitySet entitySet, MergeOption mergeOption)
		{
			if (this._relationships != null)
			{
				foreach (RelatedEnd relatedEnd in this.Relationships)
				{
					EdmType edmType;
					RelationshipSet relationshipSet;
					relatedEnd.FindRelationshipSet(context, entitySet, out edmType, out relationshipSet);
					if (relationshipSet != null || !relatedEnd.IsEmpty())
					{
						relatedEnd.AttachContext(context, entitySet, mergeOption);
					}
					else
					{
						this._relationships.Remove(relatedEnd);
					}
				}
			}
		}

		// Token: 0x06003933 RID: 14643 RVA: 0x000BC638 File Offset: 0x000BA838
		internal void ResetContextOnRelatedEnds(ObjectContext context, EntitySet entitySet, MergeOption mergeOption)
		{
			if (this._relationships != null)
			{
				foreach (RelatedEnd relatedEnd in this.Relationships)
				{
					relatedEnd.AttachContext(context, entitySet, mergeOption);
					foreach (IEntityWrapper entityWrapper in relatedEnd.GetWrappedEntities())
					{
						entityWrapper.ResetContext(context, relatedEnd.GetTargetEntitySetFromRelationshipSet(), mergeOption);
					}
				}
			}
		}

		// Token: 0x06003934 RID: 14644 RVA: 0x000BC6D0 File Offset: 0x000BA8D0
		internal void DetachContextFromRelatedEnds()
		{
			if (this._relationships != null)
			{
				foreach (RelatedEnd relatedEnd in this._relationships)
				{
					relatedEnd.DetachContext();
				}
			}
		}

		// Token: 0x06003935 RID: 14645 RVA: 0x000BC728 File Offset: 0x000BA928
		[Conditional("DEBUG")]
		internal void VerifyIsNotRelated()
		{
			if (this._relationships != null)
			{
				foreach (RelatedEnd relatedEnd in this._relationships)
				{
					relatedEnd.IsEmpty();
				}
			}
		}

		// Token: 0x04001304 RID: 4868
		private IEntityWithRelationships _owner;

		// Token: 0x04001305 RID: 4869
		private List<RelatedEnd> _relationships;

		// Token: 0x04001306 RID: 4870
		[NonSerialized]
		private bool _nodeVisited;

		// Token: 0x04001307 RID: 4871
		[NonSerialized]
		private IEntityWrapper _wrappedOwner;

		// Token: 0x04001308 RID: 4872
		[NonSerialized]
		private EntityWrapperFactory _entityWrapperFactory;

		// Token: 0x04001309 RID: 4873
		[NonSerialized]
		private ExpensiveOSpaceLoader _expensiveLoader;
	}
}
