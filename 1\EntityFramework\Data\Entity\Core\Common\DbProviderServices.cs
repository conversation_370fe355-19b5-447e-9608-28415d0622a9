﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.EntityClient;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Infrastructure.DependencyResolution;
using System.Data.Entity.Infrastructure.Interception;
using System.Data.Entity.Resources;
using System.Data.Entity.Spatial;
using System.Data.Entity.Utilities;
using System.IO;
using System.Transactions;
using System.Xml;

namespace System.Data.Entity.Core.Common
{
	// Token: 0x020005EC RID: 1516
	public abstract class DbProviderServices : IDbDependencyResolver
	{
		// Token: 0x06004A20 RID: 18976 RVA: 0x00105EF7 File Offset: 0x001040F7
		protected DbProviderServices()
			: this(() => DbConfiguration.DependencyResolver)
		{
		}

		// Token: 0x06004A21 RID: 18977 RVA: 0x00105F1E File Offset: 0x0010411E
		internal DbProviderServices(Func<IDbDependencyResolver> resolver)
			: this(resolver, new Lazy<DbCommandTreeDispatcher>(() => DbInterception.Dispatch.CommandTree))
		{
		}

		// Token: 0x06004A22 RID: 18978 RVA: 0x00105F4B File Offset: 0x0010414B
		internal DbProviderServices(Func<IDbDependencyResolver> resolver, Lazy<DbCommandTreeDispatcher> treeDispatcher)
		{
			Check.NotNull<Func<IDbDependencyResolver>>(resolver, "resolver");
			this._resolver = new Lazy<IDbDependencyResolver>(resolver);
			this._treeDispatcher = treeDispatcher;
		}

		// Token: 0x06004A23 RID: 18979 RVA: 0x00105F7D File Offset: 0x0010417D
		public virtual void RegisterInfoMessageHandler(DbConnection connection, Action<string> handler)
		{
		}

		// Token: 0x06004A24 RID: 18980 RVA: 0x00105F7F File Offset: 0x0010417F
		public DbCommandDefinition CreateCommandDefinition(DbCommandTree commandTree)
		{
			Check.NotNull<DbCommandTree>(commandTree, "commandTree");
			return this.CreateCommandDefinition(commandTree, new DbInterceptionContext());
		}

		// Token: 0x06004A25 RID: 18981 RVA: 0x00105F9C File Offset: 0x0010419C
		internal DbCommandDefinition CreateCommandDefinition(DbCommandTree commandTree, DbInterceptionContext interceptionContext)
		{
			this.ValidateDataSpace(commandTree);
			StoreItemCollection storeItemCollection = (StoreItemCollection)commandTree.MetadataWorkspace.GetItemCollection(DataSpace.SSpace);
			commandTree = this._treeDispatcher.Value.Created(commandTree, interceptionContext);
			return this.CreateDbCommandDefinition(storeItemCollection.ProviderManifest, commandTree, interceptionContext);
		}

		// Token: 0x06004A26 RID: 18982 RVA: 0x00105FE4 File Offset: 0x001041E4
		internal virtual DbCommandDefinition CreateDbCommandDefinition(DbProviderManifest providerManifest, DbCommandTree commandTree, DbInterceptionContext interceptionContext)
		{
			return this.CreateDbCommandDefinition(providerManifest, commandTree);
		}

		// Token: 0x06004A27 RID: 18983 RVA: 0x00105FF0 File Offset: 0x001041F0
		public DbCommandDefinition CreateCommandDefinition(DbProviderManifest providerManifest, DbCommandTree commandTree)
		{
			Check.NotNull<DbProviderManifest>(providerManifest, "providerManifest");
			Check.NotNull<DbCommandTree>(commandTree, "commandTree");
			DbCommandDefinition dbCommandDefinition;
			try
			{
				dbCommandDefinition = this.CreateDbCommandDefinition(providerManifest, commandTree);
			}
			catch (ProviderIncompatibleException)
			{
				throw;
			}
			catch (Exception ex)
			{
				if (ex.IsCatchableExceptionType())
				{
					throw new ProviderIncompatibleException(Strings.ProviderDidNotCreateACommandDefinition, ex);
				}
				throw;
			}
			return dbCommandDefinition;
		}

		// Token: 0x06004A28 RID: 18984
		protected abstract DbCommandDefinition CreateDbCommandDefinition(DbProviderManifest providerManifest, DbCommandTree commandTree);

		// Token: 0x06004A29 RID: 18985 RVA: 0x00106058 File Offset: 0x00104258
		internal virtual void ValidateDataSpace(DbCommandTree commandTree)
		{
			if (commandTree.DataSpace != DataSpace.SSpace)
			{
				throw new ProviderIncompatibleException(Strings.ProviderRequiresStoreCommandTree);
			}
		}

		// Token: 0x06004A2A RID: 18986 RVA: 0x0010606E File Offset: 0x0010426E
		internal virtual DbCommand CreateCommand(DbCommandTree commandTree, DbInterceptionContext interceptionContext)
		{
			return this.CreateCommandDefinition(commandTree, interceptionContext).CreateCommand();
		}

		// Token: 0x06004A2B RID: 18987 RVA: 0x0010607D File Offset: 0x0010427D
		public virtual DbCommandDefinition CreateCommandDefinition(DbCommand prototype)
		{
			return new DbCommandDefinition(prototype, new Func<DbCommand, DbCommand>(this.CloneDbCommand));
		}

		// Token: 0x06004A2C RID: 18988 RVA: 0x00106094 File Offset: 0x00104294
		protected virtual DbCommand CloneDbCommand(DbCommand fromDbCommand)
		{
			Check.NotNull<DbCommand>(fromDbCommand, "fromDbCommand");
			ICloneable cloneable = fromDbCommand as ICloneable;
			if (cloneable == null)
			{
				throw new ProviderIncompatibleException(Strings.EntityClient_CannotCloneStoreProvider);
			}
			return (DbCommand)cloneable.Clone();
		}

		// Token: 0x06004A2D RID: 18989 RVA: 0x001060CD File Offset: 0x001042CD
		public virtual DbConnection CloneDbConnection(DbConnection connection)
		{
			return this.CloneDbConnection(connection, DbProviderServices.GetProviderFactory(connection));
		}

		// Token: 0x06004A2E RID: 18990 RVA: 0x001060DC File Offset: 0x001042DC
		public virtual DbConnection CloneDbConnection(DbConnection connection, DbProviderFactory factory)
		{
			return factory.CreateConnection();
		}

		// Token: 0x06004A2F RID: 18991 RVA: 0x001060E4 File Offset: 0x001042E4
		public string GetProviderManifestToken(DbConnection connection)
		{
			Check.NotNull<DbConnection>(connection, "connection");
			string text;
			try
			{
				string dbProviderManifestToken;
				using (new TransactionScope(TransactionScopeOption.Suppress))
				{
					dbProviderManifestToken = this.GetDbProviderManifestToken(connection);
				}
				if (dbProviderManifestToken == null)
				{
					throw new ProviderIncompatibleException(Strings.ProviderDidNotReturnAProviderManifestToken);
				}
				text = dbProviderManifestToken;
			}
			catch (ProviderIncompatibleException)
			{
				throw;
			}
			catch (Exception ex)
			{
				if (ex.IsCatchableExceptionType())
				{
					throw new ProviderIncompatibleException(Strings.ProviderDidNotReturnAProviderManifestToken, ex);
				}
				throw;
			}
			return text;
		}

		// Token: 0x06004A30 RID: 18992
		protected abstract string GetDbProviderManifestToken(DbConnection connection);

		// Token: 0x06004A31 RID: 18993 RVA: 0x00106170 File Offset: 0x00104370
		public DbProviderManifest GetProviderManifest(string manifestToken)
		{
			Check.NotNull<string>(manifestToken, "manifestToken");
			DbProviderManifest dbProviderManifest2;
			try
			{
				DbProviderManifest dbProviderManifest = this.GetDbProviderManifest(manifestToken);
				if (dbProviderManifest == null)
				{
					throw new ProviderIncompatibleException(Strings.ProviderDidNotReturnAProviderManifest);
				}
				dbProviderManifest2 = dbProviderManifest;
			}
			catch (ProviderIncompatibleException)
			{
				throw;
			}
			catch (Exception ex)
			{
				if (ex.IsCatchableExceptionType())
				{
					throw new ProviderIncompatibleException(Strings.ProviderDidNotReturnAProviderManifest, ex);
				}
				throw;
			}
			return dbProviderManifest2;
		}

		// Token: 0x06004A32 RID: 18994
		protected abstract DbProviderManifest GetDbProviderManifest(string manifestToken);

		// Token: 0x06004A33 RID: 18995 RVA: 0x001061D8 File Offset: 0x001043D8
		public static IDbExecutionStrategy GetExecutionStrategy(DbConnection connection)
		{
			return DbProviderServices.GetExecutionStrategy(connection, DbProviderServices.GetProviderFactory(connection), null);
		}

		// Token: 0x06004A34 RID: 18996 RVA: 0x001061E8 File Offset: 0x001043E8
		internal static IDbExecutionStrategy GetExecutionStrategy(DbConnection connection, MetadataWorkspace metadataWorkspace)
		{
			StoreItemCollection storeItemCollection = (StoreItemCollection)metadataWorkspace.GetItemCollection(DataSpace.SSpace);
			return DbProviderServices.GetExecutionStrategy(connection, storeItemCollection.ProviderFactory, null);
		}

		// Token: 0x06004A35 RID: 18997 RVA: 0x0010620F File Offset: 0x0010440F
		protected static IDbExecutionStrategy GetExecutionStrategy(DbConnection connection, string providerInvariantName)
		{
			return DbProviderServices.GetExecutionStrategy(connection, DbProviderServices.GetProviderFactory(connection), providerInvariantName);
		}

		// Token: 0x06004A36 RID: 18998 RVA: 0x00106220 File Offset: 0x00104420
		private static IDbExecutionStrategy GetExecutionStrategy(DbConnection connection, DbProviderFactory providerFactory, string providerInvariantName = null)
		{
			EntityConnection entityConnection = connection as EntityConnection;
			if (entityConnection != null)
			{
				connection = entityConnection.StoreConnection;
			}
			string dataSource = DbInterception.Dispatch.Connection.GetDataSource(connection, new DbInterceptionContext());
			ExecutionStrategyKey executionStrategyKey = new ExecutionStrategyKey(providerFactory.GetType().FullName, dataSource);
			return DbProviderServices._executionStrategyFactories.GetOrAdd(executionStrategyKey, (ExecutionStrategyKey k) => DbConfiguration.DependencyResolver.GetService(new ExecutionStrategyKey(providerInvariantName ?? DbConfiguration.DependencyResolver.GetService(providerFactory).Name, dataSource)))();
		}

		// Token: 0x06004A37 RID: 18999 RVA: 0x001062A8 File Offset: 0x001044A8
		public DbSpatialDataReader GetSpatialDataReader(DbDataReader fromReader, string manifestToken)
		{
			DbSpatialDataReader dbSpatialDataReader;
			try
			{
				dbSpatialDataReader = this.GetDbSpatialDataReader(fromReader, manifestToken);
			}
			catch (ProviderIncompatibleException)
			{
				throw;
			}
			catch (Exception ex)
			{
				if (ex.IsCatchableExceptionType())
				{
					throw new ProviderIncompatibleException(Strings.ProviderDidNotReturnSpatialServices, ex);
				}
				throw;
			}
			return dbSpatialDataReader;
		}

		// Token: 0x06004A38 RID: 19000 RVA: 0x001062F8 File Offset: 0x001044F8
		[Obsolete("Use GetSpatialServices(DbProviderInfo) or DbConfiguration to ensure the configured spatial services are used. See http://go.microsoft.com/fwlink/?LinkId=260882 for more information.")]
		public DbSpatialServices GetSpatialServices(string manifestToken)
		{
			DbSpatialServices dbSpatialServices;
			try
			{
				dbSpatialServices = this.DbGetSpatialServices(manifestToken);
			}
			catch (ProviderIncompatibleException)
			{
				throw;
			}
			catch (Exception ex)
			{
				throw new ProviderIncompatibleException(Strings.ProviderDidNotReturnSpatialServices, ex);
			}
			return dbSpatialServices;
		}

		// Token: 0x06004A39 RID: 19001 RVA: 0x0010633C File Offset: 0x0010453C
		internal static DbSpatialServices GetSpatialServices(IDbDependencyResolver resolver, EntityConnection connection)
		{
			StoreItemCollection storeItemCollection = (StoreItemCollection)connection.GetMetadataWorkspace().GetItemCollection(DataSpace.SSpace);
			DbProviderInfo dbProviderInfo = new DbProviderInfo(storeItemCollection.ProviderInvariantName, storeItemCollection.ProviderManifestToken);
			return DbProviderServices.GetSpatialServices(resolver, dbProviderInfo, () => DbProviderServices.GetProviderServices(connection.StoreConnection));
		}

		// Token: 0x06004A3A RID: 19002 RVA: 0x00106392 File Offset: 0x00104592
		public DbSpatialServices GetSpatialServices(DbProviderInfo key)
		{
			return DbProviderServices.GetSpatialServices(this._resolver.Value, key, () => this);
		}

		// Token: 0x06004A3B RID: 19003 RVA: 0x001063B4 File Offset: 0x001045B4
		private static DbSpatialServices GetSpatialServices(IDbDependencyResolver resolver, DbProviderInfo key, Func<DbProviderServices> providerServices)
		{
			DbSpatialServices orAdd = DbProviderServices._spatialServices.GetOrAdd(key, delegate(DbProviderInfo k)
			{
				DbSpatialServices dbSpatialServices;
				if ((dbSpatialServices = resolver.GetService(k)) == null)
				{
					dbSpatialServices = providerServices().GetSpatialServices(k.ProviderManifestToken) ?? resolver.GetService<DbSpatialServices>();
				}
				return dbSpatialServices;
			});
			if (orAdd == null)
			{
				throw new ProviderIncompatibleException(Strings.ProviderDidNotReturnSpatialServices);
			}
			return orAdd;
		}

		// Token: 0x06004A3C RID: 19004 RVA: 0x001063FA File Offset: 0x001045FA
		protected virtual DbSpatialDataReader GetDbSpatialDataReader(DbDataReader fromReader, string manifestToken)
		{
			Check.NotNull<DbDataReader>(fromReader, "fromReader");
			return null;
		}

		// Token: 0x06004A3D RID: 19005 RVA: 0x00106409 File Offset: 0x00104609
		[Obsolete("Return DbSpatialServices from the GetService method. See http://go.microsoft.com/fwlink/?LinkId=260882 for more information.")]
		protected virtual DbSpatialServices DbGetSpatialServices(string manifestToken)
		{
			return null;
		}

		// Token: 0x06004A3E RID: 19006 RVA: 0x0010640C File Offset: 0x0010460C
		public void SetParameterValue(DbParameter parameter, TypeUsage parameterType, object value)
		{
			Check.NotNull<DbParameter>(parameter, "parameter");
			Check.NotNull<TypeUsage>(parameterType, "parameterType");
			this.SetDbParameterValue(parameter, parameterType, value);
		}

		// Token: 0x06004A3F RID: 19007 RVA: 0x0010642F File Offset: 0x0010462F
		protected virtual void SetDbParameterValue(DbParameter parameter, TypeUsage parameterType, object value)
		{
			Check.NotNull<DbParameter>(parameter, "parameter");
			Check.NotNull<TypeUsage>(parameterType, "parameterType");
			parameter.Value = value;
		}

		// Token: 0x06004A40 RID: 19008 RVA: 0x00106450 File Offset: 0x00104650
		public static DbProviderServices GetProviderServices(DbConnection connection)
		{
			return DbProviderServices.GetProviderFactory(connection).GetProviderServices();
		}

		// Token: 0x06004A41 RID: 19009 RVA: 0x0010645D File Offset: 0x0010465D
		public static DbProviderFactory GetProviderFactory(DbConnection connection)
		{
			Check.NotNull<DbConnection>(connection, "connection");
			DbProviderFactory providerFactory = connection.GetProviderFactory();
			if (providerFactory == null)
			{
				throw new ProviderIncompatibleException(Strings.EntityClient_ReturnedNullOnProviderMethod("get_ProviderFactory", connection.GetType().ToString()));
			}
			return providerFactory;
		}

		// Token: 0x06004A42 RID: 19010 RVA: 0x0010648F File Offset: 0x0010468F
		public static XmlReader GetConceptualSchemaDefinition(string csdlName)
		{
			Check.NotEmpty(csdlName, "csdlName");
			return DbProviderServices.GetXmlResource("System.Data.Resources.DbProviderServices." + csdlName + ".csdl");
		}

		// Token: 0x06004A43 RID: 19011 RVA: 0x001064B2 File Offset: 0x001046B2
		internal static XmlReader GetXmlResource(string resourceName)
		{
			Stream manifestResourceStream = typeof(DbProviderServices).Assembly().GetManifestResourceStream(resourceName);
			if (manifestResourceStream == null)
			{
				throw Error.InvalidResourceName(resourceName);
			}
			return XmlReader.Create(manifestResourceStream);
		}

		// Token: 0x06004A44 RID: 19012 RVA: 0x001064D8 File Offset: 0x001046D8
		public string CreateDatabaseScript(string providerManifestToken, StoreItemCollection storeItemCollection)
		{
			Check.NotNull<string>(providerManifestToken, "providerManifestToken");
			Check.NotNull<StoreItemCollection>(storeItemCollection, "storeItemCollection");
			return this.DbCreateDatabaseScript(providerManifestToken, storeItemCollection);
		}

		// Token: 0x06004A45 RID: 19013 RVA: 0x001064FA File Offset: 0x001046FA
		protected virtual string DbCreateDatabaseScript(string providerManifestToken, StoreItemCollection storeItemCollection)
		{
			Check.NotNull<string>(providerManifestToken, "providerManifestToken");
			Check.NotNull<StoreItemCollection>(storeItemCollection, "storeItemCollection");
			throw new ProviderIncompatibleException(Strings.ProviderDoesNotSupportCreateDatabaseScript);
		}

		// Token: 0x06004A46 RID: 19014 RVA: 0x0010651E File Offset: 0x0010471E
		public void CreateDatabase(DbConnection connection, int? commandTimeout, StoreItemCollection storeItemCollection)
		{
			Check.NotNull<DbConnection>(connection, "connection");
			Check.NotNull<StoreItemCollection>(storeItemCollection, "storeItemCollection");
			this.DbCreateDatabase(connection, commandTimeout, storeItemCollection);
		}

		// Token: 0x06004A47 RID: 19015 RVA: 0x00106541 File Offset: 0x00104741
		protected virtual void DbCreateDatabase(DbConnection connection, int? commandTimeout, StoreItemCollection storeItemCollection)
		{
			Check.NotNull<DbConnection>(connection, "connection");
			Check.NotNull<StoreItemCollection>(storeItemCollection, "storeItemCollection");
			throw new ProviderIncompatibleException(Strings.ProviderDoesNotSupportCreateDatabase);
		}

		// Token: 0x06004A48 RID: 19016 RVA: 0x00106568 File Offset: 0x00104768
		public bool DatabaseExists(DbConnection connection, int? commandTimeout, StoreItemCollection storeItemCollection)
		{
			Check.NotNull<DbConnection>(connection, "connection");
			Check.NotNull<StoreItemCollection>(storeItemCollection, "storeItemCollection");
			bool flag;
			using (new TransactionScope(TransactionScopeOption.Suppress))
			{
				flag = this.DbDatabaseExists(connection, commandTimeout, storeItemCollection);
			}
			return flag;
		}

		// Token: 0x06004A49 RID: 19017 RVA: 0x001065BC File Offset: 0x001047BC
		public bool DatabaseExists(DbConnection connection, int? commandTimeout, Lazy<StoreItemCollection> storeItemCollection)
		{
			Check.NotNull<DbConnection>(connection, "connection");
			Check.NotNull<Lazy<StoreItemCollection>>(storeItemCollection, "storeItemCollection");
			bool flag;
			using (new TransactionScope(TransactionScopeOption.Suppress))
			{
				flag = this.DbDatabaseExists(connection, commandTimeout, storeItemCollection);
			}
			return flag;
		}

		// Token: 0x06004A4A RID: 19018 RVA: 0x00106610 File Offset: 0x00104810
		protected virtual bool DbDatabaseExists(DbConnection connection, int? commandTimeout, StoreItemCollection storeItemCollection)
		{
			Check.NotNull<DbConnection>(connection, "connection");
			Check.NotNull<StoreItemCollection>(storeItemCollection, "storeItemCollection");
			throw new ProviderIncompatibleException(Strings.ProviderDoesNotSupportDatabaseExists);
		}

		// Token: 0x06004A4B RID: 19019 RVA: 0x00106634 File Offset: 0x00104834
		protected virtual bool DbDatabaseExists(DbConnection connection, int? commandTimeout, Lazy<StoreItemCollection> storeItemCollection)
		{
			Check.NotNull<DbConnection>(connection, "connection");
			Check.NotNull<Lazy<StoreItemCollection>>(storeItemCollection, "storeItemCollection");
			return this.DbDatabaseExists(connection, commandTimeout, storeItemCollection.Value);
		}

		// Token: 0x06004A4C RID: 19020 RVA: 0x0010665C File Offset: 0x0010485C
		public void DeleteDatabase(DbConnection connection, int? commandTimeout, StoreItemCollection storeItemCollection)
		{
			Check.NotNull<DbConnection>(connection, "connection");
			Check.NotNull<StoreItemCollection>(storeItemCollection, "storeItemCollection");
			this.DbDeleteDatabase(connection, commandTimeout, storeItemCollection);
		}

		// Token: 0x06004A4D RID: 19021 RVA: 0x0010667F File Offset: 0x0010487F
		protected virtual void DbDeleteDatabase(DbConnection connection, int? commandTimeout, StoreItemCollection storeItemCollection)
		{
			Check.NotNull<DbConnection>(connection, "connection");
			Check.NotNull<StoreItemCollection>(storeItemCollection, "storeItemCollection");
			throw new ProviderIncompatibleException(Strings.ProviderDoesNotSupportDeleteDatabase);
		}

		// Token: 0x06004A4E RID: 19022 RVA: 0x001066A4 File Offset: 0x001048A4
		public static string ExpandDataDirectory(string path)
		{
			if (string.IsNullOrEmpty(path) || !path.StartsWith("|datadirectory|", StringComparison.OrdinalIgnoreCase))
			{
				return path;
			}
			object data = AppDomain.CurrentDomain.GetData("DataDirectory");
			string text = data as string;
			if (data != null && text == null)
			{
				throw new InvalidOperationException(Strings.ADP_InvalidDataDirectory);
			}
			if (text == string.Empty)
			{
				text = AppDomain.CurrentDomain.BaseDirectory;
			}
			if (text == null)
			{
				text = string.Empty;
			}
			path = path.Substring("|datadirectory|".Length);
			if (path.StartsWith("\\", StringComparison.Ordinal))
			{
				path = path.Substring(1);
			}
			path = (text.EndsWith("\\", StringComparison.Ordinal) ? text : (text + "\\")) + path;
			if (text.Contains(".."))
			{
				throw new ArgumentException(Strings.ExpandingDataDirectoryFailed);
			}
			return path;
		}

		// Token: 0x06004A4F RID: 19023 RVA: 0x00106778 File Offset: 0x00104978
		protected void AddDependencyResolver(IDbDependencyResolver resolver)
		{
			Check.NotNull<IDbDependencyResolver>(resolver, "resolver");
			this._resolvers.Add(resolver);
		}

		// Token: 0x06004A50 RID: 19024 RVA: 0x00106792 File Offset: 0x00104992
		public virtual object GetService(Type type, object key)
		{
			return this._resolvers.GetService(type, key);
		}

		// Token: 0x06004A51 RID: 19025 RVA: 0x001067A1 File Offset: 0x001049A1
		public virtual IEnumerable<object> GetServices(Type type, object key)
		{
			return this._resolvers.GetServices(type, key);
		}

		// Token: 0x04001A2B RID: 6699
		private readonly Lazy<IDbDependencyResolver> _resolver;

		// Token: 0x04001A2C RID: 6700
		private readonly Lazy<DbCommandTreeDispatcher> _treeDispatcher;

		// Token: 0x04001A2D RID: 6701
		private static readonly ConcurrentDictionary<DbProviderInfo, DbSpatialServices> _spatialServices = new ConcurrentDictionary<DbProviderInfo, DbSpatialServices>();

		// Token: 0x04001A2E RID: 6702
		private static readonly ConcurrentDictionary<ExecutionStrategyKey, Func<IDbExecutionStrategy>> _executionStrategyFactories = new ConcurrentDictionary<ExecutionStrategyKey, Func<IDbExecutionStrategy>>();

		// Token: 0x04001A2F RID: 6703
		private readonly ResolverChain _resolvers = new ResolverChain();
	}
}
