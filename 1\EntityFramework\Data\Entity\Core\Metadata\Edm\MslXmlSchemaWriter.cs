﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Mapping;
using System.Linq;
using System.Xml;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004E4 RID: 1252
	internal class MslXmlSchemaWriter : XmlSchemaWriter
	{
		// Token: 0x06003E65 RID: 15973 RVA: 0x000CE550 File Offset: 0x000CC750
		internal MslXmlSchemaWriter(XmlWriter xmlWriter, double version)
		{
			this._xmlWriter = xmlWriter;
			this._version = version;
		}

		// Token: 0x06003E66 RID: 15974 RVA: 0x000CE566 File Offset: 0x000CC766
		internal void WriteSchema(DbDatabaseMapping databaseMapping)
		{
			this.WriteSchemaElementHeader();
			this.WriteDbModelElement(databaseMapping);
			this.WriteEndElement();
		}

		// Token: 0x06003E67 RID: 15975 RVA: 0x000CE57C File Offset: 0x000CC77C
		private void WriteSchemaElementHeader()
		{
			string mslNamespace = MslConstructs.GetMslNamespace(this._version);
			this._xmlWriter.WriteStartElement("Mapping", mslNamespace);
			this._xmlWriter.WriteAttributeString("Space", "C-S");
		}

		// Token: 0x06003E68 RID: 15976 RVA: 0x000CE5BC File Offset: 0x000CC7BC
		private void WriteDbModelElement(DbDatabaseMapping databaseMapping)
		{
			this._entityTypeNamespace = databaseMapping.Model.NamespaceNames.SingleOrDefault<string>();
			this._dbSchemaName = databaseMapping.Database.Containers.Single<EntityContainer>().Name;
			this.WriteEntityContainerMappingElement(databaseMapping.EntityContainerMappings.First<EntityContainerMapping>());
		}

		// Token: 0x06003E69 RID: 15977 RVA: 0x000CE60C File Offset: 0x000CC80C
		internal void WriteEntityContainerMappingElement(EntityContainerMapping containerMapping)
		{
			this._xmlWriter.WriteStartElement("EntityContainerMapping");
			this._xmlWriter.WriteAttributeString("StorageEntityContainer", this._dbSchemaName);
			this._xmlWriter.WriteAttributeString("CdmEntityContainer", containerMapping.EdmEntityContainer.Name);
			foreach (EntitySetMapping entitySetMapping in containerMapping.EntitySetMappings)
			{
				this.WriteEntitySetMappingElement(entitySetMapping);
			}
			foreach (AssociationSetMapping associationSetMapping in containerMapping.AssociationSetMappings)
			{
				this.WriteAssociationSetMappingElement(associationSetMapping);
			}
			foreach (FunctionImportMappingComposable functionImportMappingComposable in containerMapping.FunctionImportMappings.OfType<FunctionImportMappingComposable>())
			{
				this.WriteFunctionImportMappingElement(functionImportMappingComposable);
			}
			foreach (FunctionImportMappingNonComposable functionImportMappingNonComposable in containerMapping.FunctionImportMappings.OfType<FunctionImportMappingNonComposable>())
			{
				this.WriteFunctionImportMappingElement(functionImportMappingNonComposable);
			}
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E6A RID: 15978 RVA: 0x000CE774 File Offset: 0x000CC974
		public void WriteEntitySetMappingElement(EntitySetMapping entitySetMapping)
		{
			this._xmlWriter.WriteStartElement("EntitySetMapping");
			this._xmlWriter.WriteAttributeString("Name", entitySetMapping.EntitySet.Name);
			foreach (EntityTypeMapping entityTypeMapping in entitySetMapping.EntityTypeMappings)
			{
				this.WriteEntityTypeMappingElement(entityTypeMapping);
			}
			foreach (EntityTypeModificationFunctionMapping entityTypeModificationFunctionMapping in entitySetMapping.ModificationFunctionMappings)
			{
				this._xmlWriter.WriteStartElement("EntityTypeMapping");
				this._xmlWriter.WriteAttributeString("TypeName", MslXmlSchemaWriter.GetEntityTypeName(this._entityTypeNamespace + "." + entityTypeModificationFunctionMapping.EntityType.Name, false));
				this.WriteModificationFunctionMapping(entityTypeModificationFunctionMapping);
				this._xmlWriter.WriteEndElement();
			}
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E6B RID: 15979 RVA: 0x000CE880 File Offset: 0x000CCA80
		public void WriteAssociationSetMappingElement(AssociationSetMapping associationSetMapping)
		{
			this._xmlWriter.WriteStartElement("AssociationSetMapping");
			this._xmlWriter.WriteAttributeString("Name", associationSetMapping.AssociationSet.Name);
			this._xmlWriter.WriteAttributeString("TypeName", this._entityTypeNamespace + "." + associationSetMapping.AssociationSet.ElementType.Name);
			this._xmlWriter.WriteAttributeString("StoreEntitySet", associationSetMapping.Table.Name);
			this.WriteAssociationEndMappingElement(associationSetMapping.SourceEndMapping);
			this.WriteAssociationEndMappingElement(associationSetMapping.TargetEndMapping);
			if (associationSetMapping.ModificationFunctionMapping != null)
			{
				this.WriteModificationFunctionMapping(associationSetMapping.ModificationFunctionMapping);
			}
			foreach (ConditionPropertyMapping conditionPropertyMapping in associationSetMapping.Conditions)
			{
				this.WriteConditionElement(conditionPropertyMapping);
			}
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E6C RID: 15980 RVA: 0x000CE97C File Offset: 0x000CCB7C
		private void WriteAssociationEndMappingElement(EndPropertyMapping endMapping)
		{
			this._xmlWriter.WriteStartElement("EndProperty");
			this._xmlWriter.WriteAttributeString("Name", endMapping.AssociationEnd.Name);
			foreach (ScalarPropertyMapping scalarPropertyMapping in endMapping.PropertyMappings)
			{
				this.WriteScalarPropertyElement(scalarPropertyMapping.Property.Name, scalarPropertyMapping.Column.Name);
			}
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E6D RID: 15981 RVA: 0x000CEA14 File Offset: 0x000CCC14
		private void WriteEntityTypeMappingElement(EntityTypeMapping entityTypeMapping)
		{
			this._xmlWriter.WriteStartElement("EntityTypeMapping");
			this._xmlWriter.WriteAttributeString("TypeName", MslXmlSchemaWriter.GetEntityTypeName(this._entityTypeNamespace + "." + entityTypeMapping.EntityType.Name, entityTypeMapping.IsHierarchyMapping));
			foreach (MappingFragment mappingFragment in entityTypeMapping.MappingFragments)
			{
				this.WriteMappingFragmentElement(mappingFragment);
			}
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E6E RID: 15982 RVA: 0x000CEAB4 File Offset: 0x000CCCB4
		internal void WriteMappingFragmentElement(MappingFragment mappingFragment)
		{
			this._xmlWriter.WriteStartElement("MappingFragment");
			this._xmlWriter.WriteAttributeString("StoreEntitySet", mappingFragment.TableSet.Name);
			foreach (PropertyMapping propertyMapping in mappingFragment.PropertyMappings)
			{
				this.WritePropertyMapping(propertyMapping);
			}
			foreach (ConditionPropertyMapping conditionPropertyMapping in mappingFragment.ColumnConditions)
			{
				this.WriteConditionElement(conditionPropertyMapping);
			}
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E6F RID: 15983 RVA: 0x000CEB74 File Offset: 0x000CCD74
		public void WriteFunctionImportMappingElement(FunctionImportMappingComposable functionImportMapping)
		{
			this.WriteFunctionImportMappingStartElement(functionImportMapping);
			if (functionImportMapping.StructuralTypeMappings != null)
			{
				this._xmlWriter.WriteStartElement("ResultMapping");
				Tuple<StructuralType, List<ConditionPropertyMapping>, List<PropertyMapping>> tuple = functionImportMapping.StructuralTypeMappings.Single<Tuple<StructuralType, List<ConditionPropertyMapping>, List<PropertyMapping>>>();
				if (tuple.Item1.BuiltInTypeKind == BuiltInTypeKind.ComplexType)
				{
					this._xmlWriter.WriteStartElement("ComplexTypeMapping");
					this._xmlWriter.WriteAttributeString("TypeName", tuple.Item1.FullName);
				}
				else
				{
					this._xmlWriter.WriteStartElement("EntityTypeMapping");
					this._xmlWriter.WriteAttributeString("TypeName", tuple.Item1.FullName);
					foreach (ConditionPropertyMapping conditionPropertyMapping in tuple.Item2)
					{
						this.WriteConditionElement(conditionPropertyMapping);
					}
				}
				foreach (PropertyMapping propertyMapping in tuple.Item3)
				{
					this.WritePropertyMapping(propertyMapping);
				}
				this._xmlWriter.WriteEndElement();
				this._xmlWriter.WriteEndElement();
			}
			this.WriteFunctionImportEndElement();
		}

		// Token: 0x06003E70 RID: 15984 RVA: 0x000CECBC File Offset: 0x000CCEBC
		public void WriteFunctionImportMappingElement(FunctionImportMappingNonComposable functionImportMapping)
		{
			this.WriteFunctionImportMappingStartElement(functionImportMapping);
			foreach (FunctionImportResultMapping functionImportResultMapping in functionImportMapping.ResultMappings)
			{
				this.WriteFunctionImportResultMappingElement(functionImportResultMapping);
			}
			this.WriteFunctionImportEndElement();
		}

		// Token: 0x06003E71 RID: 15985 RVA: 0x000CED18 File Offset: 0x000CCF18
		private void WriteFunctionImportMappingStartElement(FunctionImportMapping functionImportMapping)
		{
			this._xmlWriter.WriteStartElement("FunctionImportMapping");
			this._xmlWriter.WriteAttributeString("FunctionName", functionImportMapping.TargetFunction.FullName);
			this._xmlWriter.WriteAttributeString("FunctionImportName", functionImportMapping.FunctionImport.Name);
		}

		// Token: 0x06003E72 RID: 15986 RVA: 0x000CED6C File Offset: 0x000CCF6C
		private void WriteFunctionImportResultMappingElement(FunctionImportResultMapping resultMapping)
		{
			this._xmlWriter.WriteStartElement("ResultMapping");
			foreach (FunctionImportStructuralTypeMapping functionImportStructuralTypeMapping in resultMapping.TypeMappings)
			{
				FunctionImportEntityTypeMapping functionImportEntityTypeMapping = functionImportStructuralTypeMapping as FunctionImportEntityTypeMapping;
				if (functionImportEntityTypeMapping != null)
				{
					this.WriteFunctionImportEntityTypeMappingElement(functionImportEntityTypeMapping);
				}
				else
				{
					this.WriteFunctionImportComplexTypeMappingElement((FunctionImportComplexTypeMapping)functionImportStructuralTypeMapping);
				}
			}
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E73 RID: 15987 RVA: 0x000CEDEC File Offset: 0x000CCFEC
		private void WriteFunctionImportEntityTypeMappingElement(FunctionImportEntityTypeMapping entityTypeMapping)
		{
			this._xmlWriter.WriteStartElement("EntityTypeMapping");
			string text = MslXmlSchemaWriter.CreateFunctionImportEntityTypeMappingTypeName(entityTypeMapping);
			this._xmlWriter.WriteAttributeString("TypeName", text);
			this.WriteFunctionImportPropertyMappingElements(entityTypeMapping.PropertyMappings.Cast<FunctionImportReturnTypeScalarPropertyMapping>());
			foreach (FunctionImportEntityTypeMappingCondition functionImportEntityTypeMappingCondition in entityTypeMapping.Conditions)
			{
				this.WriteFunctionImportConditionElement(functionImportEntityTypeMappingCondition);
			}
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E74 RID: 15988 RVA: 0x000CEE80 File Offset: 0x000CD080
		internal static string CreateFunctionImportEntityTypeMappingTypeName(FunctionImportEntityTypeMapping entityTypeMapping)
		{
			return string.Join(";", entityTypeMapping.EntityTypes.Select((EntityType e) => MslXmlSchemaWriter.GetEntityTypeName(e.FullName, false)).Concat(entityTypeMapping.IsOfTypeEntityTypes.Select((EntityType e) => MslXmlSchemaWriter.GetEntityTypeName(e.FullName, true))));
		}

		// Token: 0x06003E75 RID: 15989 RVA: 0x000CEEF0 File Offset: 0x000CD0F0
		private void WriteFunctionImportComplexTypeMappingElement(FunctionImportComplexTypeMapping complexTypeMapping)
		{
			this._xmlWriter.WriteStartElement("ComplexTypeMapping");
			this._xmlWriter.WriteAttributeString("TypeName", complexTypeMapping.ReturnType.FullName);
			this.WriteFunctionImportPropertyMappingElements(complexTypeMapping.PropertyMappings.Cast<FunctionImportReturnTypeScalarPropertyMapping>());
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E76 RID: 15990 RVA: 0x000CEF44 File Offset: 0x000CD144
		private void WriteFunctionImportPropertyMappingElements(IEnumerable<FunctionImportReturnTypeScalarPropertyMapping> propertyMappings)
		{
			foreach (FunctionImportReturnTypeScalarPropertyMapping functionImportReturnTypeScalarPropertyMapping in propertyMappings)
			{
				this.WriteScalarPropertyElement(functionImportReturnTypeScalarPropertyMapping.PropertyName, functionImportReturnTypeScalarPropertyMapping.ColumnName);
			}
		}

		// Token: 0x06003E77 RID: 15991 RVA: 0x000CEF98 File Offset: 0x000CD198
		private void WriteFunctionImportConditionElement(FunctionImportEntityTypeMappingCondition condition)
		{
			this._xmlWriter.WriteStartElement("Condition");
			this._xmlWriter.WriteAttributeString("ColumnName", condition.ColumnName);
			FunctionImportEntityTypeMappingConditionIsNull functionImportEntityTypeMappingConditionIsNull = condition as FunctionImportEntityTypeMappingConditionIsNull;
			if (functionImportEntityTypeMappingConditionIsNull != null)
			{
				this.WriteIsNullConditionAttribute(functionImportEntityTypeMappingConditionIsNull.IsNull);
			}
			else
			{
				this.WriteConditionValue(((FunctionImportEntityTypeMappingConditionValue)condition).Value);
			}
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E78 RID: 15992 RVA: 0x000CEFFF File Offset: 0x000CD1FF
		private void WriteFunctionImportEndElement()
		{
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E79 RID: 15993 RVA: 0x000CF00C File Offset: 0x000CD20C
		private void WriteModificationFunctionMapping(EntityTypeModificationFunctionMapping modificationFunctionMapping)
		{
			this._xmlWriter.WriteStartElement("ModificationFunctionMapping");
			this.WriteFunctionMapping("InsertFunction", modificationFunctionMapping.InsertFunctionMapping, false);
			this.WriteFunctionMapping("UpdateFunction", modificationFunctionMapping.UpdateFunctionMapping, false);
			this.WriteFunctionMapping("DeleteFunction", modificationFunctionMapping.DeleteFunctionMapping, false);
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E7A RID: 15994 RVA: 0x000CF06C File Offset: 0x000CD26C
		private void WriteModificationFunctionMapping(AssociationSetModificationFunctionMapping modificationFunctionMapping)
		{
			this._xmlWriter.WriteStartElement("ModificationFunctionMapping");
			this.WriteFunctionMapping("InsertFunction", modificationFunctionMapping.InsertFunctionMapping, true);
			this.WriteFunctionMapping("DeleteFunction", modificationFunctionMapping.DeleteFunctionMapping, true);
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E7B RID: 15995 RVA: 0x000CF0B8 File Offset: 0x000CD2B8
		public void WriteFunctionMapping(string functionElement, ModificationFunctionMapping functionMapping, bool associationSetMapping = false)
		{
			this._xmlWriter.WriteStartElement(functionElement);
			this._xmlWriter.WriteAttributeString("FunctionName", functionMapping.Function.FullName);
			if (functionMapping.RowsAffectedParameter != null)
			{
				this._xmlWriter.WriteAttributeString("RowsAffectedParameter", functionMapping.RowsAffectedParameter.Name);
			}
			if (!associationSetMapping)
			{
				this.WritePropertyParameterBindings(functionMapping.ParameterBindings, 0);
				this.WriteAssociationParameterBindings(functionMapping.ParameterBindings);
				if (functionMapping.ResultBindings != null)
				{
					this.WriteResultBindings(functionMapping.ResultBindings);
				}
			}
			else
			{
				this.WriteAssociationSetMappingParameterBindings(functionMapping.ParameterBindings);
			}
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E7C RID: 15996 RVA: 0x000CF158 File Offset: 0x000CD358
		private void WriteAssociationSetMappingParameterBindings(IEnumerable<ModificationFunctionParameterBinding> parameterBindings)
		{
			foreach (IGrouping<AssociationSetEnd, ModificationFunctionParameterBinding> grouping in from pm in parameterBindings
				where pm.MemberPath.AssociationSetEnd != null
				group pm by pm.MemberPath.AssociationSetEnd)
			{
				this._xmlWriter.WriteStartElement("EndProperty");
				this._xmlWriter.WriteAttributeString("Name", grouping.Key.Name);
				foreach (ModificationFunctionParameterBinding modificationFunctionParameterBinding in grouping)
				{
					this.WriteScalarParameterElement(modificationFunctionParameterBinding.MemberPath.Members.First<EdmMember>(), modificationFunctionParameterBinding);
				}
				this._xmlWriter.WriteEndElement();
			}
		}

		// Token: 0x06003E7D RID: 15997 RVA: 0x000CF260 File Offset: 0x000CD460
		private void WritePropertyParameterBindings(IEnumerable<ModificationFunctionParameterBinding> parameterBindings, int level = 0)
		{
			foreach (IGrouping<EdmMember, ModificationFunctionParameterBinding> grouping in from pm in parameterBindings
				where pm.MemberPath.AssociationSetEnd == null && pm.MemberPath.Members.Count<EdmMember>() > level
				group pm by pm.MemberPath.Members.ElementAt(level))
			{
				EdmProperty edmProperty = (EdmProperty)grouping.Key;
				if (edmProperty.IsComplexType)
				{
					this._xmlWriter.WriteStartElement("ComplexProperty");
					this._xmlWriter.WriteAttributeString("Name", edmProperty.Name);
					this._xmlWriter.WriteAttributeString("TypeName", this._entityTypeNamespace + "." + edmProperty.ComplexType.Name);
					this.WritePropertyParameterBindings(grouping, level + 1);
					this._xmlWriter.WriteEndElement();
				}
				else
				{
					foreach (ModificationFunctionParameterBinding modificationFunctionParameterBinding in grouping)
					{
						this.WriteScalarParameterElement(edmProperty, modificationFunctionParameterBinding);
					}
				}
			}
		}

		// Token: 0x06003E7E RID: 15998 RVA: 0x000CF398 File Offset: 0x000CD598
		private void WriteAssociationParameterBindings(IEnumerable<ModificationFunctionParameterBinding> parameterBindings)
		{
			using (IEnumerator<IGrouping<AssociationSetEnd, ModificationFunctionParameterBinding>> enumerator = (from pm in parameterBindings
				where pm.MemberPath.AssociationSetEnd != null
				group pm by pm.MemberPath.AssociationSetEnd).GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					IGrouping<AssociationSetEnd, ModificationFunctionParameterBinding> group = enumerator.Current;
					this._xmlWriter.WriteStartElement("AssociationEnd");
					AssociationSet parentAssociationSet = group.Key.ParentAssociationSet;
					this._xmlWriter.WriteAttributeString("AssociationSet", parentAssociationSet.Name);
					this._xmlWriter.WriteAttributeString("From", group.Key.Name);
					this._xmlWriter.WriteAttributeString("To", parentAssociationSet.AssociationSetEnds.Single((AssociationSetEnd ae) => ae != group.Key).Name);
					foreach (ModificationFunctionParameterBinding modificationFunctionParameterBinding in group)
					{
						this.WriteScalarParameterElement(modificationFunctionParameterBinding.MemberPath.Members.First<EdmMember>(), modificationFunctionParameterBinding);
					}
					this._xmlWriter.WriteEndElement();
				}
			}
		}

		// Token: 0x06003E7F RID: 15999 RVA: 0x000CF510 File Offset: 0x000CD710
		private void WriteResultBindings(IEnumerable<ModificationFunctionResultBinding> resultBindings)
		{
			foreach (ModificationFunctionResultBinding modificationFunctionResultBinding in resultBindings)
			{
				this._xmlWriter.WriteStartElement("ResultBinding");
				this._xmlWriter.WriteAttributeString("Name", modificationFunctionResultBinding.Property.Name);
				this._xmlWriter.WriteAttributeString("ColumnName", modificationFunctionResultBinding.ColumnName);
				this._xmlWriter.WriteEndElement();
			}
		}

		// Token: 0x06003E80 RID: 16000 RVA: 0x000CF5A0 File Offset: 0x000CD7A0
		private void WriteScalarParameterElement(EdmMember member, ModificationFunctionParameterBinding parameterBinding)
		{
			this._xmlWriter.WriteStartElement("ScalarProperty");
			this._xmlWriter.WriteAttributeString("Name", member.Name);
			this._xmlWriter.WriteAttributeString("ParameterName", parameterBinding.Parameter.Name);
			this._xmlWriter.WriteAttributeString("Version", parameterBinding.IsCurrent ? "Current" : "Original");
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E81 RID: 16001 RVA: 0x000CF620 File Offset: 0x000CD820
		private void WritePropertyMapping(PropertyMapping propertyMapping)
		{
			ScalarPropertyMapping scalarPropertyMapping = propertyMapping as ScalarPropertyMapping;
			if (scalarPropertyMapping != null)
			{
				this.WritePropertyMapping(scalarPropertyMapping);
				return;
			}
			ComplexPropertyMapping complexPropertyMapping = propertyMapping as ComplexPropertyMapping;
			if (complexPropertyMapping != null)
			{
				this.WritePropertyMapping(complexPropertyMapping);
			}
		}

		// Token: 0x06003E82 RID: 16002 RVA: 0x000CF650 File Offset: 0x000CD850
		private void WritePropertyMapping(ScalarPropertyMapping scalarPropertyMapping)
		{
			this.WriteScalarPropertyElement(scalarPropertyMapping.Property.Name, scalarPropertyMapping.Column.Name);
		}

		// Token: 0x06003E83 RID: 16003 RVA: 0x000CF670 File Offset: 0x000CD870
		private void WritePropertyMapping(ComplexPropertyMapping complexPropertyMapping)
		{
			this._xmlWriter.WriteStartElement("ComplexProperty");
			this._xmlWriter.WriteAttributeString("Name", complexPropertyMapping.Property.Name);
			this._xmlWriter.WriteAttributeString("TypeName", this._entityTypeNamespace + "." + complexPropertyMapping.Property.ComplexType.Name);
			foreach (PropertyMapping propertyMapping in complexPropertyMapping.TypeMappings.Single<ComplexTypeMapping>().PropertyMappings)
			{
				this.WritePropertyMapping(propertyMapping);
			}
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E84 RID: 16004 RVA: 0x000CF730 File Offset: 0x000CD930
		private static string GetEntityTypeName(string fullyQualifiedEntityTypeName, bool isHierarchyMapping)
		{
			if (isHierarchyMapping)
			{
				return "IsTypeOf(" + fullyQualifiedEntityTypeName + ")";
			}
			return fullyQualifiedEntityTypeName;
		}

		// Token: 0x06003E85 RID: 16005 RVA: 0x000CF748 File Offset: 0x000CD948
		private void WriteConditionElement(ConditionPropertyMapping condition)
		{
			this._xmlWriter.WriteStartElement("Condition");
			if (condition.IsNull != null)
			{
				this.WriteIsNullConditionAttribute(condition.IsNull.Value);
			}
			else
			{
				this.WriteConditionValue(condition.Value);
			}
			this._xmlWriter.WriteAttributeString("ColumnName", condition.Column.Name);
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x06003E86 RID: 16006 RVA: 0x000CF7BD File Offset: 0x000CD9BD
		private void WriteIsNullConditionAttribute(bool isNullValue)
		{
			this._xmlWriter.WriteAttributeString("IsNull", XmlSchemaWriter.GetLowerCaseStringFromBoolValue(isNullValue));
		}

		// Token: 0x06003E87 RID: 16007 RVA: 0x000CF7D8 File Offset: 0x000CD9D8
		private void WriteConditionValue(object conditionValue)
		{
			if (conditionValue is bool)
			{
				this._xmlWriter.WriteAttributeString("Value", ((bool)conditionValue) ? "1" : "0");
				return;
			}
			this._xmlWriter.WriteAttributeString("Value", conditionValue.ToString());
		}

		// Token: 0x06003E88 RID: 16008 RVA: 0x000CF828 File Offset: 0x000CDA28
		private void WriteScalarPropertyElement(string propertyName, string columnName)
		{
			this._xmlWriter.WriteStartElement("ScalarProperty");
			this._xmlWriter.WriteAttributeString("Name", propertyName);
			this._xmlWriter.WriteAttributeString("ColumnName", columnName);
			this._xmlWriter.WriteEndElement();
		}

		// Token: 0x0400152E RID: 5422
		private string _entityTypeNamespace;

		// Token: 0x0400152F RID: 5423
		private string _dbSchemaName;
	}
}
