﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000031 RID: 49
	public class Sys_Users_WarOrder_Info
	{
		// Token: 0x1700017A RID: 378
		// (get) Token: 0x06000324 RID: 804 RVA: 0x00003B2D File Offset: 0x00001D2D
		// (set) Token: 0x06000325 RID: 805 RVA: 0x00003B35 File Offset: 0x00001D35
		public int ID { get; set; }

		// Token: 0x1700017B RID: 379
		// (get) Token: 0x06000326 RID: 806 RVA: 0x00003B3E File Offset: 0x00001D3E
		// (set) Token: 0x06000327 RID: 807 RVA: 0x00003B46 File Offset: 0x00001D46
		public int UserID { get; set; }

		// Token: 0x1700017C RID: 380
		// (get) Token: 0x06000328 RID: 808 RVA: 0x00003B4F File Offset: 0x00001D4F
		// (set) Token: 0x06000329 RID: 809 RVA: 0x00003B57 File Offset: 0x00001D57
		public int Type { get; set; }

		// Token: 0x1700017D RID: 381
		// (get) Token: 0x0600032A RID: 810 RVA: 0x00003B60 File Offset: 0x00001D60
		// (set) Token: 0x0600032B RID: 811 RVA: 0x00003B68 File Offset: 0x00001D68
		public int PetID { get; set; }

		// Token: 0x1700017E RID: 382
		// (get) Token: 0x0600032C RID: 812 RVA: 0x00003B71 File Offset: 0x00001D71
		// (set) Token: 0x0600032D RID: 813 RVA: 0x00003B79 File Offset: 0x00001D79
		public int RechargeMoney { get; set; }

		// Token: 0x1700017F RID: 383
		// (get) Token: 0x0600032E RID: 814 RVA: 0x00003B82 File Offset: 0x00001D82
		// (set) Token: 0x0600032F RID: 815 RVA: 0x00003B8A File Offset: 0x00001D8A
		public int CurLimitWarID { get; set; }

		// Token: 0x17000180 RID: 384
		// (get) Token: 0x06000330 RID: 816 RVA: 0x00003B93 File Offset: 0x00001D93
		// (set) Token: 0x06000331 RID: 817 RVA: 0x00003B9B File Offset: 0x00001D9B
		public int ShopCoin { get; set; }

		// Token: 0x17000181 RID: 385
		// (get) Token: 0x06000332 RID: 818 RVA: 0x00003BA4 File Offset: 0x00001DA4
		// (set) Token: 0x06000333 RID: 819 RVA: 0x00003BAC File Offset: 0x00001DAC
		public DateTime LimitOpenTime { get; set; }

		// Token: 0x17000182 RID: 386
		// (get) Token: 0x06000334 RID: 820 RVA: 0x00003BB5 File Offset: 0x00001DB5
		// (set) Token: 0x06000335 RID: 821 RVA: 0x00003BBD File Offset: 0x00001DBD
		public DateTime LimitEndTime { get; set; }

		// Token: 0x17000183 RID: 387
		// (get) Token: 0x06000336 RID: 822 RVA: 0x00003BC6 File Offset: 0x00001DC6
		// (set) Token: 0x06000337 RID: 823 RVA: 0x00003BCE File Offset: 0x00001DCE
		public int WarLevel { get; set; }

		// Token: 0x17000184 RID: 388
		// (get) Token: 0x06000338 RID: 824 RVA: 0x00003BD7 File Offset: 0x00001DD7
		// (set) Token: 0x06000339 RID: 825 RVA: 0x00003BDF File Offset: 0x00001DDF
		public int Exp { get; set; }

		// Token: 0x17000185 RID: 389
		// (get) Token: 0x0600033A RID: 826 RVA: 0x00003BE8 File Offset: 0x00001DE8
		// (set) Token: 0x0600033B RID: 827 RVA: 0x00003BF0 File Offset: 0x00001DF0
		public int AwardGrade { get; set; }

		// Token: 0x17000186 RID: 390
		// (get) Token: 0x0600033C RID: 828 RVA: 0x00003BF9 File Offset: 0x00001DF9
		// (set) Token: 0x0600033D RID: 829 RVA: 0x00003C01 File Offset: 0x00001E01
		[Required]
		public string TaskIDs { get; set; }

		// Token: 0x17000187 RID: 391
		// (get) Token: 0x0600033E RID: 830 RVA: 0x00003C0A File Offset: 0x00001E0A
		// (set) Token: 0x0600033F RID: 831 RVA: 0x00003C12 File Offset: 0x00001E12
		[Required]
		public string TaskGets { get; set; }

		// Token: 0x17000188 RID: 392
		// (get) Token: 0x06000340 RID: 832 RVA: 0x00003C1B File Offset: 0x00001E1B
		// (set) Token: 0x06000341 RID: 833 RVA: 0x00003C23 File Offset: 0x00001E23
		[Required]
		public string TaskProgress { get; set; }
	}
}
