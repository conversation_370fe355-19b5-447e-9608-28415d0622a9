﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Common.CommandTrees.Internal
{
	// Token: 0x020006EF RID: 1775
	internal class ExpressionPrinter : TreePrinter
	{
		// Token: 0x060052B2 RID: 21170 RVA: 0x00127E14 File Offset: 0x00126014
		internal string Print(DbDeleteCommandTree tree)
		{
			TreeNode treeNode;
			if (tree.Target != null)
			{
				treeNode = this._visitor.VisitBinding("Target", tree.Target);
			}
			else
			{
				treeNode = new TreeNode("Target", new TreeNode[0]);
			}
			TreeNode treeNode2;
			if (tree.Predicate != null)
			{
				treeNode2 = this._visitor.VisitExpression("Predicate", tree.Predicate);
			}
			else
			{
				treeNode2 = new TreeNode("Predicate", new TreeNode[0]);
			}
			return this.Print(new TreeNode("DbDeleteCommandTree", new TreeNode[]
			{
				ExpressionPrinter.CreateParametersNode(tree),
				treeNode,
				treeNode2
			}));
		}

		// Token: 0x060052B3 RID: 21171 RVA: 0x00127EAC File Offset: 0x001260AC
		internal string Print(DbFunctionCommandTree tree)
		{
			TreeNode treeNode = new TreeNode("EdmFunction", new TreeNode[0]);
			if (tree.EdmFunction != null)
			{
				treeNode.Children.Add(this._visitor.VisitFunction(tree.EdmFunction, null));
			}
			TreeNode treeNode2 = new TreeNode("ResultType", new TreeNode[0]);
			if (tree.ResultType != null)
			{
				ExpressionPrinter.PrinterVisitor.AppendTypeSpecifier(treeNode2, tree.ResultType);
			}
			return this.Print(new TreeNode("DbFunctionCommandTree", new TreeNode[]
			{
				ExpressionPrinter.CreateParametersNode(tree),
				treeNode,
				treeNode2
			}));
		}

		// Token: 0x060052B4 RID: 21172 RVA: 0x00127F3C File Offset: 0x0012613C
		internal string Print(DbInsertCommandTree tree)
		{
			TreeNode treeNode = null;
			if (tree.Target != null)
			{
				treeNode = this._visitor.VisitBinding("Target", tree.Target);
			}
			else
			{
				treeNode = new TreeNode("Target", new TreeNode[0]);
			}
			TreeNode treeNode2 = new TreeNode("SetClauses", new TreeNode[0]);
			foreach (DbModificationClause dbModificationClause in tree.SetClauses)
			{
				if (dbModificationClause != null)
				{
					treeNode2.Children.Add(dbModificationClause.Print(this._visitor));
				}
			}
			TreeNode treeNode3;
			if (tree.Returning != null)
			{
				treeNode3 = new TreeNode("Returning", new TreeNode[] { this._visitor.VisitExpression(tree.Returning) });
			}
			else
			{
				treeNode3 = new TreeNode("Returning", new TreeNode[0]);
			}
			return this.Print(new TreeNode("DbInsertCommandTree", new TreeNode[]
			{
				ExpressionPrinter.CreateParametersNode(tree),
				treeNode,
				treeNode2,
				treeNode3
			}));
		}

		// Token: 0x060052B5 RID: 21173 RVA: 0x00128054 File Offset: 0x00126254
		internal string Print(DbUpdateCommandTree tree)
		{
			TreeNode treeNode = null;
			if (tree.Target != null)
			{
				treeNode = this._visitor.VisitBinding("Target", tree.Target);
			}
			else
			{
				treeNode = new TreeNode("Target", new TreeNode[0]);
			}
			TreeNode treeNode2 = new TreeNode("SetClauses", new TreeNode[0]);
			foreach (DbModificationClause dbModificationClause in tree.SetClauses)
			{
				if (dbModificationClause != null)
				{
					treeNode2.Children.Add(dbModificationClause.Print(this._visitor));
				}
			}
			TreeNode treeNode3;
			if (tree.Predicate != null)
			{
				treeNode3 = new TreeNode("Predicate", new TreeNode[] { this._visitor.VisitExpression(tree.Predicate) });
			}
			else
			{
				treeNode3 = new TreeNode("Predicate", new TreeNode[0]);
			}
			TreeNode treeNode4;
			if (tree.Returning != null)
			{
				treeNode4 = new TreeNode("Returning", new TreeNode[] { this._visitor.VisitExpression(tree.Returning) });
			}
			else
			{
				treeNode4 = new TreeNode("Returning", new TreeNode[0]);
			}
			return this.Print(new TreeNode("DbUpdateCommandTree", new TreeNode[]
			{
				ExpressionPrinter.CreateParametersNode(tree),
				treeNode,
				treeNode2,
				treeNode3,
				treeNode4
			}));
		}

		// Token: 0x060052B6 RID: 21174 RVA: 0x001281B0 File Offset: 0x001263B0
		internal string Print(DbQueryCommandTree tree)
		{
			TreeNode treeNode = new TreeNode("Query", new TreeNode[0]);
			if (tree.Query != null)
			{
				ExpressionPrinter.PrinterVisitor.AppendTypeSpecifier(treeNode, tree.Query.ResultType);
				treeNode.Children.Add(this._visitor.VisitExpression(tree.Query));
			}
			return this.Print(new TreeNode("DbQueryCommandTree", new TreeNode[]
			{
				ExpressionPrinter.CreateParametersNode(tree),
				treeNode
			}));
		}

		// Token: 0x060052B7 RID: 21175 RVA: 0x00128228 File Offset: 0x00126428
		private static TreeNode CreateParametersNode(DbCommandTree tree)
		{
			TreeNode treeNode = new TreeNode("Parameters", new TreeNode[0]);
			foreach (KeyValuePair<string, TypeUsage> keyValuePair in tree.Parameters)
			{
				TreeNode treeNode2 = new TreeNode(keyValuePair.Key, new TreeNode[0]);
				ExpressionPrinter.PrinterVisitor.AppendTypeSpecifier(treeNode2, keyValuePair.Value);
				treeNode.Children.Add(treeNode2);
			}
			return treeNode;
		}

		// Token: 0x04001DE6 RID: 7654
		private readonly ExpressionPrinter.PrinterVisitor _visitor = new ExpressionPrinter.PrinterVisitor();

		// Token: 0x02000C99 RID: 3225
		private class PrinterVisitor : DbExpressionVisitor<TreeNode>
		{
			// Token: 0x06006C16 RID: 27670 RVA: 0x00170034 File Offset: 0x0016E234
			private static Dictionary<DbExpressionKind, string> InitializeOpMap()
			{
				Dictionary<DbExpressionKind, string> dictionary = new Dictionary<DbExpressionKind, string>(12);
				dictionary[DbExpressionKind.Divide] = "/";
				dictionary[DbExpressionKind.Modulo] = "%";
				dictionary[DbExpressionKind.Multiply] = "*";
				dictionary[DbExpressionKind.Plus] = "+";
				dictionary[DbExpressionKind.Minus] = "-";
				dictionary[DbExpressionKind.UnaryMinus] = "-";
				dictionary[DbExpressionKind.Equals] = "=";
				dictionary[DbExpressionKind.LessThan] = "<";
				dictionary[DbExpressionKind.LessThanOrEquals] = "<=";
				dictionary[DbExpressionKind.GreaterThan] = ">";
				dictionary[DbExpressionKind.GreaterThanOrEquals] = ">=";
				dictionary[DbExpressionKind.NotEquals] = "<>";
				return dictionary;
			}

			// Token: 0x06006C17 RID: 27671 RVA: 0x001700E4 File Offset: 0x0016E2E4
			internal TreeNode VisitExpression(DbExpression expr)
			{
				return expr.Accept<TreeNode>(this);
			}

			// Token: 0x06006C18 RID: 27672 RVA: 0x001700ED File Offset: 0x0016E2ED
			internal TreeNode VisitExpression(string name, DbExpression expr)
			{
				return new TreeNode(name, new TreeNode[] { expr.Accept<TreeNode>(this) });
			}

			// Token: 0x06006C19 RID: 27673 RVA: 0x00170105 File Offset: 0x0016E305
			internal TreeNode VisitBinding(string propName, DbExpressionBinding binding)
			{
				return this.VisitWithLabel(propName, binding.VariableName, binding.Expression);
			}

			// Token: 0x06006C1A RID: 27674 RVA: 0x0017011C File Offset: 0x0016E31C
			internal TreeNode VisitFunction(EdmFunction func, IList<DbExpression> args)
			{
				TreeNode treeNode = new TreeNode();
				ExpressionPrinter.PrinterVisitor.AppendFullName(treeNode.Text, func);
				ExpressionPrinter.PrinterVisitor.AppendParameters(treeNode, func.Parameters.Select((FunctionParameter fp) => new KeyValuePair<string, TypeUsage>(fp.Name, fp.TypeUsage)));
				if (args != null)
				{
					this.AppendArguments(treeNode, func.Parameters.Select((FunctionParameter fp) => fp.Name).ToArray<string>(), args);
				}
				return treeNode;
			}

			// Token: 0x06006C1B RID: 27675 RVA: 0x001701A6 File Offset: 0x0016E3A6
			private static TreeNode NodeFromExpression(DbExpression expr)
			{
				return new TreeNode(Enum.GetName(typeof(DbExpressionKind), expr.ExpressionKind), new TreeNode[0]);
			}

			// Token: 0x06006C1C RID: 27676 RVA: 0x001701D0 File Offset: 0x0016E3D0
			private static void AppendParameters(TreeNode node, IEnumerable<KeyValuePair<string, TypeUsage>> paramInfos)
			{
				node.Text.Append("(");
				int num = 0;
				foreach (KeyValuePair<string, TypeUsage> keyValuePair in paramInfos)
				{
					if (num > 0)
					{
						node.Text.Append(", ");
					}
					ExpressionPrinter.PrinterVisitor.AppendType(node, keyValuePair.Value);
					node.Text.Append(" ");
					node.Text.Append(keyValuePair.Key);
					num++;
				}
				node.Text.Append(")");
			}

			// Token: 0x06006C1D RID: 27677 RVA: 0x00170280 File Offset: 0x0016E480
			internal static void AppendTypeSpecifier(TreeNode node, TypeUsage type)
			{
				node.Text.Append(" : ");
				ExpressionPrinter.PrinterVisitor.AppendType(node, type);
			}

			// Token: 0x06006C1E RID: 27678 RVA: 0x0017029A File Offset: 0x0016E49A
			internal static void AppendType(TreeNode node, TypeUsage type)
			{
				ExpressionPrinter.PrinterVisitor.BuildTypeName(node.Text, type);
			}

			// Token: 0x06006C1F RID: 27679 RVA: 0x001702A8 File Offset: 0x0016E4A8
			private static void BuildTypeName(StringBuilder text, TypeUsage type)
			{
				RowType rowType = type.EdmType as RowType;
				CollectionType collectionType = type.EdmType as CollectionType;
				RefType refType = type.EdmType as RefType;
				if (TypeSemantics.IsPrimitiveType(type))
				{
					text.Append(type);
					return;
				}
				if (collectionType != null)
				{
					text.Append("Collection{");
					ExpressionPrinter.PrinterVisitor.BuildTypeName(text, collectionType.TypeUsage);
					text.Append("}");
					return;
				}
				if (refType != null)
				{
					text.Append("Ref<");
					ExpressionPrinter.PrinterVisitor.AppendFullName(text, refType.ElementType);
					text.Append(">");
					return;
				}
				if (rowType != null)
				{
					text.Append("Record[");
					int num = 0;
					foreach (EdmProperty edmProperty in rowType.Properties)
					{
						text.Append("'");
						text.Append(edmProperty.Name);
						text.Append("'");
						text.Append("=");
						ExpressionPrinter.PrinterVisitor.BuildTypeName(text, edmProperty.TypeUsage);
						num++;
						if (num < rowType.Properties.Count)
						{
							text.Append(", ");
						}
					}
					text.Append("]");
					return;
				}
				if (!string.IsNullOrEmpty(type.EdmType.NamespaceName))
				{
					text.Append(type.EdmType.NamespaceName);
					text.Append(".");
				}
				text.Append(type.EdmType.Name);
			}

			// Token: 0x06006C20 RID: 27680 RVA: 0x0017043C File Offset: 0x0016E63C
			private static void AppendFullName(StringBuilder text, EdmType type)
			{
				if (BuiltInTypeKind.RowType != type.BuiltInTypeKind && !string.IsNullOrEmpty(type.NamespaceName))
				{
					text.Append(type.NamespaceName);
					text.Append(".");
				}
				text.Append(type.Name);
			}

			// Token: 0x06006C21 RID: 27681 RVA: 0x0017047C File Offset: 0x0016E67C
			private List<TreeNode> VisitParams(IList<string> paramInfo, IList<DbExpression> args)
			{
				List<TreeNode> list = new List<TreeNode>();
				for (int i = 0; i < paramInfo.Count; i++)
				{
					list.Add(new TreeNode(paramInfo[i], new TreeNode[0])
					{
						Children = { this.VisitExpression(args[i]) }
					});
				}
				return list;
			}

			// Token: 0x06006C22 RID: 27682 RVA: 0x001704D3 File Offset: 0x0016E6D3
			private void AppendArguments(TreeNode node, IList<string> paramNames, IList<DbExpression> args)
			{
				if (paramNames.Count > 0)
				{
					node.Children.Add(new TreeNode("Arguments", this.VisitParams(paramNames, args)));
				}
			}

			// Token: 0x06006C23 RID: 27683 RVA: 0x001704FC File Offset: 0x0016E6FC
			private TreeNode VisitWithLabel(string label, string name, DbExpression def)
			{
				TreeNode treeNode = new TreeNode(label, new TreeNode[0]);
				treeNode.Text.Append(" : '");
				treeNode.Text.Append(name);
				treeNode.Text.Append("'");
				treeNode.Children.Add(this.VisitExpression(def));
				return treeNode;
			}

			// Token: 0x06006C24 RID: 27684 RVA: 0x00170558 File Offset: 0x0016E758
			private TreeNode VisitBindingList(string propName, IList<DbExpressionBinding> bindings)
			{
				List<TreeNode> list = new List<TreeNode>();
				for (int i = 0; i < bindings.Count; i++)
				{
					list.Add(this.VisitBinding(StringUtil.FormatIndex(propName, i), bindings[i]));
				}
				return new TreeNode(propName, list);
			}

			// Token: 0x06006C25 RID: 27685 RVA: 0x001705A0 File Offset: 0x0016E7A0
			private TreeNode VisitGroupBinding(DbGroupExpressionBinding groupBinding)
			{
				TreeNode treeNode = this.VisitExpression(groupBinding.Expression);
				TreeNode treeNode2 = new TreeNode();
				treeNode2.Children.Add(treeNode);
				treeNode2.Text.AppendFormat(CultureInfo.InvariantCulture, "Input : '{0}', '{1}'", new object[] { groupBinding.VariableName, groupBinding.GroupVariableName });
				return treeNode2;
			}

			// Token: 0x06006C26 RID: 27686 RVA: 0x001705FC File Offset: 0x0016E7FC
			private TreeNode Visit(string name, params DbExpression[] exprs)
			{
				TreeNode treeNode = new TreeNode(name, new TreeNode[0]);
				foreach (DbExpression dbExpression in exprs)
				{
					treeNode.Children.Add(this.VisitExpression(dbExpression));
				}
				return treeNode;
			}

			// Token: 0x06006C27 RID: 27687 RVA: 0x00170640 File Offset: 0x0016E840
			private TreeNode VisitInfix(DbExpression left, string name, DbExpression right)
			{
				if (this._infix)
				{
					return new TreeNode("", new TreeNode[0])
					{
						Children = 
						{
							this.VisitExpression(left),
							new TreeNode(name, new TreeNode[0]),
							this.VisitExpression(right)
						}
					};
				}
				return this.Visit(name, new DbExpression[] { left, right });
			}

			// Token: 0x06006C28 RID: 27688 RVA: 0x001706B8 File Offset: 0x0016E8B8
			private TreeNode VisitUnary(DbUnaryExpression expr)
			{
				return this.VisitUnary(expr, false);
			}

			// Token: 0x06006C29 RID: 27689 RVA: 0x001706C4 File Offset: 0x0016E8C4
			private TreeNode VisitUnary(DbUnaryExpression expr, bool appendType)
			{
				TreeNode treeNode = ExpressionPrinter.PrinterVisitor.NodeFromExpression(expr);
				if (appendType)
				{
					ExpressionPrinter.PrinterVisitor.AppendTypeSpecifier(treeNode, expr.ResultType);
				}
				treeNode.Children.Add(this.VisitExpression(expr.Argument));
				return treeNode;
			}

			// Token: 0x06006C2A RID: 27690 RVA: 0x001706FF File Offset: 0x0016E8FF
			private TreeNode VisitBinary(DbBinaryExpression expr)
			{
				TreeNode treeNode = ExpressionPrinter.PrinterVisitor.NodeFromExpression(expr);
				treeNode.Children.Add(this.VisitExpression(expr.Left));
				treeNode.Children.Add(this.VisitExpression(expr.Right));
				return treeNode;
			}

			// Token: 0x06006C2B RID: 27691 RVA: 0x00170735 File Offset: 0x0016E935
			public override TreeNode Visit(DbExpression e)
			{
				Check.NotNull<DbExpression>(e, "e");
				throw new NotSupportedException(Strings.Cqt_General_UnsupportedExpression(e.GetType().FullName));
			}

			// Token: 0x06006C2C RID: 27692 RVA: 0x00170758 File Offset: 0x0016E958
			public override TreeNode Visit(DbConstantExpression e)
			{
				Check.NotNull<DbConstantExpression>(e, "e");
				TreeNode treeNode = new TreeNode();
				string text = e.Value as string;
				if (text != null)
				{
					text = text.Replace("\r\n", "\\r\\n");
					int num = text.Length;
					if (this._maxStringLength > 0)
					{
						num = Math.Min(text.Length, this._maxStringLength);
					}
					treeNode.Text.Append("'");
					treeNode.Text.Append(text, 0, num);
					if (text.Length > num)
					{
						treeNode.Text.Append("...");
					}
					treeNode.Text.Append("'");
				}
				else
				{
					treeNode.Text.Append(e.Value);
				}
				return treeNode;
			}

			// Token: 0x06006C2D RID: 27693 RVA: 0x0017081C File Offset: 0x0016EA1C
			public override TreeNode Visit(DbNullExpression e)
			{
				Check.NotNull<DbNullExpression>(e, "e");
				return new TreeNode("null", new TreeNode[0]);
			}

			// Token: 0x06006C2E RID: 27694 RVA: 0x0017083A File Offset: 0x0016EA3A
			public override TreeNode Visit(DbVariableReferenceExpression e)
			{
				Check.NotNull<DbVariableReferenceExpression>(e, "e");
				TreeNode treeNode = new TreeNode();
				treeNode.Text.AppendFormat("Var({0})", e.VariableName);
				return treeNode;
			}

			// Token: 0x06006C2F RID: 27695 RVA: 0x00170864 File Offset: 0x0016EA64
			public override TreeNode Visit(DbParameterReferenceExpression e)
			{
				Check.NotNull<DbParameterReferenceExpression>(e, "e");
				TreeNode treeNode = new TreeNode();
				treeNode.Text.AppendFormat("@{0}", e.ParameterName);
				return treeNode;
			}

			// Token: 0x06006C30 RID: 27696 RVA: 0x0017088E File Offset: 0x0016EA8E
			public override TreeNode Visit(DbFunctionExpression e)
			{
				Check.NotNull<DbFunctionExpression>(e, "e");
				return this.VisitFunction(e.Function, e.Arguments);
			}

			// Token: 0x06006C31 RID: 27697 RVA: 0x001708B0 File Offset: 0x0016EAB0
			public override TreeNode Visit(DbLambdaExpression expression)
			{
				Check.NotNull<DbLambdaExpression>(expression, "expression");
				TreeNode treeNode = new TreeNode();
				treeNode.Text.Append("Lambda");
				ExpressionPrinter.PrinterVisitor.AppendParameters(treeNode, expression.Lambda.Variables.Select((DbVariableReferenceExpression v) => new KeyValuePair<string, TypeUsage>(v.VariableName, v.ResultType)));
				this.AppendArguments(treeNode, expression.Lambda.Variables.Select((DbVariableReferenceExpression v) => v.VariableName).ToArray<string>(), expression.Arguments);
				treeNode.Children.Add(this.Visit("Body", new DbExpression[] { expression.Lambda.Body }));
				return treeNode;
			}

			// Token: 0x06006C32 RID: 27698 RVA: 0x00170984 File Offset: 0x0016EB84
			public override TreeNode Visit(DbPropertyExpression e)
			{
				Check.NotNull<DbPropertyExpression>(e, "e");
				TreeNode treeNode = null;
				if (e.Instance != null)
				{
					treeNode = this.VisitExpression(e.Instance);
					if (e.Instance.ExpressionKind == DbExpressionKind.VariableReference || (e.Instance.ExpressionKind == DbExpressionKind.Property && treeNode.Children.Count == 0))
					{
						treeNode.Text.Append(".");
						treeNode.Text.Append(e.Property.Name);
						return treeNode;
					}
				}
				TreeNode treeNode2 = new TreeNode(".", new TreeNode[0]);
				EdmProperty edmProperty = e.Property as EdmProperty;
				if (edmProperty != null && !(edmProperty.DeclaringType is RowType))
				{
					ExpressionPrinter.PrinterVisitor.AppendFullName(treeNode2.Text, edmProperty.DeclaringType);
					treeNode2.Text.Append(".");
				}
				treeNode2.Text.Append(e.Property.Name);
				if (treeNode != null)
				{
					treeNode2.Children.Add(new TreeNode("Instance", new TreeNode[] { treeNode }));
				}
				return treeNode2;
			}

			// Token: 0x06006C33 RID: 27699 RVA: 0x00170A92 File Offset: 0x0016EC92
			public override TreeNode Visit(DbComparisonExpression e)
			{
				Check.NotNull<DbComparisonExpression>(e, "e");
				return this.VisitInfix(e.Left, ExpressionPrinter.PrinterVisitor._opMap[e.ExpressionKind], e.Right);
			}

			// Token: 0x06006C34 RID: 27700 RVA: 0x00170AC2 File Offset: 0x0016ECC2
			public override TreeNode Visit(DbLikeExpression e)
			{
				Check.NotNull<DbLikeExpression>(e, "e");
				return this.Visit("Like", new DbExpression[] { e.Argument, e.Pattern, e.Escape });
			}

			// Token: 0x06006C35 RID: 27701 RVA: 0x00170AFC File Offset: 0x0016ECFC
			public override TreeNode Visit(DbLimitExpression e)
			{
				Check.NotNull<DbLimitExpression>(e, "e");
				return this.Visit(e.WithTies ? "LimitWithTies" : "Limit", new DbExpression[] { e.Argument, e.Limit });
			}

			// Token: 0x06006C36 RID: 27702 RVA: 0x00170B3C File Offset: 0x0016ED3C
			public override TreeNode Visit(DbIsNullExpression e)
			{
				Check.NotNull<DbIsNullExpression>(e, "e");
				return this.VisitUnary(e);
			}

			// Token: 0x06006C37 RID: 27703 RVA: 0x00170B54 File Offset: 0x0016ED54
			public override TreeNode Visit(DbArithmeticExpression e)
			{
				Check.NotNull<DbArithmeticExpression>(e, "e");
				if (DbExpressionKind.UnaryMinus == e.ExpressionKind)
				{
					return this.Visit(ExpressionPrinter.PrinterVisitor._opMap[e.ExpressionKind], new DbExpression[] { e.Arguments[0] });
				}
				return this.VisitInfix(e.Arguments[0], ExpressionPrinter.PrinterVisitor._opMap[e.ExpressionKind], e.Arguments[1]);
			}

			// Token: 0x06006C38 RID: 27704 RVA: 0x00170BD1 File Offset: 0x0016EDD1
			public override TreeNode Visit(DbAndExpression e)
			{
				Check.NotNull<DbAndExpression>(e, "e");
				return this.VisitInfix(e.Left, "And", e.Right);
			}

			// Token: 0x06006C39 RID: 27705 RVA: 0x00170BF6 File Offset: 0x0016EDF6
			public override TreeNode Visit(DbOrExpression e)
			{
				Check.NotNull<DbOrExpression>(e, "e");
				return this.VisitInfix(e.Left, "Or", e.Right);
			}

			// Token: 0x06006C3A RID: 27706 RVA: 0x00170C1C File Offset: 0x0016EE1C
			public override TreeNode Visit(DbInExpression e)
			{
				Check.NotNull<DbInExpression>(e, "e");
				TreeNode treeNode;
				if (this._infix)
				{
					treeNode = new TreeNode(string.Empty, new TreeNode[0]);
					treeNode.Children.Add(this.VisitExpression(e.Item));
					treeNode.Children.Add(new TreeNode("In", new TreeNode[0]));
				}
				else
				{
					treeNode = new TreeNode("In", new TreeNode[0]);
					treeNode.Children.Add(this.VisitExpression(e.Item));
				}
				foreach (DbExpression dbExpression in e.List)
				{
					treeNode.Children.Add(this.VisitExpression(dbExpression));
				}
				return treeNode;
			}

			// Token: 0x06006C3B RID: 27707 RVA: 0x00170CF8 File Offset: 0x0016EEF8
			public override TreeNode Visit(DbNotExpression e)
			{
				Check.NotNull<DbNotExpression>(e, "e");
				return this.VisitUnary(e);
			}

			// Token: 0x06006C3C RID: 27708 RVA: 0x00170D0D File Offset: 0x0016EF0D
			public override TreeNode Visit(DbDistinctExpression e)
			{
				Check.NotNull<DbDistinctExpression>(e, "e");
				return this.VisitUnary(e);
			}

			// Token: 0x06006C3D RID: 27709 RVA: 0x00170D22 File Offset: 0x0016EF22
			public override TreeNode Visit(DbElementExpression e)
			{
				Check.NotNull<DbElementExpression>(e, "e");
				return this.VisitUnary(e, true);
			}

			// Token: 0x06006C3E RID: 27710 RVA: 0x00170D38 File Offset: 0x0016EF38
			public override TreeNode Visit(DbIsEmptyExpression e)
			{
				Check.NotNull<DbIsEmptyExpression>(e, "e");
				return this.VisitUnary(e);
			}

			// Token: 0x06006C3F RID: 27711 RVA: 0x00170D4D File Offset: 0x0016EF4D
			public override TreeNode Visit(DbUnionAllExpression e)
			{
				Check.NotNull<DbUnionAllExpression>(e, "e");
				return this.VisitBinary(e);
			}

			// Token: 0x06006C40 RID: 27712 RVA: 0x00170D62 File Offset: 0x0016EF62
			public override TreeNode Visit(DbIntersectExpression e)
			{
				Check.NotNull<DbIntersectExpression>(e, "e");
				return this.VisitBinary(e);
			}

			// Token: 0x06006C41 RID: 27713 RVA: 0x00170D77 File Offset: 0x0016EF77
			public override TreeNode Visit(DbExceptExpression e)
			{
				Check.NotNull<DbExceptExpression>(e, "e");
				return this.VisitBinary(e);
			}

			// Token: 0x06006C42 RID: 27714 RVA: 0x00170D8C File Offset: 0x0016EF8C
			private TreeNode VisitCastOrTreat(string op, DbUnaryExpression e)
			{
				TreeNode treeNode = this.VisitExpression(e.Argument);
				TreeNode treeNode2;
				if (treeNode.Children.Count == 0)
				{
					treeNode.Text.Insert(0, op);
					treeNode.Text.Insert(op.Length, '(');
					treeNode.Text.Append(" As ");
					ExpressionPrinter.PrinterVisitor.AppendType(treeNode, e.ResultType);
					treeNode.Text.Append(")");
					treeNode2 = treeNode;
				}
				else
				{
					treeNode2 = new TreeNode(op, new TreeNode[0]);
					ExpressionPrinter.PrinterVisitor.AppendTypeSpecifier(treeNode2, e.ResultType);
					treeNode2.Children.Add(treeNode);
				}
				return treeNode2;
			}

			// Token: 0x06006C43 RID: 27715 RVA: 0x00170E2F File Offset: 0x0016F02F
			public override TreeNode Visit(DbTreatExpression e)
			{
				Check.NotNull<DbTreatExpression>(e, "e");
				return this.VisitCastOrTreat("Treat", e);
			}

			// Token: 0x06006C44 RID: 27716 RVA: 0x00170E49 File Offset: 0x0016F049
			public override TreeNode Visit(DbCastExpression e)
			{
				Check.NotNull<DbCastExpression>(e, "e");
				return this.VisitCastOrTreat("Cast", e);
			}

			// Token: 0x06006C45 RID: 27717 RVA: 0x00170E64 File Offset: 0x0016F064
			public override TreeNode Visit(DbIsOfExpression e)
			{
				Check.NotNull<DbIsOfExpression>(e, "e");
				TreeNode treeNode = new TreeNode();
				if (DbExpressionKind.IsOfOnly == e.ExpressionKind)
				{
					treeNode.Text.Append("IsOfOnly");
				}
				else
				{
					treeNode.Text.Append("IsOf");
				}
				ExpressionPrinter.PrinterVisitor.AppendTypeSpecifier(treeNode, e.OfType);
				treeNode.Children.Add(this.VisitExpression(e.Argument));
				return treeNode;
			}

			// Token: 0x06006C46 RID: 27718 RVA: 0x00170ED8 File Offset: 0x0016F0D8
			public override TreeNode Visit(DbOfTypeExpression e)
			{
				Check.NotNull<DbOfTypeExpression>(e, "e");
				TreeNode treeNode = new TreeNode((e.ExpressionKind == DbExpressionKind.OfTypeOnly) ? "OfTypeOnly" : "OfType", new TreeNode[0]);
				ExpressionPrinter.PrinterVisitor.AppendTypeSpecifier(treeNode, e.OfType);
				treeNode.Children.Add(this.VisitExpression(e.Argument));
				return treeNode;
			}

			// Token: 0x06006C47 RID: 27719 RVA: 0x00170F38 File Offset: 0x0016F138
			public override TreeNode Visit(DbCaseExpression e)
			{
				Check.NotNull<DbCaseExpression>(e, "e");
				TreeNode treeNode = new TreeNode("Case", new TreeNode[0]);
				for (int i = 0; i < e.When.Count; i++)
				{
					treeNode.Children.Add(this.Visit("When", new DbExpression[] { e.When[i] }));
					treeNode.Children.Add(this.Visit("Then", new DbExpression[] { e.Then[i] }));
				}
				treeNode.Children.Add(this.Visit("Else", new DbExpression[] { e.Else }));
				return treeNode;
			}

			// Token: 0x06006C48 RID: 27720 RVA: 0x00170FF4 File Offset: 0x0016F1F4
			public override TreeNode Visit(DbNewInstanceExpression e)
			{
				Check.NotNull<DbNewInstanceExpression>(e, "e");
				TreeNode treeNode = ExpressionPrinter.PrinterVisitor.NodeFromExpression(e);
				ExpressionPrinter.PrinterVisitor.AppendTypeSpecifier(treeNode, e.ResultType);
				if (BuiltInTypeKind.CollectionType == e.ResultType.EdmType.BuiltInTypeKind)
				{
					using (IEnumerator<DbExpression> enumerator = e.Arguments.GetEnumerator())
					{
						while (enumerator.MoveNext())
						{
							DbExpression dbExpression = enumerator.Current;
							treeNode.Children.Add(this.VisitExpression(dbExpression));
						}
						return treeNode;
					}
				}
				string text = ((BuiltInTypeKind.RowType == e.ResultType.EdmType.BuiltInTypeKind) ? "Column" : "Property");
				IList<EdmProperty> properties = TypeHelpers.GetProperties(e.ResultType);
				for (int i = 0; i < properties.Count; i++)
				{
					treeNode.Children.Add(this.VisitWithLabel(text, properties[i].Name, e.Arguments[i]));
				}
				if (BuiltInTypeKind.EntityType == e.ResultType.EdmType.BuiltInTypeKind && e.HasRelatedEntityReferences)
				{
					TreeNode treeNode2 = new TreeNode("RelatedEntityReferences", new TreeNode[0]);
					foreach (DbRelatedEntityRef dbRelatedEntityRef in e.RelatedEntityReferences)
					{
						TreeNode treeNode3 = ExpressionPrinter.PrinterVisitor.CreateNavigationNode(dbRelatedEntityRef.SourceEnd, dbRelatedEntityRef.TargetEnd);
						treeNode3.Children.Add(ExpressionPrinter.PrinterVisitor.CreateRelationshipNode((RelationshipType)dbRelatedEntityRef.SourceEnd.DeclaringType));
						treeNode3.Children.Add(this.VisitExpression(dbRelatedEntityRef.TargetEntityReference));
						treeNode2.Children.Add(treeNode3);
					}
					treeNode.Children.Add(treeNode2);
				}
				return treeNode;
			}

			// Token: 0x06006C49 RID: 27721 RVA: 0x001711D0 File Offset: 0x0016F3D0
			public override TreeNode Visit(DbRefExpression e)
			{
				Check.NotNull<DbRefExpression>(e, "e");
				TreeNode treeNode = new TreeNode("Ref", new TreeNode[0]);
				treeNode.Text.Append("<");
				ExpressionPrinter.PrinterVisitor.AppendFullName(treeNode.Text, TypeHelpers.GetEdmType<RefType>(e.ResultType).ElementType);
				treeNode.Text.Append(">");
				TreeNode treeNode2 = new TreeNode("EntitySet : ", new TreeNode[0]);
				treeNode2.Text.Append(e.EntitySet.EntityContainer.Name);
				treeNode2.Text.Append(".");
				treeNode2.Text.Append(e.EntitySet.Name);
				treeNode.Children.Add(treeNode2);
				treeNode.Children.Add(this.Visit("Keys", new DbExpression[] { e.Argument }));
				return treeNode;
			}

			// Token: 0x06006C4A RID: 27722 RVA: 0x001712BE File Offset: 0x0016F4BE
			private static TreeNode CreateRelationshipNode(RelationshipType relType)
			{
				TreeNode treeNode = new TreeNode("Relationship", new TreeNode[0]);
				treeNode.Text.Append(" : ");
				ExpressionPrinter.PrinterVisitor.AppendFullName(treeNode.Text, relType);
				return treeNode;
			}

			// Token: 0x06006C4B RID: 27723 RVA: 0x001712F0 File Offset: 0x0016F4F0
			private static TreeNode CreateNavigationNode(RelationshipEndMember fromEnd, RelationshipEndMember toEnd)
			{
				TreeNode treeNode = new TreeNode();
				treeNode.Text.Append("Navigation : ");
				treeNode.Text.Append(fromEnd.Name);
				treeNode.Text.Append(" -> ");
				treeNode.Text.Append(toEnd.Name);
				return treeNode;
			}

			// Token: 0x06006C4C RID: 27724 RVA: 0x00171348 File Offset: 0x0016F548
			public override TreeNode Visit(DbRelationshipNavigationExpression e)
			{
				Check.NotNull<DbRelationshipNavigationExpression>(e, "e");
				TreeNode treeNode = ExpressionPrinter.PrinterVisitor.NodeFromExpression(e);
				treeNode.Children.Add(ExpressionPrinter.PrinterVisitor.CreateRelationshipNode(e.Relationship));
				treeNode.Children.Add(ExpressionPrinter.PrinterVisitor.CreateNavigationNode(e.NavigateFrom, e.NavigateTo));
				treeNode.Children.Add(this.Visit("Source", new DbExpression[] { e.NavigationSource }));
				return treeNode;
			}

			// Token: 0x06006C4D RID: 27725 RVA: 0x001713C0 File Offset: 0x0016F5C0
			public override TreeNode Visit(DbDerefExpression e)
			{
				Check.NotNull<DbDerefExpression>(e, "e");
				return this.VisitUnary(e);
			}

			// Token: 0x06006C4E RID: 27726 RVA: 0x001713D5 File Offset: 0x0016F5D5
			public override TreeNode Visit(DbRefKeyExpression e)
			{
				Check.NotNull<DbRefKeyExpression>(e, "e");
				return this.VisitUnary(e, true);
			}

			// Token: 0x06006C4F RID: 27727 RVA: 0x001713EB File Offset: 0x0016F5EB
			public override TreeNode Visit(DbEntityRefExpression e)
			{
				Check.NotNull<DbEntityRefExpression>(e, "e");
				return this.VisitUnary(e, true);
			}

			// Token: 0x06006C50 RID: 27728 RVA: 0x00171404 File Offset: 0x0016F604
			public override TreeNode Visit(DbScanExpression e)
			{
				Check.NotNull<DbScanExpression>(e, "e");
				TreeNode treeNode = ExpressionPrinter.PrinterVisitor.NodeFromExpression(e);
				treeNode.Text.Append(" : ");
				treeNode.Text.Append(e.Target.EntityContainer.Name);
				treeNode.Text.Append(".");
				treeNode.Text.Append(e.Target.Name);
				return treeNode;
			}

			// Token: 0x06006C51 RID: 27729 RVA: 0x00171478 File Offset: 0x0016F678
			public override TreeNode Visit(DbFilterExpression e)
			{
				Check.NotNull<DbFilterExpression>(e, "e");
				TreeNode treeNode = ExpressionPrinter.PrinterVisitor.NodeFromExpression(e);
				treeNode.Children.Add(this.VisitBinding("Input", e.Input));
				treeNode.Children.Add(this.Visit("Predicate", new DbExpression[] { e.Predicate }));
				return treeNode;
			}

			// Token: 0x06006C52 RID: 27730 RVA: 0x001714DC File Offset: 0x0016F6DC
			public override TreeNode Visit(DbProjectExpression e)
			{
				Check.NotNull<DbProjectExpression>(e, "e");
				TreeNode treeNode = ExpressionPrinter.PrinterVisitor.NodeFromExpression(e);
				treeNode.Children.Add(this.VisitBinding("Input", e.Input));
				treeNode.Children.Add(this.Visit("Projection", new DbExpression[] { e.Projection }));
				return treeNode;
			}

			// Token: 0x06006C53 RID: 27731 RVA: 0x0017153E File Offset: 0x0016F73E
			public override TreeNode Visit(DbCrossJoinExpression e)
			{
				Check.NotNull<DbCrossJoinExpression>(e, "e");
				TreeNode treeNode = ExpressionPrinter.PrinterVisitor.NodeFromExpression(e);
				treeNode.Children.Add(this.VisitBindingList("Inputs", e.Inputs));
				return treeNode;
			}

			// Token: 0x06006C54 RID: 27732 RVA: 0x00171570 File Offset: 0x0016F770
			public override TreeNode Visit(DbJoinExpression e)
			{
				Check.NotNull<DbJoinExpression>(e, "e");
				TreeNode treeNode = ExpressionPrinter.PrinterVisitor.NodeFromExpression(e);
				treeNode.Children.Add(this.VisitBinding("Left", e.Left));
				treeNode.Children.Add(this.VisitBinding("Right", e.Right));
				treeNode.Children.Add(this.Visit("JoinCondition", new DbExpression[] { e.JoinCondition }));
				return treeNode;
			}

			// Token: 0x06006C55 RID: 27733 RVA: 0x001715F0 File Offset: 0x0016F7F0
			public override TreeNode Visit(DbApplyExpression e)
			{
				Check.NotNull<DbApplyExpression>(e, "e");
				TreeNode treeNode = ExpressionPrinter.PrinterVisitor.NodeFromExpression(e);
				treeNode.Children.Add(this.VisitBinding("Input", e.Input));
				treeNode.Children.Add(this.VisitBinding("Apply", e.Apply));
				return treeNode;
			}

			// Token: 0x06006C56 RID: 27734 RVA: 0x00171648 File Offset: 0x0016F848
			public override TreeNode Visit(DbGroupByExpression e)
			{
				Check.NotNull<DbGroupByExpression>(e, "e");
				List<TreeNode> list = new List<TreeNode>();
				List<TreeNode> list2 = new List<TreeNode>();
				RowType edmType = TypeHelpers.GetEdmType<RowType>(TypeHelpers.GetEdmType<CollectionType>(e.ResultType).TypeUsage);
				int num = 0;
				for (int i = 0; i < e.Keys.Count; i++)
				{
					list.Add(this.VisitWithLabel("Key", edmType.Properties[i].Name, e.Keys[num]));
					num++;
				}
				int num2 = 0;
				for (int j = e.Keys.Count; j < edmType.Properties.Count; j++)
				{
					TreeNode treeNode = new TreeNode("Aggregate : '", new TreeNode[0]);
					treeNode.Text.Append(edmType.Properties[j].Name);
					treeNode.Text.Append("'");
					DbFunctionAggregate dbFunctionAggregate = e.Aggregates[num2] as DbFunctionAggregate;
					if (dbFunctionAggregate != null)
					{
						TreeNode treeNode2 = this.VisitFunction(dbFunctionAggregate.Function, dbFunctionAggregate.Arguments);
						if (dbFunctionAggregate.Distinct)
						{
							treeNode2 = new TreeNode("Distinct", new TreeNode[] { treeNode2 });
						}
						treeNode.Children.Add(treeNode2);
					}
					else
					{
						DbGroupAggregate dbGroupAggregate = e.Aggregates[num2] as DbGroupAggregate;
						treeNode.Children.Add(this.Visit("GroupAggregate", new DbExpression[] { dbGroupAggregate.Arguments[0] }));
					}
					list2.Add(treeNode);
					num2++;
				}
				TreeNode treeNode3 = ExpressionPrinter.PrinterVisitor.NodeFromExpression(e);
				treeNode3.Children.Add(this.VisitGroupBinding(e.Input));
				if (list.Count > 0)
				{
					treeNode3.Children.Add(new TreeNode("Keys", list));
				}
				if (list2.Count > 0)
				{
					treeNode3.Children.Add(new TreeNode("Aggregates", list2));
				}
				return treeNode3;
			}

			// Token: 0x06006C57 RID: 27735 RVA: 0x00171854 File Offset: 0x0016FA54
			private TreeNode VisitSortOrder(IList<DbSortClause> sortOrder)
			{
				TreeNode treeNode = new TreeNode("SortOrder", new TreeNode[0]);
				foreach (DbSortClause dbSortClause in sortOrder)
				{
					TreeNode treeNode2 = this.Visit(dbSortClause.Ascending ? "Asc" : "Desc", new DbExpression[] { dbSortClause.Expression });
					if (!string.IsNullOrEmpty(dbSortClause.Collation))
					{
						treeNode2.Text.Append(" : ");
						treeNode2.Text.Append(dbSortClause.Collation);
					}
					treeNode.Children.Add(treeNode2);
				}
				return treeNode;
			}

			// Token: 0x06006C58 RID: 27736 RVA: 0x00171910 File Offset: 0x0016FB10
			public override TreeNode Visit(DbSkipExpression e)
			{
				Check.NotNull<DbSkipExpression>(e, "e");
				TreeNode treeNode = ExpressionPrinter.PrinterVisitor.NodeFromExpression(e);
				treeNode.Children.Add(this.VisitBinding("Input", e.Input));
				treeNode.Children.Add(this.VisitSortOrder(e.SortOrder));
				treeNode.Children.Add(this.Visit("Count", new DbExpression[] { e.Count }));
				return treeNode;
			}

			// Token: 0x06006C59 RID: 27737 RVA: 0x0017198C File Offset: 0x0016FB8C
			public override TreeNode Visit(DbSortExpression e)
			{
				Check.NotNull<DbSortExpression>(e, "e");
				TreeNode treeNode = ExpressionPrinter.PrinterVisitor.NodeFromExpression(e);
				treeNode.Children.Add(this.VisitBinding("Input", e.Input));
				treeNode.Children.Add(this.VisitSortOrder(e.SortOrder));
				return treeNode;
			}

			// Token: 0x06006C5A RID: 27738 RVA: 0x001719E0 File Offset: 0x0016FBE0
			public override TreeNode Visit(DbQuantifierExpression e)
			{
				Check.NotNull<DbQuantifierExpression>(e, "e");
				TreeNode treeNode = ExpressionPrinter.PrinterVisitor.NodeFromExpression(e);
				treeNode.Children.Add(this.VisitBinding("Input", e.Input));
				treeNode.Children.Add(this.Visit("Predicate", new DbExpression[] { e.Predicate }));
				return treeNode;
			}

			// Token: 0x040031C2 RID: 12738
			private static readonly Dictionary<DbExpressionKind, string> _opMap = ExpressionPrinter.PrinterVisitor.InitializeOpMap();

			// Token: 0x040031C3 RID: 12739
			private int _maxStringLength = 80;

			// Token: 0x040031C4 RID: 12740
			private bool _infix = true;
		}
	}
}
