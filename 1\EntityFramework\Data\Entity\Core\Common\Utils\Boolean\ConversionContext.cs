﻿using System;
using System.Collections.Generic;

namespace System.Data.Entity.Core.Common.Utils.Boolean
{
	// Token: 0x0200060A RID: 1546
	internal abstract class ConversionContext<T_Identifier>
	{
		// Token: 0x06004B86 RID: 19334
		internal abstract Vertex TranslateTermToVertex(TermExpr<T_Identifier> term);

		// Token: 0x06004B87 RID: 19335
		internal abstract IEnumerable<LiteralVertexPair<T_Identifier>> GetSuccessors(Vertex vertex);

		// Token: 0x04001A5F RID: 6751
		internal readonly Solver Solver = new Solver();
	}
}
