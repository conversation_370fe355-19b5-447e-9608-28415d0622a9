﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.Common;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects.DataClasses;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Objects
{
	// Token: 0x02000428 RID: 1064
	internal static class ObjectViewFactory
	{
		// Token: 0x060033E1 RID: 13281 RVA: 0x000A6848 File Offset: 0x000A4A48
		internal static IBindingList CreateViewForQuery<TElement>(TypeUsage elementEdmTypeUsage, IEnumerable<TElement> queryResults, ObjectContext objectContext, bool forceReadOnly, EntitySet singleEntitySet)
		{
			TypeUsage ospaceTypeUsage = ObjectViewFactory.GetOSpaceTypeUsage(elementEdmTypeUsage, objectContext);
			Type type;
			if (ospaceTypeUsage == null)
			{
				type = typeof(TElement);
			}
			type = ObjectViewFactory.GetClrType<TElement>(ospaceTypeUsage.EdmType);
			object objectStateManager = objectContext.ObjectStateManager;
			IBindingList bindingList;
			if (type == typeof(TElement))
			{
				bindingList = new ObjectView<TElement>(new ObjectViewQueryResultData<TElement>(queryResults, objectContext, forceReadOnly, singleEntitySet), objectStateManager);
			}
			else if (type == null)
			{
				bindingList = new DataRecordObjectView(new ObjectViewQueryResultData<DbDataRecord>(queryResults, objectContext, true, null), objectStateManager, (RowType)ospaceTypeUsage.EdmType, typeof(TElement));
			}
			else
			{
				if (!typeof(TElement).IsAssignableFrom(type))
				{
					throw EntityUtil.ValueInvalidCast(type, typeof(TElement));
				}
				Type type2 = ObjectViewFactory._genericObjectViewQueryResultDataType.MakeGenericType(new Type[] { type });
				object obj = type2.GetDeclaredConstructor(new Type[]
				{
					typeof(IEnumerable),
					typeof(ObjectContext),
					typeof(bool),
					typeof(EntitySet)
				}).Invoke(new object[] { queryResults, objectContext, forceReadOnly, singleEntitySet });
				bindingList = ObjectViewFactory.CreateObjectView(type, type2, obj, objectStateManager);
			}
			return bindingList;
		}

		// Token: 0x060033E2 RID: 13282 RVA: 0x000A6988 File Offset: 0x000A4B88
		internal static IBindingList CreateViewForEntityCollection<TElement>(EntityType entityType, EntityCollection<TElement> entityCollection) where TElement : class
		{
			TypeUsage ospaceTypeUsage = ObjectViewFactory.GetOSpaceTypeUsage((entityType == null) ? null : TypeUsage.Create(entityType), entityCollection.ObjectContext);
			Type type;
			if (ospaceTypeUsage == null)
			{
				type = typeof(TElement);
			}
			else
			{
				type = ObjectViewFactory.GetClrType<TElement>(ospaceTypeUsage.EdmType);
				if (type == null)
				{
					type = typeof(TElement);
				}
			}
			IBindingList bindingList;
			if (type == typeof(TElement))
			{
				bindingList = new ObjectView<TElement>(new ObjectViewEntityCollectionData<TElement, TElement>(entityCollection), entityCollection);
			}
			else
			{
				if (!typeof(TElement).IsAssignableFrom(type))
				{
					throw EntityUtil.ValueInvalidCast(type, typeof(TElement));
				}
				Type type2 = ObjectViewFactory._genericObjectViewEntityCollectionDataType.MakeGenericType(new Type[]
				{
					type,
					typeof(TElement)
				});
				object obj = type2.GetDeclaredConstructor(new Type[] { typeof(EntityCollection<TElement>) }).Invoke(new object[] { entityCollection });
				bindingList = ObjectViewFactory.CreateObjectView(type, type2, obj, entityCollection);
			}
			return bindingList;
		}

		// Token: 0x060033E3 RID: 13283 RVA: 0x000A6A7C File Offset: 0x000A4C7C
		private static IBindingList CreateObjectView(Type clrElementType, Type objectViewDataType, object viewData, object eventDataSource)
		{
			Type type2 = ObjectViewFactory._genericObjectViewType.MakeGenericType(new Type[] { clrElementType });
			Type[] array = objectViewDataType.FindInterfaces((Type type, object unusedFilter) => type.Name == ObjectViewFactory._genericObjectViewDataInterfaceType.Name, null);
			return (IBindingList)type2.GetDeclaredConstructor(new Type[]
			{
				array[0],
				typeof(object)
			}).Invoke(new object[] { viewData, eventDataSource });
		}

		// Token: 0x060033E4 RID: 13284 RVA: 0x000A6AFC File Offset: 0x000A4CFC
		private static TypeUsage GetOSpaceTypeUsage(TypeUsage typeUsage, ObjectContext objectContext)
		{
			TypeUsage typeUsage2;
			if (typeUsage == null || typeUsage.EdmType == null)
			{
				typeUsage2 = null;
			}
			else if (typeUsage.EdmType.DataSpace == DataSpace.OSpace)
			{
				typeUsage2 = typeUsage;
			}
			else if (objectContext == null)
			{
				typeUsage2 = null;
			}
			else
			{
				typeUsage2 = objectContext.Perspective.MetadataWorkspace.GetOSpaceTypeUsage(typeUsage);
			}
			return typeUsage2;
		}

		// Token: 0x060033E5 RID: 13285 RVA: 0x000A6B44 File Offset: 0x000A4D44
		private static Type GetClrType<TElement>(EdmType ospaceEdmType)
		{
			Type type;
			if (ospaceEdmType.BuiltInTypeKind == BuiltInTypeKind.RowType)
			{
				RowType rowType = (RowType)ospaceEdmType;
				if (rowType.InitializerMetadata != null && rowType.InitializerMetadata.ClrType != null)
				{
					type = rowType.InitializerMetadata.ClrType;
				}
				else
				{
					Type typeFromHandle = typeof(TElement);
					if (typeof(IDataRecord).IsAssignableFrom(typeFromHandle) || typeFromHandle == typeof(object))
					{
						type = null;
					}
					else
					{
						type = typeof(TElement);
					}
				}
			}
			else
			{
				type = ospaceEdmType.ClrType;
				if (type == null)
				{
					type = typeof(TElement);
				}
			}
			return type;
		}

		// Token: 0x040010C4 RID: 4292
		private static readonly Type _genericObjectViewType = typeof(ObjectView<>);

		// Token: 0x040010C5 RID: 4293
		private static readonly Type _genericObjectViewDataInterfaceType = typeof(IObjectViewData<>);

		// Token: 0x040010C6 RID: 4294
		private static readonly Type _genericObjectViewQueryResultDataType = typeof(ObjectViewQueryResultData<>);

		// Token: 0x040010C7 RID: 4295
		private static readonly Type _genericObjectViewEntityCollectionDataType = typeof(ObjectViewEntityCollectionData<, >);
	}
}
