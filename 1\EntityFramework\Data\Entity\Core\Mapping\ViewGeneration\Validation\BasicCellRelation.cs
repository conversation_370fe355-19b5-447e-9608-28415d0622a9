﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Data.Entity.Core.Metadata.Edm;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Validation
{
	// Token: 0x02000577 RID: 1399
	internal class BasicCellRelation : CellRelation
	{
		// Token: 0x060043FA RID: 17402 RVA: 0x000EC7F2 File Offset: 0x000EA9F2
		internal BasicCellRelation(CellQuery cellQuery, ViewCellRelation viewCellRelation, IEnumerable<MemberProjectedSlot> slots)
			: base(viewCellRelation.CellNumber)
		{
			this.m_cellQuery = cellQuery;
			this.m_slots = new List<MemberProjectedSlot>(slots);
			this.m_viewCellRelation = viewCellRelation;
		}

		// Token: 0x17000D79 RID: 3449
		// (get) Token: 0x060043FB RID: 17403 RVA: 0x000EC81A File Offset: 0x000EAA1A
		internal ViewCellRelation ViewCellRelation
		{
			get
			{
				return this.m_viewCellRelation;
			}
		}

		// Token: 0x060043FC RID: 17404 RVA: 0x000EC822 File Offset: 0x000EAA22
		internal void PopulateKeyConstraints(SchemaConstraints<BasicKeyConstraint> constraints)
		{
			if (this.m_cellQuery.Extent is EntitySet)
			{
				this.PopulateKeyConstraintsForEntitySet(constraints);
				return;
			}
			this.PopulateKeyConstraintsForRelationshipSet(constraints);
		}

		// Token: 0x060043FD RID: 17405 RVA: 0x000EC848 File Offset: 0x000EAA48
		private void PopulateKeyConstraintsForEntitySet(SchemaConstraints<BasicKeyConstraint> constraints)
		{
			MemberPath memberPath = new MemberPath(this.m_cellQuery.Extent);
			EntityType entityType = (EntityType)this.m_cellQuery.Extent.ElementType;
			List<ExtentKey> keysForEntityType = ExtentKey.GetKeysForEntityType(memberPath, entityType);
			this.AddKeyConstraints(keysForEntityType, constraints);
		}

		// Token: 0x060043FE RID: 17406 RVA: 0x000EC88C File Offset: 0x000EAA8C
		private void PopulateKeyConstraintsForRelationshipSet(SchemaConstraints<BasicKeyConstraint> constraints)
		{
			AssociationSet associationSet = this.m_cellQuery.Extent as AssociationSet;
			Set<MemberPath> set = new Set<MemberPath>(MemberPath.EqualityComparer);
			bool flag = false;
			foreach (AssociationSetEnd associationSetEnd in associationSet.AssociationSetEnds)
			{
				AssociationEndMember correspondingAssociationEndMember = associationSetEnd.CorrespondingAssociationEndMember;
				List<ExtentKey> keysForEntityType = ExtentKey.GetKeysForEntityType(new MemberPath(associationSet, correspondingAssociationEndMember), associationSetEnd.EntitySet.ElementType);
				if (MetadataHelper.DoesEndFormKey(associationSet, correspondingAssociationEndMember))
				{
					this.AddKeyConstraints(keysForEntityType, constraints);
					flag = true;
				}
				set.AddRange(keysForEntityType[0].KeyFields);
			}
			if (!flag)
			{
				ExtentKey extentKey = new ExtentKey(set);
				ExtentKey[] array = new ExtentKey[] { extentKey };
				this.AddKeyConstraints(array, constraints);
			}
		}

		// Token: 0x060043FF RID: 17407 RVA: 0x000EC964 File Offset: 0x000EAB64
		private void AddKeyConstraints(IEnumerable<ExtentKey> keys, SchemaConstraints<BasicKeyConstraint> constraints)
		{
			foreach (ExtentKey extentKey in keys)
			{
				List<MemberProjectedSlot> slots = MemberProjectedSlot.GetSlots(this.m_slots, extentKey.KeyFields);
				if (slots != null)
				{
					BasicKeyConstraint basicKeyConstraint = new BasicKeyConstraint(this, slots);
					constraints.Add(basicKeyConstraint);
				}
			}
		}

		// Token: 0x06004400 RID: 17408 RVA: 0x000EC9CC File Offset: 0x000EABCC
		protected override int GetHash()
		{
			return this.m_cellQuery.GetHashCode();
		}

		// Token: 0x06004401 RID: 17409 RVA: 0x000EC9D9 File Offset: 0x000EABD9
		internal override void ToCompactString(StringBuilder builder)
		{
			builder.Append("BasicRel: ");
			StringUtil.FormatStringBuilder(builder, "{0}", new object[] { this.m_slots[0] });
		}

		// Token: 0x04001889 RID: 6281
		private readonly CellQuery m_cellQuery;

		// Token: 0x0400188A RID: 6282
		private readonly List<MemberProjectedSlot> m_slots;

		// Token: 0x0400188B RID: 6283
		private readonly ViewCellRelation m_viewCellRelation;
	}
}
