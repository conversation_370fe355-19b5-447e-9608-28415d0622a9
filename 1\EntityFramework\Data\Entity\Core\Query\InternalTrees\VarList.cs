﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Text;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003FB RID: 1019
	[DebuggerDisplay("{{{ToString()}}}")]
	internal class VarList : List<Var>
	{
		// Token: 0x06002F78 RID: 12152 RVA: 0x00094E87 File Offset: 0x00093087
		internal VarList()
		{
		}

		// Token: 0x06002F79 RID: 12153 RVA: 0x00094E8F File Offset: 0x0009308F
		internal VarList(IEnumerable<Var> vars)
			: base(vars)
		{
		}

		// Token: 0x06002F7A RID: 12154 RVA: 0x00094E98 File Offset: 0x00093098
		public override string ToString()
		{
			StringBuilder stringBuilder = new StringBuilder();
			string text = string.Empty;
			foreach (Var var in this)
			{
				stringBuilder.AppendFormat(CultureInfo.InvariantCulture, "{0}{1}", new object[] { text, var.Id });
				text = ",";
			}
			return stringBuilder.ToString();
		}
	}
}
