﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Reflection;
using System.Reflection.Emit;
using System.Runtime.Serialization;

namespace System.Data.Entity.Core.Objects.Internal
{
	// Token: 0x0200043A RID: 1082
	internal sealed class DataContractImplementor
	{
		// Token: 0x060034E4 RID: 13540 RVA: 0x000A8F64 File Offset: 0x000A7164
		internal DataContractImplementor(EntityType ospaceEntityType)
		{
			this._baseClrType = ospaceEntityType.ClrType;
			this._dataContract = this._baseClrType.GetCustomAttributes(false).FirstOrDefault<DataContractAttribute>();
		}

		// Token: 0x060034E5 RID: 13541 RVA: 0x000A8F90 File Offset: 0x000A7190
		internal void Implement(TypeBuilder typeBuilder)
		{
			if (this._dataContract != null)
			{
				object[] array = new object[] { this._dataContract.IsReference };
				CustomAttributeBuilder customAttributeBuilder = new CustomAttributeBuilder(DataContractImplementor.DataContractAttributeConstructor, new object[0], DataContractImplementor.DataContractProperties, array);
				typeBuilder.SetCustomAttribute(customAttributeBuilder);
			}
		}

		// Token: 0x04001107 RID: 4359
		internal static readonly ConstructorInfo DataContractAttributeConstructor = typeof(DataContractAttribute).GetDeclaredConstructor(new Type[0]);

		// Token: 0x04001108 RID: 4360
		internal static readonly PropertyInfo[] DataContractProperties = new PropertyInfo[] { typeof(DataContractAttribute).GetDeclaredProperty("IsReference") };

		// Token: 0x04001109 RID: 4361
		private readonly Type _baseClrType;

		// Token: 0x0400110A RID: 4362
		private readonly DataContractAttribute _dataContract;
	}
}
