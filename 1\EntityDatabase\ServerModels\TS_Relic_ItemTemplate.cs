﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000015 RID: 21
	public class TS_Relic_ItemTemplate
	{
		// Token: 0x17000089 RID: 137
		// (get) Token: 0x06000125 RID: 293 RVA: 0x00002A27 File Offset: 0x00000C27
		// (set) Token: 0x06000126 RID: 294 RVA: 0x00002A2F File Offset: 0x00000C2F
		[Key]
		public int ID { get; set; }

		// Token: 0x1700008A RID: 138
		// (get) Token: 0x06000127 RID: 295 RVA: 0x00002A38 File Offset: 0x00000C38
		// (set) Token: 0x06000128 RID: 296 RVA: 0x00002A40 File Offset: 0x00000C40
		public int RelicID { get; set; }

		// Token: 0x1700008B RID: 139
		// (get) Token: 0x06000129 RID: 297 RVA: 0x00002A49 File Offset: 0x00000C49
		// (set) Token: 0x0600012A RID: 298 RVA: 0x00002A51 File Offset: 0x00000C51
		public string Name { get; set; }

		// Token: 0x1700008C RID: 140
		// (get) Token: 0x0600012B RID: 299 RVA: 0x00002A5A File Offset: 0x00000C5A
		// (set) Token: 0x0600012C RID: 300 RVA: 0x00002A62 File Offset: 0x00000C62
		public string Desc { get; set; }

		// Token: 0x1700008D RID: 141
		// (get) Token: 0x0600012D RID: 301 RVA: 0x00002A6B File Offset: 0x00000C6B
		// (set) Token: 0x0600012E RID: 302 RVA: 0x00002A73 File Offset: 0x00000C73
		public int CommonProperty { get; set; }

		// Token: 0x1700008E RID: 142
		// (get) Token: 0x0600012F RID: 303 RVA: 0x00002A7C File Offset: 0x00000C7C
		// (set) Token: 0x06000130 RID: 304 RVA: 0x00002A84 File Offset: 0x00000C84
		public int Quality { get; set; }

		// Token: 0x1700008F RID: 143
		// (get) Token: 0x06000131 RID: 305 RVA: 0x00002A8D File Offset: 0x00000C8D
		// (set) Token: 0x06000132 RID: 306 RVA: 0x00002A95 File Offset: 0x00000C95
		public int LinkItem { get; set; }

		// Token: 0x17000090 RID: 144
		// (get) Token: 0x06000133 RID: 307 RVA: 0x00002A9E File Offset: 0x00000C9E
		// (set) Token: 0x06000134 RID: 308 RVA: 0x00002AA6 File Offset: 0x00000CA6
		public int ShardNum { get; set; }

		// Token: 0x17000091 RID: 145
		// (get) Token: 0x06000135 RID: 309 RVA: 0x00002AAF File Offset: 0x00000CAF
		// (set) Token: 0x06000136 RID: 310 RVA: 0x00002AB7 File Offset: 0x00000CB7
		public int Pic { get; set; }
	}
}
