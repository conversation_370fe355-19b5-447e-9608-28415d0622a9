﻿using System;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Mapping.Update.Internal
{
	// Token: 0x020005D8 RID: 1496
	internal abstract class UpdateExpressionVisitor<TReturn> : DbExpressionVisitor<TReturn>
	{
		// Token: 0x17000E41 RID: 3649
		// (get) Token: 0x06004828 RID: 18472
		protected abstract string VisitorName { get; }

		// Token: 0x06004829 RID: 18473 RVA: 0x000FFACC File Offset: 0x000FDCCC
		protected NotSupportedException ConstructNotSupportedException(DbExpression node)
		{
			return new NotSupportedException(Strings.Update_UnsupportedExpressionKind((node == null) ? null : node.ExpressionKind.ToString(), this.VisitorName));
		}

		// Token: 0x0600482A RID: 18474 RVA: 0x000FFB03 File Offset: 0x000FDD03
		public override TReturn Visit(DbExpression expression)
		{
			Check.NotNull<DbExpression>(expression, "expression");
			if (expression != null)
			{
				return expression.Accept<TReturn>(this);
			}
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600482B RID: 18475 RVA: 0x000FFB23 File Offset: 0x000FDD23
		public override TReturn Visit(DbAndExpression expression)
		{
			Check.NotNull<DbAndExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600482C RID: 18476 RVA: 0x000FFB38 File Offset: 0x000FDD38
		public override TReturn Visit(DbApplyExpression expression)
		{
			Check.NotNull<DbApplyExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600482D RID: 18477 RVA: 0x000FFB4D File Offset: 0x000FDD4D
		public override TReturn Visit(DbArithmeticExpression expression)
		{
			Check.NotNull<DbArithmeticExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600482E RID: 18478 RVA: 0x000FFB62 File Offset: 0x000FDD62
		public override TReturn Visit(DbCaseExpression expression)
		{
			Check.NotNull<DbCaseExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600482F RID: 18479 RVA: 0x000FFB77 File Offset: 0x000FDD77
		public override TReturn Visit(DbCastExpression expression)
		{
			Check.NotNull<DbCastExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004830 RID: 18480 RVA: 0x000FFB8C File Offset: 0x000FDD8C
		public override TReturn Visit(DbComparisonExpression expression)
		{
			Check.NotNull<DbComparisonExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004831 RID: 18481 RVA: 0x000FFBA1 File Offset: 0x000FDDA1
		public override TReturn Visit(DbConstantExpression expression)
		{
			Check.NotNull<DbConstantExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004832 RID: 18482 RVA: 0x000FFBB6 File Offset: 0x000FDDB6
		public override TReturn Visit(DbCrossJoinExpression expression)
		{
			Check.NotNull<DbCrossJoinExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004833 RID: 18483 RVA: 0x000FFBCB File Offset: 0x000FDDCB
		public override TReturn Visit(DbDerefExpression expression)
		{
			Check.NotNull<DbDerefExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004834 RID: 18484 RVA: 0x000FFBE0 File Offset: 0x000FDDE0
		public override TReturn Visit(DbDistinctExpression expression)
		{
			Check.NotNull<DbDistinctExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004835 RID: 18485 RVA: 0x000FFBF5 File Offset: 0x000FDDF5
		public override TReturn Visit(DbElementExpression expression)
		{
			Check.NotNull<DbElementExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004836 RID: 18486 RVA: 0x000FFC0A File Offset: 0x000FDE0A
		public override TReturn Visit(DbExceptExpression expression)
		{
			Check.NotNull<DbExceptExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004837 RID: 18487 RVA: 0x000FFC1F File Offset: 0x000FDE1F
		public override TReturn Visit(DbFilterExpression expression)
		{
			Check.NotNull<DbFilterExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004838 RID: 18488 RVA: 0x000FFC34 File Offset: 0x000FDE34
		public override TReturn Visit(DbFunctionExpression expression)
		{
			Check.NotNull<DbFunctionExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004839 RID: 18489 RVA: 0x000FFC49 File Offset: 0x000FDE49
		public override TReturn Visit(DbLambdaExpression expression)
		{
			Check.NotNull<DbLambdaExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600483A RID: 18490 RVA: 0x000FFC5E File Offset: 0x000FDE5E
		public override TReturn Visit(DbEntityRefExpression expression)
		{
			Check.NotNull<DbEntityRefExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600483B RID: 18491 RVA: 0x000FFC73 File Offset: 0x000FDE73
		public override TReturn Visit(DbRefKeyExpression expression)
		{
			Check.NotNull<DbRefKeyExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600483C RID: 18492 RVA: 0x000FFC88 File Offset: 0x000FDE88
		public override TReturn Visit(DbGroupByExpression expression)
		{
			Check.NotNull<DbGroupByExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600483D RID: 18493 RVA: 0x000FFC9D File Offset: 0x000FDE9D
		public override TReturn Visit(DbIntersectExpression expression)
		{
			Check.NotNull<DbIntersectExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600483E RID: 18494 RVA: 0x000FFCB2 File Offset: 0x000FDEB2
		public override TReturn Visit(DbIsEmptyExpression expression)
		{
			Check.NotNull<DbIsEmptyExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600483F RID: 18495 RVA: 0x000FFCC7 File Offset: 0x000FDEC7
		public override TReturn Visit(DbIsNullExpression expression)
		{
			Check.NotNull<DbIsNullExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004840 RID: 18496 RVA: 0x000FFCDC File Offset: 0x000FDEDC
		public override TReturn Visit(DbIsOfExpression expression)
		{
			Check.NotNull<DbIsOfExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004841 RID: 18497 RVA: 0x000FFCF1 File Offset: 0x000FDEF1
		public override TReturn Visit(DbJoinExpression expression)
		{
			Check.NotNull<DbJoinExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004842 RID: 18498 RVA: 0x000FFD06 File Offset: 0x000FDF06
		public override TReturn Visit(DbLikeExpression expression)
		{
			Check.NotNull<DbLikeExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004843 RID: 18499 RVA: 0x000FFD1B File Offset: 0x000FDF1B
		public override TReturn Visit(DbLimitExpression expression)
		{
			Check.NotNull<DbLimitExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004844 RID: 18500 RVA: 0x000FFD30 File Offset: 0x000FDF30
		public override TReturn Visit(DbNewInstanceExpression expression)
		{
			Check.NotNull<DbNewInstanceExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004845 RID: 18501 RVA: 0x000FFD45 File Offset: 0x000FDF45
		public override TReturn Visit(DbNotExpression expression)
		{
			Check.NotNull<DbNotExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004846 RID: 18502 RVA: 0x000FFD5A File Offset: 0x000FDF5A
		public override TReturn Visit(DbNullExpression expression)
		{
			Check.NotNull<DbNullExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004847 RID: 18503 RVA: 0x000FFD6F File Offset: 0x000FDF6F
		public override TReturn Visit(DbOfTypeExpression expression)
		{
			Check.NotNull<DbOfTypeExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004848 RID: 18504 RVA: 0x000FFD84 File Offset: 0x000FDF84
		public override TReturn Visit(DbOrExpression expression)
		{
			Check.NotNull<DbOrExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004849 RID: 18505 RVA: 0x000FFD99 File Offset: 0x000FDF99
		public override TReturn Visit(DbInExpression expression)
		{
			Check.NotNull<DbInExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600484A RID: 18506 RVA: 0x000FFDAE File Offset: 0x000FDFAE
		public override TReturn Visit(DbParameterReferenceExpression expression)
		{
			Check.NotNull<DbParameterReferenceExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600484B RID: 18507 RVA: 0x000FFDC3 File Offset: 0x000FDFC3
		public override TReturn Visit(DbProjectExpression expression)
		{
			Check.NotNull<DbProjectExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600484C RID: 18508 RVA: 0x000FFDD8 File Offset: 0x000FDFD8
		public override TReturn Visit(DbPropertyExpression expression)
		{
			Check.NotNull<DbPropertyExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600484D RID: 18509 RVA: 0x000FFDED File Offset: 0x000FDFED
		public override TReturn Visit(DbQuantifierExpression expression)
		{
			Check.NotNull<DbQuantifierExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600484E RID: 18510 RVA: 0x000FFE02 File Offset: 0x000FE002
		public override TReturn Visit(DbRefExpression expression)
		{
			Check.NotNull<DbRefExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x0600484F RID: 18511 RVA: 0x000FFE17 File Offset: 0x000FE017
		public override TReturn Visit(DbRelationshipNavigationExpression expression)
		{
			Check.NotNull<DbRelationshipNavigationExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004850 RID: 18512 RVA: 0x000FFE2C File Offset: 0x000FE02C
		public override TReturn Visit(DbSkipExpression expression)
		{
			Check.NotNull<DbSkipExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004851 RID: 18513 RVA: 0x000FFE41 File Offset: 0x000FE041
		public override TReturn Visit(DbSortExpression expression)
		{
			Check.NotNull<DbSortExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004852 RID: 18514 RVA: 0x000FFE56 File Offset: 0x000FE056
		public override TReturn Visit(DbTreatExpression expression)
		{
			Check.NotNull<DbTreatExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004853 RID: 18515 RVA: 0x000FFE6B File Offset: 0x000FE06B
		public override TReturn Visit(DbUnionAllExpression expression)
		{
			Check.NotNull<DbUnionAllExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004854 RID: 18516 RVA: 0x000FFE80 File Offset: 0x000FE080
		public override TReturn Visit(DbVariableReferenceExpression expression)
		{
			Check.NotNull<DbVariableReferenceExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}

		// Token: 0x06004855 RID: 18517 RVA: 0x000FFE95 File Offset: 0x000FE095
		public override TReturn Visit(DbScanExpression expression)
		{
			Check.NotNull<DbScanExpression>(expression, "expression");
			throw this.ConstructNotSupportedException(expression);
		}
	}
}
