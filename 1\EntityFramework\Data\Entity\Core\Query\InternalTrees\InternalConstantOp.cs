﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003AF RID: 943
	internal sealed class InternalConstantOp : ConstantBaseOp
	{
		// Token: 0x06002D8B RID: 11659 RVA: 0x000910A3 File Offset: 0x0008F2A3
		internal InternalConstantOp(TypeUsage type, object value)
			: base(OpType.InternalConstant, type, value)
		{
		}

		// Token: 0x06002D8C RID: 11660 RVA: 0x000910AE File Offset: 0x0008F2AE
		private InternalConstantOp()
			: base(OpType.InternalConstant)
		{
		}

		// Token: 0x06002D8D RID: 11661 RVA: 0x000910B7 File Offset: 0x0008F2B7
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002D8E RID: 11662 RVA: 0x000910C1 File Offset: 0x0008F2C1
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000F3E RID: 3902
		internal static readonly InternalConstantOp Pattern = new InternalConstantOp();
	}
}
