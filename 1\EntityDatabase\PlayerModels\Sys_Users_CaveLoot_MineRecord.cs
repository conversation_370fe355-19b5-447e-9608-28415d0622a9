﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000023 RID: 35
	public class Sys_Users_CaveLoot_MineRecord
	{
		// Token: 0x17000111 RID: 273
		// (get) Token: 0x06000244 RID: 580 RVA: 0x000033B6 File Offset: 0x000015B6
		// (set) Token: 0x06000245 RID: 581 RVA: 0x000033BE File Offset: 0x000015BE
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000112 RID: 274
		// (get) Token: 0x06000246 RID: 582 RVA: 0x000033C7 File Offset: 0x000015C7
		// (set) Token: 0x06000247 RID: 583 RVA: 0x000033CF File Offset: 0x000015CF
		public int UserID { get; set; }

		// Token: 0x17000113 RID: 275
		// (get) Token: 0x06000248 RID: 584 RVA: 0x000033D8 File Offset: 0x000015D8
		// (set) Token: 0x06000249 RID: 585 RVA: 0x000033E0 File Offset: 0x000015E0
		public int MineType { get; set; }

		// Token: 0x17000114 RID: 276
		// (get) Token: 0x0600024A RID: 586 RVA: 0x000033E9 File Offset: 0x000015E9
		// (set) Token: 0x0600024B RID: 587 RVA: 0x000033F1 File Offset: 0x000015F1
		public int ItemID { get; set; }

		// Token: 0x17000115 RID: 277
		// (get) Token: 0x0600024C RID: 588 RVA: 0x000033FA File Offset: 0x000015FA
		// (set) Token: 0x0600024D RID: 589 RVA: 0x00003402 File Offset: 0x00001602
		public int Quantity { get; set; }

		// Token: 0x17000116 RID: 278
		// (get) Token: 0x0600024E RID: 590 RVA: 0x0000340B File Offset: 0x0000160B
		// (set) Token: 0x0600024F RID: 591 RVA: 0x00003413 File Offset: 0x00001613
		public bool IsBind { get; set; }

		// Token: 0x17000117 RID: 279
		// (get) Token: 0x06000250 RID: 592 RVA: 0x0000341C File Offset: 0x0000161C
		// (set) Token: 0x06000251 RID: 593 RVA: 0x00003424 File Offset: 0x00001624
		public DateTime CreateTime { get; set; }
	}
}
