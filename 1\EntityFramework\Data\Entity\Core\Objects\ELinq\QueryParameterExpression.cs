﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace System.Data.Entity.Core.Objects.ELinq
{
	// Token: 0x02000466 RID: 1126
	internal sealed class QueryParameterExpression : Expression
	{
		// Token: 0x06003786 RID: 14214 RVA: 0x000B3C0E File Offset: 0x000B1E0E
		internal QueryParameterExpression(DbParameterReferenceExpression parameterReference, Expression funcletizedExpression, IEnumerable<ParameterExpression> compiledQueryParameters)
		{
			this._compiledQueryParameters = compiledQueryParameters ?? Enumerable.Empty<ParameterExpression>();
			this._parameterReference = parameterReference;
			this._type = funcletizedExpression.Type;
			this._funcletizedExpression = funcletizedExpression;
			this._cachedDelegate = null;
		}

		// Token: 0x06003787 RID: 14215 RVA: 0x000B3C48 File Offset: 0x000B1E48
		internal object EvaluateParameter(object[] arguments)
		{
			if (this._cachedDelegate == null)
			{
				if (this._funcletizedExpression.NodeType == ExpressionType.Constant)
				{
					return ((ConstantExpression)this._funcletizedExpression).Value;
				}
				ConstantExpression constantExpression;
				if (QueryParameterExpression.TryEvaluatePath(this._funcletizedExpression, out constantExpression))
				{
					return constantExpression.Value;
				}
			}
			object obj;
			try
			{
				if (this._cachedDelegate == null)
				{
					Type delegateType = TypeSystem.GetDelegateType(this._compiledQueryParameters.Select((ParameterExpression p) => p.Type), this._type);
					this._cachedDelegate = Expression.Lambda(delegateType, this._funcletizedExpression, this._compiledQueryParameters).Compile();
				}
				obj = this._cachedDelegate.DynamicInvoke(arguments);
			}
			catch (TargetInvocationException ex)
			{
				throw ex.InnerException;
			}
			return obj;
		}

		// Token: 0x06003788 RID: 14216 RVA: 0x000B3D18 File Offset: 0x000B1F18
		internal QueryParameterExpression EscapeParameterForLike(Expression<Func<string, Tuple<string, bool>>> method)
		{
			Expression expression = Expression.Property(Expression.Invoke(method, new Expression[] { this._funcletizedExpression }), "Item1");
			return new QueryParameterExpression(this._parameterReference, expression, this._compiledQueryParameters);
		}

		// Token: 0x17000AA4 RID: 2724
		// (get) Token: 0x06003789 RID: 14217 RVA: 0x000B3D57 File Offset: 0x000B1F57
		internal DbParameterReferenceExpression ParameterReference
		{
			get
			{
				return this._parameterReference;
			}
		}

		// Token: 0x17000AA5 RID: 2725
		// (get) Token: 0x0600378A RID: 14218 RVA: 0x000B3D5F File Offset: 0x000B1F5F
		public override Type Type
		{
			get
			{
				return this._type;
			}
		}

		// Token: 0x17000AA6 RID: 2726
		// (get) Token: 0x0600378B RID: 14219 RVA: 0x000B3D67 File Offset: 0x000B1F67
		public override ExpressionType NodeType
		{
			get
			{
				return (ExpressionType)(-1);
			}
		}

		// Token: 0x0600378C RID: 14220 RVA: 0x000B3D6C File Offset: 0x000B1F6C
		private static bool TryEvaluatePath(Expression expression, out ConstantExpression constantExpression)
		{
			MemberExpression memberExpression = expression as MemberExpression;
			constantExpression = null;
			if (memberExpression != null)
			{
				Stack<MemberExpression> stack = new Stack<MemberExpression>();
				stack.Push(memberExpression);
				while ((memberExpression = memberExpression.Expression as MemberExpression) != null)
				{
					stack.Push(memberExpression);
				}
				memberExpression = stack.Pop();
				if (memberExpression.Expression is ConstantExpression)
				{
					object obj;
					if (!QueryParameterExpression.TryGetFieldOrPropertyValue(memberExpression, ((ConstantExpression)memberExpression.Expression).Value, out obj))
					{
						return false;
					}
					if (stack.Count > 0)
					{
						using (Stack<MemberExpression>.Enumerator enumerator = stack.GetEnumerator())
						{
							while (enumerator.MoveNext())
							{
								if (!QueryParameterExpression.TryGetFieldOrPropertyValue(enumerator.Current, obj, out obj))
								{
									return false;
								}
							}
						}
					}
					constantExpression = Expression.Constant(obj, expression.Type);
					return true;
				}
			}
			return false;
		}

		// Token: 0x0600378D RID: 14221 RVA: 0x000B3E44 File Offset: 0x000B2044
		private static bool TryGetFieldOrPropertyValue(MemberExpression me, object instance, out object memberValue)
		{
			bool flag = false;
			memberValue = null;
			bool flag2;
			try
			{
				if (me.Member.MemberType == MemberTypes.Field)
				{
					memberValue = ((FieldInfo)me.Member).GetValue(instance);
					flag = true;
				}
				else if (me.Member.MemberType == MemberTypes.Property)
				{
					memberValue = ((PropertyInfo)me.Member).GetValue(instance, null);
					flag = true;
				}
				flag2 = flag;
			}
			catch (TargetInvocationException ex)
			{
				throw ex.InnerException;
			}
			return flag2;
		}

		// Token: 0x04001221 RID: 4641
		private readonly DbParameterReferenceExpression _parameterReference;

		// Token: 0x04001222 RID: 4642
		private readonly Type _type;

		// Token: 0x04001223 RID: 4643
		private readonly Expression _funcletizedExpression;

		// Token: 0x04001224 RID: 4644
		private readonly IEnumerable<ParameterExpression> _compiledQueryParameters;

		// Token: 0x04001225 RID: 4645
		private Delegate _cachedDelegate;
	}
}
