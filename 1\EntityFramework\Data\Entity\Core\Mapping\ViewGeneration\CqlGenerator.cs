﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.CqlGeneration;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Data.Entity.Core.Metadata.Edm;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration
{
	// Token: 0x02000568 RID: 1384
	internal sealed class CqlGenerator : InternalBase
	{
		// Token: 0x06004395 RID: 17301 RVA: 0x000E8E87 File Offset: 0x000E7087
		internal CqlGenerator(CellTreeNode view, Dictionary<MemberPath, CaseStatement> caseStatements, CqlIdentifiers identifiers, MemberProjectionIndex projectedSlotMap, int numCellsInView, BoolExpression topLevelWhereClause, StorageMappingItemCollection mappingItemCollection)
		{
			this.m_view = view;
			this.m_caseStatements = caseStatements;
			this.m_projectedSlotMap = projectedSlotMap;
			this.m_numBools = numCellsInView;
			this.m_topLevelWhereClause = topLevelWhereClause;
			this.m_identifiers = identifiers;
			this.m_mappingItemCollection = mappingItemCollection;
		}

		// Token: 0x17000D6A RID: 3434
		// (get) Token: 0x06004396 RID: 17302 RVA: 0x000E8EC4 File Offset: 0x000E70C4
		private int TotalSlots
		{
			get
			{
				return this.m_projectedSlotMap.Count + this.m_numBools;
			}
		}

		// Token: 0x06004397 RID: 17303 RVA: 0x000E8ED8 File Offset: 0x000E70D8
		internal string GenerateEsql()
		{
			CqlBlock cqlBlock = this.GenerateCqlBlockTree();
			StringBuilder stringBuilder = new StringBuilder(1024);
			cqlBlock.AsEsql(stringBuilder, true, 1);
			return stringBuilder.ToString();
		}

		// Token: 0x06004398 RID: 17304 RVA: 0x000E8F08 File Offset: 0x000E7108
		internal DbQueryCommandTree GenerateCqt()
		{
			DbExpression dbExpression = this.GenerateCqlBlockTree().AsCqt(true);
			return DbQueryCommandTree.FromValidExpression(this.m_mappingItemCollection.Workspace, DataSpace.SSpace, dbExpression, true, false);
		}

		// Token: 0x06004399 RID: 17305 RVA: 0x000E8F38 File Offset: 0x000E7138
		private CqlBlock GenerateCqlBlockTree()
		{
			bool[] requiredSlots = this.GetRequiredSlots();
			List<WithRelationship> list = new List<WithRelationship>();
			CqlBlock cqlBlock = this.m_view.ToCqlBlock(requiredSlots, this.m_identifiers, ref this.m_currentBlockNum, ref list);
			foreach (CaseStatement caseStatement in this.m_caseStatements.Values)
			{
				caseStatement.Simplify();
			}
			return this.ConstructCaseBlocks(cqlBlock, list);
		}

		// Token: 0x0600439A RID: 17306 RVA: 0x000E8FC0 File Offset: 0x000E71C0
		private bool[] GetRequiredSlots()
		{
			bool[] array = new bool[this.TotalSlots];
			foreach (CaseStatement caseStatement in this.m_caseStatements.Values)
			{
				this.GetRequiredSlotsForCaseMember(caseStatement.MemberPath, array);
			}
			for (int i = this.TotalSlots - this.m_numBools; i < this.TotalSlots; i++)
			{
				array[i] = true;
			}
			foreach (CaseStatement caseStatement2 in this.m_caseStatements.Values)
			{
				if (!caseStatement2.MemberPath.IsPartOfKey && !caseStatement2.DependsOnMemberValue)
				{
					array[this.m_projectedSlotMap.IndexOf(caseStatement2.MemberPath)] = false;
				}
			}
			return array;
		}

		// Token: 0x0600439B RID: 17307 RVA: 0x000E90C0 File Offset: 0x000E72C0
		private CqlBlock ConstructCaseBlocks(CqlBlock viewBlock, IEnumerable<WithRelationship> withRelationships)
		{
			bool[] array = new bool[this.TotalSlots];
			array[0] = true;
			this.m_topLevelWhereClause.GetRequiredSlots(this.m_projectedSlotMap, array);
			return this.ConstructCaseBlocks(viewBlock, 0, array, withRelationships);
		}

		// Token: 0x0600439C RID: 17308 RVA: 0x000E90FC File Offset: 0x000E72FC
		private CqlBlock ConstructCaseBlocks(CqlBlock viewBlock, int startSlotNum, bool[] parentRequiredSlots, IEnumerable<WithRelationship> withRelationships)
		{
			int count = this.m_projectedSlotMap.Count;
			int num = this.FindNextCaseStatementSlot(startSlotNum, parentRequiredSlots, count);
			if (num == -1)
			{
				return viewBlock;
			}
			MemberPath memberPath = this.m_projectedSlotMap[num];
			bool[] array = new bool[this.TotalSlots];
			this.GetRequiredSlotsForCaseMember(memberPath, array);
			for (int i = 0; i < this.TotalSlots; i++)
			{
				if (parentRequiredSlots[i])
				{
					array[i] = true;
				}
			}
			CaseStatement caseStatement = this.m_caseStatements[memberPath];
			array[num] = caseStatement.DependsOnMemberValue;
			CqlBlock cqlBlock = this.ConstructCaseBlocks(viewBlock, num + 1, array, null);
			SlotInfo[] array2 = this.CreateSlotInfosForCaseStatement(parentRequiredSlots, num, cqlBlock, caseStatement, withRelationships);
			this.m_currentBlockNum++;
			BoolExpression boolExpression = ((startSlotNum == 0) ? this.m_topLevelWhereClause : BoolExpression.True);
			if (startSlotNum == 0)
			{
				for (int j = 1; j < array2.Length; j++)
				{
					array2[j].ResetIsRequiredByParent();
				}
			}
			return new CaseCqlBlock(array2, num, cqlBlock, boolExpression, this.m_identifiers, this.m_currentBlockNum);
		}

		// Token: 0x0600439D RID: 17309 RVA: 0x000E91F4 File Offset: 0x000E73F4
		private SlotInfo[] CreateSlotInfosForCaseStatement(bool[] parentRequiredSlots, int foundSlot, CqlBlock childBlock, CaseStatement thisCaseStatement, IEnumerable<WithRelationship> withRelationships)
		{
			int num = childBlock.Slots.Count - this.TotalSlots;
			SlotInfo[] array = new SlotInfo[this.TotalSlots + num];
			for (int i = 0; i < this.TotalSlots; i++)
			{
				bool flag = childBlock.IsProjected(i);
				bool flag2 = parentRequiredSlots[i];
				ProjectedSlot projectedSlot = childBlock.SlotValue(i);
				MemberPath outputMemberPath = this.GetOutputMemberPath(i);
				if (i == foundSlot)
				{
					projectedSlot = new CaseStatementProjectedSlot(thisCaseStatement.DeepQualify(childBlock), withRelationships);
					flag = true;
				}
				else if (flag && flag2)
				{
					projectedSlot = childBlock.QualifySlotWithBlockAlias(i);
				}
				SlotInfo slotInfo = new SlotInfo(flag2 && flag, flag, projectedSlot, outputMemberPath);
				array[i] = slotInfo;
			}
			for (int j = this.TotalSlots; j < this.TotalSlots + num; j++)
			{
				QualifiedSlot qualifiedSlot = childBlock.QualifySlotWithBlockAlias(j);
				array[j] = new SlotInfo(true, true, qualifiedSlot, childBlock.MemberPath(j));
			}
			return array;
		}

		// Token: 0x0600439E RID: 17310 RVA: 0x000E92CC File Offset: 0x000E74CC
		private int FindNextCaseStatementSlot(int startSlotNum, bool[] parentRequiredSlots, int numMembers)
		{
			int num = -1;
			for (int i = startSlotNum; i < numMembers; i++)
			{
				MemberPath memberPath = this.m_projectedSlotMap[i];
				if (parentRequiredSlots[i] && this.m_caseStatements.ContainsKey(memberPath))
				{
					num = i;
					break;
				}
			}
			return num;
		}

		// Token: 0x0600439F RID: 17311 RVA: 0x000E930C File Offset: 0x000E750C
		private void GetRequiredSlotsForCaseMember(MemberPath caseMemberPath, bool[] requiredSlots)
		{
			CaseStatement caseStatement = this.m_caseStatements[caseMemberPath];
			bool flag = false;
			foreach (CaseStatement.WhenThen whenThen in caseStatement.Clauses)
			{
				whenThen.Condition.GetRequiredSlots(this.m_projectedSlotMap, requiredSlots);
				if (!(whenThen.Value is ConstantProjectedSlot))
				{
					flag = true;
				}
			}
			EdmType edmType = caseMemberPath.EdmType;
			if (Helper.IsEntityType(edmType) || Helper.IsComplexType(edmType))
			{
				using (IEnumerator<EdmType> enumerator2 = caseStatement.InstantiatedTypes.GetEnumerator())
				{
					while (enumerator2.MoveNext())
					{
						EdmType edmType2 = enumerator2.Current;
						foreach (object obj in Helper.GetAllStructuralMembers(edmType2))
						{
							EdmMember edmMember = (EdmMember)obj;
							int slotIndex = this.GetSlotIndex(caseMemberPath, edmMember);
							requiredSlots[slotIndex] = true;
						}
					}
					return;
				}
			}
			if (caseMemberPath.IsScalarType())
			{
				if (flag)
				{
					int num = this.m_projectedSlotMap.IndexOf(caseMemberPath);
					requiredSlots[num] = true;
					return;
				}
			}
			else
			{
				if (Helper.IsAssociationType(edmType))
				{
					using (ReadOnlyMetadataCollection<AssociationEndMember>.Enumerator enumerator4 = ((AssociationSet)caseMemberPath.Extent).ElementType.AssociationEndMembers.GetEnumerator())
					{
						while (enumerator4.MoveNext())
						{
							AssociationEndMember associationEndMember = enumerator4.Current;
							int slotIndex2 = this.GetSlotIndex(caseMemberPath, associationEndMember);
							requiredSlots[slotIndex2] = true;
						}
						return;
					}
				}
				foreach (EdmMember edmMember2 in (edmType as RefType).ElementType.KeyMembers)
				{
					int slotIndex3 = this.GetSlotIndex(caseMemberPath, edmMember2);
					requiredSlots[slotIndex3] = true;
				}
			}
		}

		// Token: 0x060043A0 RID: 17312 RVA: 0x000E9510 File Offset: 0x000E7710
		private MemberPath GetOutputMemberPath(int slotNum)
		{
			return this.m_projectedSlotMap.GetMemberPath(slotNum, this.TotalSlots - this.m_projectedSlotMap.Count);
		}

		// Token: 0x060043A1 RID: 17313 RVA: 0x000E9530 File Offset: 0x000E7730
		private int GetSlotIndex(MemberPath member, EdmMember child)
		{
			MemberPath memberPath = new MemberPath(member, child);
			return this.m_projectedSlotMap.IndexOf(memberPath);
		}

		// Token: 0x060043A2 RID: 17314 RVA: 0x000E9554 File Offset: 0x000E7754
		internal override void ToCompactString(StringBuilder builder)
		{
			builder.Append("View: ");
			this.m_view.ToCompactString(builder);
			builder.Append("ProjectedSlotMap: ");
			this.m_projectedSlotMap.ToCompactString(builder);
			builder.Append("Case statements: ");
			foreach (MemberPath memberPath in this.m_caseStatements.Keys)
			{
				this.m_caseStatements[memberPath].ToCompactString(builder);
				builder.AppendLine();
			}
		}

		// Token: 0x0400181D RID: 6173
		private readonly CellTreeNode m_view;

		// Token: 0x0400181E RID: 6174
		private readonly Dictionary<MemberPath, CaseStatement> m_caseStatements;

		// Token: 0x0400181F RID: 6175
		private readonly MemberProjectionIndex m_projectedSlotMap;

		// Token: 0x04001820 RID: 6176
		private readonly int m_numBools;

		// Token: 0x04001821 RID: 6177
		private int m_currentBlockNum;

		// Token: 0x04001822 RID: 6178
		private readonly BoolExpression m_topLevelWhereClause;

		// Token: 0x04001823 RID: 6179
		private readonly CqlIdentifiers m_identifiers;

		// Token: 0x04001824 RID: 6180
		private readonly StorageMappingItemCollection m_mappingItemCollection;
	}
}
