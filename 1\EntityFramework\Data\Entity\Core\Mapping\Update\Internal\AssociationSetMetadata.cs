﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Linq;

namespace System.Data.Entity.Core.Mapping.Update.Internal
{
	// Token: 0x020005C1 RID: 1473
	internal sealed class AssociationSetMetadata
	{
		// Token: 0x17000E1B RID: 3611
		// (get) Token: 0x0600476D RID: 18285 RVA: 0x000FB654 File Offset: 0x000F9854
		internal bool HasEnds
		{
			get
			{
				return 0 < this.RequiredEnds.Count || 0 < this.OptionalEnds.Count || 0 < this.IncludedValueEnds.Count;
			}
		}

		// Token: 0x0600476E RID: 18286 RVA: 0x000FB684 File Offset: 0x000F9884
		internal AssociationSetMetadata(Set<EntitySet> affectedTables, AssociationSet associationSet, MetadataWorkspace workspace)
		{
			bool flag = 1 < affectedTables.Count;
			ReadOnlyMetadataCollection<AssociationSetEnd> associationSetEnds = associationSet.AssociationSetEnds;
			foreach (EntitySet entitySet in affectedTables)
			{
				foreach (EntitySet entitySet2 in MetadataHelper.GetInfluencingEntitySetsForTable(entitySet, workspace))
				{
					foreach (AssociationSetEnd associationSetEnd in associationSetEnds)
					{
						if (associationSetEnd.EntitySet.EdmEquals(entitySet2))
						{
							if (flag)
							{
								AssociationSetMetadata.AddEnd(ref this.RequiredEnds, associationSetEnd.CorrespondingAssociationEndMember);
							}
							else if (this.RequiredEnds == null || !this.RequiredEnds.Contains(associationSetEnd.CorrespondingAssociationEndMember))
							{
								AssociationSetMetadata.AddEnd(ref this.OptionalEnds, associationSetEnd.CorrespondingAssociationEndMember);
							}
						}
					}
				}
			}
			AssociationSetMetadata.FixSet(ref this.RequiredEnds);
			AssociationSetMetadata.FixSet(ref this.OptionalEnds);
			foreach (ReferentialConstraint referentialConstraint in associationSet.ElementType.ReferentialConstraints)
			{
				AssociationEndMember associationEndMember = (AssociationEndMember)referentialConstraint.FromRole;
				if (!this.RequiredEnds.Contains(associationEndMember) && !this.OptionalEnds.Contains(associationEndMember))
				{
					AssociationSetMetadata.AddEnd(ref this.IncludedValueEnds, associationEndMember);
				}
			}
			AssociationSetMetadata.FixSet(ref this.IncludedValueEnds);
		}

		// Token: 0x0600476F RID: 18287 RVA: 0x000FB848 File Offset: 0x000F9A48
		internal AssociationSetMetadata(IEnumerable<AssociationEndMember> requiredEnds)
		{
			if (requiredEnds.Any<AssociationEndMember>())
			{
				this.RequiredEnds = new Set<AssociationEndMember>(requiredEnds);
			}
			AssociationSetMetadata.FixSet(ref this.RequiredEnds);
			AssociationSetMetadata.FixSet(ref this.OptionalEnds);
			AssociationSetMetadata.FixSet(ref this.IncludedValueEnds);
		}

		// Token: 0x06004770 RID: 18288 RVA: 0x000FB885 File Offset: 0x000F9A85
		private static void AddEnd(ref Set<AssociationEndMember> set, AssociationEndMember element)
		{
			if (set == null)
			{
				set = new Set<AssociationEndMember>();
			}
			set.Add(element);
		}

		// Token: 0x06004771 RID: 18289 RVA: 0x000FB89A File Offset: 0x000F9A9A
		private static void FixSet(ref Set<AssociationEndMember> set)
		{
			if (set == null)
			{
				set = Set<AssociationEndMember>.Empty;
				return;
			}
			set.MakeReadOnly();
		}

		// Token: 0x04001956 RID: 6486
		internal readonly Set<AssociationEndMember> RequiredEnds;

		// Token: 0x04001957 RID: 6487
		internal readonly Set<AssociationEndMember> OptionalEnds;

		// Token: 0x04001958 RID: 6488
		internal readonly Set<AssociationEndMember> IncludedValueEnds;
	}
}
