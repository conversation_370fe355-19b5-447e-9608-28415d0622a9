﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Common.Utils.Boolean;
using System.Data.Entity.Core.Mapping.ViewGeneration.CqlGeneration;
using System.Data.Entity.Core.Metadata.Edm;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005B4 RID: 1460
	internal class TypeRestriction : MemberRestriction
	{
		// Token: 0x06004719 RID: 18201 RVA: 0x000FA234 File Offset: 0x000F8434
		internal TypeRestriction(MemberPath member, IEnumerable<EdmType> values)
			: base(new MemberProjectedSlot(member), TypeRestriction.CreateTypeConstants(values))
		{
		}

		// Token: 0x0600471A RID: 18202 RVA: 0x000FA248 File Offset: 0x000F8448
		internal TypeRestriction(MemberPath member, Constant value)
			: base(new MemberProjectedSlot(member), value)
		{
		}

		// Token: 0x0600471B RID: 18203 RVA: 0x000FA257 File Offset: 0x000F8457
		internal TypeRestriction(MemberProjectedSlot slot, Domain domain)
			: base(slot, domain)
		{
		}

		// Token: 0x0600471C RID: 18204 RVA: 0x000FA264 File Offset: 0x000F8464
		internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> FixRange(Set<Constant> range, MemberDomainMap memberDomainMap)
		{
			IEnumerable<Constant> domain = memberDomainMap.GetDomain(base.RestrictedMemberSlot.MemberPath);
			return new TypeRestriction(base.RestrictedMemberSlot, new Domain(range, domain)).GetDomainBoolExpression(memberDomainMap);
		}

		// Token: 0x0600471D RID: 18205 RVA: 0x000FA29B File Offset: 0x000F849B
		internal override BoolLiteral RemapBool(Dictionary<MemberPath, MemberPath> remap)
		{
			return new TypeRestriction(base.RestrictedMemberSlot.RemapSlot(remap), base.Domain);
		}

		// Token: 0x0600471E RID: 18206 RVA: 0x000FA2B4 File Offset: 0x000F84B4
		internal override MemberRestriction CreateCompleteMemberRestriction(IEnumerable<Constant> possibleValues)
		{
			return new TypeRestriction(base.RestrictedMemberSlot, new Domain(base.Domain.Values, possibleValues));
		}

		// Token: 0x0600471F RID: 18207 RVA: 0x000FA2D4 File Offset: 0x000F84D4
		internal override StringBuilder AsEsql(StringBuilder builder, string blockAlias, bool skipIsNotNull)
		{
			if (base.Domain.Count > 1)
			{
				builder.Append('(');
			}
			bool flag = true;
			foreach (Constant constant in base.Domain.Values)
			{
				TypeConstant typeConstant = constant as TypeConstant;
				if (!flag)
				{
					builder.Append(" OR ");
				}
				flag = false;
				if (Helper.IsRefType(base.RestrictedMemberSlot.MemberPath.EdmType))
				{
					builder.Append("Deref(");
					base.RestrictedMemberSlot.MemberPath.AsEsql(builder, blockAlias);
					builder.Append(')');
				}
				else
				{
					base.RestrictedMemberSlot.MemberPath.AsEsql(builder, blockAlias);
				}
				if (constant.IsNull())
				{
					builder.Append(" IS NULL");
				}
				else
				{
					builder.Append(" IS OF (ONLY ");
					CqlWriter.AppendEscapedTypeName(builder, typeConstant.EdmType);
					builder.Append(')');
				}
			}
			if (base.Domain.Count > 1)
			{
				builder.Append(')');
			}
			return builder;
		}

		// Token: 0x06004720 RID: 18208 RVA: 0x000FA3F8 File Offset: 0x000F85F8
		internal override DbExpression AsCqt(DbExpression row, bool skipIsNotNull)
		{
			DbExpression cqt = base.RestrictedMemberSlot.MemberPath.AsCqt(row);
			if (Helper.IsRefType(base.RestrictedMemberSlot.MemberPath.EdmType))
			{
				cqt = cqt.Deref();
			}
			if (base.Domain.Count == 1)
			{
				cqt = cqt.IsOfOnly(TypeUsage.Create(((TypeConstant)base.Domain.Values.Single<Constant>()).EdmType));
			}
			else
			{
				List<DbExpression> list = base.Domain.Values.Select((Constant t) => cqt.IsOfOnly(TypeUsage.Create(((TypeConstant)t).EdmType))).ToList<DbExpression>();
				cqt = Helpers.BuildBalancedTreeInPlace<DbExpression>(list, (DbExpression prev, DbExpression next) => prev.Or(next));
			}
			return cqt;
		}

		// Token: 0x06004721 RID: 18209 RVA: 0x000FA4E0 File Offset: 0x000F86E0
		internal override StringBuilder AsUserString(StringBuilder builder, string blockAlias, bool skipIsNotNull)
		{
			if (Helper.IsRefType(base.RestrictedMemberSlot.MemberPath.EdmType))
			{
				builder.Append("Deref(");
				base.RestrictedMemberSlot.MemberPath.AsEsql(builder, blockAlias);
				builder.Append(')');
			}
			else
			{
				base.RestrictedMemberSlot.MemberPath.AsEsql(builder, blockAlias);
			}
			if (base.Domain.Count > 1)
			{
				builder.Append(" is a (");
			}
			else
			{
				builder.Append(" is type ");
			}
			bool flag = true;
			foreach (Constant constant in base.Domain.Values)
			{
				TypeConstant typeConstant = constant as TypeConstant;
				if (!flag)
				{
					builder.Append(" OR ");
				}
				if (constant.IsNull())
				{
					builder.Append(" NULL");
				}
				else
				{
					CqlWriter.AppendEscapedTypeName(builder, typeConstant.EdmType);
				}
				flag = false;
			}
			if (base.Domain.Count > 1)
			{
				builder.Append(')');
			}
			return builder;
		}

		// Token: 0x06004722 RID: 18210 RVA: 0x000FA5FC File Offset: 0x000F87FC
		private static IEnumerable<Constant> CreateTypeConstants(IEnumerable<EdmType> types)
		{
			foreach (EdmType edmType in types)
			{
				if (edmType == null)
				{
					yield return Constant.Null;
				}
				else
				{
					yield return new TypeConstant(edmType);
				}
			}
			IEnumerator<EdmType> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x06004723 RID: 18211 RVA: 0x000FA60C File Offset: 0x000F880C
		internal override void ToCompactString(StringBuilder builder)
		{
			builder.Append("type(");
			base.RestrictedMemberSlot.ToCompactString(builder);
			builder.Append(") IN (");
			StringUtil.ToCommaSeparatedStringSorted(builder, base.Domain.Values);
			builder.Append(")");
		}
	}
}
