﻿using System;
using System.Data.Entity.Utilities;
using System.Xml;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004E3 RID: 1251
	internal class MslSerializer
	{
		// Token: 0x06003E63 RID: 15971 RVA: 0x000CE516 File Offset: 0x000CC716
		public virtual bool Serialize(DbDatabaseMapping databaseMapping, XmlWriter xmlWriter)
		{
			Check.NotNull<DbDatabaseMapping>(databaseMapping, "databaseMapping");
			Check.NotNull<XmlWriter>(xmlWriter, "xmlWriter");
			new MslXmlSchemaWriter(xmlWriter, databaseMapping.Model.SchemaVersion).WriteSchema(databaseMapping);
			return true;
		}
	}
}
