﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x0200002C RID: 44
	public class Sys_Users_ListBox
	{
		// Token: 0x17000154 RID: 340
		// (get) Token: 0x060002D3 RID: 723 RVA: 0x0000387A File Offset: 0x00001A7A
		// (set) Token: 0x060002D4 RID: 724 RVA: 0x00003882 File Offset: 0x00001A82
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000155 RID: 341
		// (get) Token: 0x060002D5 RID: 725 RVA: 0x0000388B File Offset: 0x00001A8B
		// (set) Token: 0x060002D6 RID: 726 RVA: 0x00003893 File Offset: 0x00001A93
		public int UserID { get; set; }

		// Token: 0x17000156 RID: 342
		// (get) Token: 0x060002D7 RID: 727 RVA: 0x0000389C File Offset: 0x00001A9C
		// (set) Token: 0x060002D8 RID: 728 RVA: 0x000038A4 File Offset: 0x00001AA4
		public int BoxId { get; set; }

		// Token: 0x17000157 RID: 343
		// (get) Token: 0x060002D9 RID: 729 RVA: 0x000038AD File Offset: 0x00001AAD
		// (set) Token: 0x060002DA RID: 730 RVA: 0x000038B5 File Offset: 0x00001AB5
		public int Count { get; set; }

		// Token: 0x17000158 RID: 344
		// (get) Token: 0x060002DB RID: 731 RVA: 0x000038BE File Offset: 0x00001ABE
		// (set) Token: 0x060002DC RID: 732 RVA: 0x000038C6 File Offset: 0x00001AC6
		public int state { get; set; }

		// Token: 0x17000159 RID: 345
		// (get) Token: 0x060002DD RID: 733 RVA: 0x000038CF File Offset: 0x00001ACF
		// (set) Token: 0x060002DE RID: 734 RVA: 0x000038D7 File Offset: 0x00001AD7
		public int boxIndex { get; set; }

		// Token: 0x1700015A RID: 346
		// (get) Token: 0x060002DF RID: 735 RVA: 0x000038E0 File Offset: 0x00001AE0
		// (set) Token: 0x060002E0 RID: 736 RVA: 0x000038E8 File Offset: 0x00001AE8
		public int subtractMin { get; set; }

		// Token: 0x1700015B RID: 347
		// (get) Token: 0x060002E1 RID: 737 RVA: 0x000038F1 File Offset: 0x00001AF1
		// (set) Token: 0x060002E2 RID: 738 RVA: 0x000038F9 File Offset: 0x00001AF9
		public int min { get; set; }

		// Token: 0x1700015C RID: 348
		// (get) Token: 0x060002E3 RID: 739 RVA: 0x00003902 File Offset: 0x00001B02
		// (set) Token: 0x060002E4 RID: 740 RVA: 0x0000390A File Offset: 0x00001B0A
		public int max { get; set; }

		// Token: 0x1700015D RID: 349
		// (get) Token: 0x060002E5 RID: 741 RVA: 0x00003913 File Offset: 0x00001B13
		// (set) Token: 0x060002E6 RID: 742 RVA: 0x0000391B File Offset: 0x00001B1B
		public int subtractMax { get; set; }

		// Token: 0x1700015E RID: 350
		// (get) Token: 0x060002E7 RID: 743 RVA: 0x00003924 File Offset: 0x00001B24
		// (set) Token: 0x060002E8 RID: 744 RVA: 0x0000392C File Offset: 0x00001B2C
		public int needmoney { get; set; }

		// Token: 0x1700015F RID: 351
		// (get) Token: 0x060002E9 RID: 745 RVA: 0x00003935 File Offset: 0x00001B35
		// (set) Token: 0x060002EA RID: 746 RVA: 0x0000393D File Offset: 0x00001B3D
		public int currentIndex { get; set; }

		// Token: 0x17000160 RID: 352
		// (get) Token: 0x060002EB RID: 747 RVA: 0x00003946 File Offset: 0x00001B46
		// (set) Token: 0x060002EC RID: 748 RVA: 0x0000394E File Offset: 0x00001B4E
		public DateTime OpenDate { get; set; }
	}
}
