﻿using System;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Xml.XPath;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x02000536 RID: 1334
	public sealed class FunctionImportEntityTypeMappingConditionValue : FunctionImportEntityTypeMappingCondition
	{
		// Token: 0x060041CA RID: 16842 RVA: 0x000DD124 File Offset: 0x000DB324
		public FunctionImportEntityTypeMappingConditionValue(string columnName, object value)
			: base(Check.NotNull<string>(columnName, "columnName"), LineInfo.Empty)
		{
			Check.NotNull<object>(value, "value");
			this._value = value;
			this._convertedValues = new Memoizer<Type, object>(new Func<Type, object>(this.GetConditionValue), null);
		}

		// Token: 0x060041CB RID: 16843 RVA: 0x000DD172 File Offset: 0x000DB372
		internal FunctionImportEntityTypeMappingConditionValue(string columnName, XPathNavigator columnValue, LineInfo lineInfo)
			: base(columnName, lineInfo)
		{
			this._xPathValue = columnValue;
			this._convertedValues = new Memoizer<Type, object>(new Func<Type, object>(this.GetConditionValue), null);
		}

		// Token: 0x17000D07 RID: 3335
		// (get) Token: 0x060041CC RID: 16844 RVA: 0x000DD19B File Offset: 0x000DB39B
		public object Value
		{
			get
			{
				return this._value;
			}
		}

		// Token: 0x17000D08 RID: 3336
		// (get) Token: 0x060041CD RID: 16845 RVA: 0x000DD1A3 File Offset: 0x000DB3A3
		internal override ValueCondition ConditionValue
		{
			get
			{
				return new ValueCondition((this._value != null) ? this._value.ToString() : this._xPathValue.Value);
			}
		}

		// Token: 0x060041CE RID: 16846 RVA: 0x000DD1CC File Offset: 0x000DB3CC
		internal override bool ColumnValueMatchesCondition(object columnValue)
		{
			if (columnValue == null || Convert.IsDBNull(columnValue))
			{
				return false;
			}
			Type type = columnValue.GetType();
			object obj = this._convertedValues.Evaluate(type);
			return ByValueEqualityComparer.Default.Equals(columnValue, obj);
		}

		// Token: 0x060041CF RID: 16847 RVA: 0x000DD208 File Offset: 0x000DB408
		private object GetConditionValue(Type columnValueType)
		{
			return this.GetConditionValue(columnValueType, delegate
			{
				throw new EntityCommandExecutionException(Strings.Mapping_FunctionImport_UnsupportedType(this.ColumnName, columnValueType.FullName));
			}, delegate
			{
				throw new EntityCommandExecutionException(Strings.Mapping_FunctionImport_ConditionValueTypeMismatch("FunctionImportMapping", this.ColumnName, columnValueType.FullName));
			});
		}

		// Token: 0x060041D0 RID: 16848 RVA: 0x000DD250 File Offset: 0x000DB450
		internal object GetConditionValue(Type columnValueType, Action handleTypeNotComparable, Action handleInvalidConditionValue)
		{
			PrimitiveType primitiveType;
			if (!ClrProviderManifest.Instance.TryGetPrimitiveType(columnValueType, out primitiveType) || !MappingItemLoader.IsTypeSupportedForCondition(primitiveType.PrimitiveTypeKind))
			{
				handleTypeNotComparable();
				return null;
			}
			if (this._value == null)
			{
				object obj;
				try
				{
					obj = this._xPathValue.ValueAs(columnValueType);
				}
				catch (FormatException)
				{
					handleInvalidConditionValue();
					obj = null;
				}
				return obj;
			}
			if (this._value.GetType() == columnValueType)
			{
				return this._value;
			}
			handleInvalidConditionValue();
			return null;
		}

		// Token: 0x040016CD RID: 5837
		private readonly object _value;

		// Token: 0x040016CE RID: 5838
		private readonly XPathNavigator _xPathValue;

		// Token: 0x040016CF RID: 5839
		private readonly Memoizer<Type, object> _convertedValues;
	}
}
