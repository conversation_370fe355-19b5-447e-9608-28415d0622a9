﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects.DataClasses;
using System.Data.Entity.Core.Objects.Internal;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Diagnostics;

namespace System.Data.Entity.Core.Objects
{
	// Token: 0x0200042E RID: 1070
	internal sealed class RelationshipEntry : ObjectStateEntry
	{
		// Token: 0x17000A0B RID: 2571
		// (get) Token: 0x06003405 RID: 13317 RVA: 0x000A729B File Offset: 0x000A549B
		internal EntityKey Key0
		{
			get
			{
				return this.RelationshipWrapper.Key0;
			}
		}

		// Token: 0x17000A0C RID: 2572
		// (get) Token: 0x06003406 RID: 13318 RVA: 0x000A72A8 File Offset: 0x000A54A8
		internal EntityKey Key1
		{
			get
			{
				return this.RelationshipWrapper.Key1;
			}
		}

		// Token: 0x17000A0D RID: 2573
		// (get) Token: 0x06003407 RID: 13319 RVA: 0x000A72B5 File Offset: 0x000A54B5
		internal override BitArray ModifiedProperties
		{
			get
			{
				return null;
			}
		}

		// Token: 0x06003408 RID: 13320 RVA: 0x000A72B8 File Offset: 0x000A54B8
		internal RelationshipEntry(ObjectStateManager cache, EntityState state, RelationshipWrapper relationshipWrapper)
			: base(cache, null, state)
		{
			this._entitySet = relationshipWrapper.AssociationSet;
			this._relationshipWrapper = relationshipWrapper;
		}

		// Token: 0x17000A0E RID: 2574
		// (get) Token: 0x06003409 RID: 13321 RVA: 0x000A72D6 File Offset: 0x000A54D6
		public override bool IsRelationship
		{
			get
			{
				base.ValidateState();
				return true;
			}
		}

		// Token: 0x0600340A RID: 13322 RVA: 0x000A72E0 File Offset: 0x000A54E0
		public override void AcceptChanges()
		{
			base.ValidateState();
			EntityState state = base.State;
			if (state <= EntityState.Added)
			{
				if (state != EntityState.Unchanged)
				{
					if (state != EntityState.Added)
					{
						return;
					}
					this._cache.ChangeState(this, EntityState.Added, EntityState.Unchanged);
					base.State = EntityState.Unchanged;
				}
			}
			else
			{
				if (state != EntityState.Deleted)
				{
					return;
				}
				this.DeleteUnnecessaryKeyEntries();
				if (this._cache != null)
				{
					this._cache.ChangeState(this, EntityState.Deleted, EntityState.Detached);
					return;
				}
			}
		}

		// Token: 0x0600340B RID: 13323 RVA: 0x000A7343 File Offset: 0x000A5543
		public override void Delete()
		{
			this.Delete(true);
		}

		// Token: 0x0600340C RID: 13324 RVA: 0x000A734C File Offset: 0x000A554C
		public override IEnumerable<string> GetModifiedProperties()
		{
			base.ValidateState();
			yield break;
		}

		// Token: 0x0600340D RID: 13325 RVA: 0x000A735C File Offset: 0x000A555C
		public override void SetModified()
		{
			base.ValidateState();
			throw new InvalidOperationException(Strings.ObjectStateEntry_CantModifyRelationState);
		}

		// Token: 0x17000A0F RID: 2575
		// (get) Token: 0x0600340E RID: 13326 RVA: 0x000A736E File Offset: 0x000A556E
		public override object Entity
		{
			get
			{
				base.ValidateState();
				return null;
			}
		}

		// Token: 0x17000A10 RID: 2576
		// (get) Token: 0x0600340F RID: 13327 RVA: 0x000A7377 File Offset: 0x000A5577
		// (set) Token: 0x06003410 RID: 13328 RVA: 0x000A7380 File Offset: 0x000A5580
		public override EntityKey EntityKey
		{
			get
			{
				base.ValidateState();
				return null;
			}
			internal set
			{
			}
		}

		// Token: 0x06003411 RID: 13329 RVA: 0x000A7382 File Offset: 0x000A5582
		public override void SetModifiedProperty(string propertyName)
		{
			base.ValidateState();
			throw new InvalidOperationException(Strings.ObjectStateEntry_CantModifyRelationState);
		}

		// Token: 0x06003412 RID: 13330 RVA: 0x000A7394 File Offset: 0x000A5594
		public override void RejectPropertyChanges(string propertyName)
		{
			base.ValidateState();
			throw new InvalidOperationException(Strings.ObjectStateEntry_CantModifyRelationState);
		}

		// Token: 0x06003413 RID: 13331 RVA: 0x000A73A6 File Offset: 0x000A55A6
		public override bool IsPropertyChanged(string propertyName)
		{
			base.ValidateState();
			throw new InvalidOperationException(Strings.ObjectStateEntry_CantModifyRelationState);
		}

		// Token: 0x17000A11 RID: 2577
		// (get) Token: 0x06003414 RID: 13332 RVA: 0x000A73B8 File Offset: 0x000A55B8
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public override DbDataRecord OriginalValues
		{
			get
			{
				base.ValidateState();
				if (base.State == EntityState.Added)
				{
					throw new InvalidOperationException(Strings.ObjectStateEntry_OriginalValuesDoesNotExist);
				}
				return new ObjectStateEntryDbDataRecord(this);
			}
		}

		// Token: 0x06003415 RID: 13333 RVA: 0x000A73DA File Offset: 0x000A55DA
		public override OriginalValueRecord GetUpdatableOriginalValues()
		{
			throw new InvalidOperationException(Strings.ObjectStateEntry_CantModifyRelationValues);
		}

		// Token: 0x17000A12 RID: 2578
		// (get) Token: 0x06003416 RID: 13334 RVA: 0x000A73E6 File Offset: 0x000A55E6
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public override CurrentValueRecord CurrentValues
		{
			get
			{
				base.ValidateState();
				if (base.State == EntityState.Deleted)
				{
					throw new InvalidOperationException(Strings.ObjectStateEntry_CurrentValuesDoesNotExist);
				}
				return new ObjectStateEntryDbUpdatableDataRecord(this);
			}
		}

		// Token: 0x17000A13 RID: 2579
		// (get) Token: 0x06003417 RID: 13335 RVA: 0x000A7408 File Offset: 0x000A5608
		public override RelationshipManager RelationshipManager
		{
			get
			{
				throw new InvalidOperationException(Strings.ObjectStateEntry_RelationshipAndKeyEntriesDoNotHaveRelationshipManagers);
			}
		}

		// Token: 0x06003418 RID: 13336 RVA: 0x000A7414 File Offset: 0x000A5614
		public override void ChangeState(EntityState state)
		{
			EntityUtil.CheckValidStateForChangeRelationshipState(state, "state");
			if (base.State == EntityState.Detached && state == EntityState.Detached)
			{
				return;
			}
			base.ValidateState();
			if (this.RelationshipWrapper.Key0 == this.Key0)
			{
				base.ObjectStateManager.ChangeRelationshipState(this.Key0, this.Key1, this.RelationshipWrapper.AssociationSet.ElementType.FullName, this.RelationshipWrapper.AssociationEndMembers[1].Name, state);
				return;
			}
			base.ObjectStateManager.ChangeRelationshipState(this.Key0, this.Key1, this.RelationshipWrapper.AssociationSet.ElementType.FullName, this.RelationshipWrapper.AssociationEndMembers[0].Name, state);
		}

		// Token: 0x06003419 RID: 13337 RVA: 0x000A74E1 File Offset: 0x000A56E1
		public override void ApplyCurrentValues(object currentEntity)
		{
			Check.NotNull<object>(currentEntity, "currentEntity");
			throw new InvalidOperationException(Strings.ObjectStateEntry_CantModifyRelationValues);
		}

		// Token: 0x0600341A RID: 13338 RVA: 0x000A74F9 File Offset: 0x000A56F9
		public override void ApplyOriginalValues(object originalEntity)
		{
			Check.NotNull<object>(originalEntity, "originalEntity");
			throw new InvalidOperationException(Strings.ObjectStateEntry_CantModifyRelationValues);
		}

		// Token: 0x17000A14 RID: 2580
		// (get) Token: 0x0600341B RID: 13339 RVA: 0x000A7511 File Offset: 0x000A5711
		internal override bool IsKeyEntry
		{
			get
			{
				return false;
			}
		}

		// Token: 0x0600341C RID: 13340 RVA: 0x000A7514 File Offset: 0x000A5714
		internal override int GetFieldCount(StateManagerTypeMetadata metadata)
		{
			return this._relationshipWrapper.AssociationEndMembers.Count;
		}

		// Token: 0x0600341D RID: 13341 RVA: 0x000A7526 File Offset: 0x000A5726
		internal override DataRecordInfo GetDataRecordInfo(StateManagerTypeMetadata metadata, object userObject)
		{
			return new DataRecordInfo(TypeUsage.Create(((RelationshipSet)base.EntitySet).ElementType));
		}

		// Token: 0x0600341E RID: 13342 RVA: 0x000A7542 File Offset: 0x000A5742
		internal override void SetModifiedAll()
		{
			base.ValidateState();
			throw new InvalidOperationException(Strings.ObjectStateEntry_CantModifyRelationState);
		}

		// Token: 0x0600341F RID: 13343 RVA: 0x000A7554 File Offset: 0x000A5754
		internal override Type GetFieldType(int ordinal, StateManagerTypeMetadata metadata)
		{
			return typeof(EntityKey);
		}

		// Token: 0x06003420 RID: 13344 RVA: 0x000A7560 File Offset: 0x000A5760
		internal override string GetCLayerName(int ordinal, StateManagerTypeMetadata metadata)
		{
			RelationshipEntry.ValidateRelationshipRange(ordinal);
			return this._relationshipWrapper.AssociationEndMembers[ordinal].Name;
		}

		// Token: 0x06003421 RID: 13345 RVA: 0x000A7580 File Offset: 0x000A5780
		internal override int GetOrdinalforCLayerName(string name, StateManagerTypeMetadata metadata)
		{
			ReadOnlyMetadataCollection<AssociationEndMember> associationEndMembers = this._relationshipWrapper.AssociationEndMembers;
			AssociationEndMember associationEndMember;
			if (associationEndMembers.TryGetValue(name, false, out associationEndMember))
			{
				return associationEndMembers.IndexOf(associationEndMember);
			}
			return -1;
		}

		// Token: 0x06003422 RID: 13346 RVA: 0x000A75AE File Offset: 0x000A57AE
		internal override void RevertDelete()
		{
			base.State = EntityState.Unchanged;
			this._cache.ChangeState(this, EntityState.Deleted, base.State);
		}

		// Token: 0x06003423 RID: 13347 RVA: 0x000A75CA File Offset: 0x000A57CA
		internal override void EntityMemberChanging(string entityMemberName)
		{
			throw new InvalidOperationException(Strings.ObjectStateEntry_CantModifyRelationValues);
		}

		// Token: 0x06003424 RID: 13348 RVA: 0x000A75D6 File Offset: 0x000A57D6
		internal override void EntityMemberChanged(string entityMemberName)
		{
			throw new InvalidOperationException(Strings.ObjectStateEntry_CantModifyRelationValues);
		}

		// Token: 0x06003425 RID: 13349 RVA: 0x000A75E2 File Offset: 0x000A57E2
		internal override void EntityComplexMemberChanging(string entityMemberName, object complexObject, string complexObjectMemberName)
		{
			throw new InvalidOperationException(Strings.ObjectStateEntry_CantModifyRelationValues);
		}

		// Token: 0x06003426 RID: 13350 RVA: 0x000A75EE File Offset: 0x000A57EE
		internal override void EntityComplexMemberChanged(string entityMemberName, object complexObject, string complexObjectMemberName)
		{
			throw new InvalidOperationException(Strings.ObjectStateEntry_CantModifyRelationValues);
		}

		// Token: 0x06003427 RID: 13351 RVA: 0x000A75FC File Offset: 0x000A57FC
		internal bool IsSameAssociationSetAndRole(AssociationSet associationSet, AssociationEndMember associationMember, EntityKey entityKey)
		{
			if (this._entitySet != associationSet)
			{
				return false;
			}
			if (this._relationshipWrapper.AssociationSet.ElementType.AssociationEndMembers[0].Name == associationMember.Name)
			{
				return entityKey == this.Key0;
			}
			return entityKey == this.Key1;
		}

		// Token: 0x06003428 RID: 13352 RVA: 0x000A765A File Offset: 0x000A585A
		private object GetCurrentRelationValue(int ordinal, bool throwException)
		{
			RelationshipEntry.ValidateRelationshipRange(ordinal);
			base.ValidateState();
			if (base.State == EntityState.Deleted && throwException)
			{
				throw new InvalidOperationException(Strings.ObjectStateEntry_CurrentValuesDoesNotExist);
			}
			return this._relationshipWrapper.GetEntityKey(ordinal);
		}

		// Token: 0x06003429 RID: 13353 RVA: 0x000A768C File Offset: 0x000A588C
		private static void ValidateRelationshipRange(int ordinal)
		{
			if (1 < ordinal)
			{
				throw new ArgumentOutOfRangeException("ordinal");
			}
		}

		// Token: 0x0600342A RID: 13354 RVA: 0x000A769D File Offset: 0x000A589D
		internal object GetCurrentRelationValue(int ordinal)
		{
			return this.GetCurrentRelationValue(ordinal, true);
		}

		// Token: 0x17000A15 RID: 2581
		// (get) Token: 0x0600342B RID: 13355 RVA: 0x000A76A7 File Offset: 0x000A58A7
		// (set) Token: 0x0600342C RID: 13356 RVA: 0x000A76AF File Offset: 0x000A58AF
		internal RelationshipWrapper RelationshipWrapper
		{
			get
			{
				return this._relationshipWrapper;
			}
			set
			{
				this._relationshipWrapper = value;
			}
		}

		// Token: 0x0600342D RID: 13357 RVA: 0x000A76B8 File Offset: 0x000A58B8
		internal override void Reset()
		{
			this._relationshipWrapper = null;
			base.Reset();
		}

		// Token: 0x0600342E RID: 13358 RVA: 0x000A76C8 File Offset: 0x000A58C8
		internal void ChangeRelatedEnd(EntityKey oldKey, EntityKey newKey)
		{
			if (!oldKey.Equals(this.Key0))
			{
				this.RelationshipWrapper = new RelationshipWrapper(this.RelationshipWrapper, 1, newKey);
				return;
			}
			if (oldKey.Equals(this.Key1))
			{
				this.RelationshipWrapper = new RelationshipWrapper(this.RelationshipWrapper.AssociationSet, newKey);
				return;
			}
			this.RelationshipWrapper = new RelationshipWrapper(this.RelationshipWrapper, 0, newKey);
		}

		// Token: 0x0600342F RID: 13359 RVA: 0x000A7730 File Offset: 0x000A5930
		internal void DeleteUnnecessaryKeyEntries()
		{
			for (int i = 0; i < 2; i++)
			{
				EntityKey entityKey = this.GetCurrentRelationValue(i, false) as EntityKey;
				EntityEntry entityEntry = this._cache.GetEntityEntry(entityKey);
				if (entityEntry.IsKeyEntry)
				{
					bool flag = false;
					using (EntityEntry.RelationshipEndEnumerator enumerator = this._cache.FindRelationshipsByKey(entityKey).GetEnumerator())
					{
						while (enumerator.MoveNext())
						{
							if (enumerator.Current != this)
							{
								flag = true;
								break;
							}
						}
					}
					if (!flag)
					{
						this._cache.DeleteKeyEntry(entityEntry);
						return;
					}
				}
			}
		}

		// Token: 0x06003430 RID: 13360 RVA: 0x000A77D0 File Offset: 0x000A59D0
		internal void Delete(bool doFixup)
		{
			base.ValidateState();
			if (doFixup)
			{
				if (base.State != EntityState.Deleted)
				{
					EntityEntry entityEntry = this._cache.GetEntityEntry((EntityKey)this.GetCurrentRelationValue(0));
					IEntityWrapper wrappedEntity = entityEntry.WrappedEntity;
					EntityEntry entityEntry2 = this._cache.GetEntityEntry((EntityKey)this.GetCurrentRelationValue(1));
					IEntityWrapper wrappedEntity2 = entityEntry2.WrappedEntity;
					if (wrappedEntity.Entity != null && wrappedEntity2.Entity != null)
					{
						string name = this._relationshipWrapper.AssociationEndMembers[1].Name;
						string fullName = ((AssociationSet)this._entitySet).ElementType.FullName;
						wrappedEntity.RelationshipManager.RemoveEntity(name, fullName, wrappedEntity2);
						return;
					}
					EntityKey entityKey;
					RelationshipManager relationshipManager;
					if (wrappedEntity.Entity == null)
					{
						entityKey = entityEntry.EntityKey;
						relationshipManager = wrappedEntity2.RelationshipManager;
					}
					else
					{
						entityKey = entityEntry2.EntityKey;
						relationshipManager = wrappedEntity.RelationshipManager;
					}
					AssociationEndMember associationEndMember = this.RelationshipWrapper.GetAssociationEndMember(entityKey);
					((EntityReference)relationshipManager.GetRelatedEndInternal(associationEndMember.DeclaringType.FullName, associationEndMember.Name)).DetachedEntityKey = null;
					if (base.State == EntityState.Added)
					{
						this.DeleteUnnecessaryKeyEntries();
						this.DetachRelationshipEntry();
						return;
					}
					this._cache.ChangeState(this, base.State, EntityState.Deleted);
					base.State = EntityState.Deleted;
					return;
				}
			}
			else
			{
				EntityState state = base.State;
				if (state != EntityState.Unchanged)
				{
					if (state != EntityState.Added)
					{
						return;
					}
					this.DeleteUnnecessaryKeyEntries();
					this.DetachRelationshipEntry();
					return;
				}
				else
				{
					this._cache.ChangeState(this, EntityState.Unchanged, EntityState.Deleted);
					base.State = EntityState.Deleted;
				}
			}
		}

		// Token: 0x06003431 RID: 13361 RVA: 0x000A7953 File Offset: 0x000A5B53
		internal object GetOriginalRelationValue(int ordinal)
		{
			return this.GetCurrentRelationValue(ordinal, false);
		}

		// Token: 0x06003432 RID: 13362 RVA: 0x000A795D File Offset: 0x000A5B5D
		internal void DetachRelationshipEntry()
		{
			if (this._cache != null)
			{
				this._cache.ChangeState(this, base.State, EntityState.Detached);
			}
		}

		// Token: 0x06003433 RID: 13363 RVA: 0x000A797C File Offset: 0x000A5B7C
		internal void ChangeRelationshipState(EntityEntry targetEntry, RelatedEnd relatedEnd, EntityState requestedState)
		{
			EntityState state = base.State;
			if (state != EntityState.Unchanged)
			{
				if (state != EntityState.Added)
				{
					if (state != EntityState.Deleted)
					{
						return;
					}
					switch (requestedState)
					{
					case EntityState.Detached:
						this.AcceptChanges();
						break;
					case EntityState.Unchanged:
						relatedEnd.Add(targetEntry.WrappedEntity, true, false, true, false, true);
						base.ObjectStateManager.ChangeState(this, EntityState.Deleted, EntityState.Unchanged);
						base.State = EntityState.Unchanged;
						return;
					case EntityState.Detached | EntityState.Unchanged:
						break;
					case EntityState.Added:
						relatedEnd.Add(targetEntry.WrappedEntity, true, false, true, false, true);
						base.ObjectStateManager.ChangeState(this, EntityState.Deleted, EntityState.Added);
						base.State = EntityState.Added;
						return;
					default:
						return;
					}
				}
				else
				{
					switch (requestedState)
					{
					case EntityState.Detached:
						this.Delete();
						return;
					case EntityState.Unchanged:
						this.AcceptChanges();
						return;
					case EntityState.Detached | EntityState.Unchanged:
					case EntityState.Added:
						break;
					default:
						if (requestedState != EntityState.Deleted)
						{
							return;
						}
						this.AcceptChanges();
						this.Delete();
						return;
					}
				}
			}
			else
			{
				switch (requestedState)
				{
				case EntityState.Detached:
					this.Delete();
					this.AcceptChanges();
					return;
				case EntityState.Unchanged:
				case EntityState.Detached | EntityState.Unchanged:
					break;
				case EntityState.Added:
					base.ObjectStateManager.ChangeState(this, EntityState.Unchanged, EntityState.Added);
					base.State = EntityState.Added;
					return;
				default:
					if (requestedState != EntityState.Deleted)
					{
						return;
					}
					this.Delete();
					return;
				}
			}
		}

		// Token: 0x06003434 RID: 13364 RVA: 0x000A7A90 File Offset: 0x000A5C90
		internal RelationshipEntry GetNextRelationshipEnd(EntityKey entityKey)
		{
			if (!entityKey.Equals(this.Key0))
			{
				return this.NextKey1;
			}
			return this.NextKey0;
		}

		// Token: 0x06003435 RID: 13365 RVA: 0x000A7AAD File Offset: 0x000A5CAD
		internal void SetNextRelationshipEnd(EntityKey entityKey, RelationshipEntry nextEnd)
		{
			if (entityKey.Equals(this.Key0))
			{
				this.NextKey0 = nextEnd;
				return;
			}
			this.NextKey1 = nextEnd;
		}

		// Token: 0x17000A16 RID: 2582
		// (get) Token: 0x06003436 RID: 13366 RVA: 0x000A7ACC File Offset: 0x000A5CCC
		// (set) Token: 0x06003437 RID: 13367 RVA: 0x000A7AD4 File Offset: 0x000A5CD4
		internal RelationshipEntry NextKey0 { get; set; }

		// Token: 0x17000A17 RID: 2583
		// (get) Token: 0x06003438 RID: 13368 RVA: 0x000A7ADD File Offset: 0x000A5CDD
		// (set) Token: 0x06003439 RID: 13369 RVA: 0x000A7AE5 File Offset: 0x000A5CE5
		internal RelationshipEntry NextKey1 { get; set; }

		// Token: 0x040010D4 RID: 4308
		internal RelationshipWrapper _relationshipWrapper;
	}
}
