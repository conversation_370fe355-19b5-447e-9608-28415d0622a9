﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.QueryRewriting;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration
{
	// Token: 0x0200056D RID: 1389
	internal class ViewgenContext : InternalBase
	{
		// Token: 0x060043BB RID: 17339 RVA: 0x000EA790 File Offset: 0x000E8990
		internal ViewgenContext(ViewTarget viewTarget, EntitySetBase extent, IList<Cell> extentCells, CqlIdentifiers identifiers, ConfigViewGenerator config, MemberDomainMap queryDomainMap, MemberDomainMap updateDomainMap, EntityContainerMapping entityContainerMapping)
		{
			foreach (Cell cell in extentCells)
			{
			}
			this.m_extent = extent;
			this.m_viewTarget = viewTarget;
			this.m_config = config;
			this.m_edmItemCollection = entityContainerMapping.StorageMappingItemCollection.EdmItemCollection;
			this.m_entityContainerMapping = entityContainerMapping;
			this.m_identifiers = identifiers;
			updateDomainMap = updateDomainMap.MakeCopy();
			MemberDomainMap memberDomainMap = ((viewTarget == ViewTarget.QueryView) ? queryDomainMap : updateDomainMap);
			this.m_memberMaps = new MemberMaps(viewTarget, MemberProjectionIndex.Create(extent, this.m_edmItemCollection), queryDomainMap, updateDomainMap);
			FragmentQueryKBChaseSupport fragmentQueryKBChaseSupport = new FragmentQueryKBChaseSupport();
			fragmentQueryKBChaseSupport.CreateVariableConstraints(extent, memberDomainMap, this.m_edmItemCollection);
			this.m_leftFragmentQP = new FragmentQueryProcessor(fragmentQueryKBChaseSupport);
			this.m_rewritingCache = new Dictionary<FragmentQuery, Tile<FragmentQuery>>(FragmentQuery.GetEqualityComparer(this.m_leftFragmentQP));
			if (!this.CreateLeftCellWrappers(extentCells, viewTarget))
			{
				return;
			}
			FragmentQueryKBChaseSupport fragmentQueryKBChaseSupport2 = new FragmentQueryKBChaseSupport();
			MemberDomainMap memberDomainMap2 = ((viewTarget == ViewTarget.QueryView) ? updateDomainMap : queryDomainMap);
			foreach (LeftCellWrapper leftCellWrapper in this.m_cellWrappers)
			{
				EntitySetBase rightExtent = leftCellWrapper.RightExtent;
				fragmentQueryKBChaseSupport2.CreateVariableConstraints(rightExtent, memberDomainMap2, this.m_edmItemCollection);
				fragmentQueryKBChaseSupport2.CreateAssociationConstraints(rightExtent, memberDomainMap2, this.m_edmItemCollection);
			}
			if (this.m_viewTarget == ViewTarget.UpdateView)
			{
				this.CreateConstraintsForForeignKeyAssociationsAffectingThisWrapper(fragmentQueryKBChaseSupport2, memberDomainMap2);
			}
			this.m_rightFragmentQP = new FragmentQueryProcessor(fragmentQueryKBChaseSupport2);
			if (this.m_viewTarget == ViewTarget.QueryView)
			{
				this.CheckConcurrencyControlTokens();
			}
			this.m_cellWrappers.Sort(LeftCellWrapper.Comparer);
		}

		// Token: 0x060043BC RID: 17340 RVA: 0x000EA930 File Offset: 0x000E8B30
		private void CreateConstraintsForForeignKeyAssociationsAffectingThisWrapper(FragmentQueryKB rightKB, MemberDomainMap rightDomainMap)
		{
			foreach (AssociationSet associationSet in new ViewgenContext.OneToOneFkAssociationsForEntitiesFilter().Filter((from it in this.m_cellWrappers.Select((LeftCellWrapper it) => it.RightExtent).OfType<EntitySet>()
				select it.ElementType).ToList<EntityType>(), this.m_entityContainerMapping.EdmEntityContainer.BaseEntitySets.OfType<AssociationSet>()))
			{
				rightKB.CreateEquivalenceConstraintForOneToOneForeignKeyAssociation(associationSet, rightDomainMap);
			}
		}

		// Token: 0x17000D6C RID: 3436
		// (get) Token: 0x060043BD RID: 17341 RVA: 0x000EA9F0 File Offset: 0x000E8BF0
		internal ViewTarget ViewTarget
		{
			get
			{
				return this.m_viewTarget;
			}
		}

		// Token: 0x17000D6D RID: 3437
		// (get) Token: 0x060043BE RID: 17342 RVA: 0x000EA9F8 File Offset: 0x000E8BF8
		internal MemberMaps MemberMaps
		{
			get
			{
				return this.m_memberMaps;
			}
		}

		// Token: 0x17000D6E RID: 3438
		// (get) Token: 0x060043BF RID: 17343 RVA: 0x000EAA00 File Offset: 0x000E8C00
		internal EntitySetBase Extent
		{
			get
			{
				return this.m_extent;
			}
		}

		// Token: 0x17000D6F RID: 3439
		// (get) Token: 0x060043C0 RID: 17344 RVA: 0x000EAA08 File Offset: 0x000E8C08
		internal ConfigViewGenerator Config
		{
			get
			{
				return this.m_config;
			}
		}

		// Token: 0x17000D70 RID: 3440
		// (get) Token: 0x060043C1 RID: 17345 RVA: 0x000EAA10 File Offset: 0x000E8C10
		internal CqlIdentifiers CqlIdentifiers
		{
			get
			{
				return this.m_identifiers;
			}
		}

		// Token: 0x17000D71 RID: 3441
		// (get) Token: 0x060043C2 RID: 17346 RVA: 0x000EAA18 File Offset: 0x000E8C18
		internal EdmItemCollection EdmItemCollection
		{
			get
			{
				return this.m_edmItemCollection;
			}
		}

		// Token: 0x17000D72 RID: 3442
		// (get) Token: 0x060043C3 RID: 17347 RVA: 0x000EAA20 File Offset: 0x000E8C20
		internal FragmentQueryProcessor LeftFragmentQP
		{
			get
			{
				return this.m_leftFragmentQP;
			}
		}

		// Token: 0x17000D73 RID: 3443
		// (get) Token: 0x060043C4 RID: 17348 RVA: 0x000EAA28 File Offset: 0x000E8C28
		internal FragmentQueryProcessor RightFragmentQP
		{
			get
			{
				return this.m_rightFragmentQP;
			}
		}

		// Token: 0x17000D74 RID: 3444
		// (get) Token: 0x060043C5 RID: 17349 RVA: 0x000EAA30 File Offset: 0x000E8C30
		internal List<LeftCellWrapper> AllWrappersForExtent
		{
			get
			{
				return this.m_cellWrappers;
			}
		}

		// Token: 0x17000D75 RID: 3445
		// (get) Token: 0x060043C6 RID: 17350 RVA: 0x000EAA38 File Offset: 0x000E8C38
		internal EntityContainerMapping EntityContainerMapping
		{
			get
			{
				return this.m_entityContainerMapping;
			}
		}

		// Token: 0x060043C7 RID: 17351 RVA: 0x000EAA40 File Offset: 0x000E8C40
		internal bool TryGetCachedRewriting(FragmentQuery query, out Tile<FragmentQuery> rewriting)
		{
			return this.m_rewritingCache.TryGetValue(query, out rewriting);
		}

		// Token: 0x060043C8 RID: 17352 RVA: 0x000EAA4F File Offset: 0x000E8C4F
		internal void SetCachedRewriting(FragmentQuery query, Tile<FragmentQuery> rewriting)
		{
			this.m_rewritingCache[query] = rewriting;
		}

		// Token: 0x060043C9 RID: 17353 RVA: 0x000EAA60 File Offset: 0x000E8C60
		private void CheckConcurrencyControlTokens()
		{
			EntityTypeBase elementType = this.m_extent.ElementType;
			Set<EdmMember> concurrencyMembersForTypeHierarchy = MetadataHelper.GetConcurrencyMembersForTypeHierarchy(elementType, this.m_edmItemCollection);
			Set<MemberPath> set = new Set<MemberPath>(MemberPath.EqualityComparer);
			foreach (EdmMember edmMember in concurrencyMembersForTypeHierarchy)
			{
				if (!edmMember.DeclaringType.IsAssignableFrom(elementType))
				{
					string text = Strings.ViewGen_Concurrency_Derived_Class(edmMember.Name, edmMember.DeclaringType.Name, this.m_extent);
					ExceptionHelpers.ThrowMappingException(new ErrorLog.Record(ViewGenErrorCode.ConcurrencyDerivedClass, text, this.m_cellWrappers, string.Empty), this.m_config);
				}
				set.Add(new MemberPath(this.m_extent, edmMember));
			}
			if (concurrencyMembersForTypeHierarchy.Count > 0)
			{
				foreach (LeftCellWrapper leftCellWrapper in this.m_cellWrappers)
				{
					Set<MemberPath> set2 = new Set<MemberPath>(leftCellWrapper.OnlyInputCell.CQuery.WhereClause.MemberRestrictions.Select((MemberRestriction oneOf) => oneOf.RestrictedMemberSlot.MemberPath), MemberPath.EqualityComparer);
					set2.Intersect(set);
					if (set2.Count > 0)
					{
						StringBuilder stringBuilder = new StringBuilder();
						stringBuilder.AppendLine(Strings.ViewGen_Concurrency_Invalid_Condition(MemberPath.PropertiesToUserString(set2, false), this.m_extent.Name));
						ExceptionHelpers.ThrowMappingException(new ErrorLog.Record(ViewGenErrorCode.ConcurrencyTokenHasCondition, stringBuilder.ToString(), new LeftCellWrapper[] { leftCellWrapper }, string.Empty), this.m_config);
					}
				}
			}
		}

		// Token: 0x060043CA RID: 17354 RVA: 0x000EAC2C File Offset: 0x000E8E2C
		private bool CreateLeftCellWrappers(IList<Cell> extentCells, ViewTarget viewTarget)
		{
			List<Cell> list = ViewgenContext.AlignFields(extentCells, this.m_memberMaps.ProjectedSlotMap, viewTarget);
			this.m_cellWrappers = new List<LeftCellWrapper>();
			for (int i = 0; i < list.Count; i++)
			{
				Cell cell = list[i];
				CellQuery leftQuery = cell.GetLeftQuery(viewTarget);
				CellQuery rightQuery = cell.GetRightQuery(viewTarget);
				Set<MemberPath> nonNullSlots = leftQuery.GetNonNullSlots();
				FragmentQuery fragmentQuery = FragmentQuery.Create(BoolExpression.CreateLiteral(new CellIdBoolean(this.m_identifiers, extentCells[i].CellNumber), this.m_memberMaps.LeftDomainMap), leftQuery);
				if (viewTarget == ViewTarget.UpdateView)
				{
					fragmentQuery = this.m_leftFragmentQP.CreateDerivedViewBySelectingConstantAttributes(fragmentQuery) ?? fragmentQuery;
				}
				LeftCellWrapper leftCellWrapper = new LeftCellWrapper(this.m_viewTarget, nonNullSlots, fragmentQuery, leftQuery, rightQuery, this.m_memberMaps, extentCells[i]);
				this.m_cellWrappers.Add(leftCellWrapper);
			}
			return true;
		}

		// Token: 0x060043CB RID: 17355 RVA: 0x000EAD04 File Offset: 0x000E8F04
		private static List<Cell> AlignFields(IEnumerable<Cell> cells, MemberProjectionIndex projectedSlotMap, ViewTarget viewTarget)
		{
			List<Cell> list = new List<Cell>();
			foreach (Cell cell in cells)
			{
				CellQuery leftQuery = cell.GetLeftQuery(viewTarget);
				CellQuery rightQuery = cell.GetRightQuery(viewTarget);
				CellQuery cellQuery;
				CellQuery cellQuery2;
				leftQuery.CreateFieldAlignedCellQueries(rightQuery, projectedSlotMap, out cellQuery, out cellQuery2);
				Cell cell2 = ((viewTarget == ViewTarget.QueryView) ? Cell.CreateCS(cellQuery, cellQuery2, cell.CellLabel, cell.CellNumber) : Cell.CreateCS(cellQuery2, cellQuery, cell.CellLabel, cell.CellNumber));
				list.Add(cell2);
			}
			return list;
		}

		// Token: 0x060043CC RID: 17356 RVA: 0x000EADA0 File Offset: 0x000E8FA0
		internal override void ToCompactString(StringBuilder builder)
		{
			LeftCellWrapper.WrappersToStringBuilder(builder, this.m_cellWrappers, "Left Cell Wrappers");
		}

		// Token: 0x04001845 RID: 6213
		private readonly ConfigViewGenerator m_config;

		// Token: 0x04001846 RID: 6214
		private readonly ViewTarget m_viewTarget;

		// Token: 0x04001847 RID: 6215
		private readonly EntitySetBase m_extent;

		// Token: 0x04001848 RID: 6216
		private readonly MemberMaps m_memberMaps;

		// Token: 0x04001849 RID: 6217
		private readonly EdmItemCollection m_edmItemCollection;

		// Token: 0x0400184A RID: 6218
		private readonly EntityContainerMapping m_entityContainerMapping;

		// Token: 0x0400184B RID: 6219
		private List<LeftCellWrapper> m_cellWrappers;

		// Token: 0x0400184C RID: 6220
		private readonly FragmentQueryProcessor m_leftFragmentQP;

		// Token: 0x0400184D RID: 6221
		private readonly FragmentQueryProcessor m_rightFragmentQP;

		// Token: 0x0400184E RID: 6222
		private readonly CqlIdentifiers m_identifiers;

		// Token: 0x0400184F RID: 6223
		private readonly Dictionary<FragmentQuery, Tile<FragmentQuery>> m_rewritingCache;

		// Token: 0x02000B7F RID: 2943
		internal class OneToOneFkAssociationsForEntitiesFilter
		{
			// Token: 0x06006687 RID: 26247 RVA: 0x0015F11C File Offset: 0x0015D31C
			public virtual IEnumerable<AssociationSet> Filter(IList<EntityType> entityTypes, IEnumerable<AssociationSet> associationSets)
			{
				Func<AssociationEndMember, bool> <>9__1;
				return associationSets.Where(delegate(AssociationSet a)
				{
					if (a.ElementType.IsForeignKey)
					{
						IEnumerable<AssociationEndMember> associationEndMembers = a.ElementType.AssociationEndMembers;
						Func<AssociationEndMember, bool> func;
						if ((func = <>9__1) == null)
						{
							func = (<>9__1 = (AssociationEndMember aem) => aem.RelationshipMultiplicity == RelationshipMultiplicity.One && entityTypes.Contains(aem.GetEntityType()));
						}
						return associationEndMembers.All(func);
					}
					return false;
				});
			}
		}
	}
}
