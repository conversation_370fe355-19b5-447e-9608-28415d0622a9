﻿using System;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x0200038D RID: 909
	internal sealed class ColumnVar : Var
	{
		// Token: 0x06002C1C RID: 11292 RVA: 0x0008E137 File Offset: 0x0008C337
		internal ColumnVar(int id, Table table, ColumnMD columnMetadata)
			: base(id, VarType.Column, columnMetadata.Type)
		{
			this.m_table = table;
			this.m_columnMetadata = columnMetadata;
		}

		// Token: 0x170008BB RID: 2235
		// (get) Token: 0x06002C1D RID: 11293 RVA: 0x0008E155 File Offset: 0x0008C355
		internal Table Table
		{
			get
			{
				return this.m_table;
			}
		}

		// Token: 0x170008BC RID: 2236
		// (get) Token: 0x06002C1E RID: 11294 RVA: 0x0008E15D File Offset: 0x0008C35D
		internal ColumnMD ColumnMetadata
		{
			get
			{
				return this.m_columnMetadata;
			}
		}

		// Token: 0x06002C1F RID: 11295 RVA: 0x0008E165 File Offset: 0x0008C365
		internal override bool TryGetName(out string name)
		{
			name = this.m_columnMetadata.Name;
			return true;
		}

		// Token: 0x04000EF0 RID: 3824
		private readonly ColumnMD m_columnMetadata;

		// Token: 0x04000EF1 RID: 3825
		private readonly Table m_table;
	}
}
