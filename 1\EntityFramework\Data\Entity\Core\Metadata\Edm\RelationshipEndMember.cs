﻿using System;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004F2 RID: 1266
	public abstract class RelationshipEndMember : EdmMember
	{
		// Token: 0x06003EFD RID: 16125 RVA: 0x000D098F File Offset: 0x000CEB8F
		internal RelationshipEndMember(string name, RefType endRefType, RelationshipMultiplicity multiplicity)
			: base(name, TypeUsage.Create(endRefType, new FacetValues
			{
				Nullable = new bool?(false)
			}))
		{
			this._relationshipMultiplicity = multiplicity;
			this._deleteBehavior = OperationAction.None;
		}

		// Token: 0x17000C5E RID: 3166
		// (get) Token: 0x06003EFE RID: 16126 RVA: 0x000D09C2 File Offset: 0x000CEBC2
		// (set) Token: 0x06003EFF RID: 16127 RVA: 0x000D09CA File Offset: 0x000CEBCA
		[MetadataProperty(BuiltInTypeKind.OperationAction, true)]
		public OperationAction DeleteBehavior
		{
			get
			{
				return this._deleteBehavior;
			}
			set
			{
				Util.ThrowIfReadOnly(this);
				this._deleteBehavior = value;
			}
		}

		// Token: 0x17000C5F RID: 3167
		// (get) Token: 0x06003F00 RID: 16128 RVA: 0x000D09D9 File Offset: 0x000CEBD9
		// (set) Token: 0x06003F01 RID: 16129 RVA: 0x000D09E1 File Offset: 0x000CEBE1
		[MetadataProperty(BuiltInTypeKind.RelationshipMultiplicity, false)]
		public RelationshipMultiplicity RelationshipMultiplicity
		{
			get
			{
				return this._relationshipMultiplicity;
			}
			set
			{
				Util.ThrowIfReadOnly(this);
				this._relationshipMultiplicity = value;
			}
		}

		// Token: 0x06003F02 RID: 16130 RVA: 0x000D09F0 File Offset: 0x000CEBF0
		public EntityType GetEntityType()
		{
			if (this.TypeUsage == null)
			{
				return null;
			}
			return (EntityType)((RefType)this.TypeUsage.EdmType).ElementType;
		}

		// Token: 0x0400157C RID: 5500
		private OperationAction _deleteBehavior;

		// Token: 0x0400157D RID: 5501
		private RelationshipMultiplicity _relationshipMultiplicity;
	}
}
