﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000026 RID: 38
	public class Sys_Users_CaveLoot_Rank
	{
		// Token: 0x17000126 RID: 294
		// (get) Token: 0x06000271 RID: 625 RVA: 0x00003536 File Offset: 0x00001736
		// (set) Token: 0x06000272 RID: 626 RVA: 0x0000353E File Offset: 0x0000173E
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000127 RID: 295
		// (get) Token: 0x06000273 RID: 627 RVA: 0x00003547 File Offset: 0x00001747
		// (set) Token: 0x06000274 RID: 628 RVA: 0x0000354F File Offset: 0x0000174F
		public int UserID { get; set; }

		// Token: 0x17000128 RID: 296
		// (get) Token: 0x06000275 RID: 629 RVA: 0x00003558 File Offset: 0x00001758
		// (set) Token: 0x06000276 RID: 630 RVA: 0x00003560 File Offset: 0x00001760
		public string PlayerName { get; set; }

		// Token: 0x17000129 RID: 297
		// (get) Token: 0x06000277 RID: 631 RVA: 0x00003569 File Offset: 0x00001769
		// (set) Token: 0x06000278 RID: 632 RVA: 0x00003571 File Offset: 0x00001771
		public int Score { get; set; }

		// Token: 0x1700012A RID: 298
		// (get) Token: 0x06000279 RID: 633 RVA: 0x0000357A File Offset: 0x0000177A
		// (set) Token: 0x0600027A RID: 634 RVA: 0x00003582 File Offset: 0x00001782
		public string AreaName { get; set; }

		// Token: 0x1700012B RID: 299
		// (get) Token: 0x0600027B RID: 635 RVA: 0x0000358B File Offset: 0x0000178B
		// (set) Token: 0x0600027C RID: 636 RVA: 0x00003593 File Offset: 0x00001793
		public DateTime CreateTime { get; set; }
	}
}
