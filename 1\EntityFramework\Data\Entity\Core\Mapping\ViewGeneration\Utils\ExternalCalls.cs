﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Common.EntitySql;
using System.Data.Entity.Core.Metadata.Edm;
using System.Linq;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Utils
{
	// Token: 0x02000574 RID: 1396
	internal static class ExternalCalls
	{
		// Token: 0x060043EB RID: 17387 RVA: 0x000EBDF6 File Offset: 0x000E9FF6
		internal static bool IsReservedKeyword(string name)
		{
			return CqlLexer.IsReservedKeyword(name);
		}

		// Token: 0x060043EC RID: 17388 RVA: 0x000EBE00 File Offset: 0x000EA000
		internal static DbCommandTree CompileView(string viewDef, StorageMappingItemCollection mappingItemCollection, ParserOptions.CompilationMode compilationMode)
		{
			Perspective perspective = new TargetPerspective(mappingItemCollection.Workspace);
			return CqlQuery.Compile(viewDef, perspective, new ParserOptions
			{
				ParserCompilationMode = compilationMode
			}, null).CommandTree;
		}

		// Token: 0x060043ED RID: 17389 RVA: 0x000EBE34 File Offset: 0x000EA034
		internal static DbExpression CompileFunctionView(string viewDef, StorageMappingItemCollection mappingItemCollection, ParserOptions.CompilationMode compilationMode, IEnumerable<DbParameterReferenceExpression> parameters)
		{
			Perspective perspective = new TargetPerspective(mappingItemCollection.Workspace);
			ParserOptions parserOptions = new ParserOptions();
			parserOptions.ParserCompilationMode = compilationMode;
			return CqlQuery.CompileQueryCommandLambda(viewDef, perspective, parserOptions, null, parameters.Select((DbParameterReferenceExpression pInfo) => pInfo.ResultType.Variable(pInfo.ParameterName))).Invoke(parameters);
		}

		// Token: 0x060043EE RID: 17390 RVA: 0x000EBE90 File Offset: 0x000EA090
		internal static DbLambda CompileFunctionDefinition(string functionDefinition, IList<FunctionParameter> functionParameters, EdmItemCollection edmItemCollection)
		{
			ModelPerspective modelPerspective = new ModelPerspective(new MetadataWorkspace(() => edmItemCollection, () => null, () => null));
			return CqlQuery.CompileQueryCommandLambda(functionDefinition, modelPerspective, null, null, functionParameters.Select((FunctionParameter pInfo) => pInfo.TypeUsage.Variable(pInfo.Name)));
		}
	}
}
