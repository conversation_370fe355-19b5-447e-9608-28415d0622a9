﻿using System;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003E1 RID: 993
	internal sealed class ScanViewOp : ScanTableBaseOp
	{
		// Token: 0x06002EFA RID: 12026 RVA: 0x000944A6 File Offset: 0x000926A6
		internal ScanViewOp(Table table)
			: base(OpType.ScanView, table)
		{
		}

		// Token: 0x06002EFB RID: 12027 RVA: 0x000944B1 File Offset: 0x000926B1
		private ScanViewOp()
			: base(OpType.ScanView)
		{
		}

		// Token: 0x17000938 RID: 2360
		// (get) Token: 0x06002EFC RID: 12028 RVA: 0x000944BB File Offset: 0x000926BB
		internal override int Arity
		{
			get
			{
				return 1;
			}
		}

		// Token: 0x06002EFD RID: 12029 RVA: 0x000944BE File Offset: 0x000926BE
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002EFE RID: 12030 RVA: 0x000944C8 File Offset: 0x000926C8
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000FD7 RID: 4055
		internal static readonly ScanViewOp Pattern = new ScanViewOp();
	}
}
