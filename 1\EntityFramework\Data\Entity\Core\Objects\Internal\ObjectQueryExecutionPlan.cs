﻿using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Common.Internal.Materialization;
using System.Data.Entity.Core.EntityClient;
using System.Data.Entity.Core.EntityClient.Internal;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects.ELinq;
using System.Data.Entity.Infrastructure.DependencyResolution;
using System.Data.Entity.Utilities;
using System.Threading;
using System.Threading.Tasks;

namespace System.Data.Entity.Core.Objects.Internal
{
	// Token: 0x02000450 RID: 1104
	internal class ObjectQueryExecutionPlan
	{
		// Token: 0x060035E9 RID: 13801 RVA: 0x000AC774 File Offset: 0x000AA974
		public ObjectQueryExecutionPlan(DbCommandDefinition commandDefinition, ShaperFactory resultShaperFactory, TypeUsage resultType, MergeOption mergeOption, bool streaming, EntitySet singleEntitySet, IEnumerable<Tuple<ObjectParameter, QueryParameterExpression>> compiledQueryParameters)
		{
			this.CommandDefinition = commandDefinition;
			this.ResultShaperFactory = resultShaperFactory;
			this.ResultType = resultType;
			this.MergeOption = mergeOption;
			this.Streaming = streaming;
			this._singleEntitySet = singleEntitySet;
			this.CompiledQueryParameters = compiledQueryParameters;
		}

		// Token: 0x060035EA RID: 13802 RVA: 0x000AC7B4 File Offset: 0x000AA9B4
		internal string ToTraceString()
		{
			EntityCommandDefinition entityCommandDefinition = this.CommandDefinition as EntityCommandDefinition;
			if (entityCommandDefinition == null)
			{
				return string.Empty;
			}
			return entityCommandDefinition.ToTraceString();
		}

		// Token: 0x060035EB RID: 13803 RVA: 0x000AC7DC File Offset: 0x000AA9DC
		internal virtual ObjectResult<TResultType> Execute<TResultType>(ObjectContext context, ObjectParameterCollection parameterValues)
		{
			DbDataReader dbDataReader = null;
			BufferedDataReader bufferedDataReader = null;
			ObjectResult<TResultType> objectResult;
			try
			{
				using (EntityCommand entityCommand = this.PrepareEntityCommand(context, parameterValues))
				{
					dbDataReader = entityCommand.GetCommandDefinition().ExecuteStoreCommands(entityCommand, this.Streaming ? CommandBehavior.Default : CommandBehavior.SequentialAccess);
				}
				ShaperFactory<TResultType> shaperFactory = (ShaperFactory<TResultType>)this.ResultShaperFactory;
				Shaper<TResultType> shaper;
				if (this.Streaming)
				{
					shaper = shaperFactory.Create(dbDataReader, context, context.MetadataWorkspace, this.MergeOption, true, this.Streaming);
				}
				else
				{
					StoreItemCollection storeItemCollection = (StoreItemCollection)context.MetadataWorkspace.GetItemCollection(DataSpace.SSpace);
					DbProviderServices service = DbConfiguration.DependencyResolver.GetService(storeItemCollection.ProviderInvariantName);
					bufferedDataReader = new BufferedDataReader(dbDataReader);
					bufferedDataReader.Initialize(storeItemCollection.ProviderManifestToken, service, shaperFactory.ColumnTypes, shaperFactory.NullableColumns);
					shaper = shaperFactory.Create(bufferedDataReader, context, context.MetadataWorkspace, this.MergeOption, true, this.Streaming);
				}
				TypeUsage typeUsage;
				if (this.ResultType.EdmType.BuiltInTypeKind == BuiltInTypeKind.CollectionType)
				{
					typeUsage = ((CollectionType)this.ResultType.EdmType).TypeUsage;
				}
				else
				{
					typeUsage = this.ResultType;
				}
				objectResult = new ObjectResult<TResultType>(shaper, this._singleEntitySet, typeUsage);
			}
			catch (Exception)
			{
				if (this.Streaming && dbDataReader != null)
				{
					dbDataReader.Dispose();
				}
				if (!this.Streaming && bufferedDataReader != null)
				{
					bufferedDataReader.Dispose();
				}
				throw;
			}
			return objectResult;
		}

		// Token: 0x060035EC RID: 13804 RVA: 0x000AC958 File Offset: 0x000AAB58
		internal virtual async Task<ObjectResult<TResultType>> ExecuteAsync<TResultType>(ObjectContext context, ObjectParameterCollection parameterValues, CancellationToken cancellationToken)
		{
			cancellationToken.ThrowIfCancellationRequested();
			DbDataReader storeReader = null;
			BufferedDataReader bufferedReader = null;
			ObjectResult<TResultType> objectResult;
			try
			{
				using (EntityCommand entityCommand = this.PrepareEntityCommand(context, parameterValues))
				{
					DbDataReader dbDataReader = await entityCommand.GetCommandDefinition().ExecuteStoreCommandsAsync(entityCommand, this.Streaming ? CommandBehavior.Default : CommandBehavior.SequentialAccess, cancellationToken).WithCurrentCulture<DbDataReader>();
					storeReader = dbDataReader;
				}
				EntityCommand entityCommand = null;
				ShaperFactory<TResultType> shaperFactory = (ShaperFactory<TResultType>)this.ResultShaperFactory;
				Shaper<TResultType> shaper;
				if (this.Streaming)
				{
					shaper = shaperFactory.Create(storeReader, context, context.MetadataWorkspace, this.MergeOption, true, this.Streaming);
				}
				else
				{
					StoreItemCollection storeItemCollection = (StoreItemCollection)context.MetadataWorkspace.GetItemCollection(DataSpace.SSpace);
					DbProviderServices service = DbConfiguration.DependencyResolver.GetService(storeItemCollection.ProviderInvariantName);
					bufferedReader = new BufferedDataReader(storeReader);
					await bufferedReader.InitializeAsync(storeItemCollection.ProviderManifestToken, service, shaperFactory.ColumnTypes, shaperFactory.NullableColumns, cancellationToken).WithCurrentCulture();
					shaper = shaperFactory.Create(bufferedReader, context, context.MetadataWorkspace, this.MergeOption, true, this.Streaming);
				}
				TypeUsage typeUsage;
				if (this.ResultType.EdmType.BuiltInTypeKind == BuiltInTypeKind.CollectionType)
				{
					typeUsage = ((CollectionType)this.ResultType.EdmType).TypeUsage;
				}
				else
				{
					typeUsage = this.ResultType;
				}
				objectResult = new ObjectResult<TResultType>(shaper, this._singleEntitySet, typeUsage);
			}
			catch (Exception)
			{
				if (this.Streaming && storeReader != null)
				{
					storeReader.Dispose();
				}
				if (!this.Streaming && bufferedReader != null)
				{
					bufferedReader.Dispose();
				}
				throw;
			}
			return objectResult;
		}

		// Token: 0x060035ED RID: 13805 RVA: 0x000AC9B8 File Offset: 0x000AABB8
		private EntityCommand PrepareEntityCommand(ObjectContext context, ObjectParameterCollection parameterValues)
		{
			EntityCommandDefinition entityCommandDefinition = (EntityCommandDefinition)this.CommandDefinition;
			EntityConnection entityConnection = (EntityConnection)context.Connection;
			EntityCommand entityCommand = new EntityCommand(entityConnection, entityCommandDefinition, context.InterceptionContext, null);
			if (context.CommandTimeout != null)
			{
				entityCommand.CommandTimeout = context.CommandTimeout.Value;
			}
			if (parameterValues != null)
			{
				foreach (ObjectParameter objectParameter in parameterValues)
				{
					int num = entityCommand.Parameters.IndexOf(objectParameter.Name);
					if (num != -1)
					{
						entityCommand.Parameters[num].Value = objectParameter.Value ?? DBNull.Value;
					}
				}
			}
			if (entityConnection.CurrentTransaction != null)
			{
				entityCommand.Transaction = entityConnection.CurrentTransaction;
			}
			return entityCommand;
		}

		// Token: 0x04001164 RID: 4452
		internal readonly DbCommandDefinition CommandDefinition;

		// Token: 0x04001165 RID: 4453
		internal readonly bool Streaming;

		// Token: 0x04001166 RID: 4454
		internal readonly ShaperFactory ResultShaperFactory;

		// Token: 0x04001167 RID: 4455
		internal readonly TypeUsage ResultType;

		// Token: 0x04001168 RID: 4456
		internal readonly MergeOption MergeOption;

		// Token: 0x04001169 RID: 4457
		internal readonly IEnumerable<Tuple<ObjectParameter, QueryParameterExpression>> CompiledQueryParameters;

		// Token: 0x0400116A RID: 4458
		private readonly EntitySet _singleEntitySet;
	}
}
