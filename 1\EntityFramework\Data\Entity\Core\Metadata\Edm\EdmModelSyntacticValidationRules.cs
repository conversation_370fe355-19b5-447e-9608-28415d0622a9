﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004AD RID: 1197
	internal static class EdmModelSyntacticValidationRules
	{
		// Token: 0x06003AF7 RID: 15095 RVA: 0x000C1F94 File Offset: 0x000C0194
		private static bool IsEdmTypeUsageValid(TypeUsage typeUsage)
		{
			HashSet<TypeUsage> hashSet = new HashSet<TypeUsage>();
			return EdmModelSyntacticValidationRules.IsEdmTypeUsageValid(typeUsage, hashSet);
		}

		// Token: 0x06003AF8 RID: 15096 RVA: 0x000C1FAE File Offset: 0x000C01AE
		private static bool IsEdmTypeUsageValid(TypeUsage typeUsage, HashSet<TypeUsage> visitedValidTypeUsages)
		{
			if (visitedValidTypeUsages.Contains(typeUsage))
			{
				return false;
			}
			visitedValidTypeUsages.Add(typeUsage);
			return true;
		}

		// Token: 0x04001462 RID: 5218
		internal static readonly EdmModelValidationRule<INamedDataModelItem> EdmModel_NameMustNotBeEmptyOrWhiteSpace = new EdmModelValidationRule<INamedDataModelItem>(delegate(EdmModelValidationContext context, INamedDataModelItem item)
		{
			if (string.IsNullOrWhiteSpace(item.Name))
			{
				context.AddError((MetadataItem)item, "Name", Strings.EdmModel_Validator_Syntactic_MissingName);
			}
		});

		// Token: 0x04001463 RID: 5219
		internal static readonly EdmModelValidationRule<INamedDataModelItem> EdmModel_NameIsTooLong = new EdmModelValidationRule<INamedDataModelItem>(delegate(EdmModelValidationContext context, INamedDataModelItem item)
		{
			if (!string.IsNullOrWhiteSpace(item.Name) && item.Name.Length > 480 && !(item is RowType) && !(item is CollectionType))
			{
				context.AddError((MetadataItem)item, "Name", Strings.EdmModel_Validator_Syntactic_EdmModel_NameIsTooLong(item.Name));
			}
		});

		// Token: 0x04001464 RID: 5220
		internal static readonly EdmModelValidationRule<INamedDataModelItem> EdmModel_NameIsNotAllowed = new EdmModelValidationRule<INamedDataModelItem>(delegate(EdmModelValidationContext context, INamedDataModelItem item)
		{
			if (string.IsNullOrWhiteSpace(item.Name) || item is RowType || item is CollectionType || (!context.IsCSpace && item is EdmProperty))
			{
				return;
			}
			if (item.Name.Contains(".") || (context.IsCSpace && !item.Name.IsValidUndottedName()))
			{
				context.AddError((MetadataItem)item, "Name", Strings.EdmModel_Validator_Syntactic_EdmModel_NameIsNotAllowed(item.Name));
			}
		});

		// Token: 0x04001465 RID: 5221
		internal static readonly EdmModelValidationRule<AssociationType> EdmAssociationType_AssociationEndMustNotBeNull = new EdmModelValidationRule<AssociationType>(delegate(EdmModelValidationContext context, AssociationType edmAssociationType)
		{
			if (edmAssociationType.SourceEnd == null || edmAssociationType.TargetEnd == null)
			{
				context.AddError(edmAssociationType, "End", Strings.EdmModel_Validator_Syntactic_EdmAssociationType_AssociationEndMustNotBeNull);
			}
		});

		// Token: 0x04001466 RID: 5222
		internal static readonly EdmModelValidationRule<ReferentialConstraint> EdmAssociationConstraint_DependentEndMustNotBeNull = new EdmModelValidationRule<ReferentialConstraint>(delegate(EdmModelValidationContext context, ReferentialConstraint edmAssociationConstraint)
		{
			if (edmAssociationConstraint.ToRole == null)
			{
				context.AddError(edmAssociationConstraint, "Dependent", Strings.EdmModel_Validator_Syntactic_EdmAssociationConstraint_DependentEndMustNotBeNull);
			}
		});

		// Token: 0x04001467 RID: 5223
		internal static readonly EdmModelValidationRule<ReferentialConstraint> EdmAssociationConstraint_DependentPropertiesMustNotBeEmpty = new EdmModelValidationRule<ReferentialConstraint>(delegate(EdmModelValidationContext context, ReferentialConstraint edmAssociationConstraint)
		{
			if (edmAssociationConstraint.ToProperties == null || !edmAssociationConstraint.ToProperties.Any<EdmProperty>())
			{
				context.AddError(edmAssociationConstraint, "Dependent", Strings.EdmModel_Validator_Syntactic_EdmAssociationConstraint_DependentPropertiesMustNotBeEmpty);
			}
		});

		// Token: 0x04001468 RID: 5224
		internal static readonly EdmModelValidationRule<NavigationProperty> EdmNavigationProperty_AssociationMustNotBeNull = new EdmModelValidationRule<NavigationProperty>(delegate(EdmModelValidationContext context, NavigationProperty edmNavigationProperty)
		{
			if (edmNavigationProperty.Association == null)
			{
				context.AddError(edmNavigationProperty, "Relationship", Strings.EdmModel_Validator_Syntactic_EdmNavigationProperty_AssociationMustNotBeNull);
			}
		});

		// Token: 0x04001469 RID: 5225
		internal static readonly EdmModelValidationRule<NavigationProperty> EdmNavigationProperty_ResultEndMustNotBeNull = new EdmModelValidationRule<NavigationProperty>(delegate(EdmModelValidationContext context, NavigationProperty edmNavigationProperty)
		{
			if (edmNavigationProperty.ToEndMember == null)
			{
				context.AddError(edmNavigationProperty, "ToRole", Strings.EdmModel_Validator_Syntactic_EdmNavigationProperty_ResultEndMustNotBeNull);
			}
		});

		// Token: 0x0400146A RID: 5226
		internal static readonly EdmModelValidationRule<AssociationEndMember> EdmAssociationEnd_EntityTypeMustNotBeNull = new EdmModelValidationRule<AssociationEndMember>(delegate(EdmModelValidationContext context, AssociationEndMember edmAssociationEnd)
		{
			if (edmAssociationEnd.GetEntityType() == null)
			{
				context.AddError(edmAssociationEnd, "Type", Strings.EdmModel_Validator_Syntactic_EdmAssociationEnd_EntityTypeMustNotBeNull);
			}
		});

		// Token: 0x0400146B RID: 5227
		internal static readonly EdmModelValidationRule<EntitySet> EdmEntitySet_ElementTypeMustNotBeNull = new EdmModelValidationRule<EntitySet>(delegate(EdmModelValidationContext context, EntitySet edmEntitySet)
		{
			if (edmEntitySet.ElementType == null)
			{
				context.AddError(edmEntitySet, "ElementType", Strings.EdmModel_Validator_Syntactic_EdmEntitySet_ElementTypeMustNotBeNull);
			}
		});

		// Token: 0x0400146C RID: 5228
		internal static readonly EdmModelValidationRule<AssociationSet> EdmAssociationSet_ElementTypeMustNotBeNull = new EdmModelValidationRule<AssociationSet>(delegate(EdmModelValidationContext context, AssociationSet edmAssociationSet)
		{
			if (edmAssociationSet.ElementType == null)
			{
				context.AddError(edmAssociationSet, "ElementType", Strings.EdmModel_Validator_Syntactic_EdmAssociationSet_ElementTypeMustNotBeNull);
			}
		});

		// Token: 0x0400146D RID: 5229
		internal static readonly EdmModelValidationRule<AssociationSet> EdmAssociationSet_SourceSetMustNotBeNull = new EdmModelValidationRule<AssociationSet>(delegate(EdmModelValidationContext context, AssociationSet edmAssociationSet)
		{
			if (context.IsCSpace && edmAssociationSet.SourceSet == null)
			{
				context.AddError(edmAssociationSet, "FromRole", Strings.EdmModel_Validator_Syntactic_EdmAssociationSet_SourceSetMustNotBeNull);
			}
		});

		// Token: 0x0400146E RID: 5230
		internal static readonly EdmModelValidationRule<AssociationSet> EdmAssociationSet_TargetSetMustNotBeNull = new EdmModelValidationRule<AssociationSet>(delegate(EdmModelValidationContext context, AssociationSet edmAssociationSet)
		{
			if (context.IsCSpace && edmAssociationSet.TargetSet == null)
			{
				context.AddError(edmAssociationSet, "ToRole", Strings.EdmModel_Validator_Syntactic_EdmAssociationSet_TargetSetMustNotBeNull);
			}
		});

		// Token: 0x0400146F RID: 5231
		internal static readonly EdmModelValidationRule<TypeUsage> EdmTypeReference_TypeNotValid = new EdmModelValidationRule<TypeUsage>(delegate(EdmModelValidationContext context, TypeUsage edmTypeReference)
		{
			if (!EdmModelSyntacticValidationRules.IsEdmTypeUsageValid(edmTypeReference))
			{
				context.AddError(edmTypeReference, null, Strings.EdmModel_Validator_Syntactic_EdmTypeReferenceNotValid);
			}
		});
	}
}
