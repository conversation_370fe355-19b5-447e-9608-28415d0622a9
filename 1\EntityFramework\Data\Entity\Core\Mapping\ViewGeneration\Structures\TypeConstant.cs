﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Mapping.ViewGeneration.CqlGeneration;
using System.Data.Entity.Core.Metadata.Edm;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005B3 RID: 1459
	internal sealed class TypeConstant : Constant
	{
		// Token: 0x0600470C RID: 18188 RVA: 0x000FA01C File Offset: 0x000F821C
		internal TypeConstant(EdmType type)
		{
			this.m_edmType = type;
		}

		// Token: 0x17000E0E RID: 3598
		// (get) Token: 0x0600470D RID: 18189 RVA: 0x000FA02B File Offset: 0x000F822B
		internal EdmType EdmType
		{
			get
			{
				return this.m_edmType;
			}
		}

		// Token: 0x0600470E RID: 18190 RVA: 0x000FA033 File Offset: 0x000F8233
		internal override bool IsNull()
		{
			return false;
		}

		// Token: 0x0600470F RID: 18191 RVA: 0x000FA036 File Offset: 0x000F8236
		internal override bool IsNotNull()
		{
			return false;
		}

		// Token: 0x06004710 RID: 18192 RVA: 0x000FA039 File Offset: 0x000F8239
		internal override bool IsUndefined()
		{
			return false;
		}

		// Token: 0x06004711 RID: 18193 RVA: 0x000FA03C File Offset: 0x000F823C
		internal override bool HasNotNull()
		{
			return false;
		}

		// Token: 0x06004712 RID: 18194 RVA: 0x000FA040 File Offset: 0x000F8240
		protected override bool IsEqualTo(Constant right)
		{
			TypeConstant typeConstant = right as TypeConstant;
			return typeConstant != null && this.m_edmType == typeConstant.m_edmType;
		}

		// Token: 0x06004713 RID: 18195 RVA: 0x000FA067 File Offset: 0x000F8267
		public override int GetHashCode()
		{
			if (this.m_edmType == null)
			{
				return 0;
			}
			return this.m_edmType.GetHashCode();
		}

		// Token: 0x06004714 RID: 18196 RVA: 0x000FA080 File Offset: 0x000F8280
		internal override StringBuilder AsEsql(StringBuilder builder, MemberPath outputMember, string blockAlias)
		{
			this.AsCql(delegate(EntitySet refScopeEntitySet, IList<MemberPath> keyMemberOutputPaths)
			{
				EntityType entityType = (EntityType)((RefType)outputMember.EdmType).ElementType;
				builder.Append("CreateRef(");
				CqlWriter.AppendEscapedQualifiedName(builder, refScopeEntitySet.EntityContainer.Name, refScopeEntitySet.Name);
				builder.Append(", row(");
				for (int i = 0; i < keyMemberOutputPaths.Count; i++)
				{
					if (i > 0)
					{
						builder.Append(", ");
					}
					string qualifiedName = CqlWriter.GetQualifiedName(blockAlias, keyMemberOutputPaths[i].CqlFieldAlias);
					builder.Append(qualifiedName);
				}
				builder.Append("), ");
				CqlWriter.AppendEscapedTypeName(builder, entityType);
				builder.Append(')');
			}, delegate(IList<MemberPath> membersOutputPaths)
			{
				CqlWriter.AppendEscapedTypeName(builder, this.m_edmType);
				builder.Append('(');
				for (int j = 0; j < membersOutputPaths.Count; j++)
				{
					if (j > 0)
					{
						builder.Append(", ");
					}
					string qualifiedName2 = CqlWriter.GetQualifiedName(blockAlias, membersOutputPaths[j].CqlFieldAlias);
					builder.Append(qualifiedName2);
				}
				builder.Append(')');
			}, outputMember);
			return builder;
		}

		// Token: 0x06004715 RID: 18197 RVA: 0x000FA0DC File Offset: 0x000F82DC
		internal override DbExpression AsCqt(DbExpression row, MemberPath outputMember)
		{
			DbExpression cqt = null;
			Func<MemberPath, DbPropertyExpression> <>9__2;
			Func<MemberPath, DbPropertyExpression> <>9__3;
			this.AsCql(delegate(EntitySet refScopeEntitySet, IList<MemberPath> keyMemberOutputPaths)
			{
				EntityType entityType = (EntityType)((RefType)outputMember.EdmType).ElementType;
				EntityType entityType2 = entityType;
				Func<MemberPath, DbPropertyExpression> func;
				if ((func = <>9__2) == null)
				{
					func = (<>9__2 = (MemberPath km) => row.Property(km.CqlFieldAlias));
				}
				cqt = refScopeEntitySet.CreateRef(entityType2, keyMemberOutputPaths.Select(func));
			}, delegate(IList<MemberPath> membersOutputPaths)
			{
				TypeUsage typeUsage = TypeUsage.Create(this.m_edmType);
				Func<MemberPath, DbPropertyExpression> func2;
				if ((func2 = <>9__3) == null)
				{
					func2 = (<>9__3 = (MemberPath m) => row.Property(m.CqlFieldAlias));
				}
				cqt = typeUsage.New(membersOutputPaths.Select(func2));
			}, outputMember);
			return cqt;
		}

		// Token: 0x06004716 RID: 18198 RVA: 0x000FA138 File Offset: 0x000F8338
		private void AsCql(Action<EntitySet, IList<MemberPath>> createRef, Action<IList<MemberPath>> createType, MemberPath outputMember)
		{
			EntitySet scopeOfRelationEnd = outputMember.GetScopeOfRelationEnd();
			if (scopeOfRelationEnd != null)
			{
				List<MemberPath> list = new List<MemberPath>(scopeOfRelationEnd.ElementType.KeyMembers.Select((EdmMember km) => new MemberPath(outputMember, km)));
				createRef(scopeOfRelationEnd, list);
				return;
			}
			List<MemberPath> list2 = new List<MemberPath>();
			foreach (object obj in Helper.GetAllStructuralMembers(this.m_edmType))
			{
				EdmMember edmMember = (EdmMember)obj;
				list2.Add(new MemberPath(outputMember, edmMember));
			}
			createType(list2);
		}

		// Token: 0x06004717 RID: 18199 RVA: 0x000FA200 File Offset: 0x000F8400
		internal override string ToUserString()
		{
			StringBuilder stringBuilder = new StringBuilder();
			this.ToCompactString(stringBuilder);
			return stringBuilder.ToString();
		}

		// Token: 0x06004718 RID: 18200 RVA: 0x000FA220 File Offset: 0x000F8420
		internal override void ToCompactString(StringBuilder builder)
		{
			builder.Append(this.m_edmType.Name);
		}

		// Token: 0x04001934 RID: 6452
		private readonly EdmType m_edmType;
	}
}
