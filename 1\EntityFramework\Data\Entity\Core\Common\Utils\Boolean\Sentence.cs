﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Common.Utils.Boolean
{
	// Token: 0x0200061D RID: 1565
	internal abstract class Sentence<T_Identifier, T_Clause> : NormalFormNode<T_Identifier> where T_Clause : Clause<T_Identifier>, IEquatable<T_Clause>
	{
		// Token: 0x06004BDE RID: 19422 RVA: 0x0010A8B7 File Offset: 0x00108AB7
		protected Sentence(Set<T_Clause> clauses, ExprType treeType)
			: base(Sentence<T_Identifier, T_Clause>.ConvertClausesToExpr(clauses, treeType))
		{
			this._clauses = clauses.AsReadOnly();
		}

		// Token: 0x06004BDF RID: 19423 RVA: 0x0010A8D4 File Offset: 0x00108AD4
		private static BoolExpr<T_Identifier> ConvertClausesToExpr(Set<T_Clause> clauses, ExprType treeType)
		{
			bool flag = treeType == ExprType.And;
			IEnumerable<BoolExpr<T_Identifier>> enumerable = clauses.Select(new Func<T_Clause, BoolExpr<T_Identifier>>(NormalFormNode<T_Identifier>.ExprSelector<T_Clause>));
			if (flag)
			{
				return new AndExpr<T_Identifier>(enumerable);
			}
			return new OrExpr<T_Identifier>(enumerable);
		}

		// Token: 0x06004BE0 RID: 19424 RVA: 0x0010A907 File Offset: 0x00108B07
		public override string ToString()
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("Sentence{");
			stringBuilder.Append(this._clauses);
			return stringBuilder.Append("}").ToString();
		}

		// Token: 0x04001A81 RID: 6785
		private readonly Set<T_Clause> _clauses;
	}
}
