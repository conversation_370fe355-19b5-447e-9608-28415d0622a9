﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.Update.Internal;
using System.Data.Entity.Core.Mapping.ViewGeneration;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.SchemaObjectModel;
using System.Data.Entity.Infrastructure.MappingViews;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Xml;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x0200055C RID: 1372
	public class StorageMappingItemCollection : MappingItemCollection
	{
		// Token: 0x0600430A RID: 17162 RVA: 0x000E56E3 File Offset: 0x000E38E3
		internal StorageMappingItemCollection()
			: base(DataSpace.CSSpace)
		{
		}

		// Token: 0x0600430B RID: 17163 RVA: 0x000E5704 File Offset: 0x000E3904
		public StorageMappingItemCollection(EdmItemCollection edmCollection, StoreItemCollection storeCollection, params string[] filePaths)
			: base(DataSpace.CSSpace)
		{
			Check.NotNull<EdmItemCollection>(edmCollection, "edmCollection");
			Check.NotNull<StoreItemCollection>(storeCollection, "storeCollection");
			Check.NotNull<string[]>(filePaths, "filePaths");
			this._edmCollection = edmCollection;
			this._storeItemCollection = storeCollection;
			List<XmlReader> list = null;
			try
			{
				MetadataArtifactLoader metadataArtifactLoader = MetadataArtifactLoader.CreateCompositeFromFilePaths(filePaths, ".msl");
				list = metadataArtifactLoader.CreateReaders(DataSpace.CSSpace);
				this.Init(edmCollection, storeCollection, list, metadataArtifactLoader.GetPaths(DataSpace.CSSpace), true);
			}
			finally
			{
				if (list != null)
				{
					Helper.DisposeXmlReaders(list);
				}
			}
		}

		// Token: 0x0600430C RID: 17164 RVA: 0x000E57A8 File Offset: 0x000E39A8
		public StorageMappingItemCollection(EdmItemCollection edmCollection, StoreItemCollection storeCollection, IEnumerable<XmlReader> xmlReaders)
			: base(DataSpace.CSSpace)
		{
			Check.NotNull<IEnumerable<XmlReader>>(xmlReaders, "xmlReaders");
			MetadataArtifactLoader metadataArtifactLoader = MetadataArtifactLoader.CreateCompositeFromXmlReaders(xmlReaders);
			this.Init(edmCollection, storeCollection, metadataArtifactLoader.GetReaders(), metadataArtifactLoader.GetPaths(), true);
		}

		// Token: 0x0600430D RID: 17165 RVA: 0x000E57FB File Offset: 0x000E39FB
		private StorageMappingItemCollection(EdmItemCollection edmItemCollection, StoreItemCollection storeItemCollection, IEnumerable<XmlReader> xmlReaders, IList<string> filePaths, out IList<EdmSchemaError> errors)
			: base(DataSpace.CSSpace)
		{
			errors = this.Init(edmItemCollection, storeItemCollection, xmlReaders, filePaths, false);
		}

		// Token: 0x0600430E RID: 17166 RVA: 0x000E5829 File Offset: 0x000E3A29
		internal StorageMappingItemCollection(EdmItemCollection edmCollection, StoreItemCollection storeCollection, IEnumerable<XmlReader> xmlReaders, IList<string> filePaths)
			: base(DataSpace.CSSpace)
		{
			this.Init(edmCollection, storeCollection, xmlReaders, filePaths, true);
		}

		// Token: 0x0600430F RID: 17167 RVA: 0x000E5858 File Offset: 0x000E3A58
		private IList<EdmSchemaError> Init(EdmItemCollection edmCollection, StoreItemCollection storeCollection, IEnumerable<XmlReader> xmlReaders, IList<string> filePaths, bool throwOnError)
		{
			this._edmCollection = edmCollection;
			this._storeItemCollection = storeCollection;
			Dictionary<EntitySetBase, GeneratedView> dictionary;
			Dictionary<Pair<EntitySetBase, Pair<EntityTypeBase, bool>>, GeneratedView> dictionary2;
			this.m_viewDictionary = new StorageMappingItemCollection.ViewDictionary(this, out dictionary, out dictionary2);
			List<EdmSchemaError> list = new List<EdmSchemaError>();
			if (this._edmCollection.EdmVersion != 0.0 && this._storeItemCollection.StoreSchemaVersion != 0.0 && this._edmCollection.EdmVersion != this._storeItemCollection.StoreSchemaVersion)
			{
				list.Add(new EdmSchemaError(Strings.Mapping_DifferentEdmStoreVersion, 2102, EdmSchemaErrorSeverity.Error));
			}
			else
			{
				double num = ((this._edmCollection.EdmVersion != 0.0) ? this._edmCollection.EdmVersion : this._storeItemCollection.StoreSchemaVersion);
				list.AddRange(this.LoadItems(xmlReaders, filePaths, dictionary, dictionary2, num));
			}
			if (list.Count > 0 && throwOnError && !MetadataHelper.CheckIfAllErrorsAreWarnings(list))
			{
				throw new MappingException(string.Format(CultureInfo.CurrentCulture, EntityRes.GetString("InvalidSchemaEncountered"), new object[] { Helper.CombineErrorMessage(list) }));
			}
			return list;
		}

		// Token: 0x17000D51 RID: 3409
		// (get) Token: 0x06004310 RID: 17168 RVA: 0x000E5966 File Offset: 0x000E3B66
		// (set) Token: 0x06004311 RID: 17169 RVA: 0x000E596E File Offset: 0x000E3B6E
		public DbMappingViewCacheFactory MappingViewCacheFactory
		{
			get
			{
				return this._mappingViewCacheFactory;
			}
			set
			{
				Check.NotNull<DbMappingViewCacheFactory>(value, "value");
				Interlocked.CompareExchange<DbMappingViewCacheFactory>(ref this._mappingViewCacheFactory, value, null);
				if (!this._mappingViewCacheFactory.Equals(value))
				{
					throw new ArgumentException(Strings.MappingViewCacheFactory_MustNotChange, "value");
				}
			}
		}

		// Token: 0x17000D52 RID: 3410
		// (get) Token: 0x06004312 RID: 17170 RVA: 0x000E59A8 File Offset: 0x000E3BA8
		internal MetadataWorkspace Workspace
		{
			get
			{
				if (this._workspace == null)
				{
					this._workspace = new MetadataWorkspace(() => this._edmCollection, () => this._storeItemCollection, () => this);
				}
				return this._workspace;
			}
		}

		// Token: 0x17000D53 RID: 3411
		// (get) Token: 0x06004313 RID: 17171 RVA: 0x000E59E7 File Offset: 0x000E3BE7
		internal EdmItemCollection EdmItemCollection
		{
			get
			{
				return this._edmCollection;
			}
		}

		// Token: 0x17000D54 RID: 3412
		// (get) Token: 0x06004314 RID: 17172 RVA: 0x000E59EF File Offset: 0x000E3BEF
		public double MappingVersion
		{
			get
			{
				return this.m_mappingVersion;
			}
		}

		// Token: 0x17000D55 RID: 3413
		// (get) Token: 0x06004315 RID: 17173 RVA: 0x000E59F7 File Offset: 0x000E3BF7
		internal StoreItemCollection StoreItemCollection
		{
			get
			{
				return this._storeItemCollection;
			}
		}

		// Token: 0x06004316 RID: 17174 RVA: 0x000E59FF File Offset: 0x000E3BFF
		internal override MappingBase GetMap(string identity, DataSpace typeSpace, bool ignoreCase)
		{
			if (typeSpace != DataSpace.CSpace)
			{
				throw new InvalidOperationException(Strings.Mapping_Storage_InvalidSpace(typeSpace));
			}
			return base.GetItem<MappingBase>(identity, ignoreCase);
		}

		// Token: 0x06004317 RID: 17175 RVA: 0x000E5A1E File Offset: 0x000E3C1E
		internal override bool TryGetMap(string identity, DataSpace typeSpace, bool ignoreCase, out MappingBase map)
		{
			if (typeSpace != DataSpace.CSpace)
			{
				throw new InvalidOperationException(Strings.Mapping_Storage_InvalidSpace(typeSpace));
			}
			return base.TryGetItem<MappingBase>(identity, ignoreCase, out map);
		}

		// Token: 0x06004318 RID: 17176 RVA: 0x000E5A3F File Offset: 0x000E3C3F
		internal override MappingBase GetMap(string identity, DataSpace typeSpace)
		{
			return this.GetMap(identity, typeSpace, false);
		}

		// Token: 0x06004319 RID: 17177 RVA: 0x000E5A4A File Offset: 0x000E3C4A
		internal override bool TryGetMap(string identity, DataSpace typeSpace, out MappingBase map)
		{
			return this.TryGetMap(identity, typeSpace, false, out map);
		}

		// Token: 0x0600431A RID: 17178 RVA: 0x000E5A58 File Offset: 0x000E3C58
		internal override MappingBase GetMap(GlobalItem item)
		{
			DataSpace dataSpace = item.DataSpace;
			if (dataSpace != DataSpace.CSpace)
			{
				throw new InvalidOperationException(Strings.Mapping_Storage_InvalidSpace(dataSpace));
			}
			return this.GetMap(item.Identity, dataSpace);
		}

		// Token: 0x0600431B RID: 17179 RVA: 0x000E5A90 File Offset: 0x000E3C90
		internal override bool TryGetMap(GlobalItem item, out MappingBase map)
		{
			if (item == null)
			{
				map = null;
				return false;
			}
			DataSpace dataSpace = item.DataSpace;
			if (dataSpace != DataSpace.CSpace)
			{
				map = null;
				return false;
			}
			return this.TryGetMap(item.Identity, dataSpace, out map);
		}

		// Token: 0x0600431C RID: 17180 RVA: 0x000E5AC4 File Offset: 0x000E3CC4
		internal ReadOnlyCollection<EdmMember> GetInterestingMembers(EntitySetBase entitySet, EntityTypeBase entityType, StorageMappingItemCollection.InterestingMembersKind interestingMembersKind)
		{
			Tuple<EntitySetBase, EntityTypeBase, StorageMappingItemCollection.InterestingMembersKind> tuple = new Tuple<EntitySetBase, EntityTypeBase, StorageMappingItemCollection.InterestingMembersKind>(entitySet, entityType, interestingMembersKind);
			return this._cachedInterestingMembers.GetOrAdd(tuple, this.FindInterestingMembers(entitySet, entityType, interestingMembersKind));
		}

		// Token: 0x0600431D RID: 17181 RVA: 0x000E5AF0 File Offset: 0x000E3CF0
		private ReadOnlyCollection<EdmMember> FindInterestingMembers(EntitySetBase entitySet, EntityTypeBase entityType, StorageMappingItemCollection.InterestingMembersKind interestingMembersKind)
		{
			List<EdmMember> list = new List<EdmMember>();
			foreach (TypeMapping typeMapping in MappingMetadataHelper.GetMappingsForEntitySetAndSuperTypes(this, entitySet.EntityContainer, entitySet, entityType))
			{
				AssociationTypeMapping associationTypeMapping = typeMapping as AssociationTypeMapping;
				if (associationTypeMapping != null)
				{
					StorageMappingItemCollection.FindInterestingAssociationMappingMembers(associationTypeMapping, list);
				}
				else
				{
					StorageMappingItemCollection.FindInterestingEntityMappingMembers((EntityTypeMapping)typeMapping, interestingMembersKind, list);
				}
			}
			if (interestingMembersKind != StorageMappingItemCollection.InterestingMembersKind.RequiredOriginalValueMembers)
			{
				StorageMappingItemCollection.FindForeignKeyProperties(entitySet, entityType, list);
			}
			foreach (EntityTypeModificationFunctionMapping entityTypeModificationFunctionMapping in from functionMappings in MappingMetadataHelper.GetModificationFunctionMappingsForEntitySetAndType(this, entitySet.EntityContainer, entitySet, entityType)
				where functionMappings.UpdateFunctionMapping != null
				select functionMappings)
			{
				StorageMappingItemCollection.FindInterestingFunctionMappingMembers(entityTypeModificationFunctionMapping, interestingMembersKind, ref list);
			}
			return new ReadOnlyCollection<EdmMember>(list.Distinct<EdmMember>().ToList<EdmMember>());
		}

		// Token: 0x0600431E RID: 17182 RVA: 0x000E5BEC File Offset: 0x000E3DEC
		private static void FindInterestingAssociationMappingMembers(AssociationTypeMapping associationTypeMapping, List<EdmMember> interestingMembers)
		{
			interestingMembers.AddRange(from epm in associationTypeMapping.MappingFragments.SelectMany((MappingFragment m) => m.AllProperties).OfType<EndPropertyMapping>()
				select epm.AssociationEnd);
		}

		// Token: 0x0600431F RID: 17183 RVA: 0x000E5C54 File Offset: 0x000E3E54
		private static void FindInterestingEntityMappingMembers(EntityTypeMapping entityTypeMapping, StorageMappingItemCollection.InterestingMembersKind interestingMembersKind, List<EdmMember> interestingMembers)
		{
			foreach (PropertyMapping propertyMapping in entityTypeMapping.MappingFragments.SelectMany((MappingFragment mf) => mf.AllProperties))
			{
				ScalarPropertyMapping scalarPropertyMapping = propertyMapping as ScalarPropertyMapping;
				ComplexPropertyMapping complexPropertyMapping = propertyMapping as ComplexPropertyMapping;
				ConditionPropertyMapping conditionPropertyMapping = propertyMapping as ConditionPropertyMapping;
				if (scalarPropertyMapping != null && scalarPropertyMapping.Property != null)
				{
					if (MetadataHelper.IsPartOfEntityTypeKey(scalarPropertyMapping.Property))
					{
						if (interestingMembersKind == StorageMappingItemCollection.InterestingMembersKind.RequiredOriginalValueMembers)
						{
							interestingMembers.Add(scalarPropertyMapping.Property);
						}
					}
					else if (MetadataHelper.GetConcurrencyMode(scalarPropertyMapping.Property) == ConcurrencyMode.Fixed)
					{
						interestingMembers.Add(scalarPropertyMapping.Property);
					}
				}
				else if (complexPropertyMapping != null)
				{
					if (interestingMembersKind == StorageMappingItemCollection.InterestingMembersKind.PartialUpdate || MetadataHelper.GetConcurrencyMode(complexPropertyMapping.Property) == ConcurrencyMode.Fixed || StorageMappingItemCollection.HasFixedConcurrencyModeInAnyChildProperty(complexPropertyMapping))
					{
						interestingMembers.Add(complexPropertyMapping.Property);
					}
				}
				else if (conditionPropertyMapping != null && conditionPropertyMapping.Property != null)
				{
					interestingMembers.Add(conditionPropertyMapping.Property);
				}
			}
		}

		// Token: 0x06004320 RID: 17184 RVA: 0x000E5D60 File Offset: 0x000E3F60
		private static bool HasFixedConcurrencyModeInAnyChildProperty(ComplexPropertyMapping complexMapping)
		{
			foreach (PropertyMapping propertyMapping in complexMapping.TypeMappings.SelectMany((ComplexTypeMapping m) => m.AllProperties))
			{
				ScalarPropertyMapping scalarPropertyMapping = propertyMapping as ScalarPropertyMapping;
				ComplexPropertyMapping complexPropertyMapping = propertyMapping as ComplexPropertyMapping;
				if (scalarPropertyMapping != null && MetadataHelper.GetConcurrencyMode(scalarPropertyMapping.Property) == ConcurrencyMode.Fixed)
				{
					return true;
				}
				if (complexPropertyMapping != null && (MetadataHelper.GetConcurrencyMode(complexPropertyMapping.Property) == ConcurrencyMode.Fixed || StorageMappingItemCollection.HasFixedConcurrencyModeInAnyChildProperty(complexPropertyMapping)))
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x06004321 RID: 17185 RVA: 0x000E5E0C File Offset: 0x000E400C
		private static void FindForeignKeyProperties(EntitySetBase entitySetBase, EntityTypeBase entityType, List<EdmMember> interestingMembers)
		{
			EntitySet entitySet = entitySetBase as EntitySet;
			if (entitySet != null && entitySet.HasForeignKeyRelationships)
			{
				interestingMembers.AddRange(from p in MetadataHelper.GetTypeAndParentTypesOf(entityType, true).SelectMany((EdmType e) => ((EntityType)e).Properties)
					where entitySet.ForeignKeyDependents.SelectMany((Tuple<AssociationSet, global::System.Data.Entity.Core.Metadata.Edm.ReferentialConstraint> fk) => fk.Item2.ToProperties).Contains(p)
					select p);
			}
		}

		// Token: 0x06004322 RID: 17186 RVA: 0x000E5E84 File Offset: 0x000E4084
		private static void FindInterestingFunctionMappingMembers(EntityTypeModificationFunctionMapping functionMappings, StorageMappingItemCollection.InterestingMembersKind interestingMembersKind, ref List<EdmMember> interestingMembers)
		{
			if (interestingMembersKind == StorageMappingItemCollection.InterestingMembersKind.PartialUpdate)
			{
				interestingMembers.AddRange(functionMappings.UpdateFunctionMapping.ParameterBindings.Select((ModificationFunctionParameterBinding p) => p.MemberPath.Members.Last<EdmMember>()));
				return;
			}
			foreach (ModificationFunctionParameterBinding modificationFunctionParameterBinding in functionMappings.UpdateFunctionMapping.ParameterBindings.Where((ModificationFunctionParameterBinding p) => !p.IsCurrent))
			{
				interestingMembers.Add(modificationFunctionParameterBinding.MemberPath.Members.Last<EdmMember>());
			}
		}

		// Token: 0x06004323 RID: 17187 RVA: 0x000E5F48 File Offset: 0x000E4148
		internal GeneratedView GetGeneratedView(EntitySetBase extent, MetadataWorkspace workspace)
		{
			return this.m_viewDictionary.GetGeneratedView(extent, workspace, this);
		}

		// Token: 0x06004324 RID: 17188 RVA: 0x000E5F58 File Offset: 0x000E4158
		private void AddInternal(MappingBase storageMap)
		{
			storageMap.DataSpace = DataSpace.CSSpace;
			try
			{
				base.AddInternal(storageMap);
			}
			catch (ArgumentException ex)
			{
				throw new MappingException(Strings.Mapping_Duplicate_Type(storageMap.EdmItem.Identity), ex);
			}
		}

		// Token: 0x06004325 RID: 17189 RVA: 0x000E5FA0 File Offset: 0x000E41A0
		internal bool ContainsStorageEntityContainer(string storageEntityContainerName)
		{
			return this.GetItems<EntityContainerMapping>().Any((EntityContainerMapping map) => map.StorageEntityContainer.Name.Equals(storageEntityContainerName, StringComparison.Ordinal));
		}

		// Token: 0x06004326 RID: 17190 RVA: 0x000E5FD4 File Offset: 0x000E41D4
		private List<EdmSchemaError> LoadItems(IEnumerable<XmlReader> xmlReaders, IList<string> mappingSchemaUris, Dictionary<EntitySetBase, GeneratedView> userDefinedQueryViewsDict, Dictionary<Pair<EntitySetBase, Pair<EntityTypeBase, bool>>, GeneratedView> userDefinedQueryViewsOfTypeDict, double expectedVersion)
		{
			List<EdmSchemaError> list = new List<EdmSchemaError>();
			int num = -1;
			foreach (XmlReader xmlReader in xmlReaders)
			{
				num++;
				string text = null;
				if (mappingSchemaUris == null)
				{
					SchemaManager.TryGetBaseUri(xmlReader, out text);
				}
				else
				{
					text = mappingSchemaUris[num];
				}
				MappingItemLoader mappingItemLoader = new MappingItemLoader(xmlReader, this, text, this.m_memberMappings);
				list.AddRange(mappingItemLoader.ParsingErrors);
				this.CheckIsSameVersion(expectedVersion, mappingItemLoader.MappingVersion, list);
				EntityContainerMapping containerMapping = mappingItemLoader.ContainerMapping;
				if (mappingItemLoader.HasQueryViews && containerMapping != null)
				{
					StorageMappingItemCollection.CompileUserDefinedQueryViews(containerMapping, userDefinedQueryViewsDict, userDefinedQueryViewsOfTypeDict, list);
				}
				if (MetadataHelper.CheckIfAllErrorsAreWarnings(list) && !base.Contains(containerMapping))
				{
					containerMapping.SetReadOnly();
					this.AddInternal(containerMapping);
				}
			}
			StorageMappingItemCollection.CheckForDuplicateItems(this.EdmItemCollection, this.StoreItemCollection, list);
			return list;
		}

		// Token: 0x06004327 RID: 17191 RVA: 0x000E60C4 File Offset: 0x000E42C4
		private static void CompileUserDefinedQueryViews(EntityContainerMapping entityContainerMapping, Dictionary<EntitySetBase, GeneratedView> userDefinedQueryViewsDict, Dictionary<Pair<EntitySetBase, Pair<EntityTypeBase, bool>>, GeneratedView> userDefinedQueryViewsOfTypeDict, IList<EdmSchemaError> errors)
		{
			ConfigViewGenerator configViewGenerator = new ConfigViewGenerator();
			foreach (EntitySetBaseMapping entitySetBaseMapping in entityContainerMapping.AllSetMaps)
			{
				GeneratedView generatedView;
				if (entitySetBaseMapping.QueryView != null && !userDefinedQueryViewsDict.TryGetValue(entitySetBaseMapping.Set, out generatedView))
				{
					if (GeneratedView.TryParseUserSpecifiedView(entitySetBaseMapping, entitySetBaseMapping.Set.ElementType, entitySetBaseMapping.QueryView, true, entityContainerMapping.StorageMappingItemCollection, configViewGenerator, errors, out generatedView))
					{
						userDefinedQueryViewsDict.Add(entitySetBaseMapping.Set, generatedView);
					}
					foreach (Pair<EntitySetBase, Pair<EntityTypeBase, bool>> pair in entitySetBaseMapping.GetTypeSpecificQVKeys())
					{
						if (GeneratedView.TryParseUserSpecifiedView(entitySetBaseMapping, pair.Second.First, entitySetBaseMapping.GetTypeSpecificQueryView(pair), pair.Second.Second, entityContainerMapping.StorageMappingItemCollection, configViewGenerator, errors, out generatedView))
						{
							userDefinedQueryViewsOfTypeDict.Add(pair, generatedView);
						}
					}
				}
			}
		}

		// Token: 0x06004328 RID: 17192 RVA: 0x000E61DC File Offset: 0x000E43DC
		private void CheckIsSameVersion(double expectedVersion, double currentLoaderVersion, IList<EdmSchemaError> errors)
		{
			if (this.m_mappingVersion == 0.0)
			{
				this.m_mappingVersion = currentLoaderVersion;
			}
			if (expectedVersion != 0.0 && currentLoaderVersion != 0.0 && currentLoaderVersion != expectedVersion)
			{
				errors.Add(new EdmSchemaError(Strings.Mapping_DifferentMappingEdmStoreVersion, 2101, EdmSchemaErrorSeverity.Error));
			}
			if (currentLoaderVersion != this.m_mappingVersion && currentLoaderVersion != 0.0)
			{
				errors.Add(new EdmSchemaError(Strings.CannotLoadDifferentVersionOfSchemaInTheSameItemCollection, 2100, EdmSchemaErrorSeverity.Error));
			}
		}

		// Token: 0x06004329 RID: 17193 RVA: 0x000E625E File Offset: 0x000E445E
		internal ViewLoader GetUpdateViewLoader()
		{
			if (this._viewLoader == null)
			{
				this._viewLoader = new ViewLoader(this);
			}
			return this._viewLoader;
		}

		// Token: 0x0600432A RID: 17194 RVA: 0x000E627A File Offset: 0x000E447A
		internal bool TryGetGeneratedViewOfType(EntitySetBase entity, EntityTypeBase type, bool includeSubtypes, out GeneratedView generatedView)
		{
			return this.m_viewDictionary.TryGetGeneratedViewOfType(entity, type, includeSubtypes, out generatedView);
		}

		// Token: 0x0600432B RID: 17195 RVA: 0x000E628C File Offset: 0x000E448C
		private static void CheckForDuplicateItems(EdmItemCollection edmItemCollection, StoreItemCollection storeItemCollection, List<EdmSchemaError> errorCollection)
		{
			foreach (GlobalItem globalItem in edmItemCollection)
			{
				if (storeItemCollection.Contains(globalItem.Identity))
				{
					errorCollection.Add(new EdmSchemaError(Strings.Mapping_ItemWithSameNameExistsBothInCSpaceAndSSpace(globalItem.Identity), 2070, EdmSchemaErrorSeverity.Error));
				}
			}
		}

		// Token: 0x0600432C RID: 17196 RVA: 0x000E6300 File Offset: 0x000E4500
		public string ComputeMappingHashValue(string conceptualModelContainerName, string storeModelContainerName)
		{
			Check.NotEmpty(conceptualModelContainerName, "conceptualModelContainerName");
			Check.NotEmpty(storeModelContainerName, "storeModelContainerName");
			EntityContainerMapping entityContainerMapping = this.GetItems<EntityContainerMapping>().SingleOrDefault((EntityContainerMapping m) => m.EdmEntityContainer.Name == conceptualModelContainerName && m.StorageEntityContainer.Name == storeModelContainerName);
			if (entityContainerMapping == null)
			{
				throw new InvalidOperationException(Strings.HashCalcContainersNotFound(conceptualModelContainerName, storeModelContainerName));
			}
			return MetadataMappingHasherVisitor.GetMappingClosureHash(this.MappingVersion, entityContainerMapping, true);
		}

		// Token: 0x0600432D RID: 17197 RVA: 0x000E6382 File Offset: 0x000E4582
		public string ComputeMappingHashValue()
		{
			if (this.GetItems<EntityContainerMapping>().Count != 1)
			{
				throw new InvalidOperationException(Strings.HashCalcMultipleContainers);
			}
			return MetadataMappingHasherVisitor.GetMappingClosureHash(this.MappingVersion, this.GetItems<EntityContainerMapping>().Single<EntityContainerMapping>(), true);
		}

		// Token: 0x0600432E RID: 17198 RVA: 0x000E63B4 File Offset: 0x000E45B4
		public Dictionary<EntitySetBase, DbMappingView> GenerateViews(string conceptualModelContainerName, string storeModelContainerName, IList<EdmSchemaError> errors)
		{
			Check.NotEmpty(conceptualModelContainerName, "conceptualModelContainerName");
			Check.NotEmpty(storeModelContainerName, "storeModelContainerName");
			Check.NotNull<IList<EdmSchemaError>>(errors, "errors");
			EntityContainerMapping entityContainerMapping = this.GetItems<EntityContainerMapping>().SingleOrDefault((EntityContainerMapping m) => m.EdmEntityContainer.Name == conceptualModelContainerName && m.StorageEntityContainer.Name == storeModelContainerName);
			if (entityContainerMapping == null)
			{
				throw new InvalidOperationException(Strings.ViewGenContainersNotFound(conceptualModelContainerName, storeModelContainerName));
			}
			return StorageMappingItemCollection.GenerateViews(entityContainerMapping, errors);
		}

		// Token: 0x0600432F RID: 17199 RVA: 0x000E643A File Offset: 0x000E463A
		public Dictionary<EntitySetBase, DbMappingView> GenerateViews(IList<EdmSchemaError> errors)
		{
			Check.NotNull<IList<EdmSchemaError>>(errors, "errors");
			if (this.GetItems<EntityContainerMapping>().Count != 1)
			{
				throw new InvalidOperationException(Strings.ViewGenMultipleContainers);
			}
			return StorageMappingItemCollection.GenerateViews(this.GetItems<EntityContainerMapping>().Single<EntityContainerMapping>(), errors);
		}

		// Token: 0x06004330 RID: 17200 RVA: 0x000E6474 File Offset: 0x000E4674
		internal static Dictionary<EntitySetBase, DbMappingView> GenerateViews(EntityContainerMapping containerMapping, IList<EdmSchemaError> errors)
		{
			Dictionary<EntitySetBase, DbMappingView> dictionary = new Dictionary<EntitySetBase, DbMappingView>();
			if (!containerMapping.HasViews)
			{
				return dictionary;
			}
			if (!containerMapping.HasMappingFragments())
			{
				errors.Add(new EdmSchemaError(Strings.Mapping_AllQueryViewAtCompileTime(containerMapping.Identity), 2088, EdmSchemaErrorSeverity.Warning));
				return dictionary;
			}
			ViewGenResults viewGenResults = ViewgenGatekeeper.GenerateViewsFromMapping(containerMapping, new ConfigViewGenerator
			{
				GenerateEsql = true
			});
			if (viewGenResults.HasErrors)
			{
				viewGenResults.Errors.Each(delegate(EdmSchemaError e)
				{
					errors.Add(e);
				});
			}
			foreach (KeyValuePair<EntitySetBase, List<GeneratedView>> keyValuePair in viewGenResults.Views.KeyValuePairs)
			{
				dictionary.Add(keyValuePair.Key, new DbMappingView(keyValuePair.Value[0].eSQL));
			}
			return dictionary;
		}

		// Token: 0x06004331 RID: 17201 RVA: 0x000E6560 File Offset: 0x000E4760
		public static StorageMappingItemCollection Create(EdmItemCollection edmItemCollection, StoreItemCollection storeItemCollection, IEnumerable<XmlReader> xmlReaders, IList<string> filePaths, out IList<EdmSchemaError> errors)
		{
			Check.NotNull<EdmItemCollection>(edmItemCollection, "edmItemCollection");
			Check.NotNull<StoreItemCollection>(storeItemCollection, "storeItemCollection");
			Check.NotNull<IEnumerable<XmlReader>>(xmlReaders, "xmlReaders");
			EntityUtil.CheckArgumentContainsNull<XmlReader>(ref xmlReaders, "xmlReaders");
			StorageMappingItemCollection storageMappingItemCollection = new StorageMappingItemCollection(edmItemCollection, storeItemCollection, xmlReaders, filePaths, out errors);
			if (errors == null || errors.Count <= 0)
			{
				return storageMappingItemCollection;
			}
			return null;
		}

		// Token: 0x040017F1 RID: 6129
		private EdmItemCollection _edmCollection;

		// Token: 0x040017F2 RID: 6130
		private StoreItemCollection _storeItemCollection;

		// Token: 0x040017F3 RID: 6131
		private StorageMappingItemCollection.ViewDictionary m_viewDictionary;

		// Token: 0x040017F4 RID: 6132
		private double m_mappingVersion;

		// Token: 0x040017F5 RID: 6133
		private MetadataWorkspace _workspace;

		// Token: 0x040017F6 RID: 6134
		private readonly Dictionary<EdmMember, KeyValuePair<TypeUsage, TypeUsage>> m_memberMappings = new Dictionary<EdmMember, KeyValuePair<TypeUsage, TypeUsage>>();

		// Token: 0x040017F7 RID: 6135
		private ViewLoader _viewLoader;

		// Token: 0x040017F8 RID: 6136
		private readonly ConcurrentDictionary<Tuple<EntitySetBase, EntityTypeBase, StorageMappingItemCollection.InterestingMembersKind>, ReadOnlyCollection<EdmMember>> _cachedInterestingMembers = new ConcurrentDictionary<Tuple<EntitySetBase, EntityTypeBase, StorageMappingItemCollection.InterestingMembersKind>, ReadOnlyCollection<EdmMember>>();

		// Token: 0x040017F9 RID: 6137
		private DbMappingViewCacheFactory _mappingViewCacheFactory;

		// Token: 0x02000B60 RID: 2912
		// (Invoke) Token: 0x060065E5 RID: 26085
		internal delegate bool TryGetUserDefinedQueryView(EntitySetBase extent, out GeneratedView generatedView);

		// Token: 0x02000B61 RID: 2913
		// (Invoke) Token: 0x060065E9 RID: 26089
		internal delegate bool TryGetUserDefinedQueryViewOfType(Pair<EntitySetBase, Pair<EntityTypeBase, bool>> extent, out GeneratedView generatedView);

		// Token: 0x02000B62 RID: 2914
		internal class ViewDictionary
		{
			// Token: 0x060065EC RID: 26092 RVA: 0x0015D8F8 File Offset: 0x0015BAF8
			internal ViewDictionary(StorageMappingItemCollection storageMappingItemCollection, out Dictionary<EntitySetBase, GeneratedView> userDefinedQueryViewsDict, out Dictionary<Pair<EntitySetBase, Pair<EntityTypeBase, bool>>, GeneratedView> userDefinedQueryViewsOfTypeDict)
			{
				this._storageMappingItemCollection = storageMappingItemCollection;
				this._generatedViewsMemoizer = new Memoizer<global::System.Data.Entity.Core.Metadata.Edm.EntityContainer, Dictionary<EntitySetBase, GeneratedView>>(new Func<global::System.Data.Entity.Core.Metadata.Edm.EntityContainer, Dictionary<EntitySetBase, GeneratedView>>(this.SerializedGetGeneratedViews), null);
				this._generatedViewOfTypeMemoizer = new Memoizer<Pair<EntitySetBase, Pair<EntityTypeBase, bool>>, GeneratedView>(new Func<Pair<EntitySetBase, Pair<EntityTypeBase, bool>>, GeneratedView>(this.SerializedGeneratedViewOfType), Pair<EntitySetBase, Pair<EntityTypeBase, bool>>.PairComparer.Instance);
				userDefinedQueryViewsDict = new Dictionary<EntitySetBase, GeneratedView>(EqualityComparer<EntitySetBase>.Default);
				userDefinedQueryViewsOfTypeDict = new Dictionary<Pair<EntitySetBase, Pair<EntityTypeBase, bool>>, GeneratedView>(Pair<EntitySetBase, Pair<EntityTypeBase, bool>>.PairComparer.Instance);
				this._tryGetUserDefinedQueryView = new StorageMappingItemCollection.TryGetUserDefinedQueryView(userDefinedQueryViewsDict.TryGetValue);
				this._tryGetUserDefinedQueryViewOfType = new StorageMappingItemCollection.TryGetUserDefinedQueryViewOfType(userDefinedQueryViewsOfTypeDict.TryGetValue);
			}

			// Token: 0x060065ED RID: 26093 RVA: 0x0015D990 File Offset: 0x0015BB90
			private Dictionary<EntitySetBase, GeneratedView> SerializedGetGeneratedViews(global::System.Data.Entity.Core.Metadata.Edm.EntityContainer container)
			{
				EntityContainerMapping entityContainerMap = MappingMetadataHelper.GetEntityContainerMap(this._storageMappingItemCollection, container);
				global::System.Data.Entity.Core.Metadata.Edm.EntityContainer entityContainer = ((container.DataSpace == DataSpace.CSpace) ? entityContainerMap.StorageEntityContainer : entityContainerMap.EdmEntityContainer);
				Dictionary<EntitySetBase, GeneratedView> dictionary;
				if (this._generatedViewsMemoizer.TryGetValue(entityContainer, out dictionary))
				{
					return dictionary;
				}
				dictionary = new Dictionary<EntitySetBase, GeneratedView>();
				if (!entityContainerMap.HasViews)
				{
					return dictionary;
				}
				if (this._generatedViewsMode && this._storageMappingItemCollection.MappingViewCacheFactory != null)
				{
					this.SerializedCollectViewsFromCache(entityContainerMap, dictionary);
				}
				if (dictionary.Count == 0)
				{
					this._generatedViewsMode = false;
					StorageMappingItemCollection.ViewDictionary.SerializedGenerateViews(entityContainerMap, dictionary);
				}
				return dictionary;
			}

			// Token: 0x060065EE RID: 26094 RVA: 0x0015DA18 File Offset: 0x0015BC18
			private static void SerializedGenerateViews(EntityContainerMapping entityContainerMap, Dictionary<EntitySetBase, GeneratedView> resultDictionary)
			{
				ViewGenResults viewGenResults = ViewgenGatekeeper.GenerateViewsFromMapping(entityContainerMap, StorageMappingItemCollection.ViewDictionary._config);
				KeyToListMap<EntitySetBase, GeneratedView> views = viewGenResults.Views;
				if (viewGenResults.HasErrors)
				{
					throw new MappingException(Helper.CombineErrorMessage(viewGenResults.Errors));
				}
				foreach (KeyValuePair<EntitySetBase, List<GeneratedView>> keyValuePair in views.KeyValuePairs)
				{
					GeneratedView generatedView;
					if (!resultDictionary.TryGetValue(keyValuePair.Key, out generatedView))
					{
						generatedView = keyValuePair.Value[0];
						resultDictionary.Add(keyValuePair.Key, generatedView);
					}
				}
			}

			// Token: 0x060065EF RID: 26095 RVA: 0x0015DABC File Offset: 0x0015BCBC
			private bool TryGenerateQueryViewOfType(global::System.Data.Entity.Core.Metadata.Edm.EntityContainer entityContainer, EntitySetBase entity, EntityTypeBase type, bool includeSubtypes, out GeneratedView generatedView)
			{
				if (type.Abstract)
				{
					generatedView = null;
					return false;
				}
				bool flag;
				ViewGenResults viewGenResults = ViewgenGatekeeper.GenerateTypeSpecificQueryView(MappingMetadataHelper.GetEntityContainerMap(this._storageMappingItemCollection, entityContainer), StorageMappingItemCollection.ViewDictionary._config, entity, type, includeSubtypes, out flag);
				if (!flag)
				{
					generatedView = null;
					return false;
				}
				KeyToListMap<EntitySetBase, GeneratedView> views = viewGenResults.Views;
				if (viewGenResults.HasErrors)
				{
					throw new MappingException(Helper.CombineErrorMessage(viewGenResults.Errors));
				}
				generatedView = views.AllValues.First<GeneratedView>();
				return true;
			}

			// Token: 0x060065F0 RID: 26096 RVA: 0x0015DB2C File Offset: 0x0015BD2C
			internal bool TryGetGeneratedViewOfType(EntitySetBase entity, EntityTypeBase type, bool includeSubtypes, out GeneratedView generatedView)
			{
				Pair<EntitySetBase, Pair<EntityTypeBase, bool>> pair = new Pair<EntitySetBase, Pair<EntityTypeBase, bool>>(entity, new Pair<EntityTypeBase, bool>(type, includeSubtypes));
				generatedView = this._generatedViewOfTypeMemoizer.Evaluate(pair);
				return generatedView != null;
			}

			// Token: 0x060065F1 RID: 26097 RVA: 0x0015DB5C File Offset: 0x0015BD5C
			private GeneratedView SerializedGeneratedViewOfType(Pair<EntitySetBase, Pair<EntityTypeBase, bool>> arg)
			{
				GeneratedView generatedView;
				if (this._tryGetUserDefinedQueryViewOfType(arg, out generatedView))
				{
					return generatedView;
				}
				EntitySetBase first = arg.First;
				EntityTypeBase first2 = arg.Second.First;
				bool second = arg.Second.Second;
				if (!this.TryGenerateQueryViewOfType(first.EntityContainer, first, first2, second, out generatedView))
				{
					generatedView = null;
				}
				return generatedView;
			}

			// Token: 0x060065F2 RID: 26098 RVA: 0x0015DBB0 File Offset: 0x0015BDB0
			internal GeneratedView GetGeneratedView(EntitySetBase extent, MetadataWorkspace workspace, StorageMappingItemCollection storageMappingItemCollection)
			{
				GeneratedView generatedView;
				if (this._tryGetUserDefinedQueryView(extent, out generatedView))
				{
					return generatedView;
				}
				if (extent.BuiltInTypeKind == BuiltInTypeKind.AssociationSet)
				{
					AssociationSet aSet = (AssociationSet)extent;
					if (aSet.ElementType.IsForeignKey)
					{
						if (StorageMappingItemCollection.ViewDictionary._config.IsViewTracing)
						{
							Helpers.StringTraceLine(string.Empty);
							Helpers.StringTraceLine(string.Empty);
							Helpers.FormatTraceLine("================= Generating FK Query View for: {0} =================", new object[] { aSet.Name });
							Helpers.StringTraceLine(string.Empty);
							Helpers.StringTraceLine(string.Empty);
						}
						global::System.Data.Entity.Core.Metadata.Edm.ReferentialConstraint rc = aSet.ElementType.ReferentialConstraints.Single<global::System.Data.Entity.Core.Metadata.Edm.ReferentialConstraint>();
						EntitySet dependentSet = aSet.AssociationSetEnds[rc.ToRole.Name].EntitySet;
						EntitySet principalSet = aSet.AssociationSetEnds[rc.FromRole.Name].EntitySet;
						DbExpression dbExpression = dependentSet.Scan();
						EntityType dependentType = MetadataHelper.GetEntityTypeForEnd((AssociationEndMember)rc.ToRole);
						EntityType principalType = MetadataHelper.GetEntityTypeForEnd((AssociationEndMember)rc.FromRole);
						if (dependentSet.ElementType.IsBaseTypeOf(dependentType))
						{
							dbExpression = dbExpression.OfType(TypeUsage.Create(dependentType));
						}
						if (rc.FromRole.RelationshipMultiplicity == RelationshipMultiplicity.ZeroOrOne)
						{
							dbExpression = dbExpression.Where(delegate(DbExpression e)
							{
								DbExpression dbExpression2 = null;
								foreach (EdmProperty edmProperty in rc.ToProperties)
								{
									DbExpression dbExpression3 = e.Property(edmProperty).IsNull().Not();
									dbExpression2 = ((dbExpression2 == null) ? dbExpression3 : dbExpression2.And(dbExpression3));
								}
								return dbExpression2;
							});
						}
						dbExpression = dbExpression.Select(delegate(DbExpression e)
						{
							List<DbExpression> list = new List<DbExpression>();
							using (ReadOnlyMetadataCollection<AssociationEndMember>.Enumerator enumerator2 = aSet.ElementType.AssociationEndMembers.GetEnumerator())
							{
								while (enumerator2.MoveNext())
								{
									if (enumerator2.Current.Name == rc.ToRole.Name)
									{
										List<KeyValuePair<string, DbExpression>> list2 = new List<KeyValuePair<string, DbExpression>>();
										foreach (EdmMember edmMember in dependentSet.ElementType.KeyMembers)
										{
											list2.Add(e.Property((EdmProperty)edmMember));
										}
										list.Add(dependentSet.RefFromKey(DbExpressionBuilder.NewRow(list2), dependentType));
									}
									else
									{
										List<KeyValuePair<string, DbExpression>> list3 = new List<KeyValuePair<string, DbExpression>>();
										foreach (EdmMember edmMember2 in principalSet.ElementType.KeyMembers)
										{
											int num = rc.FromProperties.IndexOf((EdmProperty)edmMember2);
											list3.Add(e.Property(rc.ToProperties[num]));
										}
										list.Add(principalSet.RefFromKey(DbExpressionBuilder.NewRow(list3), principalType));
									}
								}
							}
							return TypeUsage.Create(aSet.ElementType).New(list);
						});
						return GeneratedView.CreateGeneratedViewForFKAssociationSet(aSet, aSet.ElementType, new DbQueryCommandTree(workspace, DataSpace.SSpace, dbExpression), storageMappingItemCollection, StorageMappingItemCollection.ViewDictionary._config);
					}
				}
				if (!this._generatedViewsMemoizer.Evaluate(extent.EntityContainer).TryGetValue(extent, out generatedView))
				{
					throw new InvalidOperationException(Strings.Mapping_Views_For_Extent_Not_Generated((extent.EntityContainer.DataSpace == DataSpace.SSpace) ? "Table" : "EntitySet", extent.Name));
				}
				return generatedView;
			}

			// Token: 0x060065F3 RID: 26099 RVA: 0x0015DDD8 File Offset: 0x0015BFD8
			private void SerializedCollectViewsFromCache(EntityContainerMapping containerMapping, Dictionary<EntitySetBase, GeneratedView> extentMappingViews)
			{
				DbMappingViewCache dbMappingViewCache = this._storageMappingItemCollection.MappingViewCacheFactory.Create(containerMapping);
				if (dbMappingViewCache == null)
				{
					return;
				}
				if (MetadataMappingHasherVisitor.GetMappingClosureHash(containerMapping.StorageMappingItemCollection.MappingVersion, containerMapping, true) != dbMappingViewCache.MappingHashValue)
				{
					throw new MappingException(Strings.ViewGen_HashOnMappingClosure_Not_Matching(dbMappingViewCache.GetType().Name));
				}
				foreach (EntitySetBase entitySetBase in containerMapping.StorageEntityContainer.BaseEntitySets.Union(containerMapping.EdmEntityContainer.BaseEntitySets))
				{
					GeneratedView generatedView;
					if (!extentMappingViews.TryGetValue(entitySetBase, out generatedView))
					{
						DbMappingView view = dbMappingViewCache.GetView(entitySetBase);
						if (view != null)
						{
							generatedView = GeneratedView.CreateGeneratedView(entitySetBase, null, null, view.EntitySql, this._storageMappingItemCollection, new ConfigViewGenerator());
							extentMappingViews.Add(entitySetBase, generatedView);
						}
					}
				}
			}

			// Token: 0x04002DAF RID: 11695
			private readonly StorageMappingItemCollection.TryGetUserDefinedQueryView _tryGetUserDefinedQueryView;

			// Token: 0x04002DB0 RID: 11696
			private readonly StorageMappingItemCollection.TryGetUserDefinedQueryViewOfType _tryGetUserDefinedQueryViewOfType;

			// Token: 0x04002DB1 RID: 11697
			private readonly StorageMappingItemCollection _storageMappingItemCollection;

			// Token: 0x04002DB2 RID: 11698
			private static readonly ConfigViewGenerator _config = new ConfigViewGenerator();

			// Token: 0x04002DB3 RID: 11699
			private bool _generatedViewsMode = true;

			// Token: 0x04002DB4 RID: 11700
			private readonly Memoizer<global::System.Data.Entity.Core.Metadata.Edm.EntityContainer, Dictionary<EntitySetBase, GeneratedView>> _generatedViewsMemoizer;

			// Token: 0x04002DB5 RID: 11701
			private readonly Memoizer<Pair<EntitySetBase, Pair<EntityTypeBase, bool>>, GeneratedView> _generatedViewOfTypeMemoizer;
		}

		// Token: 0x02000B63 RID: 2915
		internal enum InterestingMembersKind
		{
			// Token: 0x04002DB7 RID: 11703
			RequiredOriginalValueMembers,
			// Token: 0x04002DB8 RID: 11704
			FullUpdate,
			// Token: 0x04002DB9 RID: 11705
			PartialUpdate
		}
	}
}
