﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Utilities;
using System.Reflection;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004DF RID: 1247
	internal sealed class MetadataPropertyCollection : MetadataCollection<MetadataProperty>
	{
		// Token: 0x06003E0F RID: 15887 RVA: 0x000CD1EC File Offset: 0x000CB3EC
		internal MetadataPropertyCollection(MetadataItem item)
			: base(MetadataPropertyCollection.GetSystemMetadataProperties(item))
		{
		}

		// Token: 0x06003E10 RID: 15888 RVA: 0x000CD1FA File Offset: 0x000CB3FA
		private static IEnumerable<MetadataProperty> GetSystemMetadataProperties(MetadataItem item)
		{
			return MetadataPropertyCollection.GetItemTypeInformation(item.GetType()).GetItemAttributes(item);
		}

		// Token: 0x06003E11 RID: 15889 RVA: 0x000CD20D File Offset: 0x000CB40D
		private static MetadataPropertyCollection.ItemTypeInformation GetItemTypeInformation(Type clrType)
		{
			return MetadataPropertyCollection._itemTypeMemoizer.Evaluate(clrType);
		}

		// Token: 0x04001520 RID: 5408
		private static readonly Memoizer<Type, MetadataPropertyCollection.ItemTypeInformation> _itemTypeMemoizer = new Memoizer<Type, MetadataPropertyCollection.ItemTypeInformation>((Type clrType) => new MetadataPropertyCollection.ItemTypeInformation(clrType), null);

		// Token: 0x02000AFC RID: 2812
		private class ItemTypeInformation
		{
			// Token: 0x0600641F RID: 25631 RVA: 0x00159F4C File Offset: 0x0015814C
			internal ItemTypeInformation(Type clrType)
			{
				this._itemProperties = MetadataPropertyCollection.ItemTypeInformation.GetItemProperties(clrType);
			}

			// Token: 0x06006420 RID: 25632 RVA: 0x00159F60 File Offset: 0x00158160
			internal IEnumerable<MetadataProperty> GetItemAttributes(MetadataItem item)
			{
				foreach (MetadataPropertyCollection.ItemPropertyInfo itemPropertyInfo in this._itemProperties)
				{
					yield return itemPropertyInfo.GetMetadataProperty(item);
				}
				List<MetadataPropertyCollection.ItemPropertyInfo>.Enumerator enumerator = default(List<MetadataPropertyCollection.ItemPropertyInfo>.Enumerator);
				yield break;
				yield break;
			}

			// Token: 0x06006421 RID: 25633 RVA: 0x00159F78 File Offset: 0x00158178
			private static List<MetadataPropertyCollection.ItemPropertyInfo> GetItemProperties(Type clrType)
			{
				List<MetadataPropertyCollection.ItemPropertyInfo> list = new List<MetadataPropertyCollection.ItemPropertyInfo>();
				foreach (PropertyInfo propertyInfo in clrType.GetInstanceProperties())
				{
					foreach (MetadataPropertyAttribute metadataPropertyAttribute in propertyInfo.GetCustomAttributes(false))
					{
						list.Add(new MetadataPropertyCollection.ItemPropertyInfo(propertyInfo, metadataPropertyAttribute));
					}
				}
				return list;
			}

			// Token: 0x04002C77 RID: 11383
			private readonly List<MetadataPropertyCollection.ItemPropertyInfo> _itemProperties;
		}

		// Token: 0x02000AFD RID: 2813
		private class ItemPropertyInfo
		{
			// Token: 0x06006422 RID: 25634 RVA: 0x0015A00C File Offset: 0x0015820C
			internal ItemPropertyInfo(PropertyInfo propertyInfo, MetadataPropertyAttribute attribute)
			{
				this._propertyInfo = propertyInfo;
				this._attribute = attribute;
			}

			// Token: 0x06006423 RID: 25635 RVA: 0x0015A022 File Offset: 0x00158222
			internal MetadataProperty GetMetadataProperty(MetadataItem item)
			{
				return new MetadataProperty(this._propertyInfo.Name, this._attribute.Type, this._attribute.IsCollectionType, new MetadataPropertyValue(this._propertyInfo, item));
			}

			// Token: 0x04002C78 RID: 11384
			private readonly MetadataPropertyAttribute _attribute;

			// Token: 0x04002C79 RID: 11385
			private readonly PropertyInfo _propertyInfo;
		}
	}
}
