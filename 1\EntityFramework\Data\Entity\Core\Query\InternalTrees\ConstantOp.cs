﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x02000394 RID: 916
	internal sealed class ConstantOp : ConstantBaseOp
	{
		// Token: 0x06002CC4 RID: 11460 RVA: 0x0008F211 File Offset: 0x0008D411
		internal ConstantOp(TypeUsage type, object value)
			: base(OpType.Constant, type, value)
		{
		}

		// Token: 0x06002CC5 RID: 11461 RVA: 0x0008F21C File Offset: 0x0008D41C
		private ConstantOp()
			: base(OpType.Constant)
		{
		}

		// Token: 0x06002CC6 RID: 11462 RVA: 0x0008F225 File Offset: 0x0008D425
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002CC7 RID: 11463 RVA: 0x0008F22F File Offset: 0x0008D42F
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000F0D RID: 3853
		internal static readonly ConstantOp Pattern = new ConstantOp();
	}
}
