﻿using System;
using System.Data.Entity.Resources;

namespace System.Data.Entity.Core.Objects
{
	// Token: 0x02000421 RID: 1057
	internal sealed class ObjectStateEntryDbUpdatableDataRecord : CurrentValueRecord
	{
		// Token: 0x06003311 RID: 13073 RVA: 0x000A1F50 File Offset: 0x000A0150
		internal ObjectStateEntryDbUpdatableDataRecord(EntityEntry cacheEntry, StateManagerTypeMetadata metadata, object userObject)
			: base(cacheEntry, metadata, userObject)
		{
			EntityState state = cacheEntry.State;
			if (state == EntityState.Unchanged || state != EntityState.Added)
			{
			}
		}

		// Token: 0x06003312 RID: 13074 RVA: 0x000A1F7C File Offset: 0x000A017C
		internal ObjectStateEntryDbUpdatableDataRecord(RelationshipEntry cacheEntry)
			: base(cacheEntry)
		{
			EntityState state = cacheEntry.State;
			if (state == EntityState.Unchanged || state != EntityState.Added)
			{
			}
		}

		// Token: 0x06003313 RID: 13075 RVA: 0x000A1FA4 File Offset: 0x000A01A4
		protected override object GetRecordValue(int ordinal)
		{
			if (this._cacheEntry.IsRelationship)
			{
				return (this._cacheEntry as RelationshipEntry).GetCurrentRelationValue(ordinal);
			}
			return (this._cacheEntry as EntityEntry).GetCurrentEntityValue(this._metadata, ordinal, this._userObject, ObjectStateValueRecord.CurrentUpdatable);
		}

		// Token: 0x06003314 RID: 13076 RVA: 0x000A1FE3 File Offset: 0x000A01E3
		protected override void SetRecordValue(int ordinal, object value)
		{
			if (this._cacheEntry.IsRelationship)
			{
				throw new InvalidOperationException(Strings.ObjectStateEntry_CantModifyRelationValues);
			}
			(this._cacheEntry as EntityEntry).SetCurrentEntityValue(this._metadata, ordinal, this._userObject, value);
		}
	}
}
