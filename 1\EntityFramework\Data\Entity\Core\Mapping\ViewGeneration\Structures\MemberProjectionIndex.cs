﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005AA RID: 1450
	internal sealed class MemberProjectionIndex : InternalBase
	{
		// Token: 0x06004693 RID: 18067 RVA: 0x000F8500 File Offset: 0x000F6700
		internal static MemberProjectionIndex Create(EntitySetBase extent, EdmItemCollection edmItemCollection)
		{
			MemberProjectionIndex memberProjectionIndex = new MemberProjectionIndex();
			MemberProjectionIndex.GatherPartialSignature(memberProjectionIndex, edmItemCollection, new MemberPath(extent), false);
			return memberProjectionIndex;
		}

		// Token: 0x06004694 RID: 18068 RVA: 0x000F8515 File Offset: 0x000F6715
		private MemberProjectionIndex()
		{
			this.m_indexMap = new Dictionary<MemberPath, int>(MemberPath.EqualityComparer);
			this.m_members = new List<MemberPath>();
		}

		// Token: 0x17000DFD RID: 3581
		// (get) Token: 0x06004695 RID: 18069 RVA: 0x000F8538 File Offset: 0x000F6738
		internal int Count
		{
			get
			{
				return this.m_members.Count;
			}
		}

		// Token: 0x17000DFE RID: 3582
		internal MemberPath this[int index]
		{
			get
			{
				return this.m_members[index];
			}
		}

		// Token: 0x17000DFF RID: 3583
		// (get) Token: 0x06004697 RID: 18071 RVA: 0x000F8554 File Offset: 0x000F6754
		internal IEnumerable<int> KeySlots
		{
			get
			{
				List<int> list = new List<int>();
				for (int i = 0; i < this.Count; i++)
				{
					if (this.IsKeySlot(i, 0))
					{
						list.Add(i);
					}
				}
				return list;
			}
		}

		// Token: 0x17000E00 RID: 3584
		// (get) Token: 0x06004698 RID: 18072 RVA: 0x000F858A File Offset: 0x000F678A
		internal IEnumerable<MemberPath> Members
		{
			get
			{
				return this.m_members;
			}
		}

		// Token: 0x06004699 RID: 18073 RVA: 0x000F8594 File Offset: 0x000F6794
		internal int IndexOf(MemberPath member)
		{
			int num;
			if (this.m_indexMap.TryGetValue(member, out num))
			{
				return num;
			}
			return -1;
		}

		// Token: 0x0600469A RID: 18074 RVA: 0x000F85B4 File Offset: 0x000F67B4
		internal int CreateIndex(MemberPath member)
		{
			int count;
			if (!this.m_indexMap.TryGetValue(member, out count))
			{
				count = this.m_indexMap.Count;
				this.m_indexMap[member] = count;
				this.m_members.Add(member);
			}
			return count;
		}

		// Token: 0x0600469B RID: 18075 RVA: 0x000F85F7 File Offset: 0x000F67F7
		internal MemberPath GetMemberPath(int slotNum, int numBoolSlots)
		{
			if (!this.IsBoolSlot(slotNum, numBoolSlots))
			{
				return this[slotNum];
			}
			return null;
		}

		// Token: 0x0600469C RID: 18076 RVA: 0x000F860C File Offset: 0x000F680C
		internal int BoolIndexToSlot(int boolIndex, int numBoolSlots)
		{
			return this.Count + boolIndex;
		}

		// Token: 0x0600469D RID: 18077 RVA: 0x000F8616 File Offset: 0x000F6816
		internal int SlotToBoolIndex(int slotNum, int numBoolSlots)
		{
			return slotNum - this.Count;
		}

		// Token: 0x0600469E RID: 18078 RVA: 0x000F8620 File Offset: 0x000F6820
		internal bool IsKeySlot(int slotNum, int numBoolSlots)
		{
			return slotNum < this.Count && this[slotNum].IsPartOfKey;
		}

		// Token: 0x0600469F RID: 18079 RVA: 0x000F8639 File Offset: 0x000F6839
		internal bool IsBoolSlot(int slotNum, int numBoolSlots)
		{
			return slotNum >= this.Count;
		}

		// Token: 0x060046A0 RID: 18080 RVA: 0x000F8647 File Offset: 0x000F6847
		internal override void ToCompactString(StringBuilder builder)
		{
			builder.Append('<');
			StringUtil.ToCommaSeparatedString(builder, this.m_members);
			builder.Append('>');
		}

		// Token: 0x060046A1 RID: 18081 RVA: 0x000F8668 File Offset: 0x000F6868
		private static void GatherPartialSignature(MemberProjectionIndex index, EdmItemCollection edmItemCollection, MemberPath member, bool needKeysOnly)
		{
			EdmType edmType = member.EdmType;
			if (edmType is ComplexType && needKeysOnly)
			{
				return;
			}
			index.CreateIndex(member);
			foreach (EdmType edmType2 in MetadataHelper.GetTypeAndSubtypesOf(edmType, edmItemCollection, false))
			{
				StructuralType structuralType = edmType2 as StructuralType;
				MemberProjectionIndex.GatherSignatureFromTypeStructuralMembers(index, edmItemCollection, member, structuralType, needKeysOnly);
			}
		}

		// Token: 0x060046A2 RID: 18082 RVA: 0x000F86DC File Offset: 0x000F68DC
		private static void GatherSignatureFromTypeStructuralMembers(MemberProjectionIndex index, EdmItemCollection edmItemCollection, MemberPath member, StructuralType possibleType, bool needKeysOnly)
		{
			foreach (object obj in Helper.GetAllStructuralMembers(possibleType))
			{
				EdmMember edmMember = (EdmMember)obj;
				if (MetadataHelper.IsNonRefSimpleMember(edmMember))
				{
					if (!needKeysOnly || MetadataHelper.IsPartOfEntityTypeKey(edmMember))
					{
						MemberPath memberPath = new MemberPath(member, edmMember);
						index.CreateIndex(memberPath);
					}
				}
				else
				{
					MemberPath memberPath2 = new MemberPath(member, edmMember);
					MemberProjectionIndex.GatherPartialSignature(index, edmItemCollection, memberPath2, needKeysOnly || Helper.IsAssociationEndMember(edmMember));
				}
			}
		}

		// Token: 0x04001926 RID: 6438
		private readonly Dictionary<MemberPath, int> m_indexMap;

		// Token: 0x04001927 RID: 6439
		private readonly List<MemberPath> m_members;
	}
}
