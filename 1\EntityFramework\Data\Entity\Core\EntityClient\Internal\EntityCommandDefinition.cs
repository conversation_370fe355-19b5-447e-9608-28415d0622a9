﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Common;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Core.Query.InternalTrees;
using System.Data.Entity.Core.Query.PlanCompiler;
using System.Data.Entity.Core.Query.ResultAssembly;
using System.Data.Entity.Infrastructure.DependencyResolution;
using System.Data.Entity.Infrastructure.Interception;
using System.Data.Entity.Internal;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace System.Data.Entity.Core.EntityClient.Internal
{
	// Token: 0x020005E6 RID: 1510
	internal class EntityCommandDefinition : DbCommandDefinition
	{
		// Token: 0x060049DC RID: 18908 RVA: 0x00104FA3 File Offset: 0x001031A3
		internal EntityCommandDefinition()
		{
		}

		// Token: 0x060049DD RID: 18909 RVA: 0x00104FAC File Offset: 0x001031AC
		internal EntityCommandDefinition(DbProviderFactory storeProviderFactory, DbCommandTree commandTree, DbInterceptionContext interceptionContext, IDbDependencyResolver resolver = null, BridgeDataReaderFactory bridgeDataReaderFactory = null, ColumnMapFactory columnMapFactory = null)
		{
			this._bridgeDataReaderFactory = bridgeDataReaderFactory ?? new BridgeDataReaderFactory(null);
			this._columnMapFactory = columnMapFactory ?? new ColumnMapFactory();
			this._storeProviderServices = ((resolver != null) ? resolver.GetService(storeProviderFactory.GetProviderInvariantName()) : null) ?? storeProviderFactory.GetProviderServices();
			try
			{
				if (commandTree.CommandTreeKind == DbCommandTreeKind.Query)
				{
					List<ProviderCommandInfo> list = new List<ProviderCommandInfo>();
					ColumnMap columnMap;
					int num;
					PlanCompiler.Compile(commandTree, out list, out columnMap, out num, out this._entitySets);
					this._columnMapGenerators = new EntityCommandDefinition.IColumnMapGenerator[]
					{
						new EntityCommandDefinition.ConstantColumnMapGenerator(columnMap, num)
					};
					this._mappedCommandDefinitions = new List<DbCommandDefinition>(list.Count);
					using (List<ProviderCommandInfo>.Enumerator enumerator = list.GetEnumerator())
					{
						while (enumerator.MoveNext())
						{
							ProviderCommandInfo providerCommandInfo = enumerator.Current;
							DbCommandDefinition dbCommandDefinition = this._storeProviderServices.CreateCommandDefinition(providerCommandInfo.CommandTree, interceptionContext);
							if (dbCommandDefinition == null)
							{
								throw new ProviderIncompatibleException(Strings.ProviderReturnedNullForCreateCommandDefinition);
							}
							this._mappedCommandDefinitions.Add(dbCommandDefinition);
						}
						goto IL_023C;
					}
				}
				DbFunctionCommandTree dbFunctionCommandTree = (DbFunctionCommandTree)commandTree;
				FunctionImportMappingNonComposable targetFunctionMapping = EntityCommandDefinition.GetTargetFunctionMapping(dbFunctionCommandTree);
				IList<FunctionParameter> returnParameters = dbFunctionCommandTree.EdmFunction.ReturnParameters;
				int num2 = ((returnParameters.Count > 1) ? returnParameters.Count : 1);
				this._columnMapGenerators = new EntityCommandDefinition.IColumnMapGenerator[num2];
				TypeUsage typeUsage = this.DetermineStoreResultType(targetFunctionMapping, 0, out this._columnMapGenerators[0]);
				for (int i = 1; i < num2; i++)
				{
					this.DetermineStoreResultType(targetFunctionMapping, i, out this._columnMapGenerators[i]);
				}
				List<KeyValuePair<string, TypeUsage>> list2 = new List<KeyValuePair<string, TypeUsage>>();
				foreach (KeyValuePair<string, TypeUsage> keyValuePair in dbFunctionCommandTree.Parameters)
				{
					list2.Add(keyValuePair);
				}
				DbFunctionCommandTree dbFunctionCommandTree2 = new DbFunctionCommandTree(dbFunctionCommandTree.MetadataWorkspace, DataSpace.SSpace, targetFunctionMapping.TargetFunction, typeUsage, list2);
				DbCommandDefinition dbCommandDefinition2 = this._storeProviderServices.CreateCommandDefinition(dbFunctionCommandTree2);
				this._mappedCommandDefinitions = new List<DbCommandDefinition>(1) { dbCommandDefinition2 };
				if (targetFunctionMapping.FunctionImport.EntitySets.FirstOrDefault<EntitySet>() != null)
				{
					this._entitySets = new Set<EntitySet>();
					this._entitySets.Add(targetFunctionMapping.FunctionImport.EntitySets.FirstOrDefault<EntitySet>());
					this._entitySets.MakeReadOnly();
				}
				IL_023C:
				List<EntityParameter> list3 = new List<EntityParameter>();
				foreach (KeyValuePair<string, TypeUsage> keyValuePair2 in commandTree.Parameters)
				{
					EntityParameter entityParameter = EntityCommandDefinition.CreateEntityParameterFromQueryParameter(keyValuePair2);
					list3.Add(entityParameter);
				}
				this._parameters = new ReadOnlyCollection<EntityParameter>(list3);
			}
			catch (EntityCommandCompilationException)
			{
				throw;
			}
			catch (Exception ex)
			{
				if (ex.IsCatchableExceptionType())
				{
					throw new EntityCommandCompilationException(Strings.EntityClient_CommandDefinitionPreparationFailed, ex);
				}
				throw;
			}
		}

		// Token: 0x060049DE RID: 18910 RVA: 0x001052E0 File Offset: 0x001034E0
		protected EntityCommandDefinition(BridgeDataReaderFactory factory = null, ColumnMapFactory columnMapFactory = null, List<DbCommandDefinition> mappedCommandDefinitions = null)
		{
			this._bridgeDataReaderFactory = factory ?? new BridgeDataReaderFactory(null);
			this._columnMapFactory = columnMapFactory ?? new ColumnMapFactory();
			this._mappedCommandDefinitions = mappedCommandDefinitions;
		}

		// Token: 0x060049DF RID: 18911 RVA: 0x00105310 File Offset: 0x00103510
		private TypeUsage DetermineStoreResultType(FunctionImportMappingNonComposable mapping, int resultSetIndex, out EntityCommandDefinition.IColumnMapGenerator columnMapGenerator)
		{
			EdmFunction functionImport = mapping.FunctionImport;
			StructuralType structuralType;
			TypeUsage typeUsage;
			if (MetadataHelper.TryGetFunctionImportReturnType<StructuralType>(functionImport, resultSetIndex, out structuralType))
			{
				EntityCommandDefinition.ValidateEdmResultType(structuralType, functionImport);
				EntitySet entitySet = ((functionImport.EntitySets.Count > resultSetIndex) ? functionImport.EntitySets[resultSetIndex] : null);
				columnMapGenerator = new EntityCommandDefinition.FunctionColumnMapGenerator(mapping, resultSetIndex, entitySet, structuralType, this._columnMapFactory);
				typeUsage = mapping.GetExpectedTargetResultType(resultSetIndex);
			}
			else
			{
				FunctionParameter returnParameter = MetadataHelper.GetReturnParameter(functionImport, resultSetIndex);
				if (returnParameter != null && returnParameter.TypeUsage != null)
				{
					typeUsage = returnParameter.TypeUsage;
					ScalarColumnMap scalarColumnMap = new ScalarColumnMap(((CollectionType)typeUsage.EdmType).TypeUsage, string.Empty, 0, 0);
					SimpleCollectionColumnMap simpleCollectionColumnMap = new SimpleCollectionColumnMap(typeUsage, string.Empty, scalarColumnMap, null, null);
					columnMapGenerator = new EntityCommandDefinition.ConstantColumnMapGenerator(simpleCollectionColumnMap, 1);
				}
				else
				{
					typeUsage = null;
					columnMapGenerator = new EntityCommandDefinition.ConstantColumnMapGenerator(null, 0);
				}
			}
			return typeUsage;
		}

		// Token: 0x060049E0 RID: 18912 RVA: 0x001053D4 File Offset: 0x001035D4
		private static void ValidateEdmResultType(EdmType resultType, EdmFunction functionImport)
		{
			if (Helper.IsComplexType(resultType))
			{
				ComplexType complexType = resultType as ComplexType;
				foreach (EdmProperty edmProperty in complexType.Properties)
				{
					if (edmProperty.TypeUsage.EdmType.BuiltInTypeKind == BuiltInTypeKind.ComplexType)
					{
						throw new NotSupportedException(Strings.ComplexTypeAsReturnTypeAndNestedComplexProperty(edmProperty.Name, complexType.Name, functionImport.FullName));
					}
				}
			}
		}

		// Token: 0x060049E1 RID: 18913 RVA: 0x00105460 File Offset: 0x00103660
		private static FunctionImportMappingNonComposable GetTargetFunctionMapping(DbFunctionCommandTree functionCommandTree)
		{
			FunctionImportMapping functionImportMapping;
			if (!functionCommandTree.MetadataWorkspace.TryGetFunctionImportMapping(functionCommandTree.EdmFunction, out functionImportMapping))
			{
				throw new InvalidOperationException(Strings.EntityClient_UnmappedFunctionImport(functionCommandTree.EdmFunction.FullName));
			}
			return (FunctionImportMappingNonComposable)functionImportMapping;
		}

		// Token: 0x17000EA0 RID: 3744
		// (get) Token: 0x060049E2 RID: 18914 RVA: 0x0010549E File Offset: 0x0010369E
		internal virtual IEnumerable<EntityParameter> Parameters
		{
			get
			{
				return this._parameters;
			}
		}

		// Token: 0x17000EA1 RID: 3745
		// (get) Token: 0x060049E3 RID: 18915 RVA: 0x001054A6 File Offset: 0x001036A6
		internal virtual Set<EntitySet> EntitySets
		{
			get
			{
				return this._entitySets;
			}
		}

		// Token: 0x060049E4 RID: 18916 RVA: 0x001054AE File Offset: 0x001036AE
		public override DbCommand CreateCommand()
		{
			return new EntityCommand(this, new DbInterceptionContext(), null);
		}

		// Token: 0x060049E5 RID: 18917 RVA: 0x001054BC File Offset: 0x001036BC
		internal ColumnMap CreateColumnMap(DbDataReader storeDataReader)
		{
			return this.CreateColumnMap(storeDataReader, 0);
		}

		// Token: 0x060049E6 RID: 18918 RVA: 0x001054C6 File Offset: 0x001036C6
		internal virtual ColumnMap CreateColumnMap(DbDataReader storeDataReader, int resultSetIndex)
		{
			return this._columnMapGenerators[resultSetIndex].CreateColumnMap(storeDataReader);
		}

		// Token: 0x060049E7 RID: 18919 RVA: 0x001054D6 File Offset: 0x001036D6
		private static EntityParameter CreateEntityParameterFromQueryParameter(KeyValuePair<string, TypeUsage> queryParameter)
		{
			EntityParameter entityParameter = new EntityParameter();
			entityParameter.ParameterName = queryParameter.Key;
			EntityCommandDefinition.PopulateParameterFromTypeUsage(entityParameter, queryParameter.Value, false);
			return entityParameter;
		}

		// Token: 0x060049E8 RID: 18920 RVA: 0x001054F8 File Offset: 0x001036F8
		internal static void PopulateParameterFromTypeUsage(EntityParameter parameter, TypeUsage type, bool isOutParam)
		{
			if (type != null)
			{
				PrimitiveTypeKind primitiveTypeKind;
				if (Helper.IsEnumType(type.EdmType))
				{
					type = TypeUsage.Create(Helper.GetUnderlyingEdmTypeForEnumType(type.EdmType));
				}
				else if (Helper.IsSpatialType(type, out primitiveTypeKind))
				{
					parameter.EdmType = EdmProviderManifest.Instance.GetPrimitiveType(primitiveTypeKind);
				}
			}
			DbCommandDefinition.PopulateParameterFromTypeUsage(parameter, type, isOutParam);
		}

		// Token: 0x060049E9 RID: 18921 RVA: 0x0010554C File Offset: 0x0010374C
		internal virtual DbDataReader Execute(EntityCommand entityCommand, CommandBehavior behavior)
		{
			if (CommandBehavior.SequentialAccess != (behavior & CommandBehavior.SequentialAccess))
			{
				throw new InvalidOperationException(Strings.ADP_MustUseSequentialAccess);
			}
			DbDataReader dbDataReader = this.ExecuteStoreCommands(entityCommand, behavior & ~CommandBehavior.SequentialAccess);
			DbDataReader dbDataReader2 = null;
			if (dbDataReader != null)
			{
				try
				{
					ColumnMap columnMap = this.CreateColumnMap(dbDataReader, 0);
					if (columnMap == null)
					{
						CommandHelper.ConsumeReader(dbDataReader);
						dbDataReader2 = dbDataReader;
					}
					else
					{
						MetadataWorkspace metadataWorkspace = entityCommand.Connection.GetMetadataWorkspace();
						IEnumerable<ColumnMap> nextResultColumnMaps = this.GetNextResultColumnMaps(dbDataReader);
						dbDataReader2 = this._bridgeDataReaderFactory.Create(dbDataReader, columnMap, metadataWorkspace, nextResultColumnMaps);
					}
				}
				catch
				{
					dbDataReader.Dispose();
					throw;
				}
			}
			return dbDataReader2;
		}

		// Token: 0x060049EA RID: 18922 RVA: 0x001055D8 File Offset: 0x001037D8
		internal virtual async Task<DbDataReader> ExecuteAsync(EntityCommand entityCommand, CommandBehavior behavior, CancellationToken cancellationToken)
		{
			if (CommandBehavior.SequentialAccess != (behavior & CommandBehavior.SequentialAccess))
			{
				throw new InvalidOperationException(Strings.ADP_MustUseSequentialAccess);
			}
			cancellationToken.ThrowIfCancellationRequested();
			DbDataReader dbDataReader = await this.ExecuteStoreCommandsAsync(entityCommand, behavior & ~CommandBehavior.SequentialAccess, cancellationToken).WithCurrentCulture<DbDataReader>();
			DbDataReader storeDataReader = dbDataReader;
			DbDataReader dbDataReader2 = null;
			if (storeDataReader != null)
			{
				try
				{
					ColumnMap columnMap = this.CreateColumnMap(storeDataReader, 0);
					if (columnMap == null)
					{
						await CommandHelper.ConsumeReaderAsync(storeDataReader, cancellationToken).WithCurrentCulture();
						dbDataReader2 = storeDataReader;
					}
					else
					{
						MetadataWorkspace metadataWorkspace = entityCommand.Connection.GetMetadataWorkspace();
						IEnumerable<ColumnMap> nextResultColumnMaps = this.GetNextResultColumnMaps(storeDataReader);
						dbDataReader2 = this._bridgeDataReaderFactory.Create(storeDataReader, columnMap, metadataWorkspace, nextResultColumnMaps);
					}
				}
				catch
				{
					storeDataReader.Dispose();
					throw;
				}
			}
			return dbDataReader2;
		}

		// Token: 0x060049EB RID: 18923 RVA: 0x00105635 File Offset: 0x00103835
		private IEnumerable<ColumnMap> GetNextResultColumnMaps(DbDataReader storeDataReader)
		{
			int num;
			for (int i = 1; i < this._columnMapGenerators.Length; i = num)
			{
				yield return this.CreateColumnMap(storeDataReader, i);
				num = i + 1;
			}
			yield break;
		}

		// Token: 0x060049EC RID: 18924 RVA: 0x0010564C File Offset: 0x0010384C
		internal virtual DbDataReader ExecuteStoreCommands(EntityCommand entityCommand, CommandBehavior behavior)
		{
			DbCommand dbCommand = this.PrepareEntityCommandBeforeExecution(entityCommand);
			DbDataReader dbDataReader = null;
			try
			{
				dbDataReader = dbCommand.ExecuteReader(behavior);
			}
			catch (Exception ex)
			{
				if (ex.IsCatchableExceptionType())
				{
					throw new EntityCommandExecutionException(Strings.EntityClient_CommandDefinitionExecutionFailed, ex);
				}
				throw;
			}
			return dbDataReader;
		}

		// Token: 0x060049ED RID: 18925 RVA: 0x00105698 File Offset: 0x00103898
		internal virtual async Task<DbDataReader> ExecuteStoreCommandsAsync(EntityCommand entityCommand, CommandBehavior behavior, CancellationToken cancellationToken)
		{
			cancellationToken.ThrowIfCancellationRequested();
			DbCommand dbCommand = this.PrepareEntityCommandBeforeExecution(entityCommand);
			DbDataReader dbDataReader = null;
			try
			{
				dbDataReader = await dbCommand.ExecuteReaderAsync(behavior, cancellationToken).WithCurrentCulture<DbDataReader>();
			}
			catch (Exception ex)
			{
				if (ex.IsCatchableExceptionType())
				{
					throw new EntityCommandExecutionException(Strings.EntityClient_CommandDefinitionExecutionFailed, ex);
				}
				throw;
			}
			return dbDataReader;
		}

		// Token: 0x060049EE RID: 18926 RVA: 0x001056F8 File Offset: 0x001038F8
		private DbCommand PrepareEntityCommandBeforeExecution(EntityCommand entityCommand)
		{
			if (1 != this._mappedCommandDefinitions.Count)
			{
				throw new NotSupportedException("MARS");
			}
			EntityTransaction entityTransaction = entityCommand.ValidateAndGetEntityTransaction();
			InterceptableDbCommand interceptableDbCommand = new InterceptableDbCommand(this._mappedCommandDefinitions[0].CreateCommand(), entityCommand.InterceptionContext, null);
			CommandHelper.SetStoreProviderCommandState(entityCommand, entityTransaction, interceptableDbCommand);
			bool flag = false;
			if (interceptableDbCommand.Parameters != null)
			{
				foreach (object obj in interceptableDbCommand.Parameters)
				{
					DbParameter dbParameter = (DbParameter)obj;
					int num = entityCommand.Parameters.IndexOf(dbParameter.ParameterName);
					if (-1 != num)
					{
						EntityCommandDefinition.SyncParameterProperties(entityCommand.Parameters[num], dbParameter, this._storeProviderServices);
						if (dbParameter.Direction != ParameterDirection.Input)
						{
							flag = true;
						}
					}
				}
			}
			if (flag)
			{
				entityCommand.SetStoreProviderCommand(interceptableDbCommand);
			}
			return interceptableDbCommand;
		}

		// Token: 0x060049EF RID: 18927 RVA: 0x001057E8 File Offset: 0x001039E8
		private static void SyncParameterProperties(EntityParameter entityParameter, DbParameter storeParameter, DbProviderServices storeProviderServices)
		{
			TypeUsage primitiveTypeUsageForScalar = TypeHelpers.GetPrimitiveTypeUsageForScalar(entityParameter.GetTypeUsage());
			storeProviderServices.SetParameterValue(storeParameter, primitiveTypeUsageForScalar, entityParameter.Value);
			if (entityParameter.IsDirectionSpecified)
			{
				storeParameter.Direction = entityParameter.Direction;
			}
			if (entityParameter.IsIsNullableSpecified)
			{
				storeParameter.IsNullable = entityParameter.IsNullable;
			}
			if (entityParameter.IsSizeSpecified)
			{
				storeParameter.Size = entityParameter.Size;
			}
			if (entityParameter.IsPrecisionSpecified)
			{
				((IDbDataParameter)storeParameter).Precision = entityParameter.Precision;
			}
			if (entityParameter.IsScaleSpecified)
			{
				((IDbDataParameter)storeParameter).Scale = entityParameter.Scale;
			}
		}

		// Token: 0x060049F0 RID: 18928 RVA: 0x00105878 File Offset: 0x00103A78
		internal virtual string ToTraceString()
		{
			if (this._mappedCommandDefinitions == null)
			{
				return string.Empty;
			}
			if (this._mappedCommandDefinitions.Count == 1)
			{
				return this._mappedCommandDefinitions[0].CreateCommand().CommandText;
			}
			StringBuilder stringBuilder = new StringBuilder();
			foreach (DbCommandDefinition dbCommandDefinition in this._mappedCommandDefinitions)
			{
				DbCommand dbCommand = dbCommandDefinition.CreateCommand();
				stringBuilder.Append(dbCommand.CommandText);
			}
			return stringBuilder.ToString();
		}

		// Token: 0x04001A0F RID: 6671
		private readonly List<DbCommandDefinition> _mappedCommandDefinitions;

		// Token: 0x04001A10 RID: 6672
		private readonly EntityCommandDefinition.IColumnMapGenerator[] _columnMapGenerators;

		// Token: 0x04001A11 RID: 6673
		private readonly ReadOnlyCollection<EntityParameter> _parameters;

		// Token: 0x04001A12 RID: 6674
		private readonly Set<EntitySet> _entitySets;

		// Token: 0x04001A13 RID: 6675
		private readonly BridgeDataReaderFactory _bridgeDataReaderFactory;

		// Token: 0x04001A14 RID: 6676
		private readonly ColumnMapFactory _columnMapFactory;

		// Token: 0x04001A15 RID: 6677
		private readonly DbProviderServices _storeProviderServices;

		// Token: 0x02000C2C RID: 3116
		private interface IColumnMapGenerator
		{
			// Token: 0x060069F7 RID: 27127
			ColumnMap CreateColumnMap(DbDataReader reader);
		}

		// Token: 0x02000C2D RID: 3117
		private sealed class ConstantColumnMapGenerator : EntityCommandDefinition.IColumnMapGenerator
		{
			// Token: 0x060069F8 RID: 27128 RVA: 0x00169E00 File Offset: 0x00168000
			internal ConstantColumnMapGenerator(ColumnMap columnMap, int fieldsRequired)
			{
				this._columnMap = columnMap;
				this._fieldsRequired = fieldsRequired;
			}

			// Token: 0x060069F9 RID: 27129 RVA: 0x00169E16 File Offset: 0x00168016
			ColumnMap EntityCommandDefinition.IColumnMapGenerator.CreateColumnMap(DbDataReader reader)
			{
				if (reader != null && reader.FieldCount < this._fieldsRequired)
				{
					throw new EntityCommandExecutionException(Strings.EntityClient_TooFewColumns);
				}
				return this._columnMap;
			}

			// Token: 0x04003041 RID: 12353
			private readonly ColumnMap _columnMap;

			// Token: 0x04003042 RID: 12354
			private readonly int _fieldsRequired;
		}

		// Token: 0x02000C2E RID: 3118
		private sealed class FunctionColumnMapGenerator : EntityCommandDefinition.IColumnMapGenerator
		{
			// Token: 0x060069FA RID: 27130 RVA: 0x00169E3A File Offset: 0x0016803A
			internal FunctionColumnMapGenerator(FunctionImportMappingNonComposable mapping, int resultSetIndex, EntitySet entitySet, StructuralType baseStructuralType, ColumnMapFactory columnMapFactory)
			{
				this._mapping = mapping;
				this._entitySet = entitySet;
				this._baseStructuralType = baseStructuralType;
				this._resultSetIndex = resultSetIndex;
				this._columnMapFactory = columnMapFactory;
			}

			// Token: 0x060069FB RID: 27131 RVA: 0x00169E67 File Offset: 0x00168067
			ColumnMap EntityCommandDefinition.IColumnMapGenerator.CreateColumnMap(DbDataReader reader)
			{
				return this._columnMapFactory.CreateFunctionImportStructuralTypeColumnMap(reader, this._mapping, this._resultSetIndex, this._entitySet, this._baseStructuralType);
			}

			// Token: 0x04003043 RID: 12355
			private readonly FunctionImportMappingNonComposable _mapping;

			// Token: 0x04003044 RID: 12356
			private readonly EntitySet _entitySet;

			// Token: 0x04003045 RID: 12357
			private readonly StructuralType _baseStructuralType;

			// Token: 0x04003046 RID: 12358
			private readonly int _resultSetIndex;

			// Token: 0x04003047 RID: 12359
			private readonly ColumnMapFactory _columnMapFactory;
		}
	}
}
