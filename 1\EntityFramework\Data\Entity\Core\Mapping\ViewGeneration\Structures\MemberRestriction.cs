﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils.Boolean;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005AB RID: 1451
	internal abstract class MemberRestriction : BoolLiteral
	{
		// Token: 0x060046A3 RID: 18083 RVA: 0x000F8774 File Offset: 0x000F6974
		protected MemberRestriction(MemberProjectedSlot slot, Constant value)
			: this(slot, new Constant[] { value })
		{
		}

		// Token: 0x060046A4 RID: 18084 RVA: 0x000F8787 File Offset: 0x000F6987
		protected MemberRestriction(MemberProjectedSlot slot, IEnumerable<Constant> values)
		{
			this.m_restrictedMemberSlot = slot;
			this.m_domain = new Domain(values, values);
		}

		// Token: 0x060046A5 RID: 18085 RVA: 0x000F87A3 File Offset: 0x000F69A3
		protected MemberRestriction(MemberProjectedSlot slot, Domain domain)
		{
			this.m_restrictedMemberSlot = slot;
			this.m_domain = domain;
			this.m_isComplete = true;
		}

		// Token: 0x060046A6 RID: 18086 RVA: 0x000F87C0 File Offset: 0x000F69C0
		protected MemberRestriction(MemberProjectedSlot slot, IEnumerable<Constant> values, IEnumerable<Constant> possibleValues)
			: this(slot, new Domain(values, possibleValues))
		{
		}

		// Token: 0x17000E01 RID: 3585
		// (get) Token: 0x060046A7 RID: 18087 RVA: 0x000F87D0 File Offset: 0x000F69D0
		internal bool IsComplete
		{
			get
			{
				return this.m_isComplete;
			}
		}

		// Token: 0x17000E02 RID: 3586
		// (get) Token: 0x060046A8 RID: 18088 RVA: 0x000F87D8 File Offset: 0x000F69D8
		internal MemberProjectedSlot RestrictedMemberSlot
		{
			get
			{
				return this.m_restrictedMemberSlot;
			}
		}

		// Token: 0x17000E03 RID: 3587
		// (get) Token: 0x060046A9 RID: 18089 RVA: 0x000F87E0 File Offset: 0x000F69E0
		internal Domain Domain
		{
			get
			{
				return this.m_domain;
			}
		}

		// Token: 0x060046AA RID: 18090 RVA: 0x000F87E8 File Offset: 0x000F69E8
		internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> GetDomainBoolExpression(MemberDomainMap domainMap)
		{
			TermExpr<DomainConstraint<BoolLiteral, Constant>> termExpr;
			if (domainMap != null)
			{
				IEnumerable<Constant> domain = domainMap.GetDomain(this.m_restrictedMemberSlot.MemberPath);
				termExpr = BoolLiteral.MakeTermExpression(this, domain, this.m_domain.Values);
			}
			else
			{
				termExpr = BoolLiteral.MakeTermExpression(this, this.m_domain.AllPossibleValues, this.m_domain.Values);
			}
			return termExpr;
		}

		// Token: 0x060046AB RID: 18091
		internal abstract MemberRestriction CreateCompleteMemberRestriction(IEnumerable<Constant> possibleValues);

		// Token: 0x060046AC RID: 18092 RVA: 0x000F8840 File Offset: 0x000F6A40
		internal override void GetRequiredSlots(MemberProjectionIndex projectedSlotMap, bool[] requiredSlots)
		{
			MemberPath memberPath = this.RestrictedMemberSlot.MemberPath;
			int num = projectedSlotMap.IndexOf(memberPath);
			requiredSlots[num] = true;
		}

		// Token: 0x060046AD RID: 18093 RVA: 0x000F8868 File Offset: 0x000F6A68
		protected override bool IsEqualTo(BoolLiteral right)
		{
			MemberRestriction memberRestriction = right as MemberRestriction;
			return memberRestriction != null && (this == memberRestriction || (ProjectedSlot.EqualityComparer.Equals(this.m_restrictedMemberSlot, memberRestriction.m_restrictedMemberSlot) && this.m_domain.IsEqualTo(memberRestriction.m_domain)));
		}

		// Token: 0x060046AE RID: 18094 RVA: 0x000F88B2 File Offset: 0x000F6AB2
		public override int GetHashCode()
		{
			return ProjectedSlot.EqualityComparer.GetHashCode(this.m_restrictedMemberSlot) ^ this.m_domain.GetHash();
		}

		// Token: 0x060046AF RID: 18095 RVA: 0x000F88D0 File Offset: 0x000F6AD0
		protected override bool IsIdentifierEqualTo(BoolLiteral right)
		{
			MemberRestriction memberRestriction = right as MemberRestriction;
			return memberRestriction != null && (this == memberRestriction || ProjectedSlot.EqualityComparer.Equals(this.m_restrictedMemberSlot, memberRestriction.m_restrictedMemberSlot));
		}

		// Token: 0x060046B0 RID: 18096 RVA: 0x000F8905 File Offset: 0x000F6B05
		protected override int GetIdentifierHash()
		{
			return ProjectedSlot.EqualityComparer.GetHashCode(this.m_restrictedMemberSlot);
		}

		// Token: 0x060046B1 RID: 18097 RVA: 0x000F8917 File Offset: 0x000F6B17
		internal override StringBuilder AsUserString(StringBuilder builder, string blockAlias, bool skipIsNotNull)
		{
			return this.AsEsql(builder, blockAlias, skipIsNotNull);
		}

		// Token: 0x060046B2 RID: 18098 RVA: 0x000F8922 File Offset: 0x000F6B22
		internal override StringBuilder AsNegatedUserString(StringBuilder builder, string blockAlias, bool skipIsNotNull)
		{
			builder.Append("NOT(");
			builder = this.AsUserString(builder, blockAlias, skipIsNotNull);
			builder.Append(")");
			return builder;
		}

		// Token: 0x04001928 RID: 6440
		private readonly MemberProjectedSlot m_restrictedMemberSlot;

		// Token: 0x04001929 RID: 6441
		private readonly Domain m_domain;

		// Token: 0x0400192A RID: 6442
		private readonly bool m_isComplete;
	}
}
