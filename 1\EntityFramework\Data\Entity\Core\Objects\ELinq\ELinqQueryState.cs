﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.Internal;
using System.Data.Entity.Core.Common.QueryCache;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects.Internal;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace System.Data.Entity.Core.Objects.ELinq
{
	// Token: 0x0200045F RID: 1119
	internal class ELinqQueryState : ObjectQueryState
	{
		// Token: 0x060036F2 RID: 14066 RVA: 0x000B09D4 File Offset: 0x000AEBD4
		internal ELinqQueryState(Type elementType, ObjectContext context, Expression expression, ObjectQueryExecutionPlanFactory objectQueryExecutionPlanFactory = null)
			: base(elementType, context, null, null)
		{
			this._expression = expression;
			this._useCSharpNullComparisonBehavior = context.ContextOptions.UseCSharpNullComparisonBehavior;
			this._disableFilterOverProjectionSimplificationForCustomFunctions = context.ContextOptions.DisableFilterOverProjectionSimplificationForCustomFunctions;
			this._objectQueryExecutionPlanFactory = objectQueryExecutionPlanFactory ?? new ObjectQueryExecutionPlanFactory(null);
		}

		// Token: 0x060036F3 RID: 14067 RVA: 0x000B0A26 File Offset: 0x000AEC26
		internal ELinqQueryState(Type elementType, ObjectQuery query, Expression expression, ObjectQueryExecutionPlanFactory objectQueryExecutionPlanFactory = null)
			: base(elementType, query)
		{
			this._expression = expression;
			this._objectQueryExecutionPlanFactory = objectQueryExecutionPlanFactory ?? new ObjectQueryExecutionPlanFactory(null);
		}

		// Token: 0x060036F4 RID: 14068 RVA: 0x000B0A49 File Offset: 0x000AEC49
		protected override TypeUsage GetResultType()
		{
			return this.CreateExpressionConverter().Convert().ResultType;
		}

		// Token: 0x060036F5 RID: 14069 RVA: 0x000B0A5C File Offset: 0x000AEC5C
		internal override ObjectQueryExecutionPlan GetExecutionPlan(MergeOption? forMergeOption)
		{
			ObjectQueryExecutionPlan objectQueryExecutionPlan = this._cachedPlan;
			if (objectQueryExecutionPlan != null)
			{
				MergeOption? mergeOption = ObjectQueryState.GetMergeOption(new MergeOption?[] { forMergeOption, base.UserSpecifiedMergeOption });
				if ((mergeOption != null && mergeOption.Value != objectQueryExecutionPlan.MergeOption) || this._recompileRequired() || base.ObjectContext.ContextOptions.UseCSharpNullComparisonBehavior != this._useCSharpNullComparisonBehavior || base.ObjectContext.ContextOptions.DisableFilterOverProjectionSimplificationForCustomFunctions != this._disableFilterOverProjectionSimplificationForCustomFunctions)
				{
					objectQueryExecutionPlan = null;
				}
			}
			if (objectQueryExecutionPlan == null)
			{
				this._recompileRequired = null;
				this.ResetParameters();
				ExpressionConverter expressionConverter = this.CreateExpressionConverter();
				DbExpression dbExpression = expressionConverter.Convert();
				this._recompileRequired = expressionConverter.RecompileRequired;
				MergeOption mergeOption2 = ObjectQueryState.EnsureMergeOption(new MergeOption?[] { forMergeOption, base.UserSpecifiedMergeOption, expressionConverter.PropagatedMergeOption });
				this._useCSharpNullComparisonBehavior = base.ObjectContext.ContextOptions.UseCSharpNullComparisonBehavior;
				this._disableFilterOverProjectionSimplificationForCustomFunctions = base.ObjectContext.ContextOptions.DisableFilterOverProjectionSimplificationForCustomFunctions;
				this._linqParameters = expressionConverter.GetParameters();
				if (this._linqParameters != null && this._linqParameters.Any<Tuple<ObjectParameter, QueryParameterExpression>>())
				{
					ObjectParameterCollection objectParameterCollection = base.EnsureParameters();
					objectParameterCollection.SetReadOnly(false);
					foreach (Tuple<ObjectParameter, QueryParameterExpression> tuple in this._linqParameters)
					{
						ObjectParameter item = tuple.Item1;
						objectParameterCollection.Add(item);
					}
					objectParameterCollection.SetReadOnly(true);
				}
				QueryCacheManager queryCacheManager = null;
				LinqQueryCacheKey linqQueryCacheKey = null;
				string text;
				if (base.PlanCachingEnabled && !this._recompileRequired() && ExpressionKeyGen.TryGenerateKey(dbExpression, out text))
				{
					linqQueryCacheKey = new LinqQueryCacheKey(text, (base.Parameters == null) ? 0 : base.Parameters.Count, (base.Parameters == null) ? null : base.Parameters.GetCacheKey(), (expressionConverter.PropagatedSpan == null) ? null : expressionConverter.PropagatedSpan.GetCacheKey(), mergeOption2, base.EffectiveStreamingBehavior, this._useCSharpNullComparisonBehavior, base.ElementType);
					queryCacheManager = base.ObjectContext.MetadataWorkspace.GetQueryCacheManager();
					ObjectQueryExecutionPlan objectQueryExecutionPlan2 = null;
					if (queryCacheManager.TryCacheLookup<LinqQueryCacheKey, ObjectQueryExecutionPlan>(linqQueryCacheKey, out objectQueryExecutionPlan2))
					{
						objectQueryExecutionPlan = objectQueryExecutionPlan2;
					}
				}
				if (objectQueryExecutionPlan == null)
				{
					DbQueryCommandTree dbQueryCommandTree = DbQueryCommandTree.FromValidExpression(base.ObjectContext.MetadataWorkspace, DataSpace.CSpace, dbExpression, !this._useCSharpNullComparisonBehavior, this._disableFilterOverProjectionSimplificationForCustomFunctions);
					objectQueryExecutionPlan = this._objectQueryExecutionPlanFactory.Prepare(base.ObjectContext, dbQueryCommandTree, base.ElementType, mergeOption2, base.EffectiveStreamingBehavior, expressionConverter.PropagatedSpan, null, expressionConverter.AliasGenerator);
					if (linqQueryCacheKey != null)
					{
						QueryCacheEntry queryCacheEntry = new QueryCacheEntry(linqQueryCacheKey, objectQueryExecutionPlan);
						QueryCacheEntry queryCacheEntry2 = null;
						if (queryCacheManager.TryLookupAndAdd(queryCacheEntry, out queryCacheEntry2))
						{
							objectQueryExecutionPlan = (ObjectQueryExecutionPlan)queryCacheEntry2.GetTarget();
						}
					}
				}
				this._cachedPlan = objectQueryExecutionPlan;
			}
			if (this._linqParameters != null)
			{
				foreach (Tuple<ObjectParameter, QueryParameterExpression> tuple2 in this._linqParameters)
				{
					ObjectParameter item2 = tuple2.Item1;
					QueryParameterExpression item3 = tuple2.Item2;
					if (item3 != null)
					{
						item2.Value = item3.EvaluateParameter(null);
					}
				}
			}
			return objectQueryExecutionPlan;
		}

		// Token: 0x060036F6 RID: 14070 RVA: 0x000B0D98 File Offset: 0x000AEF98
		internal override ObjectQueryState Include<TElementType>(ObjectQuery<TElementType> sourceQuery, string includePath)
		{
			MethodInfo includeMethod = ELinqQueryState.GetIncludeMethod<TElementType>(sourceQuery);
			Expression expression = Expression.Call(Expression.Constant(sourceQuery), includeMethod, new Expression[] { Expression.Constant(includePath, typeof(string)) });
			ObjectQueryState objectQueryState = new ELinqQueryState(base.ElementType, base.ObjectContext, expression, null);
			base.ApplySettingsTo(objectQueryState);
			return objectQueryState;
		}

		// Token: 0x060036F7 RID: 14071 RVA: 0x000B0DEE File Offset: 0x000AEFEE
		internal static MethodInfo GetIncludeMethod<TElementType>(ObjectQuery<TElementType> sourceQuery)
		{
			return sourceQuery.GetType().GetOnlyDeclaredMethod("Include");
		}

		// Token: 0x060036F8 RID: 14072 RVA: 0x000B0E00 File Offset: 0x000AF000
		internal override bool TryGetCommandText(out string commandText)
		{
			commandText = null;
			return false;
		}

		// Token: 0x060036F9 RID: 14073 RVA: 0x000B0E06 File Offset: 0x000AF006
		internal override bool TryGetExpression(out Expression expression)
		{
			expression = this.Expression;
			return true;
		}

		// Token: 0x17000A97 RID: 2711
		// (get) Token: 0x060036FA RID: 14074 RVA: 0x000B0E11 File Offset: 0x000AF011
		internal virtual Expression Expression
		{
			get
			{
				return this._expression;
			}
		}

		// Token: 0x060036FB RID: 14075 RVA: 0x000B0E19 File Offset: 0x000AF019
		protected virtual ExpressionConverter CreateExpressionConverter()
		{
			return new ExpressionConverter(Funcletizer.CreateQueryFuncletizer(base.ObjectContext), this._expression);
		}

		// Token: 0x060036FC RID: 14076 RVA: 0x000B0E34 File Offset: 0x000AF034
		private void ResetParameters()
		{
			if (base.Parameters != null)
			{
				bool isReadOnly = ((ICollection<ObjectParameter>)base.Parameters).IsReadOnly;
				if (isReadOnly)
				{
					base.Parameters.SetReadOnly(false);
				}
				base.Parameters.Clear();
				if (isReadOnly)
				{
					base.Parameters.SetReadOnly(true);
				}
			}
			this._linqParameters = null;
		}

		// Token: 0x040011D4 RID: 4564
		private readonly Expression _expression;

		// Token: 0x040011D5 RID: 4565
		private Func<bool> _recompileRequired;

		// Token: 0x040011D6 RID: 4566
		private IEnumerable<Tuple<ObjectParameter, QueryParameterExpression>> _linqParameters;

		// Token: 0x040011D7 RID: 4567
		private bool _useCSharpNullComparisonBehavior;

		// Token: 0x040011D8 RID: 4568
		private bool _disableFilterOverProjectionSimplificationForCustomFunctions;

		// Token: 0x040011D9 RID: 4569
		private readonly ObjectQueryExecutionPlanFactory _objectQueryExecutionPlanFactory;
	}
}
