﻿using System;
using System.Data.Entity.Core.Metadata.Edm;

namespace System.Data.Entity.Core.Objects.DataClasses
{
	// Token: 0x02000481 RID: 1153
	[Serializable]
	internal class RelationshipFixer<TSourceEntity, TTargetEntity> : IRelationshipFixer where TSourceEntity : class where TTargetEntity : class
	{
		// Token: 0x060038F7 RID: 14583 RVA: 0x000BADB6 File Offset: 0x000B8FB6
		internal RelationshipFixer(RelationshipMultiplicity sourceRoleMultiplicity, RelationshipMultiplicity targetRoleMultiplicity)
		{
			this._sourceRoleMultiplicity = sourceRoleMultiplicity;
			this._targetRoleMultiplicity = targetRoleMultiplicity;
		}

		// Token: 0x060038F8 RID: 14584 RVA: 0x000BADCC File Offset: 0x000B8FCC
		RelatedEnd IRelationshipFixer.CreateSourceEnd(RelationshipNavigation navigation, RelationshipManager relationshipManager)
		{
			return relationshipManager.CreateRelatedEnd<TTargetEntity, TSourceEntity>(navigation, this._targetRoleMultiplicity, this._sourceRoleMultiplicity, null);
		}

		// Token: 0x04001300 RID: 4864
		private readonly RelationshipMultiplicity _sourceRoleMultiplicity;

		// Token: 0x04001301 RID: 4865
		private readonly RelationshipMultiplicity _targetRoleMultiplicity;
	}
}
