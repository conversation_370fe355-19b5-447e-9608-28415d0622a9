﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Common.Utils.Boolean;
using System.Data.Entity.Resources;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005B1 RID: 1457
	internal class ScalarRestriction : MemberRestriction
	{
		// Token: 0x060046FD RID: 18173 RVA: 0x000F9CAE File Offset: 0x000F7EAE
		internal ScalarRestriction(MemberPath member, Constant value)
			: base(new MemberProjectedSlot(member), value)
		{
		}

		// Token: 0x060046FE RID: 18174 RVA: 0x000F9CBD File Offset: 0x000F7EBD
		internal ScalarRestriction(MemberPath member, IEnumerable<Constant> values, IEnumerable<Constant> possibleValues)
			: base(new MemberProjectedSlot(member), values, possibleValues)
		{
		}

		// Token: 0x060046FF RID: 18175 RVA: 0x000F9CCD File Offset: 0x000F7ECD
		internal ScalarRestriction(MemberProjectedSlot slot, Domain domain)
			: base(slot, domain)
		{
		}

		// Token: 0x06004700 RID: 18176 RVA: 0x000F9CD8 File Offset: 0x000F7ED8
		internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> FixRange(Set<Constant> range, MemberDomainMap memberDomainMap)
		{
			IEnumerable<Constant> domain = memberDomainMap.GetDomain(base.RestrictedMemberSlot.MemberPath);
			return new ScalarRestriction(base.RestrictedMemberSlot, new Domain(range, domain)).GetDomainBoolExpression(memberDomainMap);
		}

		// Token: 0x06004701 RID: 18177 RVA: 0x000F9D0F File Offset: 0x000F7F0F
		internal override BoolLiteral RemapBool(Dictionary<MemberPath, MemberPath> remap)
		{
			return new ScalarRestriction(base.RestrictedMemberSlot.RemapSlot(remap), base.Domain);
		}

		// Token: 0x06004702 RID: 18178 RVA: 0x000F9D28 File Offset: 0x000F7F28
		internal override MemberRestriction CreateCompleteMemberRestriction(IEnumerable<Constant> possibleValues)
		{
			return new ScalarRestriction(base.RestrictedMemberSlot, new Domain(base.Domain.Values, possibleValues));
		}

		// Token: 0x06004703 RID: 18179 RVA: 0x000F9D46 File Offset: 0x000F7F46
		internal override StringBuilder AsEsql(StringBuilder builder, string blockAlias, bool skipIsNotNull)
		{
			return this.ToStringHelper(builder, blockAlias, skipIsNotNull, false);
		}

		// Token: 0x06004704 RID: 18180 RVA: 0x000F9D54 File Offset: 0x000F7F54
		internal override DbExpression AsCqt(DbExpression row, bool skipIsNotNull)
		{
			DbExpression cqt = null;
			Func<Constant, DbExpression> <>9__4;
			this.AsCql(delegate(NegatedConstant negated, IEnumerable<Constant> domainValues)
			{
				cqt = negated.AsCqt(row, domainValues, this.RestrictedMemberSlot.MemberPath, skipIsNotNull);
			}, delegate(Set<Constant> domainValues)
			{
				cqt = this.RestrictedMemberSlot.MemberPath.AsCqt(row);
				if (domainValues.Count == 1)
				{
					cqt = cqt.Equal(domainValues.Single<Constant>().AsCqt(row, this.RestrictedMemberSlot.MemberPath));
					return;
				}
				Func<Constant, DbExpression> func;
				if ((func = <>9__4) == null)
				{
					func = (<>9__4 = (Constant c) => cqt.Equal(c.AsCqt(row, this.RestrictedMemberSlot.MemberPath)));
				}
				List<DbExpression> list = domainValues.Select(func).ToList<DbExpression>();
				cqt = Helpers.BuildBalancedTreeInPlace<DbExpression>(list, (DbExpression prev, DbExpression next) => prev.Or(next));
			}, delegate
			{
				DbExpression dbExpression = this.RestrictedMemberSlot.MemberPath.AsCqt(row).IsNull().Not();
				cqt = ((cqt != null) ? cqt.And(dbExpression) : dbExpression);
			}, delegate
			{
				DbExpression dbExpression2 = this.RestrictedMemberSlot.MemberPath.AsCqt(row).IsNull();
				cqt = ((cqt != null) ? dbExpression2.Or(cqt) : dbExpression2);
			}, skipIsNotNull);
			return cqt;
		}

		// Token: 0x06004705 RID: 18181 RVA: 0x000F9DC5 File Offset: 0x000F7FC5
		internal override StringBuilder AsUserString(StringBuilder builder, string blockAlias, bool skipIsNotNull)
		{
			return this.ToStringHelper(builder, blockAlias, skipIsNotNull, true);
		}

		// Token: 0x06004706 RID: 18182 RVA: 0x000F9DD4 File Offset: 0x000F7FD4
		private StringBuilder ToStringHelper(StringBuilder inputBuilder, string blockAlias, bool skipIsNotNull, bool userString)
		{
			StringBuilder builder = new StringBuilder();
			this.AsCql(delegate(NegatedConstant negated, IEnumerable<Constant> domainValues)
			{
				if (userString)
				{
					negated.AsUserString(builder, blockAlias, domainValues, this.RestrictedMemberSlot.MemberPath, skipIsNotNull);
					return;
				}
				negated.AsEsql(builder, blockAlias, domainValues, this.RestrictedMemberSlot.MemberPath, skipIsNotNull);
			}, delegate(Set<Constant> domainValues)
			{
				this.RestrictedMemberSlot.MemberPath.AsEsql(builder, blockAlias);
				if (domainValues.Count != 1)
				{
					builder.Append(" IN {");
					bool flag = true;
					foreach (Constant constant in domainValues)
					{
						if (!flag)
						{
							builder.Append(", ");
						}
						if (userString)
						{
							constant.ToCompactString(builder);
						}
						else
						{
							constant.AsEsql(builder, this.RestrictedMemberSlot.MemberPath, blockAlias);
						}
						flag = false;
					}
					builder.Append('}');
					return;
				}
				builder.Append(" = ");
				if (userString)
				{
					domainValues.Single<Constant>().ToCompactString(builder);
					return;
				}
				domainValues.Single<Constant>().AsEsql(builder, this.RestrictedMemberSlot.MemberPath, blockAlias);
			}, delegate
			{
				bool flag2 = builder.Length == 0;
				builder.Insert(0, '(');
				if (!flag2)
				{
					builder.Append(" AND ");
				}
				if (userString)
				{
					this.RestrictedMemberSlot.MemberPath.ToCompactString(builder, Strings.ViewGen_EntityInstanceToken);
					builder.Append(" is not NULL)");
					return;
				}
				this.RestrictedMemberSlot.MemberPath.AsEsql(builder, blockAlias);
				builder.Append(" IS NOT NULL)");
			}, delegate
			{
				bool flag3 = builder.Length == 0;
				StringBuilder stringBuilder = new StringBuilder();
				if (!flag3)
				{
					stringBuilder.Append('(');
				}
				if (userString)
				{
					this.RestrictedMemberSlot.MemberPath.ToCompactString(stringBuilder, blockAlias);
					stringBuilder.Append(" is NULL");
				}
				else
				{
					this.RestrictedMemberSlot.MemberPath.AsEsql(stringBuilder, blockAlias);
					stringBuilder.Append(" IS NULL");
				}
				if (!flag3)
				{
					stringBuilder.Append(" OR ");
				}
				builder.Insert(0, stringBuilder.ToString());
				if (!flag3)
				{
					builder.Append(')');
				}
			}, skipIsNotNull);
			inputBuilder.Append(builder);
			return inputBuilder;
		}

		// Token: 0x06004707 RID: 18183 RVA: 0x000F9E5C File Offset: 0x000F805C
		private void AsCql(Action<NegatedConstant, IEnumerable<Constant>> negatedConstantAsCql, Action<Set<Constant>> varInDomain, Action varIsNotNull, Action varIsNull, bool skipIsNotNull)
		{
			NegatedConstant negatedConstant = (NegatedConstant)base.Domain.Values.FirstOrDefault((Constant c) => c is NegatedConstant);
			if (negatedConstant != null)
			{
				negatedConstantAsCql(negatedConstant, base.Domain.Values);
				return;
			}
			Set<Constant> set = new Set<Constant>(base.Domain.Values, Constant.EqualityComparer);
			bool flag = false;
			if (set.Contains(Constant.Null))
			{
				flag = true;
				set.Remove(Constant.Null);
			}
			if (set.Contains(Constant.Undefined))
			{
				flag = true;
				set.Remove(Constant.Undefined);
			}
			bool flag2 = !skipIsNotNull && base.RestrictedMemberSlot.MemberPath.IsNullable;
			if (set.Count > 0)
			{
				varInDomain(set);
			}
			if (flag2)
			{
				varIsNotNull();
			}
			if (flag)
			{
				varIsNull();
			}
		}

		// Token: 0x06004708 RID: 18184 RVA: 0x000F9F39 File Offset: 0x000F8139
		internal override void ToCompactString(StringBuilder builder)
		{
			base.RestrictedMemberSlot.ToCompactString(builder);
			builder.Append(" IN (");
			StringUtil.ToCommaSeparatedStringSorted(builder, base.Domain.Values);
			builder.Append(")");
		}
	}
}
