﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Hierarchy;
using System.Data.Entity.Spatial;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004EC RID: 1260
	public class PrimitiveType : SimpleType
	{
		// Token: 0x06003ECA RID: 16074 RVA: 0x000D029F File Offset: 0x000CE49F
		internal PrimitiveType()
		{
		}

		// Token: 0x06003ECB RID: 16075 RVA: 0x000D02A7 File Offset: 0x000CE4A7
		internal PrimitiveType(string name, string namespaceName, DataSpace dataSpace, PrimitiveType baseType, DbProviderManifest providerManifest)
			: base(name, namespaceName, dataSpace)
		{
			Check.NotNull<PrimitiveType>(baseType, "baseType");
			Check.NotNull<DbProviderManifest>(providerManifest, "providerManifest");
			this.BaseType = baseType;
			PrimitiveType.Initialize(this, baseType.PrimitiveTypeKind, providerManifest);
		}

		// Token: 0x06003ECC RID: 16076 RVA: 0x000D02E3 File Offset: 0x000CE4E3
		internal PrimitiveType(Type clrType, PrimitiveType baseType, DbProviderManifest providerManifest)
			: this(Check.NotNull<Type>(clrType, "clrType").Name, clrType.NestingNamespace(), DataSpace.OSpace, baseType, providerManifest)
		{
		}

		// Token: 0x17000C4B RID: 3147
		// (get) Token: 0x06003ECD RID: 16077 RVA: 0x000D0304 File Offset: 0x000CE504
		public override BuiltInTypeKind BuiltInTypeKind
		{
			get
			{
				return BuiltInTypeKind.PrimitiveType;
			}
		}

		// Token: 0x17000C4C RID: 3148
		// (get) Token: 0x06003ECE RID: 16078 RVA: 0x000D0308 File Offset: 0x000CE508
		internal override Type ClrType
		{
			get
			{
				return this.ClrEquivalentType;
			}
		}

		// Token: 0x17000C4D RID: 3149
		// (get) Token: 0x06003ECF RID: 16079 RVA: 0x000D0310 File Offset: 0x000CE510
		// (set) Token: 0x06003ED0 RID: 16080 RVA: 0x000D0318 File Offset: 0x000CE518
		[MetadataProperty(BuiltInTypeKind.PrimitiveTypeKind, false)]
		public virtual PrimitiveTypeKind PrimitiveTypeKind
		{
			get
			{
				return this._primitiveTypeKind;
			}
			internal set
			{
				this._primitiveTypeKind = value;
			}
		}

		// Token: 0x17000C4E RID: 3150
		// (get) Token: 0x06003ED1 RID: 16081 RVA: 0x000D0321 File Offset: 0x000CE521
		// (set) Token: 0x06003ED2 RID: 16082 RVA: 0x000D0329 File Offset: 0x000CE529
		internal DbProviderManifest ProviderManifest
		{
			get
			{
				return this._providerManifest;
			}
			set
			{
				this._providerManifest = value;
			}
		}

		// Token: 0x17000C4F RID: 3151
		// (get) Token: 0x06003ED3 RID: 16083 RVA: 0x000D0332 File Offset: 0x000CE532
		public virtual ReadOnlyCollection<FacetDescription> FacetDescriptions
		{
			get
			{
				return this.ProviderManifest.GetFacetDescriptions(this);
			}
		}

		// Token: 0x17000C50 RID: 3152
		// (get) Token: 0x06003ED4 RID: 16084 RVA: 0x000D0340 File Offset: 0x000CE540
		public Type ClrEquivalentType
		{
			get
			{
				switch (this.PrimitiveTypeKind)
				{
				case PrimitiveTypeKind.Binary:
					return typeof(byte[]);
				case PrimitiveTypeKind.Boolean:
					return typeof(bool);
				case PrimitiveTypeKind.Byte:
					return typeof(byte);
				case PrimitiveTypeKind.DateTime:
					return typeof(DateTime);
				case PrimitiveTypeKind.Decimal:
					return typeof(decimal);
				case PrimitiveTypeKind.Double:
					return typeof(double);
				case PrimitiveTypeKind.Guid:
					return typeof(Guid);
				case PrimitiveTypeKind.Single:
					return typeof(float);
				case PrimitiveTypeKind.SByte:
					return typeof(sbyte);
				case PrimitiveTypeKind.Int16:
					return typeof(short);
				case PrimitiveTypeKind.Int32:
					return typeof(int);
				case PrimitiveTypeKind.Int64:
					return typeof(long);
				case PrimitiveTypeKind.String:
					return typeof(string);
				case PrimitiveTypeKind.Time:
					return typeof(TimeSpan);
				case PrimitiveTypeKind.DateTimeOffset:
					return typeof(DateTimeOffset);
				case PrimitiveTypeKind.Geometry:
				case PrimitiveTypeKind.GeometryPoint:
				case PrimitiveTypeKind.GeometryLineString:
				case PrimitiveTypeKind.GeometryPolygon:
				case PrimitiveTypeKind.GeometryMultiPoint:
				case PrimitiveTypeKind.GeometryMultiLineString:
				case PrimitiveTypeKind.GeometryMultiPolygon:
				case PrimitiveTypeKind.GeometryCollection:
					return typeof(DbGeometry);
				case PrimitiveTypeKind.Geography:
				case PrimitiveTypeKind.GeographyPoint:
				case PrimitiveTypeKind.GeographyLineString:
				case PrimitiveTypeKind.GeographyPolygon:
				case PrimitiveTypeKind.GeographyMultiPoint:
				case PrimitiveTypeKind.GeographyMultiLineString:
				case PrimitiveTypeKind.GeographyMultiPolygon:
				case PrimitiveTypeKind.GeographyCollection:
					return typeof(DbGeography);
				case PrimitiveTypeKind.HierarchyId:
					return typeof(HierarchyId);
				default:
					return null;
				}
			}
		}

		// Token: 0x06003ED5 RID: 16085 RVA: 0x000D04A6 File Offset: 0x000CE6A6
		internal override IEnumerable<FacetDescription> GetAssociatedFacetDescriptions()
		{
			return base.GetAssociatedFacetDescriptions().Concat(this.FacetDescriptions);
		}

		// Token: 0x06003ED6 RID: 16086 RVA: 0x000D04B9 File Offset: 0x000CE6B9
		internal static void Initialize(PrimitiveType primitiveType, PrimitiveTypeKind primitiveTypeKind, DbProviderManifest providerManifest)
		{
			primitiveType._primitiveTypeKind = primitiveTypeKind;
			primitiveType._providerManifest = providerManifest;
		}

		// Token: 0x06003ED7 RID: 16087 RVA: 0x000D04C9 File Offset: 0x000CE6C9
		public EdmType GetEdmPrimitiveType()
		{
			return MetadataItem.EdmProviderManifest.GetPrimitiveType(this.PrimitiveTypeKind);
		}

		// Token: 0x06003ED8 RID: 16088 RVA: 0x000D04DB File Offset: 0x000CE6DB
		public static ReadOnlyCollection<PrimitiveType> GetEdmPrimitiveTypes()
		{
			return MetadataItem.EdmProviderManifest.GetStoreTypes();
		}

		// Token: 0x06003ED9 RID: 16089 RVA: 0x000D04E7 File Offset: 0x000CE6E7
		public static PrimitiveType GetEdmPrimitiveType(PrimitiveTypeKind primitiveTypeKind)
		{
			return MetadataItem.EdmProviderManifest.GetPrimitiveType(primitiveTypeKind);
		}

		// Token: 0x04001550 RID: 5456
		private PrimitiveTypeKind _primitiveTypeKind;

		// Token: 0x04001551 RID: 5457
		private DbProviderManifest _providerManifest;
	}
}
