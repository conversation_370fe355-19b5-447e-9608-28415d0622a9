﻿using System;
using System.Data.Entity.Core.Mapping;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x0200048F RID: 1167
	internal sealed class ClrPerspective : Perspective
	{
		// Token: 0x060039E8 RID: 14824 RVA: 0x000BDD76 File Offset: 0x000BBF76
		internal ClrPerspective(MetadataWorkspace metadataWorkspace)
			: base(metadataWorkspace, DataSpace.CSpace)
		{
		}

		// Token: 0x060039E9 RID: 14825 RVA: 0x000BDD80 File Offset: 0x000BBF80
		internal bool TryGetType(Type clrType, out TypeUsage outTypeUsage)
		{
			return this.TryGetTypeByName(clrType.FullNameWithNesting(), false, out outTypeUsage);
		}

		// Token: 0x060039EA RID: 14826 RVA: 0x000BDD90 File Offset: 0x000BBF90
		internal override bool TryGetMember(StructuralType type, string memberName, bool ignoreCase, out EdmMember outMember)
		{
			outMember = null;
			MappingBase mappingBase = null;
			if (base.MetadataWorkspace.TryGetMap(type, DataSpace.OCSpace, out mappingBase))
			{
				ObjectTypeMapping objectTypeMapping = mappingBase as ObjectTypeMapping;
				if (objectTypeMapping != null)
				{
					ObjectMemberMapping memberMapForClrMember = objectTypeMapping.GetMemberMapForClrMember(memberName, ignoreCase);
					if (memberMapForClrMember != null)
					{
						outMember = memberMapForClrMember.EdmMember;
						return true;
					}
				}
			}
			return false;
		}

		// Token: 0x060039EB RID: 14827 RVA: 0x000BDDD8 File Offset: 0x000BBFD8
		internal override bool TryGetTypeByName(string fullName, bool ignoreCase, out TypeUsage typeUsage)
		{
			typeUsage = null;
			MappingBase mappingBase = null;
			if (base.MetadataWorkspace.TryGetMap(fullName, DataSpace.OSpace, ignoreCase, DataSpace.OCSpace, out mappingBase))
			{
				if (mappingBase.EdmItem.BuiltInTypeKind == BuiltInTypeKind.PrimitiveType)
				{
					PrimitiveType mappedPrimitiveType = base.MetadataWorkspace.GetMappedPrimitiveType(((PrimitiveType)mappingBase.EdmItem).PrimitiveTypeKind, DataSpace.CSpace);
					if (mappedPrimitiveType != null)
					{
						typeUsage = EdmProviderManifest.Instance.GetCanonicalModelTypeUsage(mappedPrimitiveType.PrimitiveTypeKind);
					}
				}
				else
				{
					typeUsage = ClrPerspective.GetMappedTypeUsage(mappingBase);
				}
			}
			return typeUsage != null;
		}

		// Token: 0x060039EC RID: 14828 RVA: 0x000BDE4D File Offset: 0x000BC04D
		internal override EntityContainer GetDefaultContainer()
		{
			return this._defaultContainer;
		}

		// Token: 0x060039ED RID: 14829 RVA: 0x000BDE58 File Offset: 0x000BC058
		internal void SetDefaultContainer(string defaultContainerName)
		{
			EntityContainer entityContainer = null;
			if (!string.IsNullOrEmpty(defaultContainerName) && !base.MetadataWorkspace.TryGetEntityContainer(defaultContainerName, DataSpace.CSpace, out entityContainer))
			{
				throw new ArgumentException(Strings.ObjectContext_InvalidDefaultContainerName(defaultContainerName), "defaultContainerName");
			}
			this._defaultContainer = entityContainer;
		}

		// Token: 0x060039EE RID: 14830 RVA: 0x000BDE98 File Offset: 0x000BC098
		private static TypeUsage GetMappedTypeUsage(MappingBase map)
		{
			TypeUsage typeUsage = null;
			if (map != null)
			{
				MetadataItem edmItem = map.EdmItem;
				EdmType edmType = edmItem as EdmType;
				if (edmItem != null && edmType != null)
				{
					typeUsage = TypeUsage.Create(edmType);
				}
			}
			return typeUsage;
		}

		// Token: 0x04001353 RID: 4947
		private EntityContainer _defaultContainer;
	}
}
