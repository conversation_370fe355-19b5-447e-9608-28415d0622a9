﻿using System;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder.Hierarchy
{
	// Token: 0x020006FD RID: 1789
	public static class HierarchyIdEdmFunctions
	{
		// Token: 0x06005495 RID: 21653 RVA: 0x0012F3F4 File Offset: 0x0012D5F4
		public static DbFunctionExpression HierarchyIdParse(DbExpression input)
		{
			Check.NotNull<DbExpression>(input, "input");
			return EdmFunctions.InvokeCanonicalFunction("HierarchyIdParse", new DbExpression[] { input });
		}

		// Token: 0x06005496 RID: 21654 RVA: 0x0012F416 File Offset: 0x0012D616
		public static DbFunctionExpression HierarchyIdGetRoot()
		{
			return EdmFunctions.InvokeCanonicalFunction("HierarchyIdGetRoot", new DbExpression[0]);
		}

		// Token: 0x06005497 RID: 21655 RVA: 0x0012F428 File Offset: 0x0012D628
		public static DbFunctionExpression GetAncestor(this DbExpression hierarchyIdValue, DbExpression n)
		{
			Check.NotNull<DbExpression>(hierarchyIdValue, "hierarchyIdValue");
			Check.NotNull<DbExpression>(n, "n");
			return EdmFunctions.InvokeCanonicalFunction("GetAncestor", new DbExpression[] { hierarchyIdValue, n });
		}

		// Token: 0x06005498 RID: 21656 RVA: 0x0012F45C File Offset: 0x0012D65C
		public static DbFunctionExpression GetDescendant(this DbExpression hierarchyIdValue, DbExpression child1, DbExpression child2)
		{
			Check.NotNull<DbExpression>(hierarchyIdValue, "hierarchyIdValue");
			Check.NotNull<DbExpression>(child1, "child1");
			Check.NotNull<DbExpression>(child2, "child2");
			return EdmFunctions.InvokeCanonicalFunction("GetDescendant", new DbExpression[] { hierarchyIdValue, child1, child2 });
		}

		// Token: 0x06005499 RID: 21657 RVA: 0x0012F4A9 File Offset: 0x0012D6A9
		public static DbFunctionExpression GetLevel(this DbExpression hierarchyIdValue)
		{
			Check.NotNull<DbExpression>(hierarchyIdValue, "hierarchyIdValue");
			return EdmFunctions.InvokeCanonicalFunction("GetLevel", new DbExpression[] { hierarchyIdValue });
		}

		// Token: 0x0600549A RID: 21658 RVA: 0x0012F4CB File Offset: 0x0012D6CB
		public static DbFunctionExpression IsDescendantOf(this DbExpression hierarchyIdValue, DbExpression parent)
		{
			Check.NotNull<DbExpression>(hierarchyIdValue, "hierarchyIdValue");
			Check.NotNull<DbExpression>(parent, "parent");
			return EdmFunctions.InvokeCanonicalFunction("IsDescendantOf", new DbExpression[] { hierarchyIdValue, parent });
		}

		// Token: 0x0600549B RID: 21659 RVA: 0x0012F500 File Offset: 0x0012D700
		public static DbFunctionExpression GetReparentedValue(this DbExpression hierarchyIdValue, DbExpression oldRoot, DbExpression newRoot)
		{
			Check.NotNull<DbExpression>(hierarchyIdValue, "hierarchyIdValue");
			Check.NotNull<DbExpression>(oldRoot, "oldRoot");
			Check.NotNull<DbExpression>(newRoot, "newRoot");
			return EdmFunctions.InvokeCanonicalFunction("GetReparentedValue", new DbExpression[] { hierarchyIdValue, oldRoot, newRoot });
		}
	}
}
