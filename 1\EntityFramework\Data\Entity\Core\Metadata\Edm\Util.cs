﻿using System;
using System.Data.Entity.Core.Mapping;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Diagnostics;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x02000502 RID: 1282
	internal static class Util
	{
		// Token: 0x06003FAC RID: 16300 RVA: 0x000D3135 File Offset: 0x000D1335
		internal static void ThrowIfReadOnly(MetadataItem item)
		{
			if (item.IsReadOnly)
			{
				throw new InvalidOperationException(Strings.OperationOnReadOnlyItem);
			}
		}

		// Token: 0x06003FAD RID: 16301 RVA: 0x000D314A File Offset: 0x000D134A
		[Conditional("DEBUG")]
		internal static void AssertItemHasIdentity(MetadataItem item, string argumentName)
		{
			Check.NotNull<MetadataItem>(item, argumentName);
		}

		// Token: 0x06003FAE RID: 16302 RVA: 0x000D3154 File Offset: 0x000D1354
		internal static ObjectTypeMapping GetObjectMapping(EdmType type, MetadataWorkspace workspace)
		{
			ItemCollection itemCollection;
			if (workspace.TryGetItemCollection(DataSpace.CSpace, out itemCollection))
			{
				return (ObjectTypeMapping)workspace.GetMap(type, DataSpace.OCSpace);
			}
			EdmType edmType;
			EdmType edmType2;
			if (type.DataSpace == DataSpace.CSpace)
			{
				if (Helper.IsPrimitiveType(type))
				{
					edmType = workspace.GetMappedPrimitiveType(((PrimitiveType)type).PrimitiveTypeKind, DataSpace.OSpace);
				}
				else
				{
					edmType = workspace.GetItem<EdmType>(type.FullName, DataSpace.OSpace);
				}
				edmType2 = type;
			}
			else
			{
				edmType = type;
				edmType2 = type;
			}
			if (!Helper.IsPrimitiveType(edmType) && !Helper.IsEntityType(edmType) && !Helper.IsComplexType(edmType))
			{
				throw new NotSupportedException(Strings.Materializer_UnsupportedType);
			}
			ObjectTypeMapping objectTypeMapping;
			if (Helper.IsPrimitiveType(edmType))
			{
				objectTypeMapping = new ObjectTypeMapping(edmType, edmType2);
			}
			else
			{
				objectTypeMapping = DefaultObjectMappingItemCollection.LoadObjectMapping(edmType2, edmType, null);
			}
			return objectTypeMapping;
		}
	}
}
