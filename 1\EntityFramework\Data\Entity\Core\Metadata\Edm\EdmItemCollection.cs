﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Core.Objects.ELinq;
using System.Data.Entity.Core.SchemaObjectModel;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Xml;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004A6 RID: 1190
	public sealed class EdmItemCollection : ItemCollection
	{
		// Token: 0x06003A99 RID: 15001 RVA: 0x000C07DA File Offset: 0x000BE9DA
		internal EdmItemCollection(IEnumerable<XmlReader> xmlReaders, IEnumerable<string> filePaths, bool skipInitialization = false)
			: base(DataSpace.CSpace)
		{
			if (!skipInitialization)
			{
				this.Init(xmlReaders, filePaths, true);
			}
		}

		// Token: 0x06003A9A RID: 15002 RVA: 0x000C0808 File Offset: 0x000BEA08
		public EdmItemCollection(IEnumerable<XmlReader> xmlReaders)
			: base(DataSpace.CSpace)
		{
			Check.NotNull<IEnumerable<XmlReader>>(xmlReaders, "xmlReaders");
			EntityUtil.CheckArgumentContainsNull<XmlReader>(ref xmlReaders, "xmlReaders");
			MetadataArtifactLoader metadataArtifactLoader = MetadataArtifactLoader.CreateCompositeFromXmlReaders(xmlReaders);
			this.Init(metadataArtifactLoader.GetReaders(), metadataArtifactLoader.GetPaths(), true);
		}

		// Token: 0x06003A9B RID: 15003 RVA: 0x000C0868 File Offset: 0x000BEA68
		public EdmItemCollection(EdmModel model)
			: base(DataSpace.CSpace)
		{
			Check.NotNull<EdmModel>(model, "model");
			this.Init();
			this._edmVersion = model.SchemaVersion;
			model.Validate();
			foreach (GlobalItem globalItem in model.GlobalItems)
			{
				globalItem.SetReadOnly();
				base.AddInternal(globalItem);
			}
		}

		// Token: 0x06003A9C RID: 15004 RVA: 0x000C08FC File Offset: 0x000BEAFC
		public EdmItemCollection(params string[] filePaths)
			: base(DataSpace.CSpace)
		{
			Check.NotNull<string[]>(filePaths, "filePaths");
			List<XmlReader> list = null;
			try
			{
				MetadataArtifactLoader metadataArtifactLoader = MetadataArtifactLoader.CreateCompositeFromFilePaths(filePaths, ".csdl");
				list = metadataArtifactLoader.CreateReaders(DataSpace.CSpace);
				this.Init(list, metadataArtifactLoader.GetPaths(DataSpace.CSpace), true);
			}
			finally
			{
				if (list != null)
				{
					Helper.DisposeXmlReaders(list);
				}
			}
		}

		// Token: 0x06003A9D RID: 15005 RVA: 0x000C0978 File Offset: 0x000BEB78
		private EdmItemCollection(IEnumerable<XmlReader> xmlReaders, ReadOnlyCollection<string> filePaths, out IList<EdmSchemaError> errors)
			: base(DataSpace.CSpace)
		{
			errors = this.Init(xmlReaders, filePaths, false);
		}

		// Token: 0x06003A9E RID: 15006 RVA: 0x000C09A2 File Offset: 0x000BEBA2
		private void Init()
		{
			this.LoadEdmPrimitiveTypesAndFunctions();
		}

		// Token: 0x06003A9F RID: 15007 RVA: 0x000C09AA File Offset: 0x000BEBAA
		private IList<EdmSchemaError> Init(IEnumerable<XmlReader> xmlReaders, IEnumerable<string> filePaths, bool throwOnError)
		{
			this.Init();
			return EdmItemCollection.LoadItems(xmlReaders, filePaths, SchemaDataModelOption.EntityDataModel, MetadataItem.EdmProviderManifest, this, throwOnError);
		}

		// Token: 0x17000B4D RID: 2893
		// (get) Token: 0x06003AA0 RID: 15008 RVA: 0x000C09C1 File Offset: 0x000BEBC1
		// (set) Token: 0x06003AA1 RID: 15009 RVA: 0x000C09C9 File Offset: 0x000BEBC9
		public double EdmVersion
		{
			get
			{
				return this._edmVersion;
			}
			internal set
			{
				this._edmVersion = value;
			}
		}

		// Token: 0x17000B4E RID: 2894
		// (get) Token: 0x06003AA2 RID: 15010 RVA: 0x000C09D2 File Offset: 0x000BEBD2
		internal OcAssemblyCache ConventionalOcCache
		{
			get
			{
				return this._conventionalOcCache;
			}
		}

		// Token: 0x06003AA3 RID: 15011 RVA: 0x000C09DC File Offset: 0x000BEBDC
		internal InitializerMetadata GetCanonicalInitializerMetadata(InitializerMetadata metadata)
		{
			if (this._getCanonicalInitializerMetadataMemoizer == null)
			{
				Interlocked.CompareExchange<Memoizer<InitializerMetadata, InitializerMetadata>>(ref this._getCanonicalInitializerMetadataMemoizer, new Memoizer<InitializerMetadata, InitializerMetadata>((InitializerMetadata m) => m, EqualityComparer<InitializerMetadata>.Default), null);
			}
			return this._getCanonicalInitializerMetadataMemoizer.Evaluate(metadata);
		}

		// Token: 0x06003AA4 RID: 15012 RVA: 0x000C0A34 File Offset: 0x000BEC34
		internal static bool IsSystemNamespace(DbProviderManifest manifest, string namespaceName)
		{
			if (manifest == MetadataItem.EdmProviderManifest)
			{
				return namespaceName == "Transient" || namespaceName == "Edm" || namespaceName == "System";
			}
			return namespaceName == "Transient" || namespaceName == "Edm" || namespaceName == "System" || (manifest != null && namespaceName == manifest.NamespaceName);
		}

		// Token: 0x06003AA5 RID: 15013 RVA: 0x000C0AAC File Offset: 0x000BECAC
		internal static IList<EdmSchemaError> LoadItems(IEnumerable<XmlReader> xmlReaders, IEnumerable<string> sourceFilePaths, SchemaDataModelOption dataModelOption, DbProviderManifest providerManifest, ItemCollection itemCollection, bool throwOnError)
		{
			IList<Schema> list = null;
			IList<EdmSchemaError> list2 = SchemaManager.ParseAndValidate(xmlReaders, sourceFilePaths, dataModelOption, providerManifest, out list);
			if (MetadataHelper.CheckIfAllErrorsAreWarnings(list2))
			{
				foreach (EdmSchemaError edmSchemaError in EdmItemCollection.LoadItems(providerManifest, list, itemCollection))
				{
					list2.Add(edmSchemaError);
				}
			}
			if (!MetadataHelper.CheckIfAllErrorsAreWarnings(list2) && throwOnError)
			{
				throw EntityUtil.InvalidSchemaEncountered(Helper.CombineErrorMessage(list2));
			}
			return list2;
		}

		// Token: 0x06003AA6 RID: 15014 RVA: 0x000C0B34 File Offset: 0x000BED34
		internal static List<EdmSchemaError> LoadItems(DbProviderManifest manifest, IList<Schema> somSchemas, ItemCollection itemCollection)
		{
			List<EdmSchemaError> list = new List<EdmSchemaError>();
			IEnumerable<GlobalItem> enumerable = EdmItemCollection.LoadSomSchema(somSchemas, manifest, itemCollection);
			List<string> list2 = new List<string>();
			foreach (GlobalItem globalItem in enumerable)
			{
				if (globalItem.BuiltInTypeKind == BuiltInTypeKind.EdmFunction && globalItem.DataSpace == DataSpace.SSpace)
				{
					EdmFunction edmFunction = (EdmFunction)globalItem;
					StringBuilder stringBuilder = new StringBuilder();
					EdmFunction.BuildIdentity<FunctionParameter>(stringBuilder, edmFunction.FullName, edmFunction.Parameters, (FunctionParameter param) => MetadataHelper.ConvertStoreTypeUsageToEdmTypeUsage(param.TypeUsage), (FunctionParameter param) => param.Mode);
					string text = stringBuilder.ToString();
					if (list2.Contains(text))
					{
						list.Add(new EdmSchemaError(Strings.DuplicatedFunctionoverloads(edmFunction.FullName, text.Substring(edmFunction.FullName.Length)).Trim(), 174, EdmSchemaErrorSeverity.Error));
						continue;
					}
					list2.Add(text);
				}
				globalItem.SetReadOnly();
				itemCollection.AddInternal(globalItem);
			}
			return list;
		}

		// Token: 0x06003AA7 RID: 15015 RVA: 0x000C0C64 File Offset: 0x000BEE64
		internal static IEnumerable<GlobalItem> LoadSomSchema(IList<Schema> somSchemas, DbProviderManifest providerManifest, ItemCollection itemCollection)
		{
			return Converter.ConvertSchema(somSchemas, providerManifest, itemCollection);
		}

		// Token: 0x06003AA8 RID: 15016 RVA: 0x000C0C6E File Offset: 0x000BEE6E
		public ReadOnlyCollection<PrimitiveType> GetPrimitiveTypes()
		{
			return this._primitiveTypeMaps.GetTypes();
		}

		// Token: 0x06003AA9 RID: 15017 RVA: 0x000C0C7C File Offset: 0x000BEE7C
		public ReadOnlyCollection<PrimitiveType> GetPrimitiveTypes(double edmVersion)
		{
			if (edmVersion == 1.0 || edmVersion == 1.1 || edmVersion == 2.0)
			{
				return new ReadOnlyCollection<PrimitiveType>((from type in this._primitiveTypeMaps.GetTypes()
					where !Helper.IsSpatialType(type) && !Helper.IsHierarchyIdType(type)
					select type).ToList<PrimitiveType>());
			}
			if (edmVersion == 3.0)
			{
				return this._primitiveTypeMaps.GetTypes();
			}
			throw new ArgumentException(Strings.InvalidEDMVersion(edmVersion.ToString(CultureInfo.CurrentCulture)));
		}

		// Token: 0x06003AAA RID: 15018 RVA: 0x000C0D18 File Offset: 0x000BEF18
		internal override PrimitiveType GetMappedPrimitiveType(PrimitiveTypeKind primitiveTypeKind)
		{
			PrimitiveType primitiveType = null;
			this._primitiveTypeMaps.TryGetType(primitiveTypeKind, null, out primitiveType);
			return primitiveType;
		}

		// Token: 0x06003AAB RID: 15019 RVA: 0x000C0D38 File Offset: 0x000BEF38
		private void LoadEdmPrimitiveTypesAndFunctions()
		{
			EdmProviderManifest instance = EdmProviderManifest.Instance;
			ReadOnlyCollection<PrimitiveType> storeTypes = instance.GetStoreTypes();
			for (int i = 0; i < storeTypes.Count; i++)
			{
				base.AddInternal(storeTypes[i]);
				this._primitiveTypeMaps.Add(storeTypes[i]);
			}
			ReadOnlyCollection<EdmFunction> storeFunctions = instance.GetStoreFunctions();
			for (int j = 0; j < storeFunctions.Count; j++)
			{
				base.AddInternal(storeFunctions[j]);
			}
		}

		// Token: 0x06003AAC RID: 15020 RVA: 0x000C0DAC File Offset: 0x000BEFAC
		internal DbLambda GetGeneratedFunctionDefinition(EdmFunction function)
		{
			if (this._getGeneratedFunctionDefinitionsMemoizer == null)
			{
				Interlocked.CompareExchange<Memoizer<EdmFunction, DbLambda>>(ref this._getGeneratedFunctionDefinitionsMemoizer, new Memoizer<EdmFunction, DbLambda>(new Func<EdmFunction, DbLambda>(this.GenerateFunctionDefinition), null), null);
			}
			return this._getGeneratedFunctionDefinitionsMemoizer.Evaluate(function);
		}

		// Token: 0x06003AAD RID: 15021 RVA: 0x000C0DE4 File Offset: 0x000BEFE4
		internal DbLambda GenerateFunctionDefinition(EdmFunction function)
		{
			if (!function.HasUserDefinedBody)
			{
				throw new InvalidOperationException(Strings.Cqt_UDF_FunctionHasNoDefinition(function.Identity));
			}
			DbLambda dbLambda = ExternalCalls.CompileFunctionDefinition(function.CommandTextAttribute, function.Parameters, this);
			if (!TypeSemantics.IsStructurallyEqual(function.ReturnParameter.TypeUsage, dbLambda.Body.ResultType))
			{
				throw new InvalidOperationException(Strings.Cqt_UDF_FunctionDefinitionResultTypeMismatch(function.ReturnParameter.TypeUsage.ToString(), function.FullName, dbLambda.Body.ResultType.ToString()));
			}
			return dbLambda;
		}

		// Token: 0x06003AAE RID: 15022 RVA: 0x000C0E6C File Offset: 0x000BF06C
		public static EdmItemCollection Create(IEnumerable<XmlReader> xmlReaders, ReadOnlyCollection<string> filePaths, out IList<EdmSchemaError> errors)
		{
			Check.NotNull<IEnumerable<XmlReader>>(xmlReaders, "xmlReaders");
			EntityUtil.CheckArgumentContainsNull<XmlReader>(ref xmlReaders, "xmlReaders");
			EdmItemCollection edmItemCollection = new EdmItemCollection(xmlReaders, filePaths, out errors);
			if (errors == null || errors.Count <= 0)
			{
				return edmItemCollection;
			}
			return null;
		}

		// Token: 0x04001427 RID: 5159
		private readonly CacheForPrimitiveTypes _primitiveTypeMaps = new CacheForPrimitiveTypes();

		// Token: 0x04001428 RID: 5160
		private double _edmVersion;

		// Token: 0x04001429 RID: 5161
		private Memoizer<InitializerMetadata, InitializerMetadata> _getCanonicalInitializerMetadataMemoizer;

		// Token: 0x0400142A RID: 5162
		private Memoizer<EdmFunction, DbLambda> _getGeneratedFunctionDefinitionsMemoizer;

		// Token: 0x0400142B RID: 5163
		private readonly OcAssemblyCache _conventionalOcCache = new OcAssemblyCache();
	}
}
