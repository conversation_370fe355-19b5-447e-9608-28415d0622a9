﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Text;
using System.Xml;
using System.Xml.XPath;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004CA RID: 1226
	internal static class Helper
	{
		// Token: 0x06003CAC RID: 15532 RVA: 0x000C8404 File Offset: 0x000C6604
		internal static string GetAttributeValue(XPathNavigator nav, string attributeName)
		{
			nav = nav.Clone();
			string text = null;
			if (nav.MoveToAttribute(attributeName, string.Empty))
			{
				text = nav.Value;
			}
			return text;
		}

		// Token: 0x06003CAD RID: 15533 RVA: 0x000C8434 File Offset: 0x000C6634
		internal static object GetTypedAttributeValue(XPathNavigator nav, string attributeName, Type clrType)
		{
			nav = nav.Clone();
			object obj = null;
			if (nav.MoveToAttribute(attributeName, string.Empty))
			{
				obj = nav.ValueAs(clrType);
			}
			return obj;
		}

		// Token: 0x06003CAE RID: 15534 RVA: 0x000C8464 File Offset: 0x000C6664
		internal static FacetDescription GetFacet(IEnumerable<FacetDescription> facetCollection, string facetName)
		{
			foreach (FacetDescription facetDescription in facetCollection)
			{
				if (facetDescription.FacetName == facetName)
				{
					return facetDescription;
				}
			}
			return null;
		}

		// Token: 0x06003CAF RID: 15535 RVA: 0x000C84BC File Offset: 0x000C66BC
		internal static bool IsAssignableFrom(EdmType firstType, EdmType secondType)
		{
			return secondType != null && (firstType.Equals(secondType) || Helper.IsSubtypeOf(secondType, firstType));
		}

		// Token: 0x06003CB0 RID: 15536 RVA: 0x000C84D8 File Offset: 0x000C66D8
		internal static bool IsSubtypeOf(EdmType firstType, EdmType secondType)
		{
			if (secondType == null)
			{
				return false;
			}
			for (EdmType edmType = firstType.BaseType; edmType != null; edmType = edmType.BaseType)
			{
				if (edmType == secondType)
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x06003CB1 RID: 15537 RVA: 0x000C8504 File Offset: 0x000C6704
		internal static IList GetAllStructuralMembers(EdmType edmType)
		{
			BuiltInTypeKind builtInTypeKind = edmType.BuiltInTypeKind;
			if (builtInTypeKind <= BuiltInTypeKind.ComplexType)
			{
				if (builtInTypeKind == BuiltInTypeKind.AssociationType)
				{
					return ((AssociationType)edmType).AssociationEndMembers;
				}
				if (builtInTypeKind == BuiltInTypeKind.ComplexType)
				{
					return ((ComplexType)edmType).Properties;
				}
			}
			else
			{
				if (builtInTypeKind == BuiltInTypeKind.EntityType)
				{
					return ((EntityType)edmType).Properties;
				}
				if (builtInTypeKind == BuiltInTypeKind.RowType)
				{
					return ((RowType)edmType).Properties;
				}
			}
			return Helper.EmptyArrayEdmProperty;
		}

		// Token: 0x06003CB2 RID: 15538 RVA: 0x000C8568 File Offset: 0x000C6768
		internal static AssociationEndMember GetEndThatShouldBeMappedToKey(AssociationType associationType)
		{
			if (associationType.AssociationEndMembers.Any((AssociationEndMember it) => it.RelationshipMultiplicity.Equals(RelationshipMultiplicity.One)))
			{
				return associationType.AssociationEndMembers.SingleOrDefault((AssociationEndMember it) => it.RelationshipMultiplicity.Equals(RelationshipMultiplicity.Many) || it.RelationshipMultiplicity.Equals(RelationshipMultiplicity.ZeroOrOne));
			}
			if (associationType.AssociationEndMembers.Any((AssociationEndMember it) => it.RelationshipMultiplicity.Equals(RelationshipMultiplicity.ZeroOrOne)))
			{
				return associationType.AssociationEndMembers.SingleOrDefault((AssociationEndMember it) => it.RelationshipMultiplicity.Equals(RelationshipMultiplicity.Many));
			}
			return null;
		}

		// Token: 0x06003CB3 RID: 15539 RVA: 0x000C8624 File Offset: 0x000C6824
		internal static string GetCommaDelimitedString(IEnumerable<string> stringList)
		{
			StringBuilder stringBuilder = new StringBuilder();
			bool flag = true;
			foreach (string text in stringList)
			{
				if (!flag)
				{
					stringBuilder.Append(", ");
				}
				else
				{
					flag = false;
				}
				stringBuilder.Append(text);
			}
			return stringBuilder.ToString();
		}

		// Token: 0x06003CB4 RID: 15540 RVA: 0x000C8690 File Offset: 0x000C6890
		internal static IEnumerable<T> Concat<T>(params IEnumerable<T>[] sources)
		{
			foreach (IEnumerable<T> enumerable in sources)
			{
				if (enumerable != null)
				{
					foreach (T t in enumerable)
					{
						yield return t;
					}
					IEnumerator<T> enumerator = null;
				}
			}
			IEnumerable<T>[] array = null;
			yield break;
			yield break;
		}

		// Token: 0x06003CB5 RID: 15541 RVA: 0x000C86A0 File Offset: 0x000C68A0
		internal static void DisposeXmlReaders(IEnumerable<XmlReader> xmlReaders)
		{
			foreach (XmlReader xmlReader in xmlReaders)
			{
				((IDisposable)xmlReader).Dispose();
			}
		}

		// Token: 0x06003CB6 RID: 15542 RVA: 0x000C86E8 File Offset: 0x000C68E8
		internal static bool IsStructuralType(EdmType type)
		{
			return Helper.IsComplexType(type) || Helper.IsEntityType(type) || Helper.IsRelationshipType(type) || Helper.IsRowType(type);
		}

		// Token: 0x06003CB7 RID: 15543 RVA: 0x000C870A File Offset: 0x000C690A
		internal static bool IsCollectionType(GlobalItem item)
		{
			return BuiltInTypeKind.CollectionType == item.BuiltInTypeKind;
		}

		// Token: 0x06003CB8 RID: 15544 RVA: 0x000C8715 File Offset: 0x000C6915
		internal static bool IsEntityType(EdmType type)
		{
			return BuiltInTypeKind.EntityType == type.BuiltInTypeKind;
		}

		// Token: 0x06003CB9 RID: 15545 RVA: 0x000C8721 File Offset: 0x000C6921
		internal static bool IsComplexType(EdmType type)
		{
			return BuiltInTypeKind.ComplexType == type.BuiltInTypeKind;
		}

		// Token: 0x06003CBA RID: 15546 RVA: 0x000C872C File Offset: 0x000C692C
		internal static bool IsPrimitiveType(EdmType type)
		{
			return BuiltInTypeKind.PrimitiveType == type.BuiltInTypeKind;
		}

		// Token: 0x06003CBB RID: 15547 RVA: 0x000C8738 File Offset: 0x000C6938
		internal static bool IsRefType(GlobalItem item)
		{
			return BuiltInTypeKind.RefType == item.BuiltInTypeKind;
		}

		// Token: 0x06003CBC RID: 15548 RVA: 0x000C8744 File Offset: 0x000C6944
		internal static bool IsRowType(GlobalItem item)
		{
			return BuiltInTypeKind.RowType == item.BuiltInTypeKind;
		}

		// Token: 0x06003CBD RID: 15549 RVA: 0x000C8750 File Offset: 0x000C6950
		internal static bool IsAssociationType(EdmType type)
		{
			return BuiltInTypeKind.AssociationType == type.BuiltInTypeKind;
		}

		// Token: 0x06003CBE RID: 15550 RVA: 0x000C875B File Offset: 0x000C695B
		internal static bool IsRelationshipType(EdmType type)
		{
			return BuiltInTypeKind.AssociationType == type.BuiltInTypeKind;
		}

		// Token: 0x06003CBF RID: 15551 RVA: 0x000C8766 File Offset: 0x000C6966
		internal static bool IsEdmProperty(EdmMember member)
		{
			return BuiltInTypeKind.EdmProperty == member.BuiltInTypeKind;
		}

		// Token: 0x06003CC0 RID: 15552 RVA: 0x000C8772 File Offset: 0x000C6972
		internal static bool IsRelationshipEndMember(EdmMember member)
		{
			return member.BuiltInTypeKind == BuiltInTypeKind.AssociationEndMember;
		}

		// Token: 0x06003CC1 RID: 15553 RVA: 0x000C877D File Offset: 0x000C697D
		internal static bool IsAssociationEndMember(EdmMember member)
		{
			return member.BuiltInTypeKind == BuiltInTypeKind.AssociationEndMember;
		}

		// Token: 0x06003CC2 RID: 15554 RVA: 0x000C8788 File Offset: 0x000C6988
		internal static bool IsNavigationProperty(EdmMember member)
		{
			return BuiltInTypeKind.NavigationProperty == member.BuiltInTypeKind;
		}

		// Token: 0x06003CC3 RID: 15555 RVA: 0x000C8794 File Offset: 0x000C6994
		internal static bool IsEntityTypeBase(EdmType edmType)
		{
			return Helper.IsEntityType(edmType) || Helper.IsRelationshipType(edmType);
		}

		// Token: 0x06003CC4 RID: 15556 RVA: 0x000C87A6 File Offset: 0x000C69A6
		internal static bool IsTransientType(EdmType edmType)
		{
			return Helper.IsCollectionType(edmType) || Helper.IsRefType(edmType) || Helper.IsRowType(edmType);
		}

		// Token: 0x06003CC5 RID: 15557 RVA: 0x000C87C0 File Offset: 0x000C69C0
		internal static bool IsAssociationSet(EntitySetBase entitySetBase)
		{
			return BuiltInTypeKind.AssociationSet == entitySetBase.BuiltInTypeKind;
		}

		// Token: 0x06003CC6 RID: 15558 RVA: 0x000C87CB File Offset: 0x000C69CB
		internal static bool IsEntitySet(EntitySetBase entitySetBase)
		{
			return BuiltInTypeKind.EntitySet == entitySetBase.BuiltInTypeKind;
		}

		// Token: 0x06003CC7 RID: 15559 RVA: 0x000C87D7 File Offset: 0x000C69D7
		internal static bool IsRelationshipSet(EntitySetBase entitySetBase)
		{
			return BuiltInTypeKind.AssociationSet == entitySetBase.BuiltInTypeKind;
		}

		// Token: 0x06003CC8 RID: 15560 RVA: 0x000C87E2 File Offset: 0x000C69E2
		internal static bool IsEntityContainer(GlobalItem item)
		{
			return BuiltInTypeKind.EntityContainer == item.BuiltInTypeKind;
		}

		// Token: 0x06003CC9 RID: 15561 RVA: 0x000C87EE File Offset: 0x000C69EE
		internal static bool IsEdmFunction(GlobalItem item)
		{
			return BuiltInTypeKind.EdmFunction == item.BuiltInTypeKind;
		}

		// Token: 0x06003CCA RID: 15562 RVA: 0x000C87FA File Offset: 0x000C69FA
		internal static string GetFileNameFromUri(Uri uri)
		{
			Check.NotNull<Uri>(uri, "uri");
			if (uri.IsFile)
			{
				return uri.LocalPath;
			}
			if (uri.IsAbsoluteUri)
			{
				return uri.AbsolutePath;
			}
			throw new ArgumentException(Strings.UnacceptableUri(uri), "uri");
		}

		// Token: 0x06003CCB RID: 15563 RVA: 0x000C8836 File Offset: 0x000C6A36
		internal static bool IsEnumType(EdmType edmType)
		{
			return BuiltInTypeKind.EnumType == edmType.BuiltInTypeKind;
		}

		// Token: 0x06003CCC RID: 15564 RVA: 0x000C8842 File Offset: 0x000C6A42
		internal static bool IsUnboundedFacetValue(Facet facet)
		{
			return facet.Value == EdmConstants.UnboundedValue;
		}

		// Token: 0x06003CCD RID: 15565 RVA: 0x000C8851 File Offset: 0x000C6A51
		internal static bool IsVariableFacetValue(Facet facet)
		{
			return facet.Value == EdmConstants.VariableValue;
		}

		// Token: 0x06003CCE RID: 15566 RVA: 0x000C8860 File Offset: 0x000C6A60
		internal static bool IsScalarType(EdmType edmType)
		{
			return Helper.IsEnumType(edmType) || Helper.IsPrimitiveType(edmType);
		}

		// Token: 0x06003CCF RID: 15567 RVA: 0x000C8872 File Offset: 0x000C6A72
		internal static bool IsHierarchyIdType(PrimitiveType type)
		{
			return type.PrimitiveTypeKind == PrimitiveTypeKind.HierarchyId;
		}

		// Token: 0x06003CD0 RID: 15568 RVA: 0x000C887E File Offset: 0x000C6A7E
		internal static bool IsSpatialType(PrimitiveType type)
		{
			return Helper.IsGeographicType(type) || Helper.IsGeometricType(type);
		}

		// Token: 0x06003CD1 RID: 15569 RVA: 0x000C8890 File Offset: 0x000C6A90
		internal static bool IsSpatialType(EdmType type, out bool isGeographic)
		{
			PrimitiveType primitiveType = type as PrimitiveType;
			if (primitiveType == null)
			{
				isGeographic = false;
				return false;
			}
			isGeographic = Helper.IsGeographicType(primitiveType);
			return isGeographic || Helper.IsGeometricType(primitiveType);
		}

		// Token: 0x06003CD2 RID: 15570 RVA: 0x000C88C0 File Offset: 0x000C6AC0
		internal static bool IsGeographicType(PrimitiveType type)
		{
			return Helper.IsGeographicTypeKind(type.PrimitiveTypeKind);
		}

		// Token: 0x06003CD3 RID: 15571 RVA: 0x000C88CD File Offset: 0x000C6ACD
		internal static bool AreSameSpatialUnionType(PrimitiveType firstType, PrimitiveType secondType)
		{
			return (Helper.IsGeographicTypeKind(firstType.PrimitiveTypeKind) && Helper.IsGeographicTypeKind(secondType.PrimitiveTypeKind)) || (Helper.IsGeometricTypeKind(firstType.PrimitiveTypeKind) && Helper.IsGeometricTypeKind(secondType.PrimitiveTypeKind));
		}

		// Token: 0x06003CD4 RID: 15572 RVA: 0x000C8908 File Offset: 0x000C6B08
		internal static bool IsGeographicTypeKind(PrimitiveTypeKind kind)
		{
			return kind == PrimitiveTypeKind.Geography || Helper.IsStrongGeographicTypeKind(kind);
		}

		// Token: 0x06003CD5 RID: 15573 RVA: 0x000C8917 File Offset: 0x000C6B17
		internal static bool IsGeometricType(PrimitiveType type)
		{
			return Helper.IsGeometricTypeKind(type.PrimitiveTypeKind);
		}

		// Token: 0x06003CD6 RID: 15574 RVA: 0x000C8924 File Offset: 0x000C6B24
		internal static bool IsGeometricTypeKind(PrimitiveTypeKind kind)
		{
			return kind == PrimitiveTypeKind.Geometry || Helper.IsStrongGeometricTypeKind(kind);
		}

		// Token: 0x06003CD7 RID: 15575 RVA: 0x000C8933 File Offset: 0x000C6B33
		internal static bool IsStrongSpatialTypeKind(PrimitiveTypeKind kind)
		{
			return Helper.IsStrongGeometricTypeKind(kind) || Helper.IsStrongGeographicTypeKind(kind);
		}

		// Token: 0x06003CD8 RID: 15576 RVA: 0x000C8945 File Offset: 0x000C6B45
		private static bool IsStrongGeometricTypeKind(PrimitiveTypeKind kind)
		{
			return kind >= PrimitiveTypeKind.GeometryPoint && kind <= PrimitiveTypeKind.GeometryCollection;
		}

		// Token: 0x06003CD9 RID: 15577 RVA: 0x000C8956 File Offset: 0x000C6B56
		private static bool IsStrongGeographicTypeKind(PrimitiveTypeKind kind)
		{
			return kind >= PrimitiveTypeKind.GeographyPoint && kind <= PrimitiveTypeKind.GeographyCollection;
		}

		// Token: 0x06003CDA RID: 15578 RVA: 0x000C8967 File Offset: 0x000C6B67
		internal static bool IsHierarchyIdType(TypeUsage type)
		{
			return type.EdmType.BuiltInTypeKind == BuiltInTypeKind.PrimitiveType && ((PrimitiveType)type.EdmType).PrimitiveTypeKind == PrimitiveTypeKind.HierarchyId;
		}

		// Token: 0x06003CDB RID: 15579 RVA: 0x000C898E File Offset: 0x000C6B8E
		internal static bool IsSpatialType(TypeUsage type)
		{
			return type.EdmType.BuiltInTypeKind == BuiltInTypeKind.PrimitiveType && Helper.IsSpatialType((PrimitiveType)type.EdmType);
		}

		// Token: 0x06003CDC RID: 15580 RVA: 0x000C89B4 File Offset: 0x000C6BB4
		internal static bool IsSpatialType(TypeUsage type, out PrimitiveTypeKind spatialType)
		{
			if (type.EdmType.BuiltInTypeKind == BuiltInTypeKind.PrimitiveType)
			{
				PrimitiveType primitiveType = (PrimitiveType)type.EdmType;
				if (Helper.IsGeographicTypeKind(primitiveType.PrimitiveTypeKind) || Helper.IsGeometricTypeKind(primitiveType.PrimitiveTypeKind))
				{
					spatialType = primitiveType.PrimitiveTypeKind;
					return true;
				}
			}
			spatialType = PrimitiveTypeKind.Binary;
			return false;
		}

		// Token: 0x06003CDD RID: 15581 RVA: 0x000C8A04 File Offset: 0x000C6C04
		internal static string ToString(ParameterDirection value)
		{
			switch (value)
			{
			case ParameterDirection.Input:
				return "Input";
			case ParameterDirection.Output:
				return "Output";
			case ParameterDirection.InputOutput:
				return "InputOutput";
			case ParameterDirection.ReturnValue:
				return "ReturnValue";
			}
			return value.ToString();
		}

		// Token: 0x06003CDE RID: 15582 RVA: 0x000C8A58 File Offset: 0x000C6C58
		internal static string ToString(ParameterMode value)
		{
			switch (value)
			{
			case ParameterMode.In:
				return "In";
			case ParameterMode.Out:
				return "Out";
			case ParameterMode.InOut:
				return "InOut";
			case ParameterMode.ReturnValue:
				return "ReturnValue";
			default:
				return value.ToString();
			}
		}

		// Token: 0x06003CDF RID: 15583 RVA: 0x000C8A97 File Offset: 0x000C6C97
		internal static bool IsSupportedEnumUnderlyingType(PrimitiveTypeKind typeKind)
		{
			return typeKind == PrimitiveTypeKind.Byte || typeKind == PrimitiveTypeKind.SByte || typeKind == PrimitiveTypeKind.Int16 || typeKind == PrimitiveTypeKind.Int32 || typeKind == PrimitiveTypeKind.Int64;
		}

		// Token: 0x06003CE0 RID: 15584 RVA: 0x000C8AB2 File Offset: 0x000C6CB2
		internal static bool IsEnumMemberValueInRange(PrimitiveTypeKind underlyingTypeKind, long value)
		{
			return value >= Helper._enumUnderlyingTypeRanges[underlyingTypeKind][0] && value <= Helper._enumUnderlyingTypeRanges[underlyingTypeKind][1];
		}

		// Token: 0x06003CE1 RID: 15585 RVA: 0x000C8AD9 File Offset: 0x000C6CD9
		internal static PrimitiveType AsPrimitive(EdmType type)
		{
			if (!Helper.IsEnumType(type))
			{
				return (PrimitiveType)type;
			}
			return Helper.GetUnderlyingEdmTypeForEnumType(type);
		}

		// Token: 0x06003CE2 RID: 15586 RVA: 0x000C8AF0 File Offset: 0x000C6CF0
		internal static PrimitiveType GetUnderlyingEdmTypeForEnumType(EdmType type)
		{
			return ((EnumType)type).UnderlyingType;
		}

		// Token: 0x06003CE3 RID: 15587 RVA: 0x000C8B00 File Offset: 0x000C6D00
		internal static PrimitiveType GetSpatialNormalizedPrimitiveType(EdmType type)
		{
			PrimitiveType primitiveType = (PrimitiveType)type;
			if (Helper.IsGeographicType(primitiveType) && primitiveType.PrimitiveTypeKind != PrimitiveTypeKind.Geography)
			{
				return PrimitiveType.GetEdmPrimitiveType(PrimitiveTypeKind.Geography);
			}
			if (Helper.IsGeometricType(primitiveType) && primitiveType.PrimitiveTypeKind != PrimitiveTypeKind.Geometry)
			{
				return PrimitiveType.GetEdmPrimitiveType(PrimitiveTypeKind.Geometry);
			}
			return primitiveType;
		}

		// Token: 0x06003CE4 RID: 15588 RVA: 0x000C8B4C File Offset: 0x000C6D4C
		internal static string CombineErrorMessage(IEnumerable<EdmSchemaError> errors)
		{
			StringBuilder stringBuilder = new StringBuilder(Environment.NewLine);
			int num = 0;
			foreach (EdmSchemaError edmSchemaError in errors)
			{
				if (num++ != 0)
				{
					stringBuilder.Append(Environment.NewLine);
				}
				stringBuilder.Append(edmSchemaError);
			}
			return stringBuilder.ToString();
		}

		// Token: 0x06003CE5 RID: 15589 RVA: 0x000C8BBC File Offset: 0x000C6DBC
		internal static string CombineErrorMessage(IEnumerable<EdmItemError> errors)
		{
			StringBuilder stringBuilder = new StringBuilder(Environment.NewLine);
			int num = 0;
			foreach (EdmItemError edmItemError in errors)
			{
				if (num++ != 0)
				{
					stringBuilder.Append(Environment.NewLine);
				}
				stringBuilder.Append(edmItemError.Message);
			}
			return stringBuilder.ToString();
		}

		// Token: 0x06003CE6 RID: 15590 RVA: 0x000C8C30 File Offset: 0x000C6E30
		internal static IEnumerable<KeyValuePair<T, S>> PairEnumerations<T, S>(IBaseList<T> left, IEnumerable<S> right)
		{
			IEnumerator leftEnumerator = left.GetEnumerator();
			IEnumerator<S> rightEnumerator = right.GetEnumerator();
			while (leftEnumerator.MoveNext() && rightEnumerator.MoveNext())
			{
				yield return new KeyValuePair<T, S>((T)((object)leftEnumerator.Current), rightEnumerator.Current);
			}
			yield break;
		}

		// Token: 0x06003CE7 RID: 15591 RVA: 0x000C8C47 File Offset: 0x000C6E47
		internal static TypeUsage GetModelTypeUsage(TypeUsage typeUsage)
		{
			return typeUsage.ModelTypeUsage;
		}

		// Token: 0x06003CE8 RID: 15592 RVA: 0x000C8C4F File Offset: 0x000C6E4F
		internal static TypeUsage GetModelTypeUsage(EdmMember member)
		{
			return Helper.GetModelTypeUsage(member.TypeUsage);
		}

		// Token: 0x06003CE9 RID: 15593 RVA: 0x000C8C5C File Offset: 0x000C6E5C
		internal static TypeUsage ValidateAndConvertTypeUsage(EdmProperty edmProperty, EdmProperty columnProperty)
		{
			return Helper.ValidateAndConvertTypeUsage(edmProperty.TypeUsage, columnProperty.TypeUsage);
		}

		// Token: 0x06003CEA RID: 15594 RVA: 0x000C8C70 File Offset: 0x000C6E70
		internal static TypeUsage ValidateAndConvertTypeUsage(TypeUsage cspaceType, TypeUsage sspaceType)
		{
			TypeUsage typeUsage = sspaceType;
			if (sspaceType.EdmType.DataSpace == DataSpace.SSpace)
			{
				typeUsage = sspaceType.ModelTypeUsage;
			}
			if (Helper.ValidateScalarTypesAreCompatible(cspaceType, typeUsage))
			{
				return typeUsage;
			}
			return null;
		}

		// Token: 0x06003CEB RID: 15595 RVA: 0x000C8CA0 File Offset: 0x000C6EA0
		private static bool ValidateScalarTypesAreCompatible(TypeUsage cspaceType, TypeUsage storeType)
		{
			if (Helper.IsEnumType(cspaceType.EdmType))
			{
				return TypeSemantics.IsSubTypeOf(TypeUsage.Create(Helper.GetUnderlyingEdmTypeForEnumType(cspaceType.EdmType)), storeType);
			}
			return TypeSemantics.IsSubTypeOf(cspaceType, storeType);
		}

		// Token: 0x040014DD RID: 5341
		internal static readonly EdmMember[] EmptyArrayEdmProperty = new EdmMember[0];

		// Token: 0x040014DE RID: 5342
		private static readonly Dictionary<PrimitiveTypeKind, long[]> _enumUnderlyingTypeRanges = new Dictionary<PrimitiveTypeKind, long[]>
		{
			{
				PrimitiveTypeKind.Byte,
				new long[] { 0L, 255L }
			},
			{
				PrimitiveTypeKind.SByte,
				new long[] { -128L, 127L }
			},
			{
				PrimitiveTypeKind.Int16,
				new long[] { -32768L, 32767L }
			},
			{
				PrimitiveTypeKind.Int32,
				new long[] { -2147483648L, 2147483647L }
			},
			{
				PrimitiveTypeKind.Int64,
				new long[] { long.MinValue, long.MaxValue }
			}
		};

		// Token: 0x040014DF RID: 5343
		internal static readonly ReadOnlyCollection<KeyValuePair<string, object>> EmptyKeyValueStringObjectList = new ReadOnlyCollection<KeyValuePair<string, object>>(new KeyValuePair<string, object>[0]);

		// Token: 0x040014E0 RID: 5344
		internal static readonly ReadOnlyCollection<string> EmptyStringList = new ReadOnlyCollection<string>(new string[0]);

		// Token: 0x040014E1 RID: 5345
		internal static readonly ReadOnlyCollection<FacetDescription> EmptyFacetDescriptionEnumerable = new ReadOnlyCollection<FacetDescription>(new FacetDescription[0]);

		// Token: 0x040014E2 RID: 5346
		internal static readonly ReadOnlyCollection<EdmFunction> EmptyEdmFunctionReadOnlyCollection = new ReadOnlyCollection<EdmFunction>(new EdmFunction[0]);

		// Token: 0x040014E3 RID: 5347
		internal static readonly ReadOnlyCollection<PrimitiveType> EmptyPrimitiveTypeReadOnlyCollection = new ReadOnlyCollection<PrimitiveType>(new PrimitiveType[0]);

		// Token: 0x040014E4 RID: 5348
		internal static readonly KeyValuePair<string, object>[] EmptyKeyValueStringObjectArray = new KeyValuePair<string, object>[0];

		// Token: 0x040014E5 RID: 5349
		internal const char PeriodSymbol = '.';

		// Token: 0x040014E6 RID: 5350
		internal const char CommaSymbol = ',';
	}
}
