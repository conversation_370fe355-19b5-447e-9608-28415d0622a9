﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;
using System.Reflection;
using System.Reflection.Emit;

namespace System.Data.Entity.Core.Objects.Internal
{
	// Token: 0x0200044C RID: 1100
	internal class LazyLoadImplementor
	{
		// Token: 0x060035A6 RID: 13734 RVA: 0x000AC087 File Offset: 0x000AA287
		public LazyLoadImplementor(EntityType ospaceEntityType)
		{
			this.CheckType(ospaceEntityType);
		}

		// Token: 0x17000A56 RID: 2646
		// (get) Token: 0x060035A7 RID: 13735 RVA: 0x000AC096 File Offset: 0x000AA296
		public IEnumerable<EdmMember> Members
		{
			get
			{
				return this._members;
			}
		}

		// Token: 0x060035A8 RID: 13736 RVA: 0x000AC0A0 File Offset: 0x000AA2A0
		private void CheckType(EntityType ospaceEntityType)
		{
			this._members = new HashSet<EdmMember>();
			foreach (EdmMember edmMember in ospaceEntityType.Members)
			{
				PropertyInfo topProperty = ospaceEntityType.ClrType.GetTopProperty(edmMember.Name);
				if (topProperty != null && EntityProxyFactory.CanProxyGetter(topProperty) && LazyLoadBehavior.IsLazyLoadCandidate(ospaceEntityType, edmMember))
				{
					this._members.Add(edmMember);
				}
			}
		}

		// Token: 0x060035A9 RID: 13737 RVA: 0x000AC130 File Offset: 0x000AA330
		public bool CanProxyMember(EdmMember member)
		{
			return this._members.Contains(member);
		}

		// Token: 0x060035AA RID: 13738 RVA: 0x000AC140 File Offset: 0x000AA340
		public virtual void Implement(TypeBuilder typeBuilder, Action<FieldBuilder, bool> registerField)
		{
			FieldBuilder fieldBuilder = typeBuilder.DefineField("_entityWrapper", typeof(object), FieldAttributes.Public);
			registerField(fieldBuilder, false);
		}

		// Token: 0x060035AB RID: 13739 RVA: 0x000AC16C File Offset: 0x000AA36C
		public bool EmitMember(TypeBuilder typeBuilder, EdmMember member, PropertyBuilder propertyBuilder, PropertyInfo baseProperty, BaseProxyImplementor baseImplementor)
		{
			if (this._members.Contains(member))
			{
				MethodInfo methodInfo = baseProperty.Getter();
				MethodAttributes methodAttributes = methodInfo.Attributes & MethodAttributes.MemberAccessMask;
				Type type = typeof(Func<, , >).MakeGenericType(new Type[]
				{
					typeBuilder,
					baseProperty.PropertyType,
					typeof(bool)
				});
				MethodInfo method = TypeBuilder.GetMethod(type, typeof(Func<, , >).GetOnlyDeclaredMethod("Invoke"));
				FieldBuilder fieldBuilder = typeBuilder.DefineField(LazyLoadImplementor.GetInterceptorFieldName(baseProperty.Name), type, FieldAttributes.Private | FieldAttributes.Static);
				MethodBuilder methodBuilder = typeBuilder.DefineMethod("get_" + baseProperty.Name, methodAttributes | (MethodAttributes.Virtual | MethodAttributes.HideBySig | MethodAttributes.SpecialName), baseProperty.PropertyType, Type.EmptyTypes);
				ILGenerator ilgenerator = methodBuilder.GetILGenerator();
				Label label = ilgenerator.DefineLabel();
				ilgenerator.DeclareLocal(baseProperty.PropertyType);
				ilgenerator.Emit(OpCodes.Ldarg_0);
				ilgenerator.Emit(OpCodes.Call, methodInfo);
				ilgenerator.Emit(OpCodes.Stloc_0);
				ilgenerator.Emit(OpCodes.Ldarg_0);
				ilgenerator.Emit(OpCodes.Ldfld, fieldBuilder);
				ilgenerator.Emit(OpCodes.Ldarg_0);
				ilgenerator.Emit(OpCodes.Ldloc_0);
				ilgenerator.Emit(OpCodes.Callvirt, method);
				ilgenerator.Emit(OpCodes.Brtrue_S, label);
				ilgenerator.Emit(OpCodes.Ldarg_0);
				ilgenerator.Emit(OpCodes.Call, methodInfo);
				ilgenerator.Emit(OpCodes.Ret);
				ilgenerator.MarkLabel(label);
				ilgenerator.Emit(OpCodes.Ldloc_0);
				ilgenerator.Emit(OpCodes.Ret);
				propertyBuilder.SetGetMethod(methodBuilder);
				baseImplementor.AddBasePropertyGetter(baseProperty);
				return true;
			}
			return false;
		}

		// Token: 0x060035AC RID: 13740 RVA: 0x000AC303 File Offset: 0x000AA503
		internal static string GetInterceptorFieldName(string memberName)
		{
			return "ef_proxy_interceptorFor" + memberName;
		}

		// Token: 0x04001160 RID: 4448
		private HashSet<EdmMember> _members;
	}
}
