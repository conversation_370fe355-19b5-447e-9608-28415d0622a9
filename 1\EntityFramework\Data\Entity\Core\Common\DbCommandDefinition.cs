﻿using System;
using System.Data.Common;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common
{
	// Token: 0x020005EA RID: 1514
	public class DbCommandDefinition
	{
		// Token: 0x06004A08 RID: 18952 RVA: 0x00105BB1 File Offset: 0x00103DB1
		protected internal DbCommandDefinition(DbCommand prototype, Func<DbCommand, DbCommand> cloneMethod)
		{
			Check.NotNull<DbCommand>(prototype, "prototype");
			Check.NotNull<Func<DbCommand, DbCommand>>(cloneMethod, "cloneMethod");
			this._prototype = prototype;
			this._cloneMethod = cloneMethod;
		}

		// Token: 0x06004A09 RID: 18953 RVA: 0x00105BDF File Offset: 0x00103DDF
		protected DbCommandDefinition()
		{
		}

		// Token: 0x06004A0A RID: 18954 RVA: 0x00105BE7 File Offset: 0x00103DE7
		public virtual DbCommand CreateCommand()
		{
			return this._cloneMethod(this._prototype);
		}

		// Token: 0x06004A0B RID: 18955 RVA: 0x00105BFC File Offset: 0x00103DFC
		internal static void PopulateParameterFromTypeUsage(DbParameter parameter, TypeUsage type, bool isOutParam)
		{
			parameter.IsNullable = TypeSemantics.IsNullable(type);
			DbType dbType;
			if (Helper.IsPrimitiveType(type.EdmType) && DbCommandDefinition.TryGetDbTypeFromPrimitiveType((PrimitiveType)type.EdmType, out dbType))
			{
				if (dbType <= DbType.Decimal)
				{
					if (dbType == DbType.Binary)
					{
						DbCommandDefinition.PopulateBinaryParameter(parameter, type, dbType, isOutParam);
						return;
					}
					if (dbType != DbType.DateTime)
					{
						if (dbType != DbType.Decimal)
						{
							goto IL_0075;
						}
						DbCommandDefinition.PopulateDecimalParameter(parameter, type, dbType);
						return;
					}
				}
				else
				{
					if (dbType == DbType.String)
					{
						DbCommandDefinition.PopulateStringParameter(parameter, type, isOutParam);
						return;
					}
					if (dbType != DbType.Time && dbType != DbType.DateTimeOffset)
					{
						goto IL_0075;
					}
				}
				DbCommandDefinition.PopulateDateTimeParameter(parameter, type, dbType);
				return;
				IL_0075:
				parameter.DbType = dbType;
			}
		}

		// Token: 0x06004A0C RID: 18956 RVA: 0x00105C88 File Offset: 0x00103E88
		internal static bool TryGetDbTypeFromPrimitiveType(PrimitiveType type, out DbType dbType)
		{
			switch (type.PrimitiveTypeKind)
			{
			case PrimitiveTypeKind.Binary:
				dbType = DbType.Binary;
				return true;
			case PrimitiveTypeKind.Boolean:
				dbType = DbType.Boolean;
				return true;
			case PrimitiveTypeKind.Byte:
				dbType = DbType.Byte;
				return true;
			case PrimitiveTypeKind.DateTime:
				dbType = DbType.DateTime;
				return true;
			case PrimitiveTypeKind.Decimal:
				dbType = DbType.Decimal;
				return true;
			case PrimitiveTypeKind.Double:
				dbType = DbType.Double;
				return true;
			case PrimitiveTypeKind.Guid:
				dbType = DbType.Guid;
				return true;
			case PrimitiveTypeKind.Single:
				dbType = DbType.Single;
				return true;
			case PrimitiveTypeKind.SByte:
				dbType = DbType.SByte;
				return true;
			case PrimitiveTypeKind.Int16:
				dbType = DbType.Int16;
				return true;
			case PrimitiveTypeKind.Int32:
				dbType = DbType.Int32;
				return true;
			case PrimitiveTypeKind.Int64:
				dbType = DbType.Int64;
				return true;
			case PrimitiveTypeKind.String:
				dbType = DbType.String;
				return true;
			case PrimitiveTypeKind.Time:
				dbType = DbType.Time;
				return true;
			case PrimitiveTypeKind.DateTimeOffset:
				dbType = DbType.DateTimeOffset;
				return true;
			default:
				dbType = DbType.AnsiString;
				return false;
			}
		}

		// Token: 0x06004A0D RID: 18957 RVA: 0x00105D38 File Offset: 0x00103F38
		private static void PopulateBinaryParameter(DbParameter parameter, TypeUsage type, DbType dbType, bool isOutParam)
		{
			parameter.DbType = dbType;
			DbCommandDefinition.SetParameterSize(parameter, type, isOutParam);
		}

		// Token: 0x06004A0E RID: 18958 RVA: 0x00105D4C File Offset: 0x00103F4C
		private static void PopulateDecimalParameter(DbParameter parameter, TypeUsage type, DbType dbType)
		{
			parameter.DbType = dbType;
			byte b;
			if (TypeHelpers.TryGetPrecision(type, out b))
			{
				((IDbDataParameter)parameter).Precision = b;
			}
			byte b2;
			if (TypeHelpers.TryGetScale(type, out b2))
			{
				((IDbDataParameter)parameter).Scale = b2;
			}
		}

		// Token: 0x06004A0F RID: 18959 RVA: 0x00105D84 File Offset: 0x00103F84
		private static void PopulateDateTimeParameter(DbParameter parameter, TypeUsage type, DbType dbType)
		{
			parameter.DbType = dbType;
			byte b;
			if (TypeHelpers.TryGetPrecision(type, out b))
			{
				((IDbDataParameter)parameter).Precision = b;
			}
		}

		// Token: 0x06004A10 RID: 18960 RVA: 0x00105DAC File Offset: 0x00103FAC
		private static void PopulateStringParameter(DbParameter parameter, TypeUsage type, bool isOutParam)
		{
			bool flag = true;
			bool flag2 = false;
			if (!TypeHelpers.TryGetIsFixedLength(type, out flag2))
			{
				flag2 = false;
			}
			if (!TypeHelpers.TryGetIsUnicode(type, out flag))
			{
				flag = true;
			}
			if (flag2)
			{
				parameter.DbType = (flag ? DbType.StringFixedLength : DbType.AnsiStringFixedLength);
			}
			else
			{
				parameter.DbType = (flag ? DbType.String : DbType.AnsiString);
			}
			DbCommandDefinition.SetParameterSize(parameter, type, isOutParam);
		}

		// Token: 0x06004A11 RID: 18961 RVA: 0x00105E00 File Offset: 0x00104000
		private static void SetParameterSize(DbParameter parameter, TypeUsage type, bool isOutParam)
		{
			Facet facet;
			if (type.Facets.TryGetValue("MaxLength", true, out facet) && facet.Value != null)
			{
				if (!Helper.IsUnboundedFacetValue(facet))
				{
					parameter.Size = (int)facet.Value;
					return;
				}
				if (isOutParam)
				{
					parameter.Size = int.MaxValue;
				}
			}
		}

		// Token: 0x04001A19 RID: 6681
		private readonly DbCommand _prototype;

		// Token: 0x04001A1A RID: 6682
		private readonly Func<DbCommand, DbCommand> _cloneMethod;
	}
}
