﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006AA RID: 1706
	public class DbCastExpression : DbUnaryExpression
	{
		// Token: 0x0600501E RID: 20510 RVA: 0x00120F3D File Offset: 0x0011F13D
		internal DbCastExpression()
		{
		}

		// Token: 0x0600501F RID: 20511 RVA: 0x00120F45 File Offset: 0x0011F145
		internal DbCastExpression(TypeUsage type, DbExpression argument)
			: base(DbExpressionKind.Cast, type, argument)
		{
		}

		// Token: 0x06005020 RID: 20512 RVA: 0x00120F50 File Offset: 0x0011F150
		public override void Accept(DbExpressionVisitor visitor)
		{
			Check.NotNull<DbExpressionVisitor>(visitor, "visitor");
			visitor.Visit(this);
		}

		// Token: 0x06005021 RID: 20513 RVA: 0x00120F65 File Offset: 0x0011F165
		public override TResultType Accept<TResultType>(DbExpressionVisitor<TResultType> visitor)
		{
			Check.NotNull<DbExpressionVisitor<TResultType>>(visitor, "visitor");
			return visitor.Visit(this);
		}
	}
}
