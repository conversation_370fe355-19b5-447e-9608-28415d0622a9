﻿using System;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003FE RID: 1022
	internal sealed class VarRefOp : ScalarOp
	{
		// Token: 0x06002F97 RID: 12183 RVA: 0x0009520F File Offset: 0x0009340F
		internal VarRefOp(Var v)
			: base(OpType.VarRef, v.Type)
		{
			this.m_var = v;
		}

		// Token: 0x06002F98 RID: 12184 RVA: 0x00095225 File Offset: 0x00093425
		private VarRefOp()
			: base(OpType.VarRef)
		{
		}

		// Token: 0x17000968 RID: 2408
		// (get) Token: 0x06002F99 RID: 12185 RVA: 0x0009522E File Offset: 0x0009342E
		internal override int Arity
		{
			get
			{
				return 0;
			}
		}

		// Token: 0x06002F9A RID: 12186 RVA: 0x00095234 File Offset: 0x00093434
		internal override bool IsEquivalent(Op other)
		{
			VarRefOp varRefOp = other as VarRefOp;
			return varRefOp != null && varRefOp.Var.Equals(this.Var);
		}

		// Token: 0x17000969 RID: 2409
		// (get) Token: 0x06002F9B RID: 12187 RVA: 0x0009525E File Offset: 0x0009345E
		internal Var Var
		{
			get
			{
				return this.m_var;
			}
		}

		// Token: 0x06002F9C RID: 12188 RVA: 0x00095266 File Offset: 0x00093466
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002F9D RID: 12189 RVA: 0x00095270 File Offset: 0x00093470
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x0400100A RID: 4106
		private readonly Var m_var;

		// Token: 0x0400100B RID: 4107
		internal static readonly VarRefOp Pattern = new VarRefOp();
	}
}
