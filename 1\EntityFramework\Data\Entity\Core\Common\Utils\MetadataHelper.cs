﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Mapping;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Linq;
using System.Security.Cryptography;

namespace System.Data.Entity.Core.Common.Utils
{
	// Token: 0x020005FA RID: 1530
	internal static class MetadataHelper
	{
		// Token: 0x06004AD0 RID: 19152 RVA: 0x001082F0 File Offset: 0x001064F0
		internal static bool TryGetFunctionImportReturnType<T>(EdmFunction functionImport, int resultSetIndex, out T returnType) where T : EdmType
		{
			T t;
			if (MetadataHelper.TryGetWrappedReturnEdmTypeFromFunctionImport<T>(functionImport, resultSetIndex, out t) && ((typeof(EntityType).Equals(typeof(T)) && t is EntityType) || (typeof(ComplexType).Equals(typeof(T)) && t is ComplexType) || (typeof(StructuralType).Equals(typeof(T)) && t is StructuralType) || (typeof(EdmType).Equals(typeof(T)) && t != null)))
			{
				returnType = t;
				return true;
			}
			returnType = default(T);
			return false;
		}

		// Token: 0x06004AD1 RID: 19153 RVA: 0x001083B8 File Offset: 0x001065B8
		private static bool TryGetWrappedReturnEdmTypeFromFunctionImport<T>(EdmFunction functionImport, int resultSetIndex, out T resultType) where T : EdmType
		{
			resultType = default(T);
			CollectionType collectionType;
			if (MetadataHelper.TryGetFunctionImportReturnCollectionType(functionImport, resultSetIndex, out collectionType))
			{
				resultType = collectionType.TypeUsage.EdmType as T;
				return true;
			}
			return false;
		}

		// Token: 0x06004AD2 RID: 19154 RVA: 0x001083F8 File Offset: 0x001065F8
		internal static bool TryGetFunctionImportReturnCollectionType(EdmFunction functionImport, int resultSetIndex, out CollectionType collectionType)
		{
			FunctionParameter returnParameter = MetadataHelper.GetReturnParameter(functionImport, resultSetIndex);
			if (returnParameter != null && returnParameter.TypeUsage.EdmType.BuiltInTypeKind == BuiltInTypeKind.CollectionType)
			{
				collectionType = (CollectionType)returnParameter.TypeUsage.EdmType;
				return true;
			}
			collectionType = null;
			return false;
		}

		// Token: 0x06004AD3 RID: 19155 RVA: 0x0010843B File Offset: 0x0010663B
		internal static FunctionParameter GetReturnParameter(EdmFunction functionImport, int resultSetIndex)
		{
			if (functionImport.ReturnParameters.Count <= resultSetIndex)
			{
				return null;
			}
			return functionImport.ReturnParameters[resultSetIndex];
		}

		// Token: 0x06004AD4 RID: 19156 RVA: 0x00108459 File Offset: 0x00106659
		internal static EdmFunction GetFunctionImport(string functionName, string defaultContainerName, MetadataWorkspace workspace, out string containerName, out string functionImportName)
		{
			CommandHelper.ParseFunctionImportCommandText(functionName, defaultContainerName, out containerName, out functionImportName);
			return CommandHelper.FindFunctionImport(workspace, containerName, functionImportName);
		}

		// Token: 0x06004AD5 RID: 19157 RVA: 0x00108470 File Offset: 0x00106670
		internal static EdmType GetAndCheckFunctionImportReturnType<TElement>(EdmFunction functionImport, int resultSetIndex, MetadataWorkspace workspace)
		{
			EdmType edmType;
			if (!MetadataHelper.TryGetFunctionImportReturnType<EdmType>(functionImport, resultSetIndex, out edmType))
			{
				throw EntityUtil.ExecuteFunctionCalledWithNonReaderFunction(functionImport);
			}
			MetadataHelper.CheckFunctionImportReturnType<TElement>(edmType, workspace);
			return edmType;
		}

		// Token: 0x06004AD6 RID: 19158 RVA: 0x00108498 File Offset: 0x00106698
		internal static void CheckFunctionImportReturnType<TElement>(EdmType expectedEdmType, MetadataWorkspace workspace)
		{
			EdmType edmType = expectedEdmType;
			bool flag;
			if (Helper.IsSpatialType(expectedEdmType, out flag))
			{
				edmType = PrimitiveType.GetEdmPrimitiveType(flag ? PrimitiveTypeKind.Geography : PrimitiveTypeKind.Geometry);
			}
			EdmType edmType2;
			if (!workspace.TryDetermineCSpaceModelType<TElement>(out edmType2) || !edmType2.EdmEquals(edmType))
			{
				throw new InvalidOperationException(Strings.ObjectContext_ExecuteFunctionTypeMismatch(typeof(TElement).FullName, expectedEdmType.FullName));
			}
		}

		// Token: 0x06004AD7 RID: 19159 RVA: 0x001084F3 File Offset: 0x001066F3
		internal static ParameterDirection ParameterModeToParameterDirection(ParameterMode mode)
		{
			switch (mode)
			{
			case ParameterMode.In:
				return ParameterDirection.Input;
			case ParameterMode.Out:
				return ParameterDirection.Output;
			case ParameterMode.InOut:
				return ParameterDirection.InputOutput;
			case ParameterMode.ReturnValue:
				return ParameterDirection.ReturnValue;
			default:
				return (ParameterDirection)0;
			}
		}

		// Token: 0x06004AD8 RID: 19160 RVA: 0x00108518 File Offset: 0x00106718
		internal static bool DoesMemberExist(StructuralType type, EdmMember member)
		{
			using (ReadOnlyMetadataCollection<EdmMember>.Enumerator enumerator = type.Members.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					if (enumerator.Current.Equals(member))
					{
						return true;
					}
				}
			}
			return false;
		}

		// Token: 0x06004AD9 RID: 19161 RVA: 0x00108574 File Offset: 0x00106774
		internal static bool IsNonRefSimpleMember(EdmMember member)
		{
			return member.TypeUsage.EdmType.BuiltInTypeKind == BuiltInTypeKind.PrimitiveType || member.TypeUsage.EdmType.BuiltInTypeKind == BuiltInTypeKind.EnumType;
		}

		// Token: 0x06004ADA RID: 19162 RVA: 0x001085A0 File Offset: 0x001067A0
		internal static bool HasDiscreteDomain(EdmType edmType)
		{
			PrimitiveType primitiveType = edmType as PrimitiveType;
			return primitiveType != null && primitiveType.PrimitiveTypeKind == PrimitiveTypeKind.Boolean;
		}

		// Token: 0x06004ADB RID: 19163 RVA: 0x001085C2 File Offset: 0x001067C2
		internal static EntityType GetEntityTypeForEnd(AssociationEndMember end)
		{
			return (EntityType)((RefType)end.TypeUsage.EdmType).ElementType;
		}

		// Token: 0x06004ADC RID: 19164 RVA: 0x001085DE File Offset: 0x001067DE
		internal static EntitySet GetEntitySetAtEnd(AssociationSet associationSet, AssociationEndMember endMember)
		{
			return associationSet.AssociationSetEnds[endMember.Name].EntitySet;
		}

		// Token: 0x06004ADD RID: 19165 RVA: 0x001085F8 File Offset: 0x001067F8
		internal static AssociationEndMember GetOtherAssociationEnd(AssociationEndMember endMember)
		{
			ReadOnlyMetadataCollection<EdmMember> members = endMember.DeclaringType.Members;
			EdmMember edmMember = members[0];
			if (endMember != edmMember)
			{
				return (AssociationEndMember)edmMember;
			}
			return (AssociationEndMember)members[1];
		}

		// Token: 0x06004ADE RID: 19166 RVA: 0x00108630 File Offset: 0x00106830
		internal static bool IsEveryOtherEndAtLeastOne(AssociationSet associationSet, AssociationEndMember member)
		{
			foreach (AssociationSetEnd associationSetEnd in associationSet.AssociationSetEnds)
			{
				AssociationEndMember correspondingAssociationEndMember = associationSetEnd.CorrespondingAssociationEndMember;
				if (!correspondingAssociationEndMember.Equals(member) && MetadataHelper.GetLowerBoundOfMultiplicity(correspondingAssociationEndMember.RelationshipMultiplicity) == 0)
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x06004ADF RID: 19167 RVA: 0x001086A0 File Offset: 0x001068A0
		internal static bool IsAssociationValidForEntityType(AssociationSetEnd toEnd, EntityType type)
		{
			return MetadataHelper.GetEntityTypeForEnd(MetadataHelper.GetOppositeEnd(toEnd).CorrespondingAssociationEndMember).IsAssignableFrom(type);
		}

		// Token: 0x06004AE0 RID: 19168 RVA: 0x001086B8 File Offset: 0x001068B8
		internal static AssociationSetEnd GetOppositeEnd(AssociationSetEnd end)
		{
			return end.ParentAssociationSet.AssociationSetEnds.Where((AssociationSetEnd e) => !e.EdmEquals(end)).Single<AssociationSetEnd>();
		}

		// Token: 0x06004AE1 RID: 19169 RVA: 0x001086F8 File Offset: 0x001068F8
		internal static bool IsComposable(EdmFunction function)
		{
			MetadataProperty metadataProperty;
			if (function.MetadataProperties.TryGetValue("IsComposableAttribute", false, out metadataProperty))
			{
				return (bool)metadataProperty.Value;
			}
			return !function.IsFunctionImport;
		}

		// Token: 0x06004AE2 RID: 19170 RVA: 0x0010872F File Offset: 0x0010692F
		internal static bool IsMemberNullable(EdmMember member)
		{
			return Helper.IsEdmProperty(member) && ((EdmProperty)member).Nullable;
		}

		// Token: 0x06004AE3 RID: 19171 RVA: 0x00108748 File Offset: 0x00106948
		internal static IEnumerable<EntitySet> GetInfluencingEntitySetsForTable(EntitySet table, MetadataWorkspace workspace)
		{
			ItemCollection itemCollection = null;
			workspace.TryGetItemCollection(DataSpace.CSSpace, out itemCollection);
			Func<MappingFragment, bool> <>9__3;
			Func<TypeMapping, bool> <>9__2;
			return (from m in MappingMetadataHelper.GetEntityContainerMap((StorageMappingItemCollection)itemCollection, table.EntityContainer).EntitySetMaps.Where(delegate(EntitySetBaseMapping map)
				{
					IEnumerable<TypeMapping> typeMappings = map.TypeMappings;
					Func<TypeMapping, bool> func;
					if ((func = <>9__2) == null)
					{
						func = (<>9__2 = delegate(TypeMapping typeMap)
						{
							IEnumerable<MappingFragment> mappingFragments = typeMap.MappingFragments;
							Func<MappingFragment, bool> func2;
							if ((func2 = <>9__3) == null)
							{
								func2 = (<>9__3 = (MappingFragment mappingFrag) => mappingFrag.TableSet.EdmEquals(table));
							}
							return mappingFragments.Any(func2);
						});
					}
					return typeMappings.Any(func);
				})
				select m.Set).Cast<EntitySet>().Distinct<EntitySet>();
		}

		// Token: 0x06004AE4 RID: 19172 RVA: 0x001087C8 File Offset: 0x001069C8
		internal static IEnumerable<EdmType> GetTypeAndSubtypesOf(EdmType type, MetadataWorkspace workspace, bool includeAbstractTypes)
		{
			return MetadataHelper.GetTypeAndSubtypesOf(type, workspace.GetItemCollection(DataSpace.CSpace), includeAbstractTypes);
		}

		// Token: 0x06004AE5 RID: 19173 RVA: 0x001087D8 File Offset: 0x001069D8
		internal static IEnumerable<EdmType> GetTypeAndSubtypesOf(EdmType type, ItemCollection itemCollection, bool includeAbstractTypes)
		{
			if (Helper.IsRefType(type))
			{
				type = ((RefType)type).ElementType;
			}
			if (includeAbstractTypes || !type.Abstract)
			{
				yield return type;
			}
			foreach (EdmType edmType in MetadataHelper.GetTypeAndSubtypesOf<EntityType>(type, itemCollection, includeAbstractTypes))
			{
				yield return edmType;
			}
			IEnumerator<EdmType> enumerator = null;
			foreach (EdmType edmType2 in MetadataHelper.GetTypeAndSubtypesOf<ComplexType>(type, itemCollection, includeAbstractTypes))
			{
				yield return edmType2;
			}
			enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x06004AE6 RID: 19174 RVA: 0x001087F6 File Offset: 0x001069F6
		private static IEnumerable<EdmType> GetTypeAndSubtypesOf<T_EdmType>(EdmType type, ItemCollection itemCollection, bool includeAbstractTypes) where T_EdmType : EdmType
		{
			T_EdmType specificType = type as T_EdmType;
			if (specificType != null)
			{
				IEnumerable<T_EdmType> items = itemCollection.GetItems<T_EdmType>();
				foreach (T_EdmType t_EdmType in items)
				{
					if (!specificType.Equals(t_EdmType) && Helper.IsSubtypeOf(t_EdmType, specificType) && (includeAbstractTypes || !t_EdmType.Abstract))
					{
						yield return t_EdmType;
					}
				}
				IEnumerator<T_EdmType> enumerator = null;
			}
			yield break;
			yield break;
		}

		// Token: 0x06004AE7 RID: 19175 RVA: 0x00108814 File Offset: 0x00106A14
		internal static IEnumerable<EdmType> GetTypeAndParentTypesOf(EdmType type, bool includeAbstractTypes)
		{
			if (Helper.IsRefType(type))
			{
				type = ((RefType)type).ElementType;
			}
			for (EdmType specificType = type; specificType != null; specificType = specificType.BaseType as EntityType)
			{
				if (includeAbstractTypes || !specificType.Abstract)
				{
					yield return specificType;
				}
			}
			yield break;
		}

		// Token: 0x06004AE8 RID: 19176 RVA: 0x0010882C File Offset: 0x00106A2C
		internal static Dictionary<EntityType, Set<EntityType>> BuildUndirectedGraphOfTypes(EdmItemCollection edmItemCollection)
		{
			Dictionary<EntityType, Set<EntityType>> dictionary = new Dictionary<EntityType, Set<EntityType>>();
			foreach (EntityType entityType in ((IEnumerable<EntityType>)edmItemCollection.GetItems<EntityType>()))
			{
				if (entityType.BaseType != null)
				{
					EntityType entityType2 = entityType.BaseType as EntityType;
					MetadataHelper.AddDirectedEdgeBetweenEntityTypes(dictionary, entityType, entityType2);
					MetadataHelper.AddDirectedEdgeBetweenEntityTypes(dictionary, entityType2, entityType);
				}
			}
			return dictionary;
		}

		// Token: 0x06004AE9 RID: 19177 RVA: 0x001088A0 File Offset: 0x00106AA0
		internal static bool IsParentOf(EntityType a, EntityType b)
		{
			for (EntityType entityType = b.BaseType as EntityType; entityType != null; entityType = entityType.BaseType as EntityType)
			{
				if (entityType.EdmEquals(a))
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x06004AEA RID: 19178 RVA: 0x001088D8 File Offset: 0x00106AD8
		private static void AddDirectedEdgeBetweenEntityTypes(Dictionary<EntityType, Set<EntityType>> graph, EntityType a, EntityType b)
		{
			Set<EntityType> set;
			if (graph.ContainsKey(a))
			{
				set = graph[a];
			}
			else
			{
				set = new Set<EntityType>();
				graph.Add(a, set);
			}
			set.Add(b);
		}

		// Token: 0x06004AEB RID: 19179 RVA: 0x00108910 File Offset: 0x00106B10
		internal static bool DoesEndKeySubsumeAssociationSetKey(AssociationSet assocSet, AssociationEndMember thisEnd, HashSet<Pair<EdmMember, EntityType>> associationkeys)
		{
			AssociationType elementType = assocSet.ElementType;
			EntityType thisEndsEntityType = (EntityType)((RefType)thisEnd.TypeUsage.EdmType).ElementType;
			HashSet<Pair<EdmMember, EntityType>> hashSet = new HashSet<Pair<EdmMember, EntityType>>(thisEndsEntityType.KeyMembers.Select((EdmMember edmMember) => new Pair<EdmMember, EntityType>(edmMember, thisEndsEntityType)));
			foreach (ReferentialConstraint referentialConstraint in elementType.ReferentialConstraints)
			{
				IEnumerable<EdmMember> enumerable;
				EntityType entityType;
				if (thisEnd.Equals(referentialConstraint.ToRole))
				{
					enumerable = Helpers.AsSuperTypeList<EdmProperty, EdmMember>(referentialConstraint.FromProperties);
					entityType = (EntityType)((RefType)referentialConstraint.FromRole.TypeUsage.EdmType).ElementType;
				}
				else
				{
					if (!thisEnd.Equals(referentialConstraint.FromRole))
					{
						continue;
					}
					enumerable = Helpers.AsSuperTypeList<EdmProperty, EdmMember>(referentialConstraint.ToProperties);
					entityType = (EntityType)((RefType)referentialConstraint.ToRole.TypeUsage.EdmType).ElementType;
				}
				foreach (EdmMember edmMember2 in enumerable)
				{
					associationkeys.Remove(new Pair<EdmMember, EntityType>(edmMember2, entityType));
				}
			}
			return associationkeys.IsSubsetOf(hashSet);
		}

		// Token: 0x06004AEC RID: 19180 RVA: 0x00108A78 File Offset: 0x00106C78
		internal static bool DoesEndFormKey(AssociationSet associationSet, AssociationEndMember end)
		{
			foreach (EdmMember edmMember in associationSet.ElementType.Members)
			{
				AssociationEndMember associationEndMember = (AssociationEndMember)edmMember;
				if (!associationEndMember.Equals(end) && associationEndMember.RelationshipMultiplicity == RelationshipMultiplicity.Many)
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x06004AED RID: 19181 RVA: 0x00108AE8 File Offset: 0x00106CE8
		internal static bool IsExtentAtSomeRelationshipEnd(AssociationSet relationshipSet, EntitySetBase extent)
		{
			return Helper.IsEntitySet(extent) && MetadataHelper.GetSomeEndForEntitySet(relationshipSet, extent) != null;
		}

		// Token: 0x06004AEE RID: 19182 RVA: 0x00108B00 File Offset: 0x00106D00
		internal static AssociationEndMember GetSomeEndForEntitySet(AssociationSet associationSet, EntitySetBase entitySet)
		{
			foreach (AssociationSetEnd associationSetEnd in associationSet.AssociationSetEnds)
			{
				if (associationSetEnd.EntitySet.Equals(entitySet))
				{
					return associationSetEnd.CorrespondingAssociationEndMember;
				}
			}
			return null;
		}

		// Token: 0x06004AEF RID: 19183 RVA: 0x00108B68 File Offset: 0x00106D68
		internal static List<AssociationSet> GetAssociationsForEntitySets(EntitySet entitySet1, EntitySet entitySet2)
		{
			List<AssociationSet> list = new List<AssociationSet>();
			foreach (EntitySetBase entitySetBase in entitySet1.EntityContainer.BaseEntitySets)
			{
				if (Helper.IsRelationshipSet(entitySetBase))
				{
					AssociationSet associationSet = (AssociationSet)entitySetBase;
					if (MetadataHelper.IsExtentAtSomeRelationshipEnd(associationSet, entitySet1) && MetadataHelper.IsExtentAtSomeRelationshipEnd(associationSet, entitySet2))
					{
						list.Add(associationSet);
					}
				}
			}
			return list;
		}

		// Token: 0x06004AF0 RID: 19184 RVA: 0x00108BE8 File Offset: 0x00106DE8
		internal static List<AssociationSet> GetAssociationsForEntitySet(EntitySetBase entitySet)
		{
			List<AssociationSet> list = new List<AssociationSet>();
			foreach (EntitySetBase entitySetBase in entitySet.EntityContainer.BaseEntitySets)
			{
				if (Helper.IsRelationshipSet(entitySetBase))
				{
					AssociationSet associationSet = (AssociationSet)entitySetBase;
					if (MetadataHelper.IsExtentAtSomeRelationshipEnd(associationSet, entitySet))
					{
						list.Add(associationSet);
					}
				}
			}
			return list;
		}

		// Token: 0x06004AF1 RID: 19185 RVA: 0x00108C60 File Offset: 0x00106E60
		internal static bool IsSuperTypeOf(EdmType superType, EdmType subType)
		{
			for (EdmType edmType = subType; edmType != null; edmType = edmType.BaseType)
			{
				if (edmType.Equals(superType))
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x06004AF2 RID: 19186 RVA: 0x00108C87 File Offset: 0x00106E87
		internal static bool IsPartOfEntityTypeKey(EdmMember member)
		{
			return Helper.IsEntityType(member.DeclaringType) && Helper.IsEdmProperty(member) && ((EntityType)member.DeclaringType).KeyMembers.Contains(member);
		}

		// Token: 0x06004AF3 RID: 19187 RVA: 0x00108CB6 File Offset: 0x00106EB6
		internal static TypeUsage GetElementType(TypeUsage typeUsage)
		{
			if (BuiltInTypeKind.CollectionType == typeUsage.EdmType.BuiltInTypeKind)
			{
				return MetadataHelper.GetElementType(((CollectionType)typeUsage.EdmType).TypeUsage);
			}
			return typeUsage;
		}

		// Token: 0x06004AF4 RID: 19188 RVA: 0x00108CDD File Offset: 0x00106EDD
		internal static int GetLowerBoundOfMultiplicity(RelationshipMultiplicity multiplicity)
		{
			if (multiplicity == RelationshipMultiplicity.Many || multiplicity == RelationshipMultiplicity.ZeroOrOne)
			{
				return 0;
			}
			return 1;
		}

		// Token: 0x06004AF5 RID: 19189 RVA: 0x00108CEC File Offset: 0x00106EEC
		internal static int? GetUpperBoundOfMultiplicity(RelationshipMultiplicity multiplicity)
		{
			if (multiplicity == RelationshipMultiplicity.One || multiplicity == RelationshipMultiplicity.ZeroOrOne)
			{
				return new int?(1);
			}
			return null;
		}

		// Token: 0x06004AF6 RID: 19190 RVA: 0x00108D10 File Offset: 0x00106F10
		internal static Set<EdmMember> GetConcurrencyMembersForTypeHierarchy(EntityTypeBase superType, EdmItemCollection edmItemCollection)
		{
			Set<EdmMember> set = new Set<EdmMember>();
			foreach (EdmType edmType in MetadataHelper.GetTypeAndSubtypesOf(superType, edmItemCollection, true))
			{
				foreach (EdmMember edmMember in ((StructuralType)edmType).Members)
				{
					if (MetadataHelper.GetConcurrencyMode(edmMember) == ConcurrencyMode.Fixed)
					{
						set.Add(edmMember);
					}
				}
			}
			return set;
		}

		// Token: 0x06004AF7 RID: 19191 RVA: 0x00108DB0 File Offset: 0x00106FB0
		internal static ConcurrencyMode GetConcurrencyMode(EdmMember member)
		{
			return MetadataHelper.GetConcurrencyMode(member.TypeUsage);
		}

		// Token: 0x06004AF8 RID: 19192 RVA: 0x00108DC0 File Offset: 0x00106FC0
		internal static ConcurrencyMode GetConcurrencyMode(TypeUsage typeUsage)
		{
			Facet facet;
			if (typeUsage.Facets.TryGetValue("ConcurrencyMode", false, out facet) && facet.Value != null)
			{
				return (ConcurrencyMode)facet.Value;
			}
			return ConcurrencyMode.None;
		}

		// Token: 0x06004AF9 RID: 19193 RVA: 0x00108DF8 File Offset: 0x00106FF8
		internal static StoreGeneratedPattern GetStoreGeneratedPattern(EdmMember member)
		{
			Facet facet;
			if (member.TypeUsage.Facets.TryGetValue("StoreGeneratedPattern", false, out facet) && facet.Value != null)
			{
				return (StoreGeneratedPattern)facet.Value;
			}
			return StoreGeneratedPattern.None;
		}

		// Token: 0x06004AFA RID: 19194 RVA: 0x00108E34 File Offset: 0x00107034
		internal static bool CheckIfAllErrorsAreWarnings(IList<EdmSchemaError> schemaErrors)
		{
			int count = schemaErrors.Count;
			for (int i = 0; i < count; i++)
			{
				if (schemaErrors[i].Severity != EdmSchemaErrorSeverity.Warning)
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x06004AFB RID: 19195 RVA: 0x00108E68 File Offset: 0x00107068
		internal static HashAlgorithm CreateMetadataHashAlgorithm(double schemaVersion)
		{
			HashAlgorithm hashAlgorithm;
			if (schemaVersion < 2.0)
			{
				hashAlgorithm = new MD5CryptoServiceProvider();
			}
			else
			{
				hashAlgorithm = MetadataHelper.CreateSHA256HashAlgorithm();
			}
			return hashAlgorithm;
		}

		// Token: 0x06004AFC RID: 19196 RVA: 0x00108E90 File Offset: 0x00107090
		internal static SHA256 CreateSHA256HashAlgorithm()
		{
			SHA256 sha;
			try
			{
				sha = new SHA256CryptoServiceProvider();
			}
			catch (PlatformNotSupportedException)
			{
				sha = new SHA256Managed();
			}
			return sha;
		}

		// Token: 0x06004AFD RID: 19197 RVA: 0x00108EC0 File Offset: 0x001070C0
		internal static TypeUsage ConvertStoreTypeUsageToEdmTypeUsage(TypeUsage storeTypeUsage)
		{
			return storeTypeUsage.ModelTypeUsage.ShallowCopy(FacetValues.NullFacetValues);
		}

		// Token: 0x06004AFE RID: 19198 RVA: 0x00108ED2 File Offset: 0x001070D2
		internal static byte GetPrecision(this TypeUsage type)
		{
			return type.GetFacetValue("Precision");
		}

		// Token: 0x06004AFF RID: 19199 RVA: 0x00108EDF File Offset: 0x001070DF
		internal static byte GetScale(this TypeUsage type)
		{
			return type.GetFacetValue("Scale");
		}

		// Token: 0x06004B00 RID: 19200 RVA: 0x00108EEC File Offset: 0x001070EC
		internal static int GetMaxLength(this TypeUsage type)
		{
			return type.GetFacetValue("MaxLength");
		}

		// Token: 0x06004B01 RID: 19201 RVA: 0x00108EF9 File Offset: 0x001070F9
		internal static T GetFacetValue<T>(this TypeUsage type, string facetName)
		{
			return (T)((object)type.Facets[facetName].Value);
		}

		// Token: 0x06004B02 RID: 19202 RVA: 0x00108F11 File Offset: 0x00107111
		internal static NavigationPropertyAccessor GetNavigationPropertyAccessor(EntityType sourceEntityType, AssociationEndMember sourceMember, AssociationEndMember targetMember)
		{
			return MetadataHelper.GetNavigationPropertyAccessor(sourceEntityType, sourceMember.DeclaringType.FullName, sourceMember.Name, targetMember.Name);
		}

		// Token: 0x06004B03 RID: 19203 RVA: 0x00108F30 File Offset: 0x00107130
		internal static NavigationPropertyAccessor GetNavigationPropertyAccessor(EntityType entityType, string relationshipType, string fromName, string toName)
		{
			NavigationProperty navigationProperty;
			if (entityType.TryGetNavigationProperty(relationshipType, fromName, toName, out navigationProperty))
			{
				return navigationProperty.Accessor;
			}
			return NavigationPropertyAccessor.NoNavigationProperty;
		}
	}
}
