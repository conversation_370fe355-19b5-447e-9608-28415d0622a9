﻿using System;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004ED RID: 1261
	public enum PrimitiveTypeKind
	{
		// Token: 0x04001553 RID: 5459
		Binary,
		// Token: 0x04001554 RID: 5460
		Boolean,
		// Token: 0x04001555 RID: 5461
		Byte,
		// Token: 0x04001556 RID: 5462
		DateTime,
		// Token: 0x04001557 RID: 5463
		Decimal,
		// Token: 0x04001558 RID: 5464
		Double,
		// Token: 0x04001559 RID: 5465
		Guid,
		// Token: 0x0400155A RID: 5466
		Single,
		// Token: 0x0400155B RID: 5467
		SByte,
		// Token: 0x0400155C RID: 5468
		Int16,
		// Token: 0x0400155D RID: 5469
		Int32,
		// Token: 0x0400155E RID: 5470
		Int64,
		// Token: 0x0400155F RID: 5471
		String,
		// Token: 0x04001560 RID: 5472
		Time,
		// Token: 0x04001561 RID: 5473
		DateTimeOffset,
		// Token: 0x04001562 RID: 5474
		Geometry,
		// Token: 0x04001563 RID: 5475
		Geography,
		// Token: 0x04001564 RID: 5476
		GeometryPoint,
		// Token: 0x04001565 RID: 5477
		GeometryLineString,
		// Token: 0x04001566 RID: 5478
		GeometryPolygon,
		// Token: 0x04001567 RID: 5479
		GeometryMultiPoint,
		// Token: 0x04001568 RID: 5480
		GeometryMultiLineString,
		// Token: 0x04001569 RID: 5481
		GeometryMultiPolygon,
		// Token: 0x0400156A RID: 5482
		GeometryCollection,
		// Token: 0x0400156B RID: 5483
		GeographyPoint,
		// Token: 0x0400156C RID: 5484
		GeographyLineString,
		// Token: 0x0400156D RID: 5485
		GeographyPolygon,
		// Token: 0x0400156E RID: 5486
		GeographyMultiPoint,
		// Token: 0x0400156F RID: 5487
		GeographyMultiLineString,
		// Token: 0x04001570 RID: 5488
		GeographyMultiPolygon,
		// Token: 0x04001571 RID: 5489
		GeographyCollection,
		// Token: 0x04001572 RID: 5490
		HierarchyId
	}
}
