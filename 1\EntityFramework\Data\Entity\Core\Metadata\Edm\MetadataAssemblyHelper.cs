﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.SchemaObjectModel;
using System.IO;
using System.Reflection;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x02000510 RID: 1296
	internal static class MetadataAssemblyHelper
	{
		// Token: 0x06003FEA RID: 16362 RVA: 0x000D3B7C File Offset: 0x000D1D7C
		internal static Assembly SafeLoadReferencedAssembly(AssemblyName assemblyName)
		{
			Assembly assembly = null;
			try
			{
				assembly = Assembly.Load(assemblyName);
			}
			catch (FileNotFoundException)
			{
			}
			catch (FileLoadException)
			{
			}
			return assembly;
		}

		// Token: 0x06003FEB RID: 16363 RVA: 0x000D3BB8 File Offset: 0x000D1DB8
		private static bool ComputeShouldFilterAssembly(Assembly assembly)
		{
			return MetadataAssemblyHelper.ShouldFilterAssembly(new AssemblyName(assembly.FullName));
		}

		// Token: 0x06003FEC RID: 16364 RVA: 0x000D3BCA File Offset: 0x000D1DCA
		internal static bool ShouldFilterAssembly(Assembly assembly)
		{
			return MetadataAssemblyHelper._filterAssemblyCacheByAssembly.Evaluate(assembly);
		}

		// Token: 0x06003FED RID: 16365 RVA: 0x000D3BD7 File Offset: 0x000D1DD7
		private static bool ShouldFilterAssembly(AssemblyName assemblyName)
		{
			return MetadataAssemblyHelper.ArePublicKeyTokensEqual(assemblyName.GetPublicKeyToken(), MetadataAssemblyHelper._ecmaPublicKeyToken) || MetadataAssemblyHelper.ArePublicKeyTokensEqual(assemblyName.GetPublicKeyToken(), MetadataAssemblyHelper._msPublicKeyToken);
		}

		// Token: 0x06003FEE RID: 16366 RVA: 0x000D3C00 File Offset: 0x000D1E00
		private static bool ArePublicKeyTokensEqual(byte[] left, byte[] right)
		{
			if (left.Length != right.Length)
			{
				return false;
			}
			for (int i = 0; i < left.Length; i++)
			{
				if (left[i] != right[i])
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x06003FEF RID: 16367 RVA: 0x000D3C30 File Offset: 0x000D1E30
		internal static IEnumerable<Assembly> GetNonSystemReferencedAssemblies(Assembly assembly)
		{
			foreach (AssemblyName assemblyName in assembly.GetReferencedAssemblies())
			{
				if (!MetadataAssemblyHelper.ShouldFilterAssembly(assemblyName))
				{
					Assembly assembly2 = MetadataAssemblyHelper.SafeLoadReferencedAssembly(assemblyName);
					if (assembly2 != null)
					{
						yield return assembly2;
					}
				}
			}
			AssemblyName[] array = null;
			yield break;
		}

		// Token: 0x0400164D RID: 5709
		private const string EcmaPublicKey = "b77a5c561934e089";

		// Token: 0x0400164E RID: 5710
		private const string MicrosoftPublicKey = "b03f5f7f11d50a3a";

		// Token: 0x0400164F RID: 5711
		private static readonly byte[] _ecmaPublicKeyToken = ScalarType.ConvertToByteArray("b77a5c561934e089");

		// Token: 0x04001650 RID: 5712
		private static readonly byte[] _msPublicKeyToken = ScalarType.ConvertToByteArray("b03f5f7f11d50a3a");

		// Token: 0x04001651 RID: 5713
		private static readonly Memoizer<Assembly, bool> _filterAssemblyCacheByAssembly = new Memoizer<Assembly, bool>(new Func<Assembly, bool>(MetadataAssemblyHelper.ComputeShouldFilterAssembly), EqualityComparer<Assembly>.Default);
	}
}
