﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000014 RID: 20
	public class TS_Relic_DegreeTemplate
	{
		// Token: 0x1700007D RID: 125
		// (get) Token: 0x0600010C RID: 268 RVA: 0x00002952 File Offset: 0x00000B52
		// (set) Token: 0x0600010D RID: 269 RVA: 0x0000295A File Offset: 0x00000B5A
		[Key]
		public int ID { get; set; }

		// Token: 0x1700007E RID: 126
		// (get) Token: 0x0600010E RID: 270 RVA: 0x00002963 File Offset: 0x00000B63
		// (set) Token: 0x0600010F RID: 271 RVA: 0x0000296B File Offset: 0x00000B6B
		public int Quality { get; set; }

		// Token: 0x1700007F RID: 127
		// (get) Token: 0x06000110 RID: 272 RVA: 0x00002974 File Offset: 0x00000B74
		// (set) Token: 0x06000111 RID: 273 RVA: 0x0000297C File Offset: 0x00000B7C
		public int Level { get; set; }

		// Token: 0x17000080 RID: 128
		// (get) Token: 0x06000112 RID: 274 RVA: 0x00002985 File Offset: 0x00000B85
		// (set) Token: 0x06000113 RID: 275 RVA: 0x0000298D File Offset: 0x00000B8D
		public int Exp { get; set; }

		// Token: 0x17000081 RID: 129
		// (get) Token: 0x06000114 RID: 276 RVA: 0x00002996 File Offset: 0x00000B96
		// (set) Token: 0x06000115 RID: 277 RVA: 0x0000299E File Offset: 0x00000B9E
		public int Attack { get; set; }

		// Token: 0x17000082 RID: 130
		// (get) Token: 0x06000116 RID: 278 RVA: 0x000029A7 File Offset: 0x00000BA7
		// (set) Token: 0x06000117 RID: 279 RVA: 0x000029AF File Offset: 0x00000BAF
		public int Defence { get; set; }

		// Token: 0x17000083 RID: 131
		// (get) Token: 0x06000118 RID: 280 RVA: 0x000029B8 File Offset: 0x00000BB8
		// (set) Token: 0x06000119 RID: 281 RVA: 0x000029C0 File Offset: 0x00000BC0
		public int Agility { get; set; }

		// Token: 0x17000084 RID: 132
		// (get) Token: 0x0600011A RID: 282 RVA: 0x000029C9 File Offset: 0x00000BC9
		// (set) Token: 0x0600011B RID: 283 RVA: 0x000029D1 File Offset: 0x00000BD1
		public int Luck { get; set; }

		// Token: 0x17000085 RID: 133
		// (get) Token: 0x0600011C RID: 284 RVA: 0x000029DA File Offset: 0x00000BDA
		// (set) Token: 0x0600011D RID: 285 RVA: 0x000029E2 File Offset: 0x00000BE2
		public int MagicAttack { get; set; }

		// Token: 0x17000086 RID: 134
		// (get) Token: 0x0600011E RID: 286 RVA: 0x000029EB File Offset: 0x00000BEB
		// (set) Token: 0x0600011F RID: 287 RVA: 0x000029F3 File Offset: 0x00000BF3
		public int MagicDefence { get; set; }

		// Token: 0x17000087 RID: 135
		// (get) Token: 0x06000120 RID: 288 RVA: 0x000029FC File Offset: 0x00000BFC
		// (set) Token: 0x06000121 RID: 289 RVA: 0x00002A04 File Offset: 0x00000C04
		public int Damage { get; set; }

		// Token: 0x17000088 RID: 136
		// (get) Token: 0x06000122 RID: 290 RVA: 0x00002A0D File Offset: 0x00000C0D
		// (set) Token: 0x06000123 RID: 291 RVA: 0x00002A15 File Offset: 0x00000C15
		public int Blood { get; set; }
	}
}
