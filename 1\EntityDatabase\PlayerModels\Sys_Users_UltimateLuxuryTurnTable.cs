﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000030 RID: 48
	public class Sys_Users_UltimateLuxuryTurnTable
	{
		// Token: 0x17000175 RID: 373
		// (get) Token: 0x06000319 RID: 793 RVA: 0x00003ACF File Offset: 0x00001CCF
		// (set) Token: 0x0600031A RID: 794 RVA: 0x00003AD7 File Offset: 0x00001CD7
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000176 RID: 374
		// (get) Token: 0x0600031B RID: 795 RVA: 0x00003AE0 File Offset: 0x00001CE0
		// (set) Token: 0x0600031C RID: 796 RVA: 0x00003AE8 File Offset: 0x00001CE8
		public int UserID { get; set; }

		// Token: 0x17000177 RID: 375
		// (get) Token: 0x0600031D RID: 797 RVA: 0x00003AF1 File Offset: 0x00001CF1
		// (set) Token: 0x0600031E RID: 798 RVA: 0x00003AF9 File Offset: 0x00001CF9
		public int MyKeyCount { get; set; }

		// Token: 0x17000178 RID: 376
		// (get) Token: 0x0600031F RID: 799 RVA: 0x00003B02 File Offset: 0x00001D02
		// (set) Token: 0x06000320 RID: 800 RVA: 0x00003B0A File Offset: 0x00001D0A
		public int TurnIndex { get; set; }

		// Token: 0x17000179 RID: 377
		// (get) Token: 0x06000321 RID: 801 RVA: 0x00003B13 File Offset: 0x00001D13
		// (set) Token: 0x06000322 RID: 802 RVA: 0x00003B1B File Offset: 0x00001D1B
		public string HasGetArr { get; set; }
	}
}
