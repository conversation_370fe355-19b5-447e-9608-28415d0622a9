﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;
using System.Reflection;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x02000518 RID: 1304
	internal abstract class OSpaceTypeFactory
	{
		// Token: 0x17000C94 RID: 3220
		// (get) Token: 0x06004042 RID: 16450
		public abstract List<Action> ReferenceResolutions { get; }

		// Token: 0x06004043 RID: 16451
		public abstract void LogLoadMessage(string message, EdmType relatedType);

		// Token: 0x06004044 RID: 16452
		public abstract void LogError(string errorMessage, EdmType relatedType);

		// Token: 0x06004045 RID: 16453
		public abstract void TrackClosure(Type type);

		// Token: 0x17000C95 RID: 3221
		// (get) Token: 0x06004046 RID: 16454
		public abstract Dictionary<EdmType, EdmType> CspaceToOspace { get; }

		// Token: 0x17000C96 RID: 3222
		// (get) Token: 0x06004047 RID: 16455
		public abstract Dictionary<string, EdmType> LoadedTypes { get; }

		// Token: 0x06004048 RID: 16456
		public abstract void AddToTypesInAssembly(EdmType type);

		// Token: 0x06004049 RID: 16457 RVA: 0x000D57A8 File Offset: 0x000D39A8
		public virtual EdmType TryCreateType(Type type, EdmType cspaceType)
		{
			if (Helper.IsEnumType(cspaceType) ^ type.IsEnum())
			{
				this.LogLoadMessage(Strings.Validator_OSpace_Convention_SSpaceOSpaceTypeMismatch(cspaceType.FullName, cspaceType.FullName), cspaceType);
				return null;
			}
			EdmType edmType;
			if (Helper.IsEnumType(cspaceType))
			{
				this.TryCreateEnumType(type, (EnumType)cspaceType, out edmType);
				return edmType;
			}
			this.TryCreateStructuralType(type, (StructuralType)cspaceType, out edmType);
			return edmType;
		}

		// Token: 0x0600404A RID: 16458 RVA: 0x000D580C File Offset: 0x000D3A0C
		private bool TryCreateEnumType(Type enumType, EnumType cspaceEnumType, out EdmType newOSpaceType)
		{
			newOSpaceType = null;
			if (!this.UnderlyingEnumTypesMatch(enumType, cspaceEnumType) || !this.EnumMembersMatch(enumType, cspaceEnumType))
			{
				return false;
			}
			newOSpaceType = new ClrEnumType(enumType, cspaceEnumType.NamespaceName, cspaceEnumType.Name);
			this.LoadedTypes.Add(enumType.FullName, newOSpaceType);
			return true;
		}

		// Token: 0x0600404B RID: 16459 RVA: 0x000D585C File Offset: 0x000D3A5C
		private bool TryCreateStructuralType(Type type, StructuralType cspaceType, out EdmType newOSpaceType)
		{
			List<Action> list = new List<Action>();
			newOSpaceType = null;
			StructuralType ospaceType;
			if (Helper.IsEntityType(cspaceType))
			{
				ospaceType = new ClrEntityType(type, cspaceType.NamespaceName, cspaceType.Name);
			}
			else
			{
				ospaceType = new ClrComplexType(type, cspaceType.NamespaceName, cspaceType.Name);
			}
			if (cspaceType.BaseType != null)
			{
				if (!OSpaceTypeFactory.TypesMatchByConvention(type.BaseType(), cspaceType.BaseType))
				{
					string text = Strings.Validator_OSpace_Convention_BaseTypeIncompatible(type.BaseType().FullName, type.FullName, cspaceType.BaseType.FullName);
					this.LogLoadMessage(text, cspaceType);
					return false;
				}
				this.TrackClosure(type.BaseType());
				list.Add(delegate
				{
					ospaceType.BaseType = this.ResolveBaseType((StructuralType)cspaceType.BaseType, type);
				});
			}
			if (!this.TryCreateMembers(type, cspaceType, ospaceType, list))
			{
				return false;
			}
			this.LoadedTypes.Add(type.FullName, ospaceType);
			foreach (Action action in list)
			{
				this.ReferenceResolutions.Add(action);
			}
			newOSpaceType = ospaceType;
			return true;
		}

		// Token: 0x0600404C RID: 16460 RVA: 0x000D5A08 File Offset: 0x000D3C08
		internal static bool TypesMatchByConvention(Type type, EdmType cspaceType)
		{
			return type.Name == cspaceType.Name;
		}

		// Token: 0x0600404D RID: 16461 RVA: 0x000D5A1C File Offset: 0x000D3C1C
		private bool UnderlyingEnumTypesMatch(Type enumType, EnumType cspaceEnumType)
		{
			PrimitiveType primitiveType;
			if (!ClrProviderManifest.Instance.TryGetPrimitiveType(enumType.GetEnumUnderlyingType(), out primitiveType))
			{
				this.LogLoadMessage(Strings.Validator_UnsupportedEnumUnderlyingType(enumType.GetEnumUnderlyingType().FullName), cspaceEnumType);
				return false;
			}
			if (primitiveType.PrimitiveTypeKind != cspaceEnumType.UnderlyingType.PrimitiveTypeKind)
			{
				this.LogLoadMessage(Strings.Validator_OSpace_Convention_NonMatchingUnderlyingTypes, cspaceEnumType);
				return false;
			}
			return true;
		}

		// Token: 0x0600404E RID: 16462 RVA: 0x000D5A78 File Offset: 0x000D3C78
		private bool EnumMembersMatch(Type enumType, EnumType cspaceEnumType)
		{
			Type enumUnderlyingType = enumType.GetEnumUnderlyingType();
			IEnumerator<EnumMember> enumerator = cspaceEnumType.Members.OrderBy((EnumMember m) => m.Name).GetEnumerator();
			IEnumerator<string> enumerator2 = (from n in enumType.GetEnumNames()
				orderby n
				select n).GetEnumerator();
			if (!enumerator.MoveNext())
			{
				return true;
			}
			while (enumerator2.MoveNext())
			{
				if (enumerator.Current.Name == enumerator2.Current && enumerator.Current.Value.Equals(Convert.ChangeType(Enum.Parse(enumType, enumerator2.Current), enumUnderlyingType, CultureInfo.InvariantCulture)) && !enumerator.MoveNext())
				{
					return true;
				}
			}
			this.LogLoadMessage(Strings.Mapping_Enum_OCMapping_MemberMismatch(enumType.FullName, enumerator.Current.Name, enumerator.Current.Value, cspaceEnumType.FullName), cspaceEnumType);
			return false;
		}

		// Token: 0x0600404F RID: 16463 RVA: 0x000D5B78 File Offset: 0x000D3D78
		private bool TryCreateMembers(Type type, StructuralType cspaceType, StructuralType ospaceType, List<Action> referenceResolutionListForCurrentType)
		{
			IEnumerable<PropertyInfo> enumerable = ((cspaceType.BaseType == null) ? type.GetRuntimeProperties() : type.GetDeclaredProperties()).Where((PropertyInfo p) => !p.IsStatic());
			return this.TryFindAndCreatePrimitiveProperties(type, cspaceType, ospaceType, enumerable) && this.TryFindAndCreateEnumProperties(type, cspaceType, ospaceType, enumerable, referenceResolutionListForCurrentType) && this.TryFindComplexProperties(type, cspaceType, ospaceType, enumerable, referenceResolutionListForCurrentType) && this.TryFindNavigationProperties(type, cspaceType, ospaceType, enumerable, referenceResolutionListForCurrentType);
		}

		// Token: 0x06004050 RID: 16464 RVA: 0x000D5C00 File Offset: 0x000D3E00
		private bool TryFindComplexProperties(Type type, StructuralType cspaceType, StructuralType ospaceType, IEnumerable<PropertyInfo> clrProperties, List<Action> referenceResolutionListForCurrentType)
		{
			List<KeyValuePair<EdmProperty, PropertyInfo>> list = new List<KeyValuePair<EdmProperty, PropertyInfo>>();
			using (IEnumerator<EdmProperty> enumerator = (from m in cspaceType.GetDeclaredOnlyMembers<EdmProperty>()
				where Helper.IsComplexType(m.TypeUsage.EdmType)
				select m).GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					EdmProperty cspaceProperty = enumerator.Current;
					PropertyInfo propertyInfo = clrProperties.FirstOrDefault((PropertyInfo p) => OSpaceTypeFactory.MemberMatchesByConvention(p, cspaceProperty));
					if (!(propertyInfo != null))
					{
						string text = Strings.Validator_OSpace_Convention_MissingRequiredProperty(cspaceProperty.Name, type.FullName);
						this.LogLoadMessage(text, cspaceType);
						return false;
					}
					list.Add(new KeyValuePair<EdmProperty, PropertyInfo>(cspaceProperty, propertyInfo));
				}
			}
			foreach (KeyValuePair<EdmProperty, PropertyInfo> keyValuePair in list)
			{
				this.TrackClosure(keyValuePair.Value.PropertyType);
				StructuralType ot = ospaceType;
				EdmProperty cp = keyValuePair.Key;
				PropertyInfo clrp = keyValuePair.Value;
				referenceResolutionListForCurrentType.Add(delegate
				{
					this.CreateAndAddComplexType(type, ot, cp, clrp);
				});
			}
			return true;
		}

		// Token: 0x06004051 RID: 16465 RVA: 0x000D5D88 File Offset: 0x000D3F88
		private bool TryFindNavigationProperties(Type type, StructuralType cspaceType, StructuralType ospaceType, IEnumerable<PropertyInfo> clrProperties, List<Action> referenceResolutionListForCurrentType)
		{
			List<KeyValuePair<NavigationProperty, PropertyInfo>> list = new List<KeyValuePair<NavigationProperty, PropertyInfo>>();
			using (ReadOnlyMetadataCollection<NavigationProperty>.Enumerator enumerator = cspaceType.GetDeclaredOnlyMembers<NavigationProperty>().GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					NavigationProperty cspaceProperty = enumerator.Current;
					PropertyInfo propertyInfo = clrProperties.FirstOrDefault((PropertyInfo p) => OSpaceTypeFactory.NonPrimitiveMemberMatchesByConvention(p, cspaceProperty));
					if (!(propertyInfo != null))
					{
						string text = Strings.Validator_OSpace_Convention_MissingRequiredProperty(cspaceProperty.Name, type.FullName);
						this.LogLoadMessage(text, cspaceType);
						return false;
					}
					bool flag = cspaceProperty.ToEndMember.RelationshipMultiplicity != RelationshipMultiplicity.Many;
					if (propertyInfo.CanRead && (!flag || propertyInfo.CanWriteExtended()))
					{
						list.Add(new KeyValuePair<NavigationProperty, PropertyInfo>(cspaceProperty, propertyInfo));
					}
				}
			}
			foreach (KeyValuePair<NavigationProperty, PropertyInfo> keyValuePair in list)
			{
				this.TrackClosure(keyValuePair.Value.PropertyType);
				StructuralType ct = cspaceType;
				StructuralType ot = ospaceType;
				NavigationProperty cp = keyValuePair.Key;
				referenceResolutionListForCurrentType.Add(delegate
				{
					this.CreateAndAddNavigationProperty(ct, ot, cp);
				});
			}
			return true;
		}

		// Token: 0x06004052 RID: 16466 RVA: 0x000D5F00 File Offset: 0x000D4100
		private EdmType ResolveBaseType(StructuralType baseCSpaceType, Type type)
		{
			EdmType edmType;
			if (!this.CspaceToOspace.TryGetValue(baseCSpaceType, out edmType))
			{
				this.LogError(Strings.Validator_OSpace_Convention_BaseTypeNotLoaded(type, baseCSpaceType), baseCSpaceType);
			}
			return edmType;
		}

		// Token: 0x06004053 RID: 16467 RVA: 0x000D5F2C File Offset: 0x000D412C
		private bool TryFindAndCreatePrimitiveProperties(Type type, StructuralType cspaceType, StructuralType ospaceType, IEnumerable<PropertyInfo> clrProperties)
		{
			using (IEnumerator<EdmProperty> enumerator = (from p in cspaceType.GetDeclaredOnlyMembers<EdmProperty>()
				where Helper.IsPrimitiveType(p.TypeUsage.EdmType)
				select p).GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					EdmProperty cspaceProperty = enumerator.Current;
					PropertyInfo propertyInfo = clrProperties.FirstOrDefault((PropertyInfo p) => OSpaceTypeFactory.MemberMatchesByConvention(p, cspaceProperty));
					if (!(propertyInfo != null))
					{
						string text = Strings.Validator_OSpace_Convention_MissingRequiredProperty(cspaceProperty.Name, type.FullName);
						this.LogLoadMessage(text, cspaceType);
						return false;
					}
					PrimitiveType primitiveType;
					if (!OSpaceTypeFactory.TryGetPrimitiveType(propertyInfo.PropertyType, out primitiveType))
					{
						string text2 = Strings.Validator_OSpace_Convention_NonPrimitiveTypeProperty(propertyInfo.Name, type.FullName, propertyInfo.PropertyType.FullName);
						this.LogLoadMessage(text2, cspaceType);
						return false;
					}
					if (!propertyInfo.CanRead || !propertyInfo.CanWriteExtended())
					{
						string text3 = Strings.Validator_OSpace_Convention_ScalarPropertyMissginGetterOrSetter(propertyInfo.Name, type.FullName, type.Assembly().FullName);
						this.LogLoadMessage(text3, cspaceType);
						return false;
					}
					OSpaceTypeFactory.AddScalarMember(type, propertyInfo, ospaceType, cspaceProperty, primitiveType);
				}
			}
			return true;
		}

		// Token: 0x06004054 RID: 16468 RVA: 0x000D607C File Offset: 0x000D427C
		protected static bool TryGetPrimitiveType(Type type, out PrimitiveType primitiveType)
		{
			return ClrProviderManifest.Instance.TryGetPrimitiveType(Nullable.GetUnderlyingType(type) ?? type, out primitiveType);
		}

		// Token: 0x06004055 RID: 16469 RVA: 0x000D6094 File Offset: 0x000D4294
		private bool TryFindAndCreateEnumProperties(Type type, StructuralType cspaceType, StructuralType ospaceType, IEnumerable<PropertyInfo> clrProperties, List<Action> referenceResolutionListForCurrentType)
		{
			List<KeyValuePair<EdmProperty, PropertyInfo>> list = new List<KeyValuePair<EdmProperty, PropertyInfo>>();
			using (IEnumerator<EdmProperty> enumerator = (from p in cspaceType.GetDeclaredOnlyMembers<EdmProperty>()
				where Helper.IsEnumType(p.TypeUsage.EdmType)
				select p).GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					EdmProperty cspaceProperty = enumerator.Current;
					PropertyInfo propertyInfo = clrProperties.FirstOrDefault((PropertyInfo p) => OSpaceTypeFactory.MemberMatchesByConvention(p, cspaceProperty));
					if (!(propertyInfo != null))
					{
						string text = Strings.Validator_OSpace_Convention_MissingRequiredProperty(cspaceProperty.Name, type.FullName);
						this.LogLoadMessage(text, cspaceType);
						return false;
					}
					list.Add(new KeyValuePair<EdmProperty, PropertyInfo>(cspaceProperty, propertyInfo));
				}
			}
			foreach (KeyValuePair<EdmProperty, PropertyInfo> keyValuePair in list)
			{
				this.TrackClosure(keyValuePair.Value.PropertyType);
				StructuralType ot = ospaceType;
				EdmProperty cp = keyValuePair.Key;
				PropertyInfo clrp = keyValuePair.Value;
				referenceResolutionListForCurrentType.Add(delegate
				{
					this.CreateAndAddEnumProperty(type, ot, cp, clrp);
				});
			}
			return true;
		}

		// Token: 0x06004056 RID: 16470 RVA: 0x000D621C File Offset: 0x000D441C
		private static bool MemberMatchesByConvention(PropertyInfo clrProperty, EdmMember cspaceMember)
		{
			return clrProperty.Name == cspaceMember.Name;
		}

		// Token: 0x06004057 RID: 16471 RVA: 0x000D6230 File Offset: 0x000D4430
		private void CreateAndAddComplexType(Type type, StructuralType ospaceType, EdmProperty cspaceProperty, PropertyInfo clrProperty)
		{
			EdmType edmType;
			if (this.CspaceToOspace.TryGetValue(cspaceProperty.TypeUsage.EdmType, out edmType))
			{
				EdmProperty edmProperty = new EdmProperty(cspaceProperty.Name, TypeUsage.Create(edmType, new FacetValues
				{
					Nullable = new bool?(false)
				}), clrProperty, type);
				ospaceType.AddMember(edmProperty);
				return;
			}
			this.LogError(Strings.Validator_OSpace_Convention_MissingOSpaceType(cspaceProperty.TypeUsage.EdmType.FullName), cspaceProperty.TypeUsage.EdmType);
		}

		// Token: 0x06004058 RID: 16472 RVA: 0x000D62B0 File Offset: 0x000D44B0
		private static bool NonPrimitiveMemberMatchesByConvention(PropertyInfo clrProperty, EdmMember cspaceMember)
		{
			return !clrProperty.PropertyType.IsValueType() && !clrProperty.PropertyType.IsAssignableFrom(typeof(string)) && clrProperty.Name == cspaceMember.Name;
		}

		// Token: 0x06004059 RID: 16473 RVA: 0x000D62EC File Offset: 0x000D44EC
		private void CreateAndAddNavigationProperty(StructuralType cspaceType, StructuralType ospaceType, NavigationProperty cspaceProperty)
		{
			EdmType edmType;
			if (this.CspaceToOspace.TryGetValue(cspaceProperty.RelationshipType, out edmType))
			{
				EdmType edmType2 = null;
				EdmType edmType4;
				if (Helper.IsCollectionType(cspaceProperty.TypeUsage.EdmType))
				{
					EdmType edmType3;
					if (this.CspaceToOspace.TryGetValue(((CollectionType)cspaceProperty.TypeUsage.EdmType).TypeUsage.EdmType, out edmType3))
					{
						edmType2 = edmType3.GetCollectionType();
					}
				}
				else if (this.CspaceToOspace.TryGetValue(cspaceProperty.TypeUsage.EdmType, out edmType4))
				{
					edmType2 = edmType4;
				}
				NavigationProperty navigationProperty = new NavigationProperty(cspaceProperty.Name, TypeUsage.Create(edmType2));
				RelationshipType relationshipType = (RelationshipType)edmType;
				navigationProperty.RelationshipType = relationshipType;
				navigationProperty.ToEndMember = (RelationshipEndMember)relationshipType.Members.First((EdmMember e) => e.Name == cspaceProperty.ToEndMember.Name);
				navigationProperty.FromEndMember = (RelationshipEndMember)relationshipType.Members.First((EdmMember e) => e.Name == cspaceProperty.FromEndMember.Name);
				ospaceType.AddMember(navigationProperty);
				return;
			}
			EntityTypeBase entityTypeBase = cspaceProperty.RelationshipType.RelationshipEndMembers.Select((RelationshipEndMember e) => ((RefType)e.TypeUsage.EdmType).ElementType).First((EntityTypeBase e) => e != cspaceType);
			this.LogError(Strings.Validator_OSpace_Convention_RelationshipNotLoaded(cspaceProperty.RelationshipType.FullName, entityTypeBase.FullName), entityTypeBase);
		}

		// Token: 0x0600405A RID: 16474 RVA: 0x000D6480 File Offset: 0x000D4680
		private void CreateAndAddEnumProperty(Type type, StructuralType ospaceType, EdmProperty cspaceProperty, PropertyInfo clrProperty)
		{
			EdmType edmType;
			if (!this.CspaceToOspace.TryGetValue(cspaceProperty.TypeUsage.EdmType, out edmType))
			{
				this.LogError(Strings.Validator_OSpace_Convention_MissingOSpaceType(cspaceProperty.TypeUsage.EdmType.FullName), cspaceProperty.TypeUsage.EdmType);
				return;
			}
			if (clrProperty.CanRead && clrProperty.CanWriteExtended())
			{
				OSpaceTypeFactory.AddScalarMember(type, clrProperty, ospaceType, cspaceProperty, edmType);
				return;
			}
			this.LogError(Strings.Validator_OSpace_Convention_ScalarPropertyMissginGetterOrSetter(clrProperty.Name, type.FullName, type.Assembly().FullName), cspaceProperty.TypeUsage.EdmType);
		}

		// Token: 0x0600405B RID: 16475 RVA: 0x000D651C File Offset: 0x000D471C
		private static void AddScalarMember(Type type, PropertyInfo clrProperty, StructuralType ospaceType, EdmProperty cspaceProperty, EdmType propertyType)
		{
			StructuralType declaringType = cspaceProperty.DeclaringType;
			bool flag = Helper.IsEntityType(declaringType) && ((EntityType)declaringType).KeyMemberNames.Contains(clrProperty.Name);
			bool flag2 = !flag && (!clrProperty.PropertyType.IsValueType() || Nullable.GetUnderlyingType(clrProperty.PropertyType) != null);
			EdmProperty edmProperty = new EdmProperty(cspaceProperty.Name, TypeUsage.Create(propertyType, new FacetValues
			{
				Nullable = new bool?(flag2)
			}), clrProperty, type);
			if (flag)
			{
				((EntityType)ospaceType).AddKeyMember(edmProperty);
				return;
			}
			ospaceType.AddMember(edmProperty);
		}

		// Token: 0x0600405C RID: 16476 RVA: 0x000D65BC File Offset: 0x000D47BC
		public virtual void CreateRelationships(EdmItemCollection edmItemCollection)
		{
			foreach (AssociationType associationType in edmItemCollection.GetItems<AssociationType>())
			{
				if (!this.CspaceToOspace.ContainsKey(associationType))
				{
					EdmType[] array = new EdmType[2];
					if (this.CspaceToOspace.TryGetValue(OSpaceTypeFactory.GetRelationshipEndType(associationType.RelationshipEndMembers[0]), out array[0]) && this.CspaceToOspace.TryGetValue(OSpaceTypeFactory.GetRelationshipEndType(associationType.RelationshipEndMembers[1]), out array[1]))
					{
						AssociationType associationType2 = new AssociationType(associationType.Name, associationType.NamespaceName, associationType.IsForeignKey, DataSpace.OSpace);
						for (int i = 0; i < associationType.RelationshipEndMembers.Count; i++)
						{
							EntityType entityType = (EntityType)array[i];
							RelationshipEndMember relationshipEndMember = associationType.RelationshipEndMembers[i];
							associationType2.AddKeyMember(new AssociationEndMember(relationshipEndMember.Name, entityType.GetReferenceType(), relationshipEndMember.RelationshipMultiplicity));
						}
						this.AddToTypesInAssembly(associationType2);
						this.LoadedTypes.Add(associationType2.FullName, associationType2);
						this.CspaceToOspace.Add(associationType, associationType2);
					}
				}
			}
		}

		// Token: 0x0600405D RID: 16477 RVA: 0x000D6710 File Offset: 0x000D4910
		private static StructuralType GetRelationshipEndType(RelationshipEndMember relationshipEndMember)
		{
			return ((RefType)relationshipEndMember.TypeUsage.EdmType).ElementType;
		}
	}
}
