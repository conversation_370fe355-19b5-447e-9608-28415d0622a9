﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Threading;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004CD RID: 1229
	public abstract class ItemCollection : ReadOnlyMetadataCollection<GlobalItem>
	{
		// Token: 0x06003CF1 RID: 15601 RVA: 0x000C8DE4 File Offset: 0x000C6FE4
		internal ItemCollection()
		{
		}

		// Token: 0x06003CF2 RID: 15602 RVA: 0x000C8DEC File Offset: 0x000C6FEC
		internal ItemCollection(DataSpace dataspace)
			: base(new MetadataCollection<GlobalItem>())
		{
			this._space = dataspace;
		}

		// Token: 0x17000C06 RID: 3078
		// (get) Token: 0x06003CF3 RID: 15603 RVA: 0x000C8E00 File Offset: 0x000C7000
		public DataSpace DataSpace
		{
			get
			{
				return this._space;
			}
		}

		// Token: 0x17000C07 RID: 3079
		// (get) Token: 0x06003CF4 RID: 15604 RVA: 0x000C8E08 File Offset: 0x000C7008
		internal Dictionary<string, ReadOnlyCollection<EdmFunction>> FunctionLookUpTable
		{
			get
			{
				if (this._functionLookUpTable == null)
				{
					Dictionary<string, ReadOnlyCollection<EdmFunction>> dictionary = ItemCollection.PopulateFunctionLookUpTable(this);
					Interlocked.CompareExchange<Dictionary<string, ReadOnlyCollection<EdmFunction>>>(ref this._functionLookUpTable, dictionary, null);
				}
				return this._functionLookUpTable;
			}
		}

		// Token: 0x06003CF5 RID: 15605 RVA: 0x000C8E38 File Offset: 0x000C7038
		internal void AddInternal(GlobalItem item)
		{
			base.Source.Add(item);
		}

		// Token: 0x06003CF6 RID: 15606 RVA: 0x000C8E46 File Offset: 0x000C7046
		internal void AddRange(List<GlobalItem> items)
		{
			base.Source.AddRange(items);
		}

		// Token: 0x06003CF7 RID: 15607 RVA: 0x000C8E54 File Offset: 0x000C7054
		public T GetItem<T>(string identity) where T : GlobalItem
		{
			return this.GetItem<T>(identity, false);
		}

		// Token: 0x06003CF8 RID: 15608 RVA: 0x000C8E5E File Offset: 0x000C705E
		public bool TryGetItem<T>(string identity, out T item) where T : GlobalItem
		{
			return this.TryGetItem<T>(identity, false, out item);
		}

		// Token: 0x06003CF9 RID: 15609 RVA: 0x000C8E6C File Offset: 0x000C706C
		public bool TryGetItem<T>(string identity, bool ignoreCase, out T item) where T : GlobalItem
		{
			GlobalItem globalItem = null;
			this.TryGetValue(identity, ignoreCase, out globalItem);
			item = globalItem as T;
			return item != null;
		}

		// Token: 0x06003CFA RID: 15610 RVA: 0x000C8EA8 File Offset: 0x000C70A8
		public T GetItem<T>(string identity, bool ignoreCase) where T : GlobalItem
		{
			T t;
			if (this.TryGetItem<T>(identity, ignoreCase, out t))
			{
				return t;
			}
			throw new ArgumentException(Strings.ItemInvalidIdentity(identity), "identity");
		}

		// Token: 0x06003CFB RID: 15611 RVA: 0x000C8ED4 File Offset: 0x000C70D4
		public virtual ReadOnlyCollection<T> GetItems<T>() where T : GlobalItem
		{
			Memoizer<Type, ICollection> itemsCache = this._itemsCache;
			if (this._itemsCache == null || this._itemCount != base.Count)
			{
				Memoizer<Type, ICollection> memoizer = new Memoizer<Type, ICollection>(new Func<Type, ICollection>(this.InternalGetItems), null);
				Interlocked.CompareExchange<Memoizer<Type, ICollection>>(ref this._itemsCache, memoizer, itemsCache);
				this._itemCount = base.Count;
			}
			return this._itemsCache.Evaluate(typeof(T)) as ReadOnlyCollection<T>;
		}

		// Token: 0x06003CFC RID: 15612 RVA: 0x000C8F45 File Offset: 0x000C7145
		internal ICollection InternalGetItems(Type type)
		{
			return typeof(ItemCollection).GetOnlyDeclaredMethod("GenericGetItems").MakeGenericMethod(new Type[] { type }).Invoke(null, new object[] { this }) as ICollection;
		}

		// Token: 0x06003CFD RID: 15613 RVA: 0x000C8F80 File Offset: 0x000C7180
		private static ReadOnlyCollection<TItem> GenericGetItems<TItem>(ItemCollection collection) where TItem : GlobalItem
		{
			List<TItem> list = new List<TItem>();
			foreach (GlobalItem globalItem in collection)
			{
				TItem titem = globalItem as TItem;
				if (titem != null)
				{
					list.Add(titem);
				}
			}
			return new ReadOnlyCollection<TItem>(list);
		}

		// Token: 0x06003CFE RID: 15614 RVA: 0x000C8FEC File Offset: 0x000C71EC
		public EdmType GetType(string name, string namespaceName)
		{
			return this.GetType(name, namespaceName, false);
		}

		// Token: 0x06003CFF RID: 15615 RVA: 0x000C8FF7 File Offset: 0x000C71F7
		public bool TryGetType(string name, string namespaceName, out EdmType type)
		{
			return this.TryGetType(name, namespaceName, false, out type);
		}

		// Token: 0x06003D00 RID: 15616 RVA: 0x000C9003 File Offset: 0x000C7203
		public EdmType GetType(string name, string namespaceName, bool ignoreCase)
		{
			Check.NotNull<string>(name, "name");
			Check.NotNull<string>(namespaceName, "namespaceName");
			return this.GetItem<EdmType>(EdmType.CreateEdmTypeIdentity(namespaceName, name), ignoreCase);
		}

		// Token: 0x06003D01 RID: 15617 RVA: 0x000C902C File Offset: 0x000C722C
		public bool TryGetType(string name, string namespaceName, bool ignoreCase, out EdmType type)
		{
			Check.NotNull<string>(name, "name");
			Check.NotNull<string>(namespaceName, "namespaceName");
			GlobalItem globalItem = null;
			this.TryGetValue(EdmType.CreateEdmTypeIdentity(namespaceName, name), ignoreCase, out globalItem);
			type = globalItem as EdmType;
			return type != null;
		}

		// Token: 0x06003D02 RID: 15618 RVA: 0x000C9073 File Offset: 0x000C7273
		public ReadOnlyCollection<EdmFunction> GetFunctions(string functionName)
		{
			return this.GetFunctions(functionName, false);
		}

		// Token: 0x06003D03 RID: 15619 RVA: 0x000C907D File Offset: 0x000C727D
		public ReadOnlyCollection<EdmFunction> GetFunctions(string functionName, bool ignoreCase)
		{
			return ItemCollection.GetFunctions(this.FunctionLookUpTable, functionName, ignoreCase);
		}

		// Token: 0x06003D04 RID: 15620 RVA: 0x000C908C File Offset: 0x000C728C
		protected static ReadOnlyCollection<EdmFunction> GetFunctions(Dictionary<string, ReadOnlyCollection<EdmFunction>> functionCollection, string functionName, bool ignoreCase)
		{
			ReadOnlyCollection<EdmFunction> readOnlyCollection;
			if (!functionCollection.TryGetValue(functionName, out readOnlyCollection))
			{
				return Helper.EmptyEdmFunctionReadOnlyCollection;
			}
			if (ignoreCase)
			{
				return readOnlyCollection;
			}
			return ItemCollection.GetCaseSensitiveFunctions(readOnlyCollection, functionName);
		}

		// Token: 0x06003D05 RID: 15621 RVA: 0x000C90B8 File Offset: 0x000C72B8
		internal static ReadOnlyCollection<EdmFunction> GetCaseSensitiveFunctions(ReadOnlyCollection<EdmFunction> functionOverloads, string functionName)
		{
			List<EdmFunction> list = new List<EdmFunction>(functionOverloads.Count);
			for (int i = 0; i < functionOverloads.Count; i++)
			{
				if (functionOverloads[i].FullName == functionName)
				{
					list.Add(functionOverloads[i]);
				}
			}
			if (list.Count != functionOverloads.Count)
			{
				functionOverloads = new ReadOnlyCollection<EdmFunction>(list);
			}
			return functionOverloads;
		}

		// Token: 0x06003D06 RID: 15622 RVA: 0x000C911C File Offset: 0x000C731C
		internal bool TryGetFunction(string functionName, TypeUsage[] parameterTypes, bool ignoreCase, out EdmFunction function)
		{
			Check.NotNull<string>(functionName, "functionName");
			Check.NotNull<TypeUsage[]>(parameterTypes, "parameterTypes");
			string text = EdmFunction.BuildIdentity(functionName, parameterTypes);
			GlobalItem globalItem = null;
			function = null;
			if (this.TryGetValue(text, ignoreCase, out globalItem) && Helper.IsEdmFunction(globalItem))
			{
				function = (EdmFunction)globalItem;
				return true;
			}
			return false;
		}

		// Token: 0x06003D07 RID: 15623 RVA: 0x000C916F File Offset: 0x000C736F
		public EntityContainer GetEntityContainer(string name)
		{
			Check.NotNull<string>(name, "name");
			return this.GetEntityContainer(name, false);
		}

		// Token: 0x06003D08 RID: 15624 RVA: 0x000C9185 File Offset: 0x000C7385
		public bool TryGetEntityContainer(string name, out EntityContainer entityContainer)
		{
			Check.NotNull<string>(name, "name");
			return this.TryGetEntityContainer(name, false, out entityContainer);
		}

		// Token: 0x06003D09 RID: 15625 RVA: 0x000C919C File Offset: 0x000C739C
		public EntityContainer GetEntityContainer(string name, bool ignoreCase)
		{
			EntityContainer entityContainer = this.GetValue(name, ignoreCase) as EntityContainer;
			if (entityContainer != null)
			{
				return entityContainer;
			}
			throw new ArgumentException(Strings.ItemInvalidIdentity(name), "name");
		}

		// Token: 0x06003D0A RID: 15626 RVA: 0x000C91CC File Offset: 0x000C73CC
		public bool TryGetEntityContainer(string name, bool ignoreCase, out EntityContainer entityContainer)
		{
			Check.NotNull<string>(name, "name");
			GlobalItem globalItem = null;
			if (this.TryGetValue(name, ignoreCase, out globalItem) && Helper.IsEntityContainer(globalItem))
			{
				entityContainer = (EntityContainer)globalItem;
				return true;
			}
			entityContainer = null;
			return false;
		}

		// Token: 0x06003D0B RID: 15627 RVA: 0x000C9209 File Offset: 0x000C7409
		internal virtual PrimitiveType GetMappedPrimitiveType(PrimitiveTypeKind primitiveTypeKind)
		{
			throw Error.NotSupported();
		}

		// Token: 0x06003D0C RID: 15628 RVA: 0x000C9210 File Offset: 0x000C7410
		internal virtual bool MetadataEquals(ItemCollection other)
		{
			return this == other;
		}

		// Token: 0x06003D0D RID: 15629 RVA: 0x000C9218 File Offset: 0x000C7418
		private static Dictionary<string, ReadOnlyCollection<EdmFunction>> PopulateFunctionLookUpTable(ItemCollection itemCollection)
		{
			Dictionary<string, List<EdmFunction>> dictionary = new Dictionary<string, List<EdmFunction>>(StringComparer.OrdinalIgnoreCase);
			foreach (EdmFunction edmFunction in itemCollection.GetItems<EdmFunction>())
			{
				List<EdmFunction> list;
				if (!dictionary.TryGetValue(edmFunction.FullName, out list))
				{
					list = new List<EdmFunction>();
					dictionary[edmFunction.FullName] = list;
				}
				list.Add(edmFunction);
			}
			Dictionary<string, ReadOnlyCollection<EdmFunction>> dictionary2 = new Dictionary<string, ReadOnlyCollection<EdmFunction>>(StringComparer.OrdinalIgnoreCase);
			foreach (List<EdmFunction> list2 in dictionary.Values)
			{
				dictionary2.Add(list2[0].FullName, new ReadOnlyCollection<EdmFunction>(list2.ToArray()));
			}
			return dictionary2;
		}

		// Token: 0x040014E7 RID: 5351
		private readonly DataSpace _space;

		// Token: 0x040014E8 RID: 5352
		private Dictionary<string, ReadOnlyCollection<EdmFunction>> _functionLookUpTable;

		// Token: 0x040014E9 RID: 5353
		private Memoizer<Type, ICollection> _itemsCache;

		// Token: 0x040014EA RID: 5354
		private int _itemCount;
	}
}
