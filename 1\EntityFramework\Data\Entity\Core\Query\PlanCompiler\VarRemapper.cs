﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Query.InternalTrees;

namespace System.Data.Entity.Core.Query.PlanCompiler
{
	// Token: 0x0200037A RID: 890
	internal class VarRemapper : BasicOpVisitor
	{
		// Token: 0x06002AF1 RID: 10993 RVA: 0x0008C290 File Offset: 0x0008A490
		internal VarRemapper(Command command)
			: this(command, new Dictionary<Var, Var>())
		{
		}

		// Token: 0x06002AF2 RID: 10994 RVA: 0x0008C29E File Offset: 0x0008A49E
		internal VarRemapper(Command command, IDictionary<Var, Var> varMap)
		{
			this.m_command = command;
			this.m_varMap = varMap;
		}

		// Token: 0x06002AF3 RID: 10995 RVA: 0x0008C2B4 File Offset: 0x0008A4B4
		internal void AddMapping(Var oldVar, Var newVar)
		{
			this.m_varMap[oldVar] = newVar;
		}

		// Token: 0x06002AF4 RID: 10996 RVA: 0x0008C2C3 File Offset: 0x0008A4C3
		internal virtual void RemapNode(Node node)
		{
			if (this.m_varMap.Count == 0)
			{
				return;
			}
			this.VisitNode(node);
		}

		// Token: 0x06002AF5 RID: 10997 RVA: 0x0008C2DC File Offset: 0x0008A4DC
		internal virtual void RemapSubtree(Node subTree)
		{
			if (this.m_varMap.Count == 0)
			{
				return;
			}
			foreach (Node node in subTree.Children)
			{
				this.RemapSubtree(node);
			}
			this.RemapNode(subTree);
			this.m_command.RecomputeNodeInfo(subTree);
		}

		// Token: 0x06002AF6 RID: 10998 RVA: 0x0008C350 File Offset: 0x0008A550
		internal VarList RemapVarList(VarList varList)
		{
			return Command.CreateVarList(this.MapVars(varList));
		}

		// Token: 0x06002AF7 RID: 10999 RVA: 0x0008C35E File Offset: 0x0008A55E
		internal static VarList RemapVarList(Command command, IDictionary<Var, Var> varMap, VarList varList)
		{
			return new VarRemapper(command, varMap).RemapVarList(varList);
		}

		// Token: 0x06002AF8 RID: 11000 RVA: 0x0008C370 File Offset: 0x0008A570
		private Var Map(Var v)
		{
			Var var;
			while (this.m_varMap.TryGetValue(v, out var))
			{
				v = var;
			}
			return v;
		}

		// Token: 0x06002AF9 RID: 11001 RVA: 0x0008C393 File Offset: 0x0008A593
		private IEnumerable<Var> MapVars(IEnumerable<Var> vars)
		{
			foreach (Var var in vars)
			{
				yield return this.Map(var);
			}
			IEnumerator<Var> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x06002AFA RID: 11002 RVA: 0x0008C3AC File Offset: 0x0008A5AC
		private void Map(VarVec vec)
		{
			VarVec varVec = this.m_command.CreateVarVec(this.MapVars(vec));
			vec.InitFrom(varVec);
		}

		// Token: 0x06002AFB RID: 11003 RVA: 0x0008C3D4 File Offset: 0x0008A5D4
		private void Map(VarList varList)
		{
			VarList varList2 = Command.CreateVarList(this.MapVars(varList));
			varList.Clear();
			varList.AddRange(varList2);
		}

		// Token: 0x06002AFC RID: 11004 RVA: 0x0008C3FC File Offset: 0x0008A5FC
		private void Map(VarMap varMap)
		{
			VarMap varMap2 = new VarMap();
			foreach (KeyValuePair<Var, Var> keyValuePair in varMap)
			{
				Var var = this.Map(keyValuePair.Value);
				varMap2.Add(keyValuePair.Key, var);
			}
			varMap.Clear();
			foreach (KeyValuePair<Var, Var> keyValuePair2 in varMap2)
			{
				varMap.Add(keyValuePair2.Key, keyValuePair2.Value);
			}
		}

		// Token: 0x06002AFD RID: 11005 RVA: 0x0008C4AC File Offset: 0x0008A6AC
		private void Map(List<SortKey> sortKeys)
		{
			VarVec varVec = this.m_command.CreateVarVec();
			bool flag = false;
			foreach (SortKey sortKey in sortKeys)
			{
				sortKey.Var = this.Map(sortKey.Var);
				if (varVec.IsSet(sortKey.Var))
				{
					flag = true;
				}
				varVec.Set(sortKey.Var);
			}
			if (flag)
			{
				List<SortKey> list = new List<SortKey>(sortKeys);
				sortKeys.Clear();
				varVec.Clear();
				foreach (SortKey sortKey2 in list)
				{
					if (!varVec.IsSet(sortKey2.Var))
					{
						sortKeys.Add(sortKey2);
					}
					varVec.Set(sortKey2.Var);
				}
			}
		}

		// Token: 0x06002AFE RID: 11006 RVA: 0x0008C5A0 File Offset: 0x0008A7A0
		protected override void VisitDefault(Node n)
		{
		}

		// Token: 0x06002AFF RID: 11007 RVA: 0x0008C5A4 File Offset: 0x0008A7A4
		public override void Visit(VarRefOp op, Node n)
		{
			this.VisitScalarOpDefault(op, n);
			Var var = this.Map(op.Var);
			if (var != op.Var)
			{
				n.Op = this.m_command.CreateVarRefOp(var);
			}
		}

		// Token: 0x06002B00 RID: 11008 RVA: 0x0008C5E1 File Offset: 0x0008A7E1
		protected override void VisitNestOp(NestBaseOp op, Node n)
		{
			throw new NotSupportedException();
		}

		// Token: 0x06002B01 RID: 11009 RVA: 0x0008C5E8 File Offset: 0x0008A7E8
		public override void Visit(PhysicalProjectOp op, Node n)
		{
			this.VisitPhysicalOpDefault(op, n);
			this.Map(op.Outputs);
			SimpleCollectionColumnMap simpleCollectionColumnMap = (SimpleCollectionColumnMap)ColumnMapTranslator.Translate(op.ColumnMap, this.m_varMap);
			n.Op = this.m_command.CreatePhysicalProjectOp(op.Outputs, simpleCollectionColumnMap);
		}

		// Token: 0x06002B02 RID: 11010 RVA: 0x0008C638 File Offset: 0x0008A838
		protected override void VisitGroupByOp(GroupByBaseOp op, Node n)
		{
			this.VisitRelOpDefault(op, n);
			this.Map(op.Outputs);
			this.Map(op.Keys);
		}

		// Token: 0x06002B03 RID: 11011 RVA: 0x0008C65A File Offset: 0x0008A85A
		public override void Visit(GroupByIntoOp op, Node n)
		{
			this.VisitGroupByOp(op, n);
			this.Map(op.Inputs);
		}

		// Token: 0x06002B04 RID: 11012 RVA: 0x0008C670 File Offset: 0x0008A870
		public override void Visit(DistinctOp op, Node n)
		{
			this.VisitRelOpDefault(op, n);
			this.Map(op.Keys);
		}

		// Token: 0x06002B05 RID: 11013 RVA: 0x0008C686 File Offset: 0x0008A886
		public override void Visit(ProjectOp op, Node n)
		{
			this.VisitRelOpDefault(op, n);
			this.Map(op.Outputs);
		}

		// Token: 0x06002B06 RID: 11014 RVA: 0x0008C69C File Offset: 0x0008A89C
		public override void Visit(UnnestOp op, Node n)
		{
			this.VisitRelOpDefault(op, n);
			Var var = this.Map(op.Var);
			if (var != op.Var)
			{
				n.Op = this.m_command.CreateUnnestOp(var, op.Table);
			}
		}

		// Token: 0x06002B07 RID: 11015 RVA: 0x0008C6DF File Offset: 0x0008A8DF
		protected override void VisitSetOp(SetOp op, Node n)
		{
			this.VisitRelOpDefault(op, n);
			this.Map(op.VarMap[0]);
			this.Map(op.VarMap[1]);
		}

		// Token: 0x06002B08 RID: 11016 RVA: 0x0008C705 File Offset: 0x0008A905
		protected override void VisitSortOp(SortBaseOp op, Node n)
		{
			this.VisitRelOpDefault(op, n);
			this.Map(op.Keys);
		}

		// Token: 0x04000ED8 RID: 3800
		private readonly IDictionary<Var, Var> m_varMap;

		// Token: 0x04000ED9 RID: 3801
		protected readonly Command m_command;
	}
}
