﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x0200001F RID: 31
	public class Sys_Carnival_Server
	{
		// Token: 0x170000EB RID: 235
		// (get) Token: 0x060001F4 RID: 500 RVA: 0x0000310C File Offset: 0x0000130C
		// (set) Token: 0x060001F5 RID: 501 RVA: 0x00003114 File Offset: 0x00001314
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x170000EC RID: 236
		// (get) Token: 0x060001F6 RID: 502 RVA: 0x0000311D File Offset: 0x0000131D
		// (set) Token: 0x060001F7 RID: 503 RVA: 0x00003125 File Offset: 0x00001325
		public int TurnRewardID1 { get; set; }

		// Token: 0x170000ED RID: 237
		// (get) Token: 0x060001F8 RID: 504 RVA: 0x0000312E File Offset: 0x0000132E
		// (set) Token: 0x060001F9 RID: 505 RVA: 0x00003136 File Offset: 0x00001336
		public int TurnRewardID2 { get; set; }

		// Token: 0x170000EE RID: 238
		// (get) Token: 0x060001FA RID: 506 RVA: 0x0000313F File Offset: 0x0000133F
		// (set) Token: 0x060001FB RID: 507 RVA: 0x00003147 File Offset: 0x00001347
		public int JoinCount { get; set; }

		// Token: 0x170000EF RID: 239
		// (get) Token: 0x060001FC RID: 508 RVA: 0x00003150 File Offset: 0x00001350
		// (set) Token: 0x060001FD RID: 509 RVA: 0x00003158 File Offset: 0x00001358
		public int JoinPeople { get; set; }

		// Token: 0x170000F0 RID: 240
		// (get) Token: 0x060001FE RID: 510 RVA: 0x00003161 File Offset: 0x00001361
		// (set) Token: 0x060001FF RID: 511 RVA: 0x00003169 File Offset: 0x00001369
		public int PoolRemainCount { get; set; }

		// Token: 0x170000F1 RID: 241
		// (get) Token: 0x06000200 RID: 512 RVA: 0x00003172 File Offset: 0x00001372
		// (set) Token: 0x06000201 RID: 513 RVA: 0x0000317A File Offset: 0x0000137A
		public int PoolRemainID { get; set; }
	}
}
