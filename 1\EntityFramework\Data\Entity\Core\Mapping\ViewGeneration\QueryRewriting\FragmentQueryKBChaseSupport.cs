﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Common.Utils.Boolean;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Linq;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.QueryRewriting
{
	// Token: 0x02000587 RID: 1415
	internal class FragmentQueryKBChaseSupport : FragmentQueryKB
	{
		// Token: 0x0600447A RID: 17530 RVA: 0x000F007C File Offset: 0x000EE27C
		internal FragmentQueryKBChaseSupport()
		{
			this._chase = new FragmentQueryKBChaseSupport.AtomicConditionRuleChase(this);
		}

		// Token: 0x17000D8C RID: 3468
		// (get) Token: 0x0600447B RID: 17531 RVA: 0x000F00A4 File Offset: 0x000EE2A4
		internal Dictionary<TermExpr<DomainConstraint<BoolLiteral, Constant>>, BoolExpr<DomainConstraint<BoolLiteral, Constant>>> Implications
		{
			get
			{
				if (this._implications == null)
				{
					this._implications = new Dictionary<TermExpr<DomainConstraint<BoolLiteral, Constant>>, BoolExpr<DomainConstraint<BoolLiteral, Constant>>>();
					foreach (BoolExpr<DomainConstraint<BoolLiteral, Constant>> boolExpr in base.Facts)
					{
						this.CacheFact(boolExpr);
					}
				}
				return this._implications;
			}
		}

		// Token: 0x0600447C RID: 17532 RVA: 0x000F010C File Offset: 0x000EE30C
		internal override void AddFact(BoolExpr<DomainConstraint<BoolLiteral, Constant>> fact)
		{
			base.AddFact(fact);
			this._kbSize += fact.CountTerms();
			if (this._implications != null)
			{
				this.CacheFact(fact);
			}
		}

		// Token: 0x0600447D RID: 17533 RVA: 0x000F0138 File Offset: 0x000EE338
		private void CacheFact(BoolExpr<DomainConstraint<BoolLiteral, Constant>> fact)
		{
			KnowledgeBase<DomainConstraint<BoolLiteral, Constant>>.Implication implication = fact as KnowledgeBase<DomainConstraint<BoolLiteral, Constant>>.Implication;
			KnowledgeBase<DomainConstraint<BoolLiteral, Constant>>.Equivalence equivalence = fact as KnowledgeBase<DomainConstraint<BoolLiteral, Constant>>.Equivalence;
			if (implication != null)
			{
				this.CacheImplication(implication.Condition, implication.Implies);
				return;
			}
			if (equivalence != null)
			{
				this.CacheImplication(equivalence.Left, equivalence.Right);
				this.CacheImplication(equivalence.Right, equivalence.Left);
				return;
			}
			this.CacheResidualFact(fact);
		}

		// Token: 0x17000D8D RID: 3469
		// (get) Token: 0x0600447E RID: 17534 RVA: 0x000F0198 File Offset: 0x000EE398
		private IEnumerable<BoolExpr<DomainConstraint<BoolLiteral, Constant>>> ResidueInternal
		{
			get
			{
				if (this._residueSize < 0 && this._residualFacts.Count > 0)
				{
					this.PrepareResidue();
				}
				return this._residualFacts;
			}
		}

		// Token: 0x17000D8E RID: 3470
		// (get) Token: 0x0600447F RID: 17535 RVA: 0x000F01BD File Offset: 0x000EE3BD
		private int ResidueSize
		{
			get
			{
				if (this._residueSize < 0)
				{
					this.PrepareResidue();
				}
				return this._residueSize;
			}
		}

		// Token: 0x06004480 RID: 17536 RVA: 0x000F01D4 File Offset: 0x000EE3D4
		internal BoolExpr<DomainConstraint<BoolLiteral, Constant>> Chase(TermExpr<DomainConstraint<BoolLiteral, Constant>> expression)
		{
			BoolExpr<DomainConstraint<BoolLiteral, Constant>> boolExpr;
			this.Implications.TryGetValue(expression, out boolExpr);
			return new AndExpr<DomainConstraint<BoolLiteral, Constant>>(new BoolExpr<DomainConstraint<BoolLiteral, Constant>>[]
			{
				expression,
				boolExpr ?? TrueExpr<DomainConstraint<BoolLiteral, Constant>>.Value
			});
		}

		// Token: 0x06004481 RID: 17537 RVA: 0x000F020C File Offset: 0x000EE40C
		internal bool IsSatisfiable(BoolExpr<DomainConstraint<BoolLiteral, Constant>> expression)
		{
			ConversionContext<DomainConstraint<BoolLiteral, Constant>> conversionContext = IdentifierService<DomainConstraint<BoolLiteral, Constant>>.Instance.CreateConversionContext();
			Converter<DomainConstraint<BoolLiteral, Constant>> converter = new Converter<DomainConstraint<BoolLiteral, Constant>>(expression, conversionContext);
			if (converter.Vertex.IsZero())
			{
				return false;
			}
			if (base.KbExpression.ExprType == ExprType.True)
			{
				return true;
			}
			int num = expression.CountTerms() + this._kbSize;
			BoolExpr<DomainConstraint<BoolLiteral, Constant>> expr = converter.Dnf.Expr;
			BoolExpr<DomainConstraint<BoolLiteral, Constant>> boolExpr = ((FragmentQueryKBChaseSupport.Normalizer.EstimateNnfAndSplitTermCount(expr) > FragmentQueryKBChaseSupport.Normalizer.EstimateNnfAndSplitTermCount(expression)) ? expression : expr);
			BoolExpr<DomainConstraint<BoolLiteral, Constant>> boolExpr2 = this._chase.Chase(FragmentQueryKBChaseSupport.Normalizer.ToNnfAndSplitRange(boolExpr));
			BoolExpr<DomainConstraint<BoolLiteral, Constant>> boolExpr3;
			if (boolExpr2.CountTerms() + this.ResidueSize > num)
			{
				boolExpr3 = new AndExpr<DomainConstraint<BoolLiteral, Constant>>(new BoolExpr<DomainConstraint<BoolLiteral, Constant>>[] { base.KbExpression, expression });
			}
			else
			{
				boolExpr3 = new AndExpr<DomainConstraint<BoolLiteral, Constant>>(new List<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>(this.ResidueInternal) { boolExpr2 });
				conversionContext = IdentifierService<DomainConstraint<BoolLiteral, Constant>>.Instance.CreateConversionContext();
			}
			return !new Converter<DomainConstraint<BoolLiteral, Constant>>(boolExpr3, conversionContext).Vertex.IsZero();
		}

		// Token: 0x06004482 RID: 17538 RVA: 0x000F02F4 File Offset: 0x000EE4F4
		internal BoolExpr<DomainConstraint<BoolLiteral, Constant>> Chase(BoolExpr<DomainConstraint<BoolLiteral, Constant>> expression)
		{
			if (this.Implications.Count != 0)
			{
				return this._chase.Chase(FragmentQueryKBChaseSupport.Normalizer.ToNnfAndSplitRange(expression));
			}
			return expression;
		}

		// Token: 0x06004483 RID: 17539 RVA: 0x000F0318 File Offset: 0x000EE518
		private void CacheImplication(BoolExpr<DomainConstraint<BoolLiteral, Constant>> condition, BoolExpr<DomainConstraint<BoolLiteral, Constant>> implies)
		{
			BoolExpr<DomainConstraint<BoolLiteral, Constant>> boolExpr = FragmentQueryKBChaseSupport.Normalizer.ToDnf(condition, false);
			BoolExpr<DomainConstraint<BoolLiteral, Constant>> boolExpr2 = FragmentQueryKBChaseSupport.Normalizer.ToNnfAndSplitRange(implies);
			ExprType exprType = boolExpr.ExprType;
			if (exprType != ExprType.Or)
			{
				if (exprType != ExprType.Term)
				{
					this.CacheResidualFact(new OrExpr<DomainConstraint<BoolLiteral, Constant>>(new BoolExpr<DomainConstraint<BoolLiteral, Constant>>[]
					{
						new NotExpr<DomainConstraint<BoolLiteral, Constant>>(condition),
						implies
					}));
					return;
				}
			}
			else
			{
				using (HashSet<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>.Enumerator enumerator = ((OrExpr<DomainConstraint<BoolLiteral, Constant>>)boolExpr).Children.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						BoolExpr<DomainConstraint<BoolLiteral, Constant>> boolExpr3 = enumerator.Current;
						if (boolExpr3.ExprType != ExprType.Term)
						{
							this.CacheResidualFact(new OrExpr<DomainConstraint<BoolLiteral, Constant>>(new BoolExpr<DomainConstraint<BoolLiteral, Constant>>[]
							{
								new NotExpr<DomainConstraint<BoolLiteral, Constant>>(boolExpr3),
								implies
							}));
						}
						else
						{
							this.CacheNormalizedImplication((TermExpr<DomainConstraint<BoolLiteral, Constant>>)boolExpr3, boolExpr2);
						}
					}
					return;
				}
			}
			this.CacheNormalizedImplication((TermExpr<DomainConstraint<BoolLiteral, Constant>>)boolExpr, boolExpr2);
		}

		// Token: 0x06004484 RID: 17540 RVA: 0x000F03F0 File Offset: 0x000EE5F0
		private void CacheNormalizedImplication(TermExpr<DomainConstraint<BoolLiteral, Constant>> condition, BoolExpr<DomainConstraint<BoolLiteral, Constant>> implies)
		{
			foreach (TermExpr<DomainConstraint<BoolLiteral, Constant>> termExpr in this.Implications.Keys)
			{
				if (termExpr.Identifier.Variable.Equals(condition.Identifier.Variable) && !termExpr.Identifier.Range.SetEquals(condition.Identifier.Range))
				{
					this.CacheResidualFact(new OrExpr<DomainConstraint<BoolLiteral, Constant>>(new BoolExpr<DomainConstraint<BoolLiteral, Constant>>[]
					{
						new NotExpr<DomainConstraint<BoolLiteral, Constant>>(condition),
						implies
					}));
					return;
				}
			}
			BoolExpr<DomainConstraint<BoolLiteral, Constant>> expr = new Converter<DomainConstraint<BoolLiteral, Constant>>(this.Chase(implies), IdentifierService<DomainConstraint<BoolLiteral, Constant>>.Instance.CreateConversionContext()).Dnf.Expr;
			FragmentQueryKBChaseSupport fragmentQueryKBChaseSupport = new FragmentQueryKBChaseSupport();
			fragmentQueryKBChaseSupport.Implications[condition] = expr;
			bool flag = true;
			foreach (TermExpr<DomainConstraint<BoolLiteral, Constant>> termExpr2 in new Set<TermExpr<DomainConstraint<BoolLiteral, Constant>>>(this.Implications.Keys))
			{
				BoolExpr<DomainConstraint<BoolLiteral, Constant>> boolExpr = fragmentQueryKBChaseSupport.Chase(this.Implications[termExpr2]);
				if (termExpr2.Equals(condition))
				{
					flag = false;
					boolExpr = new AndExpr<DomainConstraint<BoolLiteral, Constant>>(new BoolExpr<DomainConstraint<BoolLiteral, Constant>>[] { boolExpr, expr });
				}
				this.Implications[termExpr2] = new Converter<DomainConstraint<BoolLiteral, Constant>>(boolExpr, IdentifierService<DomainConstraint<BoolLiteral, Constant>>.Instance.CreateConversionContext()).Dnf.Expr;
			}
			if (flag)
			{
				this.Implications[condition] = expr;
			}
			this._residueSize = -1;
		}

		// Token: 0x06004485 RID: 17541 RVA: 0x000F0594 File Offset: 0x000EE794
		private void CacheResidualFact(BoolExpr<DomainConstraint<BoolLiteral, Constant>> fact)
		{
			this._residualFacts.Add(fact);
			this._residueSize = -1;
		}

		// Token: 0x06004486 RID: 17542 RVA: 0x000F05AC File Offset: 0x000EE7AC
		private void PrepareResidue()
		{
			int num = 0;
			if (this.Implications.Count > 0 && this._residualFacts.Count > 0)
			{
				Set<BoolExpr<DomainConstraint<BoolLiteral, Constant>>> set = new Set<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>();
				foreach (BoolExpr<DomainConstraint<BoolLiteral, Constant>> boolExpr in this._residualFacts)
				{
					BoolExpr<DomainConstraint<BoolLiteral, Constant>> expr = new Converter<DomainConstraint<BoolLiteral, Constant>>(this.Chase(boolExpr), IdentifierService<DomainConstraint<BoolLiteral, Constant>>.Instance.CreateConversionContext()).Dnf.Expr;
					set.Add(expr);
					num += expr.CountTerms();
					this._residueSize = num;
				}
				this._residualFacts = set;
			}
			this._residueSize = num;
		}

		// Token: 0x040018A5 RID: 6309
		private Dictionary<TermExpr<DomainConstraint<BoolLiteral, Constant>>, BoolExpr<DomainConstraint<BoolLiteral, Constant>>> _implications;

		// Token: 0x040018A6 RID: 6310
		private readonly FragmentQueryKBChaseSupport.AtomicConditionRuleChase _chase;

		// Token: 0x040018A7 RID: 6311
		private Set<BoolExpr<DomainConstraint<BoolLiteral, Constant>>> _residualFacts = new Set<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>();

		// Token: 0x040018A8 RID: 6312
		private int _kbSize;

		// Token: 0x040018A9 RID: 6313
		private int _residueSize = -1;

		// Token: 0x02000B91 RID: 2961
		private static class Normalizer
		{
			// Token: 0x060066C7 RID: 26311 RVA: 0x0015F883 File Offset: 0x0015DA83
			internal static BoolExpr<DomainConstraint<BoolLiteral, Constant>> ToNnfAndSplitRange(BoolExpr<DomainConstraint<BoolLiteral, Constant>> expr)
			{
				return expr.Accept<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>(FragmentQueryKBChaseSupport.Normalizer.NonNegatedTreeVisitor.Instance);
			}

			// Token: 0x060066C8 RID: 26312 RVA: 0x0015F890 File Offset: 0x0015DA90
			internal static int EstimateNnfAndSplitTermCount(BoolExpr<DomainConstraint<BoolLiteral, Constant>> expr)
			{
				return expr.Accept<int>(FragmentQueryKBChaseSupport.Normalizer.NonNegatedNnfSplitCounter.Instance);
			}

			// Token: 0x060066C9 RID: 26313 RVA: 0x0015F89D File Offset: 0x0015DA9D
			internal static BoolExpr<DomainConstraint<BoolLiteral, Constant>> ToDnf(BoolExpr<DomainConstraint<BoolLiteral, Constant>> expr, bool isNnf)
			{
				if (!isNnf)
				{
					expr = FragmentQueryKBChaseSupport.Normalizer.ToNnfAndSplitRange(expr);
				}
				return expr.Accept<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>(FragmentQueryKBChaseSupport.Normalizer.DnfTreeVisitor.Instance);
			}

			// Token: 0x02000D7C RID: 3452
			private class NonNegatedTreeVisitor : BasicVisitor<DomainConstraint<BoolLiteral, Constant>>
			{
				// Token: 0x06006F35 RID: 28469 RVA: 0x0017C4FB File Offset: 0x0017A6FB
				private NonNegatedTreeVisitor()
				{
				}

				// Token: 0x06006F36 RID: 28470 RVA: 0x0017C503 File Offset: 0x0017A703
				internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> VisitNot(NotExpr<DomainConstraint<BoolLiteral, Constant>> expr)
				{
					return expr.Child.Accept<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>(FragmentQueryKBChaseSupport.Normalizer.NegatedTreeVisitor.Instance);
				}

				// Token: 0x06006F37 RID: 28471 RVA: 0x0017C518 File Offset: 0x0017A718
				internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> VisitTerm(TermExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					int count = expression.Identifier.Range.Count;
					if (count == 0)
					{
						return FalseExpr<DomainConstraint<BoolLiteral, Constant>>.Value;
					}
					if (count != 1)
					{
						List<BoolExpr<DomainConstraint<BoolLiteral, Constant>>> list = new List<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>();
						DomainVariable<BoolLiteral, Constant> variable = expression.Identifier.Variable;
						foreach (Constant constant in expression.Identifier.Range)
						{
							list.Add(new DomainConstraint<BoolLiteral, Constant>(variable, new Set<Constant>(new Constant[] { constant }, Constant.EqualityComparer)));
						}
						return new OrExpr<DomainConstraint<BoolLiteral, Constant>>(list);
					}
					return expression;
				}

				// Token: 0x04003338 RID: 13112
				internal static readonly FragmentQueryKBChaseSupport.Normalizer.NonNegatedTreeVisitor Instance = new FragmentQueryKBChaseSupport.Normalizer.NonNegatedTreeVisitor();
			}

			// Token: 0x02000D7D RID: 3453
			private class NegatedTreeVisitor : Visitor<DomainConstraint<BoolLiteral, Constant>, BoolExpr<DomainConstraint<BoolLiteral, Constant>>>
			{
				// Token: 0x06006F39 RID: 28473 RVA: 0x0017C5D8 File Offset: 0x0017A7D8
				private NegatedTreeVisitor()
				{
				}

				// Token: 0x06006F3A RID: 28474 RVA: 0x0017C5E0 File Offset: 0x0017A7E0
				internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> VisitTrue(TrueExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					return FalseExpr<DomainConstraint<BoolLiteral, Constant>>.Value;
				}

				// Token: 0x06006F3B RID: 28475 RVA: 0x0017C5E7 File Offset: 0x0017A7E7
				internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> VisitFalse(FalseExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					return TrueExpr<DomainConstraint<BoolLiteral, Constant>>.Value;
				}

				// Token: 0x06006F3C RID: 28476 RVA: 0x0017C5EE File Offset: 0x0017A7EE
				internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> VisitNot(NotExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					return expression.Child.Accept<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>(FragmentQueryKBChaseSupport.Normalizer.NonNegatedTreeVisitor.Instance);
				}

				// Token: 0x06006F3D RID: 28477 RVA: 0x0017C600 File Offset: 0x0017A800
				internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> VisitAnd(AndExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					return new OrExpr<DomainConstraint<BoolLiteral, Constant>>(expression.Children.Select((BoolExpr<DomainConstraint<BoolLiteral, Constant>> child) => child.Accept<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>(this)));
				}

				// Token: 0x06006F3E RID: 28478 RVA: 0x0017C61E File Offset: 0x0017A81E
				internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> VisitOr(OrExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					return new AndExpr<DomainConstraint<BoolLiteral, Constant>>(expression.Children.Select((BoolExpr<DomainConstraint<BoolLiteral, Constant>> child) => child.Accept<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>(this)));
				}

				// Token: 0x06006F3F RID: 28479 RVA: 0x0017C63C File Offset: 0x0017A83C
				internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> VisitTerm(TermExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					DomainConstraint<BoolLiteral, Constant> domainConstraint = expression.Identifier.InvertDomainConstraint();
					if (domainConstraint.Range.Count == 0)
					{
						return FalseExpr<DomainConstraint<BoolLiteral, Constant>>.Value;
					}
					List<BoolExpr<DomainConstraint<BoolLiteral, Constant>>> list = new List<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>();
					DomainVariable<BoolLiteral, Constant> variable = domainConstraint.Variable;
					foreach (Constant constant in domainConstraint.Range)
					{
						list.Add(new DomainConstraint<BoolLiteral, Constant>(variable, new Set<Constant>(new Constant[] { constant }, Constant.EqualityComparer)));
					}
					return new OrExpr<DomainConstraint<BoolLiteral, Constant>>(list);
				}

				// Token: 0x04003339 RID: 13113
				internal static readonly FragmentQueryKBChaseSupport.Normalizer.NegatedTreeVisitor Instance = new FragmentQueryKBChaseSupport.Normalizer.NegatedTreeVisitor();
			}

			// Token: 0x02000D7E RID: 3454
			private class NonNegatedNnfSplitCounter : TermCounter<DomainConstraint<BoolLiteral, Constant>>
			{
				// Token: 0x06006F43 RID: 28483 RVA: 0x0017C702 File Offset: 0x0017A902
				private NonNegatedNnfSplitCounter()
				{
				}

				// Token: 0x06006F44 RID: 28484 RVA: 0x0017C70A File Offset: 0x0017A90A
				internal override int VisitNot(NotExpr<DomainConstraint<BoolLiteral, Constant>> expr)
				{
					return expr.Child.Accept<int>(FragmentQueryKBChaseSupport.Normalizer.NegatedNnfSplitCountEstimator.Instance);
				}

				// Token: 0x06006F45 RID: 28485 RVA: 0x0017C71C File Offset: 0x0017A91C
				internal override int VisitTerm(TermExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					return expression.Identifier.Range.Count;
				}

				// Token: 0x0400333A RID: 13114
				internal static readonly FragmentQueryKBChaseSupport.Normalizer.NonNegatedNnfSplitCounter Instance = new FragmentQueryKBChaseSupport.Normalizer.NonNegatedNnfSplitCounter();
			}

			// Token: 0x02000D7F RID: 3455
			private class NegatedNnfSplitCountEstimator : TermCounter<DomainConstraint<BoolLiteral, Constant>>
			{
				// Token: 0x06006F47 RID: 28487 RVA: 0x0017C73A File Offset: 0x0017A93A
				private NegatedNnfSplitCountEstimator()
				{
				}

				// Token: 0x06006F48 RID: 28488 RVA: 0x0017C742 File Offset: 0x0017A942
				internal override int VisitNot(NotExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					return expression.Child.Accept<int>(FragmentQueryKBChaseSupport.Normalizer.NonNegatedNnfSplitCounter.Instance);
				}

				// Token: 0x06006F49 RID: 28489 RVA: 0x0017C754 File Offset: 0x0017A954
				internal override int VisitTerm(TermExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					return expression.Identifier.Variable.Domain.Count - expression.Identifier.Range.Count;
				}

				// Token: 0x0400333B RID: 13115
				internal static readonly FragmentQueryKBChaseSupport.Normalizer.NegatedNnfSplitCountEstimator Instance = new FragmentQueryKBChaseSupport.Normalizer.NegatedNnfSplitCountEstimator();
			}

			// Token: 0x02000D80 RID: 3456
			private class DnfTreeVisitor : BasicVisitor<DomainConstraint<BoolLiteral, Constant>>
			{
				// Token: 0x06006F4B RID: 28491 RVA: 0x0017C788 File Offset: 0x0017A988
				private DnfTreeVisitor()
				{
				}

				// Token: 0x06006F4C RID: 28492 RVA: 0x0017C790 File Offset: 0x0017A990
				internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> VisitNot(NotExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					return expression;
				}

				// Token: 0x06006F4D RID: 28493 RVA: 0x0017C794 File Offset: 0x0017A994
				internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> VisitAnd(AndExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					BoolExpr<DomainConstraint<BoolLiteral, Constant>> boolExpr = base.VisitAnd(expression);
					TreeExpr<DomainConstraint<BoolLiteral, Constant>> treeExpr = boolExpr as TreeExpr<DomainConstraint<BoolLiteral, Constant>>;
					if (treeExpr == null)
					{
						return boolExpr;
					}
					Set<BoolExpr<DomainConstraint<BoolLiteral, Constant>>> set = new Set<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>();
					Set<Set<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>> set2 = new Set<Set<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>>();
					foreach (BoolExpr<DomainConstraint<BoolLiteral, Constant>> boolExpr2 in treeExpr.Children)
					{
						OrExpr<DomainConstraint<BoolLiteral, Constant>> orExpr = boolExpr2 as OrExpr<DomainConstraint<BoolLiteral, Constant>>;
						if (orExpr != null)
						{
							set2.Add(new Set<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>(orExpr.Children));
						}
						else
						{
							set.Add(boolExpr2);
						}
					}
					set2.Add(new Set<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>(new BoolExpr<DomainConstraint<BoolLiteral, Constant>>[]
					{
						new AndExpr<DomainConstraint<BoolLiteral, Constant>>(set)
					}));
					IEnumerable<IEnumerable<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>> enumerable = new IEnumerable<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>[] { Enumerable.Empty<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>() };
					IEnumerable<IEnumerable<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>> enumerable2 = set2.Aggregate(enumerable, (IEnumerable<IEnumerable<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>> accumulator, Set<BoolExpr<DomainConstraint<BoolLiteral, Constant>>> bucket) => from accseq in accumulator
						from item in bucket
						select accseq.Concat(new BoolExpr<DomainConstraint<BoolLiteral, Constant>>[] { item }));
					List<BoolExpr<DomainConstraint<BoolLiteral, Constant>>> list = new List<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>();
					foreach (IEnumerable<BoolExpr<DomainConstraint<BoolLiteral, Constant>>> enumerable3 in enumerable2)
					{
						list.Add(new AndExpr<DomainConstraint<BoolLiteral, Constant>>(enumerable3));
					}
					return new OrExpr<DomainConstraint<BoolLiteral, Constant>>(list);
				}

				// Token: 0x0400333C RID: 13116
				internal static readonly FragmentQueryKBChaseSupport.Normalizer.DnfTreeVisitor Instance = new FragmentQueryKBChaseSupport.Normalizer.DnfTreeVisitor();
			}
		}

		// Token: 0x02000B92 RID: 2962
		private class AtomicConditionRuleChase
		{
			// Token: 0x060066CA RID: 26314 RVA: 0x0015F8B5 File Offset: 0x0015DAB5
			internal AtomicConditionRuleChase(FragmentQueryKBChaseSupport kb)
			{
				this._visitor = new FragmentQueryKBChaseSupport.AtomicConditionRuleChase.NonNegatedDomainConstraintTreeVisitor(kb);
			}

			// Token: 0x060066CB RID: 26315 RVA: 0x0015F8C9 File Offset: 0x0015DAC9
			internal BoolExpr<DomainConstraint<BoolLiteral, Constant>> Chase(BoolExpr<DomainConstraint<BoolLiteral, Constant>> expression)
			{
				return expression.Accept<BoolExpr<DomainConstraint<BoolLiteral, Constant>>>(this._visitor);
			}

			// Token: 0x04002E24 RID: 11812
			private readonly FragmentQueryKBChaseSupport.AtomicConditionRuleChase.NonNegatedDomainConstraintTreeVisitor _visitor;

			// Token: 0x02000D81 RID: 3457
			private class NonNegatedDomainConstraintTreeVisitor : BasicVisitor<DomainConstraint<BoolLiteral, Constant>>
			{
				// Token: 0x06006F4F RID: 28495 RVA: 0x0017C8DC File Offset: 0x0017AADC
				internal NonNegatedDomainConstraintTreeVisitor(FragmentQueryKBChaseSupport kb)
				{
					this._kb = kb;
				}

				// Token: 0x06006F50 RID: 28496 RVA: 0x0017C8EB File Offset: 0x0017AAEB
				internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> VisitTerm(TermExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					return this._kb.Chase(expression);
				}

				// Token: 0x06006F51 RID: 28497 RVA: 0x0017C8F9 File Offset: 0x0017AAF9
				internal override BoolExpr<DomainConstraint<BoolLiteral, Constant>> VisitNot(NotExpr<DomainConstraint<BoolLiteral, Constant>> expression)
				{
					return base.VisitNot(expression);
				}

				// Token: 0x0400333D RID: 13117
				private readonly FragmentQueryKBChaseSupport _kb;
			}
		}
	}
}
