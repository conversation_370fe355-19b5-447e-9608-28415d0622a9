﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects.DataClasses;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Reflection;
using System.Reflection.Emit;

namespace System.Data.Entity.Core.Objects.Internal
{
	// Token: 0x02000449 RID: 1097
	internal class IPocoImplementor
	{
		// Token: 0x0600358F RID: 13711 RVA: 0x000AAEB0 File Offset: 0x000A90B0
		public IPocoImplementor(EntityType ospaceEntityType)
		{
			Type clrType = ospaceEntityType.ClrType;
			this._referenceProperties = new List<KeyValuePair<NavigationProperty, PropertyInfo>>();
			this._collectionProperties = new List<KeyValuePair<NavigationProperty, PropertyInfo>>();
			this._implementIEntityWithChangeTracker = null == clrType.GetInterface(typeof(IEntityWithChangeTracker).Name);
			this._implementIEntityWithRelationships = null == clrType.GetInterface(typeof(IEntityWithRelationships).Name);
			this.CheckType(ospaceEntityType);
			this._ospaceEntityType = ospaceEntityType;
		}

		// Token: 0x06003590 RID: 13712 RVA: 0x000AAF30 File Offset: 0x000A9130
		private void CheckType(EntityType ospaceEntityType)
		{
			this._scalarMembers = new HashSet<EdmMember>();
			this._relationshipMembers = new HashSet<EdmMember>();
			foreach (EdmMember edmMember in ospaceEntityType.Members)
			{
				PropertyInfo topProperty = ospaceEntityType.ClrType.GetTopProperty(edmMember.Name);
				if (topProperty != null && EntityProxyFactory.CanProxySetter(topProperty))
				{
					if (edmMember.BuiltInTypeKind == BuiltInTypeKind.EdmProperty)
					{
						if (this._implementIEntityWithChangeTracker)
						{
							this._scalarMembers.Add(edmMember);
						}
					}
					else if (edmMember.BuiltInTypeKind == BuiltInTypeKind.NavigationProperty && this._implementIEntityWithRelationships)
					{
						if (((NavigationProperty)edmMember).ToEndMember.RelationshipMultiplicity == RelationshipMultiplicity.Many)
						{
							if (topProperty.PropertyType.IsGenericType() && topProperty.PropertyType.GetGenericTypeDefinition() == typeof(ICollection<>))
							{
								this._relationshipMembers.Add(edmMember);
							}
						}
						else
						{
							this._relationshipMembers.Add(edmMember);
						}
					}
				}
			}
			if (ospaceEntityType.Members.Count != this._scalarMembers.Count + this._relationshipMembers.Count)
			{
				this._scalarMembers.Clear();
				this._relationshipMembers.Clear();
				this._implementIEntityWithChangeTracker = false;
				this._implementIEntityWithRelationships = false;
			}
		}

		// Token: 0x06003591 RID: 13713 RVA: 0x000AB094 File Offset: 0x000A9294
		public void Implement(TypeBuilder typeBuilder, Action<FieldBuilder, bool> registerField)
		{
			if (this._implementIEntityWithChangeTracker)
			{
				this.ImplementIEntityWithChangeTracker(typeBuilder, registerField);
			}
			if (this._implementIEntityWithRelationships)
			{
				this.ImplementIEntityWithRelationships(typeBuilder, registerField);
			}
			this._resetFKSetterFlagField = typeBuilder.DefineField("_resetFKSetterFlag", typeof(Action<object>), FieldAttributes.Private | FieldAttributes.Static);
			this._compareByteArraysField = typeBuilder.DefineField("_compareByteArrays", typeof(Func<object, object, bool>), FieldAttributes.Private | FieldAttributes.Static);
		}

		// Token: 0x17000A55 RID: 2645
		// (get) Token: 0x06003592 RID: 13714 RVA: 0x000AB0FC File Offset: 0x000A92FC
		public Type[] Interfaces
		{
			get
			{
				List<Type> list = new List<Type>();
				if (this._implementIEntityWithChangeTracker)
				{
					list.Add(typeof(IEntityWithChangeTracker));
				}
				if (this._implementIEntityWithRelationships)
				{
					list.Add(typeof(IEntityWithRelationships));
				}
				return list.ToArray();
			}
		}

		// Token: 0x06003593 RID: 13715 RVA: 0x000AB145 File Offset: 0x000A9345
		private static DynamicMethod CreateDynamicMethod(string name, Type returnType, Type[] parameterTypes)
		{
			return new DynamicMethod(name, returnType, parameterTypes, true);
		}

		// Token: 0x06003594 RID: 13716 RVA: 0x000AB150 File Offset: 0x000A9350
		public DynamicMethod CreateInitializeCollectionMethod(Type proxyType)
		{
			if (this._collectionProperties.Count > 0)
			{
				DynamicMethod dynamicMethod = IPocoImplementor.CreateDynamicMethod(proxyType.Name + "_InitializeEntityCollections", typeof(IEntityWrapper), new Type[] { typeof(IEntityWrapper) });
				ILGenerator ilgenerator = dynamicMethod.GetILGenerator();
				ilgenerator.DeclareLocal(proxyType);
				ilgenerator.DeclareLocal(typeof(RelationshipManager));
				ilgenerator.Emit(OpCodes.Ldarg_0);
				ilgenerator.Emit(OpCodes.Callvirt, IPocoImplementor.GetEntityMethod);
				ilgenerator.Emit(OpCodes.Castclass, proxyType);
				ilgenerator.Emit(OpCodes.Stloc_0);
				ilgenerator.Emit(OpCodes.Ldloc_0);
				ilgenerator.Emit(OpCodes.Callvirt, IPocoImplementor.GetRelationshipManagerMethod);
				ilgenerator.Emit(OpCodes.Stloc_1);
				foreach (KeyValuePair<NavigationProperty, PropertyInfo> keyValuePair in this._collectionProperties)
				{
					MethodInfo methodInfo = IPocoImplementor.GetRelatedCollectionMethod.MakeGenericMethod(new Type[] { EntityUtil.GetCollectionElementType(keyValuePair.Value.PropertyType) });
					ilgenerator.Emit(OpCodes.Ldloc_0);
					ilgenerator.Emit(OpCodes.Ldloc_1);
					ilgenerator.Emit(OpCodes.Ldstr, keyValuePair.Key.RelationshipType.FullName);
					ilgenerator.Emit(OpCodes.Ldstr, keyValuePair.Key.ToEndMember.Name);
					ilgenerator.Emit(OpCodes.Callvirt, methodInfo);
					ilgenerator.Emit(OpCodes.Callvirt, keyValuePair.Value.Setter());
				}
				ilgenerator.Emit(OpCodes.Ldarg_0);
				ilgenerator.Emit(OpCodes.Ret);
				return dynamicMethod;
			}
			return null;
		}

		// Token: 0x06003595 RID: 13717 RVA: 0x000AB310 File Offset: 0x000A9510
		public bool CanProxyMember(EdmMember member)
		{
			return this._scalarMembers.Contains(member) || this._relationshipMembers.Contains(member);
		}

		// Token: 0x06003596 RID: 13718 RVA: 0x000AB330 File Offset: 0x000A9530
		public bool EmitMember(TypeBuilder typeBuilder, EdmMember member, PropertyBuilder propertyBuilder, PropertyInfo baseProperty, BaseProxyImplementor baseImplementor)
		{
			if (this._scalarMembers.Contains(member))
			{
				bool flag = this._ospaceEntityType.KeyMembers.Contains(member.Identity);
				this.EmitScalarSetter(typeBuilder, propertyBuilder, baseProperty, flag);
				return true;
			}
			if (this._relationshipMembers.Contains(member))
			{
				NavigationProperty navigationProperty = member as NavigationProperty;
				if (navigationProperty.ToEndMember.RelationshipMultiplicity == RelationshipMultiplicity.Many)
				{
					this.EmitCollectionProperty(typeBuilder, propertyBuilder, baseProperty, navigationProperty);
				}
				else
				{
					this.EmitReferenceProperty(typeBuilder, propertyBuilder, baseProperty, navigationProperty);
				}
				baseImplementor.AddBasePropertySetter(baseProperty);
				return true;
			}
			return false;
		}

		// Token: 0x06003597 RID: 13719 RVA: 0x000AB3B8 File Offset: 0x000A95B8
		private void EmitScalarSetter(TypeBuilder typeBuilder, PropertyBuilder propertyBuilder, PropertyInfo baseProperty, bool isKeyMember)
		{
			MethodInfo methodInfo = baseProperty.Setter();
			MethodAttributes methodAttributes = methodInfo.Attributes & MethodAttributes.MemberAccessMask;
			MethodBuilder methodBuilder = typeBuilder.DefineMethod("set_" + baseProperty.Name, methodAttributes | (MethodAttributes.Virtual | MethodAttributes.HideBySig | MethodAttributes.SpecialName), null, new Type[] { baseProperty.PropertyType });
			ILGenerator ilgenerator = methodBuilder.GetILGenerator();
			Label label = ilgenerator.DefineLabel();
			if (isKeyMember)
			{
				MethodInfo methodInfo2 = baseProperty.Getter();
				if (methodInfo2 != null)
				{
					Type propertyType = baseProperty.PropertyType;
					if (propertyType == typeof(int) || propertyType == typeof(short) || propertyType == typeof(long) || propertyType == typeof(bool) || propertyType == typeof(byte) || propertyType == typeof(uint) || propertyType == typeof(ulong) || propertyType == typeof(float) || propertyType == typeof(double) || propertyType.IsEnum())
					{
						ilgenerator.Emit(OpCodes.Ldarg_0);
						ilgenerator.Emit(OpCodes.Call, methodInfo2);
						ilgenerator.Emit(OpCodes.Ldarg_1);
						ilgenerator.Emit(OpCodes.Beq_S, label);
					}
					else if (propertyType == typeof(byte[]))
					{
						ilgenerator.Emit(OpCodes.Ldsfld, this._compareByteArraysField);
						ilgenerator.Emit(OpCodes.Ldarg_0);
						ilgenerator.Emit(OpCodes.Call, methodInfo2);
						ilgenerator.Emit(OpCodes.Ldarg_1);
						ilgenerator.Emit(OpCodes.Callvirt, IPocoImplementor.FuncInvokeMethod);
						ilgenerator.Emit(OpCodes.Brtrue_S, label);
					}
					else
					{
						MethodInfo declaredMethod = propertyType.GetDeclaredMethod("op_Inequality", new Type[] { propertyType, propertyType });
						if (declaredMethod != null)
						{
							ilgenerator.Emit(OpCodes.Ldarg_0);
							ilgenerator.Emit(OpCodes.Call, methodInfo2);
							ilgenerator.Emit(OpCodes.Ldarg_1);
							ilgenerator.Emit(OpCodes.Call, declaredMethod);
							ilgenerator.Emit(OpCodes.Brfalse_S, label);
						}
						else
						{
							ilgenerator.Emit(OpCodes.Ldarg_0);
							ilgenerator.Emit(OpCodes.Call, methodInfo2);
							if (propertyType.IsValueType())
							{
								ilgenerator.Emit(OpCodes.Box, propertyType);
							}
							ilgenerator.Emit(OpCodes.Ldarg_1);
							if (propertyType.IsValueType())
							{
								ilgenerator.Emit(OpCodes.Box, propertyType);
							}
							ilgenerator.Emit(OpCodes.Call, IPocoImplementor.ObjectEqualsMethod);
							ilgenerator.Emit(OpCodes.Brtrue_S, label);
						}
					}
				}
			}
			ilgenerator.BeginExceptionBlock();
			ilgenerator.Emit(OpCodes.Ldarg_0);
			ilgenerator.Emit(OpCodes.Ldstr, baseProperty.Name);
			ilgenerator.Emit(OpCodes.Call, this._entityMemberChanging);
			ilgenerator.Emit(OpCodes.Ldarg_0);
			ilgenerator.Emit(OpCodes.Ldarg_1);
			ilgenerator.Emit(OpCodes.Call, methodInfo);
			ilgenerator.Emit(OpCodes.Ldarg_0);
			ilgenerator.Emit(OpCodes.Ldstr, baseProperty.Name);
			ilgenerator.Emit(OpCodes.Call, this._entityMemberChanged);
			ilgenerator.BeginFinallyBlock();
			ilgenerator.Emit(OpCodes.Ldsfld, this._resetFKSetterFlagField);
			ilgenerator.Emit(OpCodes.Ldarg_0);
			ilgenerator.Emit(OpCodes.Callvirt, IPocoImplementor.InvokeMethod);
			ilgenerator.EndExceptionBlock();
			ilgenerator.MarkLabel(label);
			ilgenerator.Emit(OpCodes.Ret);
			propertyBuilder.SetSetMethod(methodBuilder);
		}

		// Token: 0x06003598 RID: 13720 RVA: 0x000AB738 File Offset: 0x000A9938
		private void EmitReferenceProperty(TypeBuilder typeBuilder, PropertyBuilder propertyBuilder, PropertyInfo baseProperty, NavigationProperty navProperty)
		{
			MethodAttributes methodAttributes = baseProperty.Setter().Attributes & MethodAttributes.MemberAccessMask;
			MethodInfo methodInfo = IPocoImplementor.GetRelatedReferenceMethod.MakeGenericMethod(new Type[] { baseProperty.PropertyType });
			MethodInfo onlyDeclaredMethod = typeof(EntityReference<>).MakeGenericType(new Type[] { baseProperty.PropertyType }).GetOnlyDeclaredMethod("set_Value");
			MethodBuilder methodBuilder = typeBuilder.DefineMethod("set_" + baseProperty.Name, methodAttributes | (MethodAttributes.Virtual | MethodAttributes.HideBySig | MethodAttributes.SpecialName), null, new Type[] { baseProperty.PropertyType });
			ILGenerator ilgenerator = methodBuilder.GetILGenerator();
			ilgenerator.Emit(OpCodes.Ldarg_0);
			ilgenerator.Emit(OpCodes.Callvirt, this._getRelationshipManager);
			ilgenerator.Emit(OpCodes.Ldstr, navProperty.RelationshipType.FullName);
			ilgenerator.Emit(OpCodes.Ldstr, navProperty.ToEndMember.Name);
			ilgenerator.Emit(OpCodes.Callvirt, methodInfo);
			ilgenerator.Emit(OpCodes.Ldarg_1);
			ilgenerator.Emit(OpCodes.Callvirt, onlyDeclaredMethod);
			ilgenerator.Emit(OpCodes.Ret);
			propertyBuilder.SetSetMethod(methodBuilder);
			this._referenceProperties.Add(new KeyValuePair<NavigationProperty, PropertyInfo>(navProperty, baseProperty));
		}

		// Token: 0x06003599 RID: 13721 RVA: 0x000AB85C File Offset: 0x000A9A5C
		private void EmitCollectionProperty(TypeBuilder typeBuilder, PropertyBuilder propertyBuilder, PropertyInfo baseProperty, NavigationProperty navProperty)
		{
			MethodAttributes methodAttributes = baseProperty.Setter().Attributes & MethodAttributes.MemberAccessMask;
			string text = Strings.EntityProxyTypeInfo_CannotSetEntityCollectionProperty(propertyBuilder.Name, typeBuilder.Name);
			MethodBuilder methodBuilder = typeBuilder.DefineMethod("set_" + baseProperty.Name, methodAttributes | (MethodAttributes.Virtual | MethodAttributes.HideBySig | MethodAttributes.SpecialName), null, new Type[] { baseProperty.PropertyType });
			ILGenerator ilgenerator = methodBuilder.GetILGenerator();
			Label label = ilgenerator.DefineLabel();
			ilgenerator.Emit(OpCodes.Ldarg_1);
			ilgenerator.Emit(OpCodes.Ldarg_0);
			ilgenerator.Emit(OpCodes.Call, this._getRelationshipManager);
			ilgenerator.Emit(OpCodes.Ldstr, navProperty.RelationshipType.FullName);
			ilgenerator.Emit(OpCodes.Ldstr, navProperty.ToEndMember.Name);
			ilgenerator.Emit(OpCodes.Callvirt, IPocoImplementor.GetRelatedEndMethod);
			ilgenerator.Emit(OpCodes.Beq_S, label);
			ilgenerator.Emit(OpCodes.Ldstr, text);
			ilgenerator.Emit(OpCodes.Newobj, IPocoImplementor._invalidOperationConstructorMethod);
			ilgenerator.Emit(OpCodes.Throw);
			ilgenerator.MarkLabel(label);
			ilgenerator.Emit(OpCodes.Ldarg_0);
			ilgenerator.Emit(OpCodes.Ldarg_1);
			ilgenerator.Emit(OpCodes.Call, baseProperty.Setter());
			ilgenerator.Emit(OpCodes.Ret);
			propertyBuilder.SetSetMethod(methodBuilder);
			this._collectionProperties.Add(new KeyValuePair<NavigationProperty, PropertyInfo>(navProperty, baseProperty));
		}

		// Token: 0x0600359A RID: 13722 RVA: 0x000AB9B0 File Offset: 0x000A9BB0
		private void ImplementIEntityWithChangeTracker(TypeBuilder typeBuilder, Action<FieldBuilder, bool> registerField)
		{
			this._changeTrackerField = typeBuilder.DefineField("_changeTracker", typeof(IEntityChangeTracker), FieldAttributes.Private);
			registerField(this._changeTrackerField, false);
			this._entityMemberChanging = typeBuilder.DefineMethod("EntityMemberChanging", MethodAttributes.Private | MethodAttributes.HideBySig, typeof(void), new Type[] { typeof(string) });
			ILGenerator ilgenerator = this._entityMemberChanging.GetILGenerator();
			Label label = ilgenerator.DefineLabel();
			ilgenerator.Emit(OpCodes.Ldarg_0);
			ilgenerator.Emit(OpCodes.Ldfld, this._changeTrackerField);
			ilgenerator.Emit(OpCodes.Brfalse_S, label);
			ilgenerator.Emit(OpCodes.Ldarg_0);
			ilgenerator.Emit(OpCodes.Ldfld, this._changeTrackerField);
			ilgenerator.Emit(OpCodes.Ldarg_1);
			ilgenerator.Emit(OpCodes.Callvirt, IPocoImplementor.EntityMemberChangingMethod);
			ilgenerator.MarkLabel(label);
			ilgenerator.Emit(OpCodes.Ret);
			this._entityMemberChanged = typeBuilder.DefineMethod("EntityMemberChanged", MethodAttributes.Private | MethodAttributes.HideBySig, typeof(void), new Type[] { typeof(string) });
			ILGenerator ilgenerator2 = this._entityMemberChanged.GetILGenerator();
			label = ilgenerator2.DefineLabel();
			ilgenerator2.Emit(OpCodes.Ldarg_0);
			ilgenerator2.Emit(OpCodes.Ldfld, this._changeTrackerField);
			ilgenerator2.Emit(OpCodes.Brfalse_S, label);
			ilgenerator2.Emit(OpCodes.Ldarg_0);
			ilgenerator2.Emit(OpCodes.Ldfld, this._changeTrackerField);
			ilgenerator2.Emit(OpCodes.Ldarg_1);
			ilgenerator2.Emit(OpCodes.Callvirt, IPocoImplementor.EntityMemberChangedMethod);
			ilgenerator2.MarkLabel(label);
			ilgenerator2.Emit(OpCodes.Ret);
			MethodBuilder methodBuilder = typeBuilder.DefineMethod("IEntityWithChangeTracker.SetChangeTracker", MethodAttributes.Private | MethodAttributes.Final | MethodAttributes.Virtual | MethodAttributes.HideBySig | MethodAttributes.VtableLayoutMask, typeof(void), new Type[] { typeof(IEntityChangeTracker) });
			ILGenerator ilgenerator3 = methodBuilder.GetILGenerator();
			ilgenerator3.Emit(OpCodes.Ldarg_0);
			ilgenerator3.Emit(OpCodes.Ldarg_1);
			ilgenerator3.Emit(OpCodes.Stfld, this._changeTrackerField);
			ilgenerator3.Emit(OpCodes.Ret);
			typeBuilder.DefineMethodOverride(methodBuilder, IPocoImplementor.SetChangeTrackerMethod);
		}

		// Token: 0x0600359B RID: 13723 RVA: 0x000ABBC4 File Offset: 0x000A9DC4
		private void ImplementIEntityWithRelationships(TypeBuilder typeBuilder, Action<FieldBuilder, bool> registerField)
		{
			this._relationshipManagerField = typeBuilder.DefineField("_relationshipManager", typeof(RelationshipManager), FieldAttributes.Private);
			registerField(this._relationshipManagerField, true);
			PropertyBuilder propertyBuilder = typeBuilder.DefineProperty("RelationshipManager", PropertyAttributes.None, typeof(RelationshipManager), Type.EmptyTypes);
			this._getRelationshipManager = typeBuilder.DefineMethod("IEntityWithRelationships.get_RelationshipManager", MethodAttributes.Private | MethodAttributes.Final | MethodAttributes.Virtual | MethodAttributes.HideBySig | MethodAttributes.VtableLayoutMask | MethodAttributes.SpecialName, typeof(RelationshipManager), Type.EmptyTypes);
			ILGenerator ilgenerator = this._getRelationshipManager.GetILGenerator();
			Label label = ilgenerator.DefineLabel();
			ilgenerator.Emit(OpCodes.Ldarg_0);
			ilgenerator.Emit(OpCodes.Ldfld, this._relationshipManagerField);
			ilgenerator.Emit(OpCodes.Brtrue_S, label);
			ilgenerator.Emit(OpCodes.Ldarg_0);
			ilgenerator.Emit(OpCodes.Ldarg_0);
			ilgenerator.Emit(OpCodes.Call, IPocoImplementor.CreateRelationshipManagerMethod);
			ilgenerator.Emit(OpCodes.Stfld, this._relationshipManagerField);
			ilgenerator.MarkLabel(label);
			ilgenerator.Emit(OpCodes.Ldarg_0);
			ilgenerator.Emit(OpCodes.Ldfld, this._relationshipManagerField);
			ilgenerator.Emit(OpCodes.Ret);
			propertyBuilder.SetGetMethod(this._getRelationshipManager);
			typeBuilder.DefineMethodOverride(this._getRelationshipManager, IPocoImplementor.GetRelationshipManagerMethod);
		}

		// Token: 0x04001145 RID: 4421
		private readonly EntityType _ospaceEntityType;

		// Token: 0x04001146 RID: 4422
		private FieldBuilder _changeTrackerField;

		// Token: 0x04001147 RID: 4423
		private FieldBuilder _relationshipManagerField;

		// Token: 0x04001148 RID: 4424
		private FieldBuilder _resetFKSetterFlagField;

		// Token: 0x04001149 RID: 4425
		private FieldBuilder _compareByteArraysField;

		// Token: 0x0400114A RID: 4426
		private MethodBuilder _entityMemberChanging;

		// Token: 0x0400114B RID: 4427
		private MethodBuilder _entityMemberChanged;

		// Token: 0x0400114C RID: 4428
		private MethodBuilder _getRelationshipManager;

		// Token: 0x0400114D RID: 4429
		private readonly List<KeyValuePair<NavigationProperty, PropertyInfo>> _referenceProperties;

		// Token: 0x0400114E RID: 4430
		private readonly List<KeyValuePair<NavigationProperty, PropertyInfo>> _collectionProperties;

		// Token: 0x0400114F RID: 4431
		private bool _implementIEntityWithChangeTracker;

		// Token: 0x04001150 RID: 4432
		private bool _implementIEntityWithRelationships;

		// Token: 0x04001151 RID: 4433
		private HashSet<EdmMember> _scalarMembers;

		// Token: 0x04001152 RID: 4434
		private HashSet<EdmMember> _relationshipMembers;

		// Token: 0x04001153 RID: 4435
		internal static readonly MethodInfo EntityMemberChangingMethod = typeof(IEntityChangeTracker).GetDeclaredMethod("EntityMemberChanging", new Type[] { typeof(string) });

		// Token: 0x04001154 RID: 4436
		internal static readonly MethodInfo EntityMemberChangedMethod = typeof(IEntityChangeTracker).GetDeclaredMethod("EntityMemberChanged", new Type[] { typeof(string) });

		// Token: 0x04001155 RID: 4437
		internal static readonly MethodInfo CreateRelationshipManagerMethod = typeof(RelationshipManager).GetDeclaredMethod("Create", new Type[] { typeof(IEntityWithRelationships) });

		// Token: 0x04001156 RID: 4438
		internal static readonly MethodInfo GetRelationshipManagerMethod = typeof(IEntityWithRelationships).GetDeclaredProperty("RelationshipManager").Getter();

		// Token: 0x04001157 RID: 4439
		internal static readonly MethodInfo GetRelatedReferenceMethod = typeof(RelationshipManager).GetDeclaredMethod("GetRelatedReference", new Type[]
		{
			typeof(string),
			typeof(string)
		});

		// Token: 0x04001158 RID: 4440
		internal static readonly MethodInfo GetRelatedCollectionMethod = typeof(RelationshipManager).GetDeclaredMethod("GetRelatedCollection", new Type[]
		{
			typeof(string),
			typeof(string)
		});

		// Token: 0x04001159 RID: 4441
		internal static readonly MethodInfo GetRelatedEndMethod = typeof(RelationshipManager).GetDeclaredMethod("GetRelatedEnd", new Type[]
		{
			typeof(string),
			typeof(string)
		});

		// Token: 0x0400115A RID: 4442
		internal static readonly MethodInfo ObjectEqualsMethod = typeof(object).GetDeclaredMethod("Equals", new Type[]
		{
			typeof(object),
			typeof(object)
		});

		// Token: 0x0400115B RID: 4443
		private static readonly ConstructorInfo _invalidOperationConstructorMethod = typeof(InvalidOperationException).GetDeclaredConstructor(new Type[] { typeof(string) });

		// Token: 0x0400115C RID: 4444
		internal static readonly MethodInfo GetEntityMethod = typeof(IEntityWrapper).GetDeclaredProperty("Entity").Getter();

		// Token: 0x0400115D RID: 4445
		internal static readonly MethodInfo InvokeMethod = typeof(Action<object>).GetDeclaredMethod("Invoke", new Type[] { typeof(object) });

		// Token: 0x0400115E RID: 4446
		internal static readonly MethodInfo FuncInvokeMethod = typeof(Func<object, object, bool>).GetDeclaredMethod("Invoke", new Type[]
		{
			typeof(object),
			typeof(object)
		});

		// Token: 0x0400115F RID: 4447
		internal static readonly MethodInfo SetChangeTrackerMethod = typeof(IEntityWithChangeTracker).GetOnlyDeclaredMethod("SetChangeTracker");
	}
}
