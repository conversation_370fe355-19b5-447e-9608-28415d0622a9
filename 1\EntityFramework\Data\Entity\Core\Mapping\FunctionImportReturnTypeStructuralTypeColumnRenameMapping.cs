﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Linq;
using System.Xml;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x02000541 RID: 1345
	internal class FunctionImportReturnTypeStructuralTypeColumnRenameMapping
	{
		// Token: 0x06004210 RID: 16912 RVA: 0x000DEC74 File Offset: 0x000DCE74
		internal FunctionImportReturnTypeStructuralTypeColumnRenameMapping(string defaultMemberName)
		{
			this._defaultMemberName = defaultMemberName;
			this._columnListForType = new Collection<FunctionImportReturnTypeStructuralTypeColumn>();
			this._columnListForIsTypeOfType = new Collection<FunctionImportReturnTypeStructuralTypeColumn>();
			this._renameCache = new Memoizer<StructuralType, FunctionImportReturnTypeStructuralTypeColumn>(new Func<StructuralType, FunctionImportReturnTypeStructuralTypeColumn>(this.GetRename), EqualityComparer<StructuralType>.Default);
		}

		// Token: 0x06004211 RID: 16913 RVA: 0x000DECC0 File Offset: 0x000DCEC0
		internal string GetRename(EdmType type)
		{
			IXmlLineInfo xmlLineInfo;
			return this.GetRename(type, out xmlLineInfo);
		}

		// Token: 0x06004212 RID: 16914 RVA: 0x000DECD8 File Offset: 0x000DCED8
		internal string GetRename(EdmType type, out IXmlLineInfo lineInfo)
		{
			FunctionImportReturnTypeStructuralTypeColumn functionImportReturnTypeStructuralTypeColumn = this._renameCache.Evaluate(type as StructuralType);
			lineInfo = functionImportReturnTypeStructuralTypeColumn.LineInfo;
			return functionImportReturnTypeStructuralTypeColumn.ColumnName;
		}

		// Token: 0x06004213 RID: 16915 RVA: 0x000DED08 File Offset: 0x000DCF08
		private FunctionImportReturnTypeStructuralTypeColumn GetRename(StructuralType typeForRename)
		{
			FunctionImportReturnTypeStructuralTypeColumn functionImportReturnTypeStructuralTypeColumn = this._columnListForType.FirstOrDefault((FunctionImportReturnTypeStructuralTypeColumn t) => t.Type == typeForRename);
			if (functionImportReturnTypeStructuralTypeColumn != null)
			{
				return functionImportReturnTypeStructuralTypeColumn;
			}
			FunctionImportReturnTypeStructuralTypeColumn functionImportReturnTypeStructuralTypeColumn2 = this._columnListForIsTypeOfType.Where((FunctionImportReturnTypeStructuralTypeColumn t) => t.Type == typeForRename).LastOrDefault<FunctionImportReturnTypeStructuralTypeColumn>();
			if (functionImportReturnTypeStructuralTypeColumn2 != null)
			{
				return functionImportReturnTypeStructuralTypeColumn2;
			}
			IEnumerable<FunctionImportReturnTypeStructuralTypeColumn> enumerable = this._columnListForIsTypeOfType.Where((FunctionImportReturnTypeStructuralTypeColumn t) => t.Type.IsAssignableFrom(typeForRename));
			if (enumerable.Count<FunctionImportReturnTypeStructuralTypeColumn>() == 0)
			{
				return new FunctionImportReturnTypeStructuralTypeColumn(this._defaultMemberName, typeForRename, false, null);
			}
			return FunctionImportReturnTypeStructuralTypeColumnRenameMapping.GetLowestParentInHierarchy(enumerable);
		}

		// Token: 0x06004214 RID: 16916 RVA: 0x000DED9C File Offset: 0x000DCF9C
		private static FunctionImportReturnTypeStructuralTypeColumn GetLowestParentInHierarchy(IEnumerable<FunctionImportReturnTypeStructuralTypeColumn> nodesInHierarchy)
		{
			FunctionImportReturnTypeStructuralTypeColumn functionImportReturnTypeStructuralTypeColumn = null;
			foreach (FunctionImportReturnTypeStructuralTypeColumn functionImportReturnTypeStructuralTypeColumn2 in nodesInHierarchy)
			{
				if (functionImportReturnTypeStructuralTypeColumn == null)
				{
					functionImportReturnTypeStructuralTypeColumn = functionImportReturnTypeStructuralTypeColumn2;
				}
				else if (functionImportReturnTypeStructuralTypeColumn.Type.IsAssignableFrom(functionImportReturnTypeStructuralTypeColumn2.Type))
				{
					functionImportReturnTypeStructuralTypeColumn = functionImportReturnTypeStructuralTypeColumn2;
				}
			}
			return functionImportReturnTypeStructuralTypeColumn;
		}

		// Token: 0x06004215 RID: 16917 RVA: 0x000DEDFC File Offset: 0x000DCFFC
		internal void AddRename(FunctionImportReturnTypeStructuralTypeColumn renamedColumn)
		{
			if (!renamedColumn.IsTypeOf)
			{
				this._columnListForType.Add(renamedColumn);
				return;
			}
			this._columnListForIsTypeOfType.Add(renamedColumn);
		}

		// Token: 0x040016EA RID: 5866
		private readonly Collection<FunctionImportReturnTypeStructuralTypeColumn> _columnListForType;

		// Token: 0x040016EB RID: 5867
		private readonly Collection<FunctionImportReturnTypeStructuralTypeColumn> _columnListForIsTypeOfType;

		// Token: 0x040016EC RID: 5868
		private readonly string _defaultMemberName;

		// Token: 0x040016ED RID: 5869
		private readonly Memoizer<StructuralType, FunctionImportReturnTypeStructuralTypeColumn> _renameCache;
	}
}
