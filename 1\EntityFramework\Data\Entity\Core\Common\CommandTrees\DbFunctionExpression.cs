﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees.Internal;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006BF RID: 1727
	public class DbFunctionExpression : DbExpression
	{
		// Token: 0x060050ED RID: 20717 RVA: 0x001218AE File Offset: 0x0011FAAE
		internal DbFunctionExpression()
		{
		}

		// Token: 0x060050EE RID: 20718 RVA: 0x001218B6 File Offset: 0x0011FAB6
		internal DbFunctionExpression(TypeUsage resultType, EdmFunction function, DbExpressionList arguments)
			: base(DbExpressionKind.Function, resultType, true)
		{
			this._functionInfo = function;
			this._arguments = arguments;
		}

		// Token: 0x17000FB7 RID: 4023
		// (get) Token: 0x060050EF RID: 20719 RVA: 0x001218D0 File Offset: 0x0011FAD0
		public virtual EdmFunction Function
		{
			get
			{
				return this._functionInfo;
			}
		}

		// Token: 0x17000FB8 RID: 4024
		// (get) Token: 0x060050F0 RID: 20720 RVA: 0x001218D8 File Offset: 0x0011FAD8
		public virtual IList<DbExpression> Arguments
		{
			get
			{
				return this._arguments;
			}
		}

		// Token: 0x060050F1 RID: 20721 RVA: 0x001218E0 File Offset: 0x0011FAE0
		public override void Accept(DbExpressionVisitor visitor)
		{
			Check.NotNull<DbExpressionVisitor>(visitor, "visitor");
			visitor.Visit(this);
		}

		// Token: 0x060050F2 RID: 20722 RVA: 0x001218F5 File Offset: 0x0011FAF5
		public override TResultType Accept<TResultType>(DbExpressionVisitor<TResultType> visitor)
		{
			Check.NotNull<DbExpressionVisitor<TResultType>>(visitor, "visitor");
			return visitor.Visit(this);
		}

		// Token: 0x04001D9F RID: 7583
		private readonly EdmFunction _functionInfo;

		// Token: 0x04001DA0 RID: 7584
		private readonly DbExpressionList _arguments;
	}
}
