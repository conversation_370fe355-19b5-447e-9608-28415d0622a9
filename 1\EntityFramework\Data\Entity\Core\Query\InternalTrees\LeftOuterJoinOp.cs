﻿using System;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003B5 RID: 949
	internal sealed class LeftOuterJoinOp : JoinBaseOp
	{
		// Token: 0x06002DAC RID: 11692 RVA: 0x000912E8 File Offset: 0x0008F4E8
		private LeftOuterJoinOp()
			: base(OpType.LeftOuterJoin)
		{
		}

		// Token: 0x06002DAD RID: 11693 RVA: 0x000912F2 File Offset: 0x0008F4F2
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002DAE RID: 11694 RVA: 0x000912FC File Offset: 0x0008F4FC
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000F47 RID: 3911
		internal static readonly LeftOuterJoinOp Instance = new LeftOuterJoinOp();

		// Token: 0x04000F48 RID: 3912
		internal static readonly LeftOuterJoinOp Pattern = LeftOuterJoinOp.Instance;
	}
}
