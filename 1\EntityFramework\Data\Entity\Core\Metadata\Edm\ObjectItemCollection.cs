﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Reflection;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004E7 RID: 1255
	public class ObjectItemCollection : ItemCollection
	{
		// Token: 0x06003EA5 RID: 16037 RVA: 0x000CFAF8 File Offset: 0x000CDCF8
		public ObjectItemCollection()
			: this(null)
		{
		}

		// Token: 0x06003EA6 RID: 16038 RVA: 0x000CFB04 File Offset: 0x000CDD04
		internal ObjectItemCollection(KnownAssembliesSet knownAssembliesSet = null)
			: base(DataSpace.OSpace)
		{
			this._knownAssemblies = knownAssembliesSet ?? new KnownAssembliesSet();
			foreach (PrimitiveType primitiveType in ClrProviderManifest.Instance.GetStoreTypes())
			{
				base.AddInternal(primitiveType);
				this._primitiveTypeMaps.Add(primitiveType);
			}
		}

		// Token: 0x17000C47 RID: 3143
		// (get) Token: 0x06003EA7 RID: 16039 RVA: 0x000CFBA4 File Offset: 0x000CDDA4
		// (set) Token: 0x06003EA8 RID: 16040 RVA: 0x000CFBAC File Offset: 0x000CDDAC
		internal bool OSpaceTypesLoaded { get; set; }

		// Token: 0x17000C48 RID: 3144
		// (get) Token: 0x06003EA9 RID: 16041 RVA: 0x000CFBB5 File Offset: 0x000CDDB5
		internal object LoadAssemblyLock
		{
			get
			{
				return this._loadAssemblyLock;
			}
		}

		// Token: 0x06003EAA RID: 16042 RVA: 0x000CFBBD File Offset: 0x000CDDBD
		internal void ImplicitLoadAllReferencedAssemblies(Assembly assembly, EdmItemCollection edmItemCollection)
		{
			if (!MetadataAssemblyHelper.ShouldFilterAssembly(assembly))
			{
				this.LoadAssemblyFromCache(assembly, true, edmItemCollection, null);
			}
		}

		// Token: 0x06003EAB RID: 16043 RVA: 0x000CFBD2 File Offset: 0x000CDDD2
		public void LoadFromAssembly(Assembly assembly)
		{
			this.ExplicitLoadFromAssembly(assembly, null, null);
		}

		// Token: 0x06003EAC RID: 16044 RVA: 0x000CFBDD File Offset: 0x000CDDDD
		public void LoadFromAssembly(Assembly assembly, EdmItemCollection edmItemCollection, Action<string> logLoadMessage)
		{
			Check.NotNull<Assembly>(assembly, "assembly");
			Check.NotNull<EdmItemCollection>(edmItemCollection, "edmItemCollection");
			Check.NotNull<Action<string>>(logLoadMessage, "logLoadMessage");
			this.ExplicitLoadFromAssembly(assembly, edmItemCollection, logLoadMessage);
		}

		// Token: 0x06003EAD RID: 16045 RVA: 0x000CFC0C File Offset: 0x000CDE0C
		public void LoadFromAssembly(Assembly assembly, EdmItemCollection edmItemCollection)
		{
			Check.NotNull<Assembly>(assembly, "assembly");
			Check.NotNull<EdmItemCollection>(edmItemCollection, "edmItemCollection");
			this.ExplicitLoadFromAssembly(assembly, edmItemCollection, null);
		}

		// Token: 0x06003EAE RID: 16046 RVA: 0x000CFC2F File Offset: 0x000CDE2F
		internal void ExplicitLoadFromAssembly(Assembly assembly, EdmItemCollection edmItemCollection, Action<string> logLoadMessage)
		{
			this.LoadAssemblyFromCache(assembly, false, edmItemCollection, logLoadMessage);
		}

		// Token: 0x06003EAF RID: 16047 RVA: 0x000CFC3C File Offset: 0x000CDE3C
		internal bool ImplicitLoadAssemblyForType(Type type, EdmItemCollection edmItemCollection)
		{
			bool flag = false;
			if (!MetadataAssemblyHelper.ShouldFilterAssembly(type.Assembly()))
			{
				flag = this.LoadAssemblyFromCache(type.Assembly(), false, edmItemCollection, null);
			}
			if (type.IsGenericType())
			{
				foreach (Type type2 in type.GetGenericArguments())
				{
					flag |= this.ImplicitLoadAssemblyForType(type2, edmItemCollection);
				}
			}
			return flag;
		}

		// Token: 0x06003EB0 RID: 16048 RVA: 0x000CFC98 File Offset: 0x000CDE98
		internal AssociationType GetRelationshipType(string relationshipName)
		{
			AssociationType associationType;
			if (base.TryGetItem<AssociationType>(relationshipName, out associationType))
			{
				return associationType;
			}
			return null;
		}

		// Token: 0x06003EB1 RID: 16049 RVA: 0x000CFCB4 File Offset: 0x000CDEB4
		private bool LoadAssemblyFromCache(Assembly assembly, bool loadReferencedAssemblies, EdmItemCollection edmItemCollection, Action<string> logLoadMessage)
		{
			if (this.OSpaceTypesLoaded)
			{
				return true;
			}
			object obj;
			if (edmItemCollection != null)
			{
				ReadOnlyCollection<EntityContainer> items = edmItemCollection.GetItems<EntityContainer>();
				if (items.Any<EntityContainer>())
				{
					if (items.All((EntityContainer c) => c.Annotations.Any((MetadataProperty a) => a.Name == "http://schemas.microsoft.com/ado/2013/11/edm/customannotation:UseClrTypes" && ((string)a.Value).ToUpperInvariant() == "TRUE")))
					{
						obj = this.LoadAssemblyLock;
						lock (obj)
						{
							if (!this.OSpaceTypesLoaded)
							{
								new CodeFirstOSpaceLoader(null).LoadTypes(edmItemCollection, this);
							}
							return true;
						}
					}
				}
			}
			KnownAssemblyEntry knownAssemblyEntry;
			if (this._knownAssemblies.TryGetKnownAssembly(assembly, this._loaderCookie, edmItemCollection, out knownAssemblyEntry))
			{
				if (!loadReferencedAssemblies)
				{
					return knownAssemblyEntry.CacheEntry.TypesInAssembly.Count != 0;
				}
				if (knownAssemblyEntry.ReferencedAssembliesAreLoaded)
				{
					return true;
				}
			}
			obj = this.LoadAssemblyLock;
			bool flag2;
			lock (obj)
			{
				if (this._knownAssemblies.TryGetKnownAssembly(assembly, this._loaderCookie, edmItemCollection, out knownAssemblyEntry) && (!loadReferencedAssemblies || knownAssemblyEntry.ReferencedAssembliesAreLoaded))
				{
					flag2 = true;
				}
				else
				{
					KnownAssembliesSet knownAssembliesSet = new KnownAssembliesSet(this._knownAssemblies);
					Dictionary<string, EdmType> dictionary;
					List<EdmItemError> list;
					AssemblyCache.LoadAssembly(assembly, loadReferencedAssemblies, knownAssembliesSet, edmItemCollection, logLoadMessage, ref this._loaderCookie, out dictionary, out list);
					if (list.Count != 0)
					{
						throw EntityUtil.InvalidSchemaEncountered(Helper.CombineErrorMessage(list));
					}
					if (dictionary.Count != 0)
					{
						this.AddLoadedTypes(dictionary);
					}
					this._knownAssemblies = knownAssembliesSet;
					flag2 = dictionary.Count != 0;
				}
			}
			return flag2;
		}

		// Token: 0x06003EB2 RID: 16050 RVA: 0x000CFE34 File Offset: 0x000CE034
		internal virtual void AddLoadedTypes(Dictionary<string, EdmType> typesInLoading)
		{
			List<GlobalItem> list = new List<GlobalItem>();
			foreach (EdmType edmType in typesInLoading.Values)
			{
				list.Add(edmType);
				string text = "";
				try
				{
					if (Helper.IsEntityType(edmType))
					{
						text = ((ClrEntityType)edmType).CSpaceTypeName;
						this._ocMapping.Add(text, edmType);
					}
					else if (Helper.IsComplexType(edmType))
					{
						text = ((ClrComplexType)edmType).CSpaceTypeName;
						this._ocMapping.Add(text, edmType);
					}
					else if (Helper.IsEnumType(edmType))
					{
						text = ((ClrEnumType)edmType).CSpaceTypeName;
						this._ocMapping.Add(text, edmType);
					}
				}
				catch (ArgumentException ex)
				{
					throw new MappingException(Strings.Mapping_CannotMapCLRTypeMultipleTimes(text), ex);
				}
			}
			base.AddRange(list);
		}

		// Token: 0x06003EB3 RID: 16051 RVA: 0x000CFF28 File Offset: 0x000CE128
		public IEnumerable<PrimitiveType> GetPrimitiveTypes()
		{
			return this._primitiveTypeMaps.GetTypes();
		}

		// Token: 0x06003EB4 RID: 16052 RVA: 0x000CFF35 File Offset: 0x000CE135
		public Type GetClrType(StructuralType objectSpaceType)
		{
			return ObjectItemCollection.GetClrType(objectSpaceType);
		}

		// Token: 0x06003EB5 RID: 16053 RVA: 0x000CFF3D File Offset: 0x000CE13D
		public bool TryGetClrType(StructuralType objectSpaceType, out Type clrType)
		{
			return ObjectItemCollection.TryGetClrType(objectSpaceType, out clrType);
		}

		// Token: 0x06003EB6 RID: 16054 RVA: 0x000CFF46 File Offset: 0x000CE146
		public Type GetClrType(EnumType objectSpaceType)
		{
			return ObjectItemCollection.GetClrType(objectSpaceType);
		}

		// Token: 0x06003EB7 RID: 16055 RVA: 0x000CFF4E File Offset: 0x000CE14E
		public bool TryGetClrType(EnumType objectSpaceType, out Type clrType)
		{
			return ObjectItemCollection.TryGetClrType(objectSpaceType, out clrType);
		}

		// Token: 0x06003EB8 RID: 16056 RVA: 0x000CFF58 File Offset: 0x000CE158
		private static Type GetClrType(EdmType objectSpaceType)
		{
			Type type;
			if (!ObjectItemCollection.TryGetClrType(objectSpaceType, out type))
			{
				throw new ArgumentException(Strings.FailedToFindClrTypeMapping(objectSpaceType.Identity));
			}
			return type;
		}

		// Token: 0x06003EB9 RID: 16057 RVA: 0x000CFF84 File Offset: 0x000CE184
		private static bool TryGetClrType(EdmType objectSpaceType, out Type clrType)
		{
			if (objectSpaceType.DataSpace != DataSpace.OSpace)
			{
				throw new ArgumentException(Strings.ArgumentMustBeOSpaceType, "objectSpaceType");
			}
			clrType = null;
			if (Helper.IsEntityType(objectSpaceType) || Helper.IsComplexType(objectSpaceType) || Helper.IsEnumType(objectSpaceType))
			{
				clrType = objectSpaceType.ClrType;
			}
			return clrType != null;
		}

		// Token: 0x06003EBA RID: 16058 RVA: 0x000CFFD4 File Offset: 0x000CE1D4
		internal override PrimitiveType GetMappedPrimitiveType(PrimitiveTypeKind modelType)
		{
			if (Helper.IsGeometricTypeKind(modelType))
			{
				modelType = PrimitiveTypeKind.Geometry;
			}
			else if (Helper.IsGeographicTypeKind(modelType))
			{
				modelType = PrimitiveTypeKind.Geography;
			}
			PrimitiveType primitiveType = null;
			this._primitiveTypeMaps.TryGetType(modelType, null, out primitiveType);
			return primitiveType;
		}

		// Token: 0x06003EBB RID: 16059 RVA: 0x000D000E File Offset: 0x000CE20E
		internal bool TryGetOSpaceType(EdmType cspaceType, out EdmType edmType)
		{
			if (Helper.IsEntityType(cspaceType) || Helper.IsComplexType(cspaceType) || Helper.IsEnumType(cspaceType))
			{
				return this._ocMapping.TryGetValue(cspaceType.Identity, out edmType);
			}
			return base.TryGetItem<EdmType>(cspaceType.Identity, out edmType);
		}

		// Token: 0x06003EBC RID: 16060 RVA: 0x000D0048 File Offset: 0x000CE248
		internal static string TryGetMappingCSpaceTypeIdentity(EdmType edmType)
		{
			if (Helper.IsEntityType(edmType))
			{
				return ((ClrEntityType)edmType).CSpaceTypeName;
			}
			if (Helper.IsComplexType(edmType))
			{
				return ((ClrComplexType)edmType).CSpaceTypeName;
			}
			if (Helper.IsEnumType(edmType))
			{
				return ((ClrEnumType)edmType).CSpaceTypeName;
			}
			return edmType.Identity;
		}

		// Token: 0x06003EBD RID: 16061 RVA: 0x000D0097 File Offset: 0x000CE297
		public override ReadOnlyCollection<T> GetItems<T>()
		{
			return base.InternalGetItems(typeof(T)) as ReadOnlyCollection<T>;
		}

		// Token: 0x0400153C RID: 5436
		private readonly CacheForPrimitiveTypes _primitiveTypeMaps = new CacheForPrimitiveTypes();

		// Token: 0x0400153D RID: 5437
		private KnownAssembliesSet _knownAssemblies = new KnownAssembliesSet();

		// Token: 0x0400153E RID: 5438
		private readonly Dictionary<string, EdmType> _ocMapping = new Dictionary<string, EdmType>();

		// Token: 0x0400153F RID: 5439
		private object _loaderCookie;

		// Token: 0x04001540 RID: 5440
		private readonly object _loadAssemblyLock = new object();
	}
}
