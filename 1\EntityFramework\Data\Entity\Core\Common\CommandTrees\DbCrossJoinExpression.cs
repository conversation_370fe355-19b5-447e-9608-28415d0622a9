﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006AF RID: 1711
	public sealed class DbCrossJoinExpression : DbExpression
	{
		// Token: 0x0600503B RID: 20539 RVA: 0x001211F2 File Offset: 0x0011F3F2
		internal DbCrossJoinExpression(TypeUsage collectionOfRowResultType, ReadOnlyCollection<DbExpressionBinding> inputs)
			: base(DbExpressionKind.CrossJoin, collectionOfRowResultType, true)
		{
			this._inputs = inputs;
		}

		// Token: 0x17000FA5 RID: 4005
		// (get) Token: 0x0600503C RID: 20540 RVA: 0x00121204 File Offset: 0x0011F404
		public IList<DbExpressionBinding> Inputs
		{
			get
			{
				return this._inputs;
			}
		}

		// Token: 0x0600503D RID: 20541 RVA: 0x0012120C File Offset: 0x0011F40C
		public override void Accept(DbExpressionVisitor visitor)
		{
			Check.NotNull<DbExpressionVisitor>(visitor, "visitor");
			visitor.Visit(this);
		}

		// Token: 0x0600503E RID: 20542 RVA: 0x00121221 File Offset: 0x0011F421
		public override TResultType Accept<TResultType>(DbExpressionVisitor<TResultType> visitor)
		{
			Check.NotNull<DbExpressionVisitor<TResultType>>(visitor, "visitor");
			return visitor.Visit(this);
		}

		// Token: 0x04001D53 RID: 7507
		private readonly ReadOnlyCollection<DbExpressionBinding> _inputs;
	}
}
