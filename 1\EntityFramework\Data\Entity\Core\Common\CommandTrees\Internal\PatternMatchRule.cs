﻿using System;

namespace System.Data.Entity.Core.Common.CommandTrees.Internal
{
	// Token: 0x020006F1 RID: 1777
	internal class PatternMatchRule : DbExpressionRule
	{
		// Token: 0x060052BC RID: 21180 RVA: 0x00128314 File Offset: 0x00126514
		private PatternMatchRule(Func<DbExpression, bool> matchFunc, Func<DbExpression, DbExpression> processor, DbExpressionRule.ProcessedAction onProcessed)
		{
			this.isMatch = matchFunc;
			this.process = processor;
			this.processed = onProcessed;
		}

		// Token: 0x060052BD RID: 21181 RVA: 0x00128331 File Offset: 0x00126531
		internal override bool ShouldProcess(DbExpression expression)
		{
			return this.isMatch(expression);
		}

		// Token: 0x060052BE RID: 21182 RVA: 0x0012833F File Offset: 0x0012653F
		internal override bool TryProcess(DbExpression expression, out DbExpression result)
		{
			result = this.process(expression);
			return result != null;
		}

		// Token: 0x17001003 RID: 4099
		// (get) Token: 0x060052BF RID: 21183 RVA: 0x00128354 File Offset: 0x00126554
		internal override DbExpressionRule.ProcessedAction OnExpressionProcessed
		{
			get
			{
				return this.processed;
			}
		}

		// Token: 0x060052C0 RID: 21184 RVA: 0x0012835C File Offset: 0x0012655C
		internal static PatternMatchRule Create(Func<DbExpression, bool> matchFunc, Func<DbExpression, DbExpression> processor)
		{
			return PatternMatchRule.Create(matchFunc, processor, DbExpressionRule.ProcessedAction.Reset);
		}

		// Token: 0x060052C1 RID: 21185 RVA: 0x00128366 File Offset: 0x00126566
		internal static PatternMatchRule Create(Func<DbExpression, bool> matchFunc, Func<DbExpression, DbExpression> processor, DbExpressionRule.ProcessedAction onProcessed)
		{
			return new PatternMatchRule(matchFunc, processor, onProcessed);
		}

		// Token: 0x04001DE8 RID: 7656
		private readonly Func<DbExpression, bool> isMatch;

		// Token: 0x04001DE9 RID: 7657
		private readonly Func<DbExpression, DbExpression> process;

		// Token: 0x04001DEA RID: 7658
		private readonly DbExpressionRule.ProcessedAction processed;
	}
}
