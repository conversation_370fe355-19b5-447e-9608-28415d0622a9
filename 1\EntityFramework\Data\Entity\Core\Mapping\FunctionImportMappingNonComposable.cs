﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x0200053A RID: 1338
	public sealed class FunctionImportMappingNonComposable : FunctionImportMapping
	{
		// Token: 0x060041F3 RID: 16883 RVA: 0x000DE520 File Offset: 0x000DC720
		public FunctionImportMappingNonComposable(EdmFunction functionImport, EdmFunction targetFunction, IEnumerable<FunctionImportResultMapping> resultMappings, EntityContainerMapping containerMapping)
			: base(Check.NotNull<EdmFunction>(functionImport, "functionImport"), Check.NotNull<EdmFunction>(targetFunction, "targetFunction"))
		{
			Check.NotNull<IEnumerable<FunctionImportResultMapping>>(resultMappings, "resultMappings");
			Check.NotNull<EntityContainerMapping>(containerMapping, "containerMapping");
			if (!resultMappings.Any<FunctionImportResultMapping>())
			{
				EdmItemCollection edmItemCollection = ((containerMapping.StorageMappingItemCollection != null) ? containerMapping.StorageMappingItemCollection.EdmItemCollection : new EdmItemCollection(new EdmModel(DataSpace.CSpace, 3.0)));
				this._internalResultMappings = new ReadOnlyCollection<FunctionImportStructuralTypeMappingKB>(new FunctionImportStructuralTypeMappingKB[]
				{
					new FunctionImportStructuralTypeMappingKB(new List<FunctionImportStructuralTypeMapping>(), edmItemCollection)
				});
				this.noExplicitResultMappings = true;
			}
			else
			{
				this._internalResultMappings = new ReadOnlyCollection<FunctionImportStructuralTypeMappingKB>(resultMappings.Select((FunctionImportResultMapping resultMapping) => new FunctionImportStructuralTypeMappingKB(resultMapping.TypeMappings, containerMapping.StorageMappingItemCollection.EdmItemCollection)).ToArray<FunctionImportStructuralTypeMappingKB>());
				this.noExplicitResultMappings = false;
			}
			this._resultMappings = new ReadOnlyCollection<FunctionImportResultMapping>(resultMappings.ToList<FunctionImportResultMapping>());
		}

		// Token: 0x060041F4 RID: 16884 RVA: 0x000DE614 File Offset: 0x000DC814
		internal FunctionImportMappingNonComposable(EdmFunction functionImport, EdmFunction targetFunction, List<List<FunctionImportStructuralTypeMapping>> structuralTypeMappingsList, ItemCollection itemCollection)
			: base(functionImport, targetFunction)
		{
			if (structuralTypeMappingsList.Count == 0)
			{
				this._internalResultMappings = new ReadOnlyCollection<FunctionImportStructuralTypeMappingKB>(new FunctionImportStructuralTypeMappingKB[]
				{
					new FunctionImportStructuralTypeMappingKB(new List<FunctionImportStructuralTypeMapping>(), itemCollection)
				});
				this.noExplicitResultMappings = true;
				return;
			}
			this._internalResultMappings = new ReadOnlyCollection<FunctionImportStructuralTypeMappingKB>(structuralTypeMappingsList.Select((List<FunctionImportStructuralTypeMapping> structuralTypeMappings) => new FunctionImportStructuralTypeMappingKB(structuralTypeMappings, itemCollection)).ToArray<FunctionImportStructuralTypeMappingKB>());
			this.noExplicitResultMappings = false;
		}

		// Token: 0x17000D0E RID: 3342
		// (get) Token: 0x060041F5 RID: 16885 RVA: 0x000DE694 File Offset: 0x000DC894
		internal ReadOnlyCollection<FunctionImportStructuralTypeMappingKB> InternalResultMappings
		{
			get
			{
				return this._internalResultMappings;
			}
		}

		// Token: 0x17000D0F RID: 3343
		// (get) Token: 0x060041F6 RID: 16886 RVA: 0x000DE69C File Offset: 0x000DC89C
		public ReadOnlyCollection<FunctionImportResultMapping> ResultMappings
		{
			get
			{
				return this._resultMappings;
			}
		}

		// Token: 0x060041F7 RID: 16887 RVA: 0x000DE6A4 File Offset: 0x000DC8A4
		internal override void SetReadOnly()
		{
			MappingItem.SetReadOnly(this._resultMappings);
			base.SetReadOnly();
		}

		// Token: 0x060041F8 RID: 16888 RVA: 0x000DE6B7 File Offset: 0x000DC8B7
		internal FunctionImportStructuralTypeMappingKB GetResultMapping(int resultSetIndex)
		{
			if (this.noExplicitResultMappings)
			{
				return this.InternalResultMappings[0];
			}
			if (this.InternalResultMappings.Count <= resultSetIndex)
			{
				throw new ArgumentOutOfRangeException("resultSetIndex");
			}
			return this.InternalResultMappings[resultSetIndex];
		}

		// Token: 0x060041F9 RID: 16889 RVA: 0x000DE6F3 File Offset: 0x000DC8F3
		internal IList<string> GetDiscriminatorColumns(int resultSetIndex)
		{
			return this.GetResultMapping(resultSetIndex).DiscriminatorColumns;
		}

		// Token: 0x060041FA RID: 16890 RVA: 0x000DE704 File Offset: 0x000DC904
		internal EntityType Discriminate(object[] discriminatorValues, int resultSetIndex)
		{
			FunctionImportStructuralTypeMappingKB resultMapping = this.GetResultMapping(resultSetIndex);
			BitArray bitArray = new BitArray(resultMapping.MappedEntityTypes.Count, true);
			foreach (FunctionImportNormalizedEntityTypeMapping functionImportNormalizedEntityTypeMapping in resultMapping.NormalizedEntityTypeMappings)
			{
				bool flag = true;
				ReadOnlyCollection<FunctionImportEntityTypeMappingCondition> columnConditions = functionImportNormalizedEntityTypeMapping.ColumnConditions;
				for (int i = 0; i < columnConditions.Count; i++)
				{
					if (columnConditions[i] != null && !columnConditions[i].ColumnValueMatchesCondition(discriminatorValues[i]))
					{
						flag = false;
						break;
					}
				}
				if (flag)
				{
					bitArray = bitArray.And(functionImportNormalizedEntityTypeMapping.ImpliedEntityTypes);
				}
				else
				{
					bitArray = bitArray.And(functionImportNormalizedEntityTypeMapping.ComplementImpliedEntityTypes);
				}
			}
			EntityType entityType = null;
			for (int j = 0; j < bitArray.Length; j++)
			{
				if (bitArray[j])
				{
					if (entityType != null)
					{
						throw new EntityCommandExecutionException(Strings.ADP_InvalidDataReaderUnableToDetermineType);
					}
					entityType = resultMapping.MappedEntityTypes[j];
				}
			}
			if (entityType == null)
			{
				throw new EntityCommandExecutionException(Strings.ADP_InvalidDataReaderUnableToDetermineType);
			}
			return entityType;
		}

		// Token: 0x060041FB RID: 16891 RVA: 0x000DE81C File Offset: 0x000DCA1C
		internal TypeUsage GetExpectedTargetResultType(int resultSetIndex)
		{
			FunctionImportStructuralTypeMappingKB resultMapping = this.GetResultMapping(resultSetIndex);
			Dictionary<string, TypeUsage> dictionary = new Dictionary<string, TypeUsage>();
			IEnumerable<StructuralType> enumerable;
			if (resultMapping.NormalizedEntityTypeMappings.Count == 0)
			{
				StructuralType structuralType;
				MetadataHelper.TryGetFunctionImportReturnType<StructuralType>(base.FunctionImport, resultSetIndex, out structuralType);
				enumerable = new StructuralType[] { structuralType };
			}
			else
			{
				enumerable = resultMapping.MappedEntityTypes.Cast<StructuralType>();
			}
			foreach (StructuralType structuralType2 in enumerable)
			{
				foreach (object obj in TypeHelpers.GetAllStructuralMembers(structuralType2))
				{
					EdmProperty edmProperty = (EdmProperty)obj;
					dictionary[edmProperty.Name] = edmProperty.TypeUsage;
				}
			}
			foreach (string text in this.GetDiscriminatorColumns(resultSetIndex))
			{
				if (!dictionary.ContainsKey(text))
				{
					TypeUsage typeUsage = TypeUsage.CreateStringTypeUsage(MetadataWorkspace.GetModelPrimitiveType(PrimitiveTypeKind.String), true, false);
					dictionary.Add(text, typeUsage);
				}
			}
			return TypeUsage.Create(new CollectionType(TypeUsage.Create(new RowType(dictionary.Select((KeyValuePair<string, TypeUsage> c) => new EdmProperty(c.Key, c.Value))))));
		}

		// Token: 0x040016DB RID: 5851
		private readonly ReadOnlyCollection<FunctionImportResultMapping> _resultMappings;

		// Token: 0x040016DC RID: 5852
		private readonly bool noExplicitResultMappings;

		// Token: 0x040016DD RID: 5853
		private readonly ReadOnlyCollection<FunctionImportStructuralTypeMappingKB> _internalResultMappings;
	}
}
