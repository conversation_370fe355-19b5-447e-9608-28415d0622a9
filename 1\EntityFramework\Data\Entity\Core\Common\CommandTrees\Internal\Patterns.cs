﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm;
using System.Linq;

namespace System.Data.Entity.Core.Common.CommandTrees.Internal
{
	// Token: 0x020006F3 RID: 1779
	internal static class Patterns
	{
		// Token: 0x060052C6 RID: 21190 RVA: 0x001283AB File Offset: 0x001265AB
		internal static Func<DbExpression, bool> And(Func<DbExpression, bool> pattern1, Func<DbExpression, bool> pattern2)
		{
			return (DbExpression e) => pattern1(e) && pattern2(e);
		}

		// Token: 0x060052C7 RID: 21191 RVA: 0x001283CB File Offset: 0x001265CB
		internal static Func<DbExpression, bool> And(Func<DbExpression, bool> pattern1, Func<DbExpression, bool> pattern2, Func<DbExpression, bool> pattern3)
		{
			return (DbExpression e) => pattern1(e) && pattern2(e) && pattern3(e);
		}

		// Token: 0x060052C8 RID: 21192 RVA: 0x001283F2 File Offset: 0x001265F2
		internal static Func<DbExpression, bool> Or(Func<DbExpression, bool> pattern1, Func<DbExpression, bool> pattern2)
		{
			return (DbExpression e) => pattern1(e) || pattern2(e);
		}

		// Token: 0x060052C9 RID: 21193 RVA: 0x00128412 File Offset: 0x00126612
		internal static Func<DbExpression, bool> Or(Func<DbExpression, bool> pattern1, Func<DbExpression, bool> pattern2, Func<DbExpression, bool> pattern3)
		{
			return (DbExpression e) => pattern1(e) || pattern2(e) || pattern3(e);
		}

		// Token: 0x17001004 RID: 4100
		// (get) Token: 0x060052CA RID: 21194 RVA: 0x00128439 File Offset: 0x00126639
		internal static Func<DbExpression, bool> AnyExpression
		{
			get
			{
				return (DbExpression e) => true;
			}
		}

		// Token: 0x17001005 RID: 4101
		// (get) Token: 0x060052CB RID: 21195 RVA: 0x0012845A File Offset: 0x0012665A
		internal static Func<IEnumerable<DbExpression>, bool> AnyExpressions
		{
			get
			{
				return (IEnumerable<DbExpression> elems) => true;
			}
		}

		// Token: 0x17001006 RID: 4102
		// (get) Token: 0x060052CC RID: 21196 RVA: 0x0012847B File Offset: 0x0012667B
		internal static Func<DbExpression, bool> MatchComplexType
		{
			get
			{
				return (DbExpression e) => TypeSemantics.IsComplexType(e.ResultType);
			}
		}

		// Token: 0x17001007 RID: 4103
		// (get) Token: 0x060052CD RID: 21197 RVA: 0x0012849C File Offset: 0x0012669C
		internal static Func<DbExpression, bool> MatchEntityType
		{
			get
			{
				return (DbExpression e) => TypeSemantics.IsEntityType(e.ResultType);
			}
		}

		// Token: 0x17001008 RID: 4104
		// (get) Token: 0x060052CE RID: 21198 RVA: 0x001284BD File Offset: 0x001266BD
		internal static Func<DbExpression, bool> MatchRowType
		{
			get
			{
				return (DbExpression e) => TypeSemantics.IsRowType(e.ResultType);
			}
		}

		// Token: 0x060052CF RID: 21199 RVA: 0x001284DE File Offset: 0x001266DE
		internal static Func<DbExpression, bool> MatchKind(DbExpressionKind kindToMatch)
		{
			return (DbExpression e) => e.ExpressionKind == kindToMatch;
		}

		// Token: 0x060052D0 RID: 21200 RVA: 0x001284F7 File Offset: 0x001266F7
		internal static Func<IEnumerable<DbExpression>, bool> MatchForAll(Func<DbExpression, bool> elementPattern)
		{
			Func<DbExpression, bool> <>9__1;
			return delegate(IEnumerable<DbExpression> elems)
			{
				Func<DbExpression, bool> func;
				if ((func = <>9__1) == null)
				{
					func = (<>9__1 = (DbExpression e) => !elementPattern(e));
				}
				return elems.FirstOrDefault(func) == null;
			};
		}

		// Token: 0x060052D1 RID: 21201 RVA: 0x00128510 File Offset: 0x00126710
		internal static Func<DbExpression, bool> MatchBinary()
		{
			return (DbExpression e) => e is DbBinaryExpression;
		}

		// Token: 0x060052D2 RID: 21202 RVA: 0x00128531 File Offset: 0x00126731
		internal static Func<DbExpression, bool> MatchFilter(Func<DbExpression, bool> inputPattern, Func<DbExpression, bool> predicatePattern)
		{
			return delegate(DbExpression e)
			{
				if (e.ExpressionKind != DbExpressionKind.Filter)
				{
					return false;
				}
				DbFilterExpression dbFilterExpression = (DbFilterExpression)e;
				return inputPattern(dbFilterExpression.Input.Expression) && predicatePattern(dbFilterExpression.Predicate);
			};
		}

		// Token: 0x060052D3 RID: 21203 RVA: 0x00128551 File Offset: 0x00126751
		internal static Func<DbExpression, bool> MatchProject(Func<DbExpression, bool> inputPattern, Func<DbExpression, bool> projectionPattern)
		{
			return delegate(DbExpression e)
			{
				if (e.ExpressionKind != DbExpressionKind.Project)
				{
					return false;
				}
				DbProjectExpression dbProjectExpression = (DbProjectExpression)e;
				return inputPattern(dbProjectExpression.Input.Expression) && projectionPattern(dbProjectExpression.Projection);
			};
		}

		// Token: 0x060052D4 RID: 21204 RVA: 0x00128571 File Offset: 0x00126771
		internal static Func<DbExpression, bool> MatchCase(Func<IEnumerable<DbExpression>, bool> whenPattern, Func<IEnumerable<DbExpression>, bool> thenPattern, Func<DbExpression, bool> elsePattern)
		{
			return delegate(DbExpression e)
			{
				if (e.ExpressionKind != DbExpressionKind.Case)
				{
					return false;
				}
				DbCaseExpression dbCaseExpression = (DbCaseExpression)e;
				return whenPattern(dbCaseExpression.When) && thenPattern(dbCaseExpression.Then) && elsePattern(dbCaseExpression.Else);
			};
		}

		// Token: 0x060052D5 RID: 21205 RVA: 0x00128598 File Offset: 0x00126798
		internal static Func<DbExpression, bool> MatchNewInstance()
		{
			return (DbExpression e) => e.ExpressionKind == DbExpressionKind.NewInstance;
		}

		// Token: 0x060052D6 RID: 21206 RVA: 0x001285B9 File Offset: 0x001267B9
		internal static Func<DbExpression, bool> MatchNewInstance(Func<IEnumerable<DbExpression>, bool> argumentsPattern)
		{
			return delegate(DbExpression e)
			{
				if (e.ExpressionKind != DbExpressionKind.NewInstance)
				{
					return false;
				}
				DbNewInstanceExpression dbNewInstanceExpression = (DbNewInstanceExpression)e;
				return argumentsPattern(dbNewInstanceExpression.Arguments);
			};
		}
	}
}
