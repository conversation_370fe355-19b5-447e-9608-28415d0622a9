﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Objects.Internal;
using System.Linq.Expressions;
using System.Text;

namespace System.Data.Entity.Core.Common.Internal.Materialization
{
	// Token: 0x02000639 RID: 1593
	internal class CoordinatorFactory<TElement> : CoordinatorFactory
	{
		// Token: 0x06004CBB RID: 19643 RVA: 0x0010E20C File Offset: 0x0010C40C
		internal CoordinatorFactory(int depth, int stateSlot, Expression<Func<Shaper, bool>> hasData, Expression<Func<Shaper, bool>> setKeys, Expression<Func<Shaper, bool>> checkKeys, CoordinatorFactory[] nestedCoordinators, Expression<Func<Shaper, TElement>> element, Expression<Func<Shaper, IEntityWrapper>> wrappedElement, Expression<Func<Shaper, TElement>> elementWithErrorHandling, Expression<Func<Shaper, ICollection<TElement>>> initializeCollection, RecordStateFactory[] recordStateFactories)
			: base(depth, stateSlot, CoordinatorFactory<TElement>.CompilePredicate(hasData), CoordinatorFactory<TElement>.CompilePredicate(setKeys), CoordinatorFactory<TElement>.CompilePredicate(checkKeys), nestedCoordinators, recordStateFactories)
		{
			this.WrappedElement = ((wrappedElement == null) ? null : wrappedElement.Compile());
			this.Element = ((element == null) ? null : element.Compile());
			this.ElementWithErrorHandling = elementWithErrorHandling.Compile();
			Func<Shaper, ICollection<TElement>> func;
			if (initializeCollection != null)
			{
				func = initializeCollection.Compile();
			}
			else
			{
				func = (Shaper s) => new List<TElement>();
			}
			this.InitializeCollection = func;
			this.Description = new StringBuilder().Append("HasData: ").AppendLine(CoordinatorFactory<TElement>.DescribeExpression(hasData)).Append("SetKeys: ")
				.AppendLine(CoordinatorFactory<TElement>.DescribeExpression(setKeys))
				.Append("CheckKeys: ")
				.AppendLine(CoordinatorFactory<TElement>.DescribeExpression(checkKeys))
				.Append("Element: ")
				.AppendLine((element == null) ? CoordinatorFactory<TElement>.DescribeExpression(wrappedElement) : CoordinatorFactory<TElement>.DescribeExpression(element))
				.Append("ElementWithExceptionHandling: ")
				.AppendLine(CoordinatorFactory<TElement>.DescribeExpression(elementWithErrorHandling))
				.Append("InitializeCollection: ")
				.AppendLine(CoordinatorFactory<TElement>.DescribeExpression(initializeCollection))
				.ToString();
		}

		// Token: 0x06004CBC RID: 19644 RVA: 0x0010E340 File Offset: 0x0010C540
		public CoordinatorFactory(int depth, int stateSlot, Expression hasData, Expression setKeys, Expression checkKeys, CoordinatorFactory[] nestedCoordinators, Expression element, Expression elementWithErrorHandling, Expression initializeCollection, RecordStateFactory[] recordStateFactories)
			: this(depth, stateSlot, CodeGenEmitter.BuildShaperLambda<bool>(hasData), CodeGenEmitter.BuildShaperLambda<bool>(setKeys), CodeGenEmitter.BuildShaperLambda<bool>(checkKeys), nestedCoordinators, typeof(IEntityWrapper).IsAssignableFrom(element.Type) ? null : CodeGenEmitter.BuildShaperLambda<TElement>(element), typeof(IEntityWrapper).IsAssignableFrom(element.Type) ? CodeGenEmitter.BuildShaperLambda<IEntityWrapper>(element) : null, CodeGenEmitter.BuildShaperLambda<TElement>(typeof(IEntityWrapper).IsAssignableFrom(element.Type) ? CodeGenEmitter.Emit_UnwrapAndEnsureType(elementWithErrorHandling, typeof(TElement)) : elementWithErrorHandling), CodeGenEmitter.BuildShaperLambda<ICollection<TElement>>(initializeCollection), recordStateFactories)
		{
		}

		// Token: 0x06004CBD RID: 19645 RVA: 0x0010E3EC File Offset: 0x0010C5EC
		private static Func<Shaper, bool> CompilePredicate(Expression<Func<Shaper, bool>> predicate)
		{
			Func<Shaper, bool> func;
			if (predicate == null)
			{
				func = null;
			}
			else
			{
				func = predicate.Compile();
			}
			return func;
		}

		// Token: 0x06004CBE RID: 19646 RVA: 0x0010E408 File Offset: 0x0010C608
		private static string DescribeExpression(Expression expression)
		{
			string text;
			if (expression == null)
			{
				text = "undefined";
			}
			else
			{
				text = expression.ToString();
			}
			return text;
		}

		// Token: 0x06004CBF RID: 19647 RVA: 0x0010E428 File Offset: 0x0010C628
		internal override Coordinator CreateCoordinator(Coordinator parent, Coordinator next)
		{
			return new Coordinator<TElement>(this, parent, next);
		}

		// Token: 0x06004CC0 RID: 19648 RVA: 0x0010E434 File Offset: 0x0010C634
		internal RecordState GetDefaultRecordState(Shaper<RecordState> shaper)
		{
			RecordState recordState = null;
			if (this.RecordStateFactories.Count > 0)
			{
				recordState = (RecordState)shaper.State[this.RecordStateFactories[0].StateSlotNumber];
				recordState.ResetToDefaultState();
			}
			return recordState;
		}

		// Token: 0x06004CC1 RID: 19649 RVA: 0x0010E476 File Offset: 0x0010C676
		public override string ToString()
		{
			return this.Description;
		}

		// Token: 0x04001B2A RID: 6954
		internal readonly Func<Shaper, IEntityWrapper> WrappedElement;

		// Token: 0x04001B2B RID: 6955
		internal readonly Func<Shaper, TElement> Element;

		// Token: 0x04001B2C RID: 6956
		internal readonly Func<Shaper, TElement> ElementWithErrorHandling;

		// Token: 0x04001B2D RID: 6957
		internal readonly Func<Shaper, ICollection<TElement>> InitializeCollection;

		// Token: 0x04001B2E RID: 6958
		private readonly string Description;
	}
}
