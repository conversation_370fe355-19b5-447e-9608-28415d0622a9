﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003BE RID: 958
	internal sealed class NewMultisetOp : ScalarOp
	{
		// Token: 0x06002DDD RID: 11741 RVA: 0x000915DE File Offset: 0x0008F7DE
		internal NewMultisetOp(TypeUsage type)
			: base(OpType.NewMultiset, type)
		{
		}

		// Token: 0x06002DDE RID: 11742 RVA: 0x000915E9 File Offset: 0x0008F7E9
		private NewMultisetOp()
			: base(OpType.NewMultiset)
		{
		}

		// Token: 0x06002DDF RID: 11743 RVA: 0x000915F3 File Offset: 0x0008F7F3
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002DE0 RID: 11744 RVA: 0x000915FD File Offset: 0x0008F7FD
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000F57 RID: 3927
		internal static readonly NewMultisetOp Pattern = new NewMultisetOp();
	}
}
