﻿using System;
using System.Collections.Generic;

namespace System.Data.Entity.Core.Common.Utils.Boolean
{
	// Token: 0x02000605 RID: 1541
	internal class BooleanExpressionTermRewriter<T_From, T_To> : Visitor<T_From, BoolExpr<T_To>>
	{
		// Token: 0x06004B67 RID: 19303 RVA: 0x00109A82 File Offset: 0x00107C82
		internal BooleanExpressionTermRewriter(Func<TermExpr<T_From>, BoolExpr<T_To>> translator)
		{
			this._translator = translator;
		}

		// Token: 0x06004B68 RID: 19304 RVA: 0x00109A91 File Offset: 0x00107C91
		internal override BoolExpr<T_To> VisitFalse(FalseExpr<T_From> expression)
		{
			return FalseExpr<T_To>.Value;
		}

		// Token: 0x06004B69 RID: 19305 RVA: 0x00109A98 File Offset: 0x00107C98
		internal override BoolExpr<T_To> VisitTrue(TrueExpr<T_From> expression)
		{
			return TrueExpr<T_To>.Value;
		}

		// Token: 0x06004B6A RID: 19306 RVA: 0x00109A9F File Offset: 0x00107C9F
		internal override BoolExpr<T_To> VisitNot(NotExpr<T_From> expression)
		{
			return new NotExpr<T_To>(expression.Child.Accept<BoolExpr<T_To>>(this));
		}

		// Token: 0x06004B6B RID: 19307 RVA: 0x00109AB2 File Offset: 0x00107CB2
		internal override BoolExpr<T_To> VisitTerm(TermExpr<T_From> expression)
		{
			return this._translator(expression);
		}

		// Token: 0x06004B6C RID: 19308 RVA: 0x00109AC0 File Offset: 0x00107CC0
		internal override BoolExpr<T_To> VisitAnd(AndExpr<T_From> expression)
		{
			return new AndExpr<T_To>(this.VisitChildren(expression));
		}

		// Token: 0x06004B6D RID: 19309 RVA: 0x00109ACE File Offset: 0x00107CCE
		internal override BoolExpr<T_To> VisitOr(OrExpr<T_From> expression)
		{
			return new OrExpr<T_To>(this.VisitChildren(expression));
		}

		// Token: 0x06004B6E RID: 19310 RVA: 0x00109ADC File Offset: 0x00107CDC
		private IEnumerable<BoolExpr<T_To>> VisitChildren(TreeExpr<T_From> expression)
		{
			foreach (BoolExpr<T_From> boolExpr in expression.Children)
			{
				yield return boolExpr.Accept<BoolExpr<T_To>>(this);
			}
			HashSet<BoolExpr<T_From>>.Enumerator enumerator = default(HashSet<BoolExpr<T_From>>.Enumerator);
			yield break;
			yield break;
		}

		// Token: 0x04001A5C RID: 6748
		private readonly Func<TermExpr<T_From>, BoolExpr<T_To>> _translator;
	}
}
