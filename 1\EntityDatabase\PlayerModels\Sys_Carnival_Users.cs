﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000020 RID: 32
	public class Sys_Carnival_Users
	{
		// Token: 0x170000F2 RID: 242
		// (get) Token: 0x06000203 RID: 515 RVA: 0x0000318C File Offset: 0x0000138C
		// (set) Token: 0x06000204 RID: 516 RVA: 0x00003194 File Offset: 0x00001394
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x170000F3 RID: 243
		// (get) Token: 0x06000205 RID: 517 RVA: 0x0000319D File Offset: 0x0000139D
		// (set) Token: 0x06000206 RID: 518 RVA: 0x000031A5 File Offset: 0x000013A5
		public int UserID { get; set; }

		// Token: 0x170000F4 RID: 244
		// (get) Token: 0x06000207 RID: 519 RVA: 0x000031AE File Offset: 0x000013AE
		// (set) Token: 0x06000208 RID: 520 RVA: 0x000031B6 File Offset: 0x000013B6
		public string NickName { get; set; }

		// Token: 0x170000F5 RID: 245
		// (get) Token: 0x06000209 RID: 521 RVA: 0x000031BF File Offset: 0x000013BF
		// (set) Token: 0x0600020A RID: 522 RVA: 0x000031C7 File Offset: 0x000013C7
		public string AreaName { get; set; }

		// Token: 0x170000F6 RID: 246
		// (get) Token: 0x0600020B RID: 523 RVA: 0x000031D0 File Offset: 0x000013D0
		// (set) Token: 0x0600020C RID: 524 RVA: 0x000031D8 File Offset: 0x000013D8
		public int RedPackageCount { get; set; }

		// Token: 0x170000F7 RID: 247
		// (get) Token: 0x0600020D RID: 525 RVA: 0x000031E1 File Offset: 0x000013E1
		// (set) Token: 0x0600020E RID: 526 RVA: 0x000031E9 File Offset: 0x000013E9
		public int RemainDrawCount { get; set; }

		// Token: 0x170000F8 RID: 248
		// (get) Token: 0x0600020F RID: 527 RVA: 0x000031F2 File Offset: 0x000013F2
		// (set) Token: 0x06000210 RID: 528 RVA: 0x000031FA File Offset: 0x000013FA
		public int Score { get; set; }

		// Token: 0x170000F9 RID: 249
		// (get) Token: 0x06000211 RID: 529 RVA: 0x00003203 File Offset: 0x00001403
		// (set) Token: 0x06000212 RID: 530 RVA: 0x0000320B File Offset: 0x0000140B
		public int TurnIndex { get; set; }

		// Token: 0x170000FA RID: 250
		// (get) Token: 0x06000213 RID: 531 RVA: 0x00003214 File Offset: 0x00001414
		// (set) Token: 0x06000214 RID: 532 RVA: 0x0000321C File Offset: 0x0000141C
		public int CountIndex { get; set; }

		// Token: 0x170000FB RID: 251
		// (get) Token: 0x06000215 RID: 533 RVA: 0x00003225 File Offset: 0x00001425
		// (set) Token: 0x06000216 RID: 534 RVA: 0x0000322D File Offset: 0x0000142D
		public int RateIndex { get; set; }

		// Token: 0x170000FC RID: 252
		// (get) Token: 0x06000217 RID: 535 RVA: 0x00003236 File Offset: 0x00001436
		// (set) Token: 0x06000218 RID: 536 RVA: 0x0000323E File Offset: 0x0000143E
		public int Rate { get; set; }

		// Token: 0x170000FD RID: 253
		// (get) Token: 0x06000219 RID: 537 RVA: 0x00003247 File Offset: 0x00001447
		// (set) Token: 0x0600021A RID: 538 RVA: 0x0000324F File Offset: 0x0000144F
		public bool IsGet { get; set; }

		// Token: 0x170000FE RID: 254
		// (get) Token: 0x0600021B RID: 539 RVA: 0x00003258 File Offset: 0x00001458
		// (set) Token: 0x0600021C RID: 540 RVA: 0x00003260 File Offset: 0x00001460
		public int AwardID { get; set; }

		// Token: 0x170000FF RID: 255
		// (get) Token: 0x0600021D RID: 541 RVA: 0x00003269 File Offset: 0x00001469
		// (set) Token: 0x0600021E RID: 542 RVA: 0x00003271 File Offset: 0x00001471
		public int RechargeMoney { get; set; }
	}
}
