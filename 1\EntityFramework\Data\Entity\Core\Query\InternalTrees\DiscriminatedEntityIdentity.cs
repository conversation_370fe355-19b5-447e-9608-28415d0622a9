﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Globalization;
using System.Text;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x0200039B RID: 923
	internal class DiscriminatedEntityIdentity : EntityIdentity
	{
		// Token: 0x06002CEE RID: 11502 RVA: 0x0008F3F4 File Offset: 0x0008D5F4
		internal DiscriminatedEntityIdentity(SimpleColumnMap entitySetColumn, EntitySet[] entitySetMap, SimpleColumnMap[] keyColumns)
			: base(keyColumns)
		{
			this.m_entitySetColumn = entitySetColumn;
			this.m_entitySetMap = entitySetMap;
		}

		// Token: 0x170008D4 RID: 2260
		// (get) Token: 0x06002CEF RID: 11503 RVA: 0x0008F40B File Offset: 0x0008D60B
		internal SimpleColumnMap EntitySetColumnMap
		{
			get
			{
				return this.m_entitySetColumn;
			}
		}

		// Token: 0x170008D5 RID: 2261
		// (get) Token: 0x06002CF0 RID: 11504 RVA: 0x0008F413 File Offset: 0x0008D613
		internal EntitySet[] EntitySetMap
		{
			get
			{
				return this.m_entitySetMap;
			}
		}

		// Token: 0x06002CF1 RID: 11505 RVA: 0x0008F41C File Offset: 0x0008D61C
		public override string ToString()
		{
			StringBuilder stringBuilder = new StringBuilder();
			string text = string.Empty;
			stringBuilder.AppendFormat(CultureInfo.InvariantCulture, "[(Keys={", new object[0]);
			foreach (SimpleColumnMap simpleColumnMap in base.Keys)
			{
				stringBuilder.AppendFormat(CultureInfo.InvariantCulture, "{0}{1}", new object[] { text, simpleColumnMap });
				text = ",";
			}
			stringBuilder.AppendFormat(CultureInfo.InvariantCulture, "})]", new object[0]);
			return stringBuilder.ToString();
		}

		// Token: 0x04000F18 RID: 3864
		private readonly SimpleColumnMap m_entitySetColumn;

		// Token: 0x04000F19 RID: 3865
		private readonly EntitySet[] m_entitySetMap;
	}
}
