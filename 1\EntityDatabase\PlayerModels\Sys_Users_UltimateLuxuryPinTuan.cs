﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x0200002E RID: 46
	public class Sys_Users_UltimateLuxuryPinTuan
	{
		// Token: 0x17000166 RID: 358
		// (get) Token: 0x060002F9 RID: 761 RVA: 0x000039BE File Offset: 0x00001BBE
		// (set) Token: 0x060002FA RID: 762 RVA: 0x000039C6 File Offset: 0x00001BC6
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000167 RID: 359
		// (get) Token: 0x060002FB RID: 763 RVA: 0x000039CF File Offset: 0x00001BCF
		// (set) Token: 0x060002FC RID: 764 RVA: 0x000039D7 File Offset: 0x00001BD7
		public int GoodID { get; set; }

		// Token: 0x17000168 RID: 360
		// (get) Token: 0x060002FD RID: 765 RVA: 0x000039E0 File Offset: 0x00001BE0
		// (set) Token: 0x060002FE RID: 766 RVA: 0x000039E8 File Offset: 0x00001BE8
		public string OrderID { get; set; }

		// Token: 0x17000169 RID: 361
		// (get) Token: 0x060002FF RID: 767 RVA: 0x000039F1 File Offset: 0x00001BF1
		// (set) Token: 0x06000300 RID: 768 RVA: 0x000039F9 File Offset: 0x00001BF9
		public int DisCount { get; set; }

		// Token: 0x1700016A RID: 362
		// (get) Token: 0x06000301 RID: 769 RVA: 0x00003A02 File Offset: 0x00001C02
		// (set) Token: 0x06000302 RID: 770 RVA: 0x00003A0A File Offset: 0x00001C0A
		public int Status { get; set; }

		// Token: 0x1700016B RID: 363
		// (get) Token: 0x06000303 RID: 771 RVA: 0x00003A13 File Offset: 0x00001C13
		// (set) Token: 0x06000304 RID: 772 RVA: 0x00003A1B File Offset: 0x00001C1B
		public int BuyTimes { get; set; }

		// Token: 0x1700016C RID: 364
		// (get) Token: 0x06000305 RID: 773 RVA: 0x00003A24 File Offset: 0x00001C24
		// (set) Token: 0x06000306 RID: 774 RVA: 0x00003A2C File Offset: 0x00001C2C
		public int Type { get; set; }

		// Token: 0x1700016D RID: 365
		// (get) Token: 0x06000307 RID: 775 RVA: 0x00003A35 File Offset: 0x00001C35
		// (set) Token: 0x06000308 RID: 776 RVA: 0x00003A3D File Offset: 0x00001C3D
		public string Users { get; set; }

		// Token: 0x1700016E RID: 366
		// (get) Token: 0x06000309 RID: 777 RVA: 0x00003A46 File Offset: 0x00001C46
		// (set) Token: 0x0600030A RID: 778 RVA: 0x00003A4E File Offset: 0x00001C4E
		public int CreateUser { get; set; }

		// Token: 0x1700016F RID: 367
		// (get) Token: 0x0600030B RID: 779 RVA: 0x00003A57 File Offset: 0x00001C57
		// (set) Token: 0x0600030C RID: 780 RVA: 0x00003A5F File Offset: 0x00001C5F
		public string CreateUserName { get; set; }
	}
}
