﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Threading;
using System.Xml;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004D3 RID: 1235
	internal class MetadataArtifactLoaderCompositeFile : MetadataArtifactLoader
	{
		// Token: 0x06003D4A RID: 15690 RVA: 0x000C9E12 File Offset: 0x000C8012
		public MetadataArtifactLoaderCompositeFile(string path, ICollection<string> uriRegistry)
		{
			this._path = path;
			this._uriRegistry = uriRegistry;
		}

		// Token: 0x17000C0F RID: 3087
		// (get) Token: 0x06003D4B RID: 15691 RVA: 0x000C9E28 File Offset: 0x000C8028
		public override string Path
		{
			get
			{
				return this._path;
			}
		}

		// Token: 0x17000C10 RID: 3088
		// (get) Token: 0x06003D4C RID: 15692 RVA: 0x000C9E30 File Offset: 0x000C8030
		public override bool IsComposite
		{
			get
			{
				return true;
			}
		}

		// Token: 0x17000C11 RID: 3089
		// (get) Token: 0x06003D4D RID: 15693 RVA: 0x000C9E33 File Offset: 0x000C8033
		internal ReadOnlyCollection<MetadataArtifactLoaderFile> CsdlChildren
		{
			get
			{
				this.LoadCollections();
				return this._csdlChildren;
			}
		}

		// Token: 0x17000C12 RID: 3090
		// (get) Token: 0x06003D4E RID: 15694 RVA: 0x000C9E41 File Offset: 0x000C8041
		internal ReadOnlyCollection<MetadataArtifactLoaderFile> SsdlChildren
		{
			get
			{
				this.LoadCollections();
				return this._ssdlChildren;
			}
		}

		// Token: 0x17000C13 RID: 3091
		// (get) Token: 0x06003D4F RID: 15695 RVA: 0x000C9E4F File Offset: 0x000C804F
		internal ReadOnlyCollection<MetadataArtifactLoaderFile> MslChildren
		{
			get
			{
				this.LoadCollections();
				return this._mslChildren;
			}
		}

		// Token: 0x06003D50 RID: 15696 RVA: 0x000C9E60 File Offset: 0x000C8060
		private void LoadCollections()
		{
			if (this._csdlChildren == null)
			{
				ReadOnlyCollection<MetadataArtifactLoaderFile> readOnlyCollection = new ReadOnlyCollection<MetadataArtifactLoaderFile>(MetadataArtifactLoaderCompositeFile.GetArtifactsInDirectory(this._path, ".csdl", this._uriRegistry));
				Interlocked.CompareExchange<ReadOnlyCollection<MetadataArtifactLoaderFile>>(ref this._csdlChildren, readOnlyCollection, null);
			}
			if (this._ssdlChildren == null)
			{
				ReadOnlyCollection<MetadataArtifactLoaderFile> readOnlyCollection2 = new ReadOnlyCollection<MetadataArtifactLoaderFile>(MetadataArtifactLoaderCompositeFile.GetArtifactsInDirectory(this._path, ".ssdl", this._uriRegistry));
				Interlocked.CompareExchange<ReadOnlyCollection<MetadataArtifactLoaderFile>>(ref this._ssdlChildren, readOnlyCollection2, null);
			}
			if (this._mslChildren == null)
			{
				ReadOnlyCollection<MetadataArtifactLoaderFile> readOnlyCollection3 = new ReadOnlyCollection<MetadataArtifactLoaderFile>(MetadataArtifactLoaderCompositeFile.GetArtifactsInDirectory(this._path, ".msl", this._uriRegistry));
				Interlocked.CompareExchange<ReadOnlyCollection<MetadataArtifactLoaderFile>>(ref this._mslChildren, readOnlyCollection3, null);
			}
		}

		// Token: 0x06003D51 RID: 15697 RVA: 0x000C9F03 File Offset: 0x000C8103
		public override List<string> GetOriginalPaths(DataSpace spaceToGet)
		{
			return this.GetOriginalPaths();
		}

		// Token: 0x06003D52 RID: 15698 RVA: 0x000C9F0C File Offset: 0x000C810C
		public override List<string> GetPaths(DataSpace spaceToGet)
		{
			List<string> list = new List<string>();
			IList<MetadataArtifactLoaderFile> list2;
			if (!this.TryGetListForSpace(spaceToGet, out list2))
			{
				return list;
			}
			foreach (MetadataArtifactLoaderFile metadataArtifactLoaderFile in list2)
			{
				list.AddRange(metadataArtifactLoaderFile.GetPaths(spaceToGet));
			}
			return list;
		}

		// Token: 0x06003D53 RID: 15699 RVA: 0x000C9F70 File Offset: 0x000C8170
		private bool TryGetListForSpace(DataSpace spaceToGet, out IList<MetadataArtifactLoaderFile> files)
		{
			switch (spaceToGet)
			{
			case DataSpace.CSpace:
				files = this.CsdlChildren;
				return true;
			case DataSpace.SSpace:
				files = this.SsdlChildren;
				return true;
			case DataSpace.CSSpace:
				files = this.MslChildren;
				return true;
			}
			files = null;
			return false;
		}

		// Token: 0x06003D54 RID: 15700 RVA: 0x000C9FB0 File Offset: 0x000C81B0
		public override List<string> GetPaths()
		{
			List<string> list = new List<string>();
			foreach (MetadataArtifactLoaderFile metadataArtifactLoaderFile in this.CsdlChildren)
			{
				list.AddRange(metadataArtifactLoaderFile.GetPaths());
			}
			foreach (MetadataArtifactLoaderFile metadataArtifactLoaderFile2 in this.SsdlChildren)
			{
				list.AddRange(metadataArtifactLoaderFile2.GetPaths());
			}
			foreach (MetadataArtifactLoaderFile metadataArtifactLoaderFile3 in this.MslChildren)
			{
				list.AddRange(metadataArtifactLoaderFile3.GetPaths());
			}
			return list;
		}

		// Token: 0x06003D55 RID: 15701 RVA: 0x000CA090 File Offset: 0x000C8290
		public override List<XmlReader> GetReaders(Dictionary<MetadataArtifactLoader, XmlReader> sourceDictionary)
		{
			List<XmlReader> list = new List<XmlReader>();
			foreach (MetadataArtifactLoaderFile metadataArtifactLoaderFile in this.CsdlChildren)
			{
				list.AddRange(metadataArtifactLoaderFile.GetReaders(sourceDictionary));
			}
			foreach (MetadataArtifactLoaderFile metadataArtifactLoaderFile2 in this.SsdlChildren)
			{
				list.AddRange(metadataArtifactLoaderFile2.GetReaders(sourceDictionary));
			}
			foreach (MetadataArtifactLoaderFile metadataArtifactLoaderFile3 in this.MslChildren)
			{
				list.AddRange(metadataArtifactLoaderFile3.GetReaders(sourceDictionary));
			}
			return list;
		}

		// Token: 0x06003D56 RID: 15702 RVA: 0x000CA170 File Offset: 0x000C8370
		public override List<XmlReader> CreateReaders(DataSpace spaceToGet)
		{
			List<XmlReader> list = new List<XmlReader>();
			IList<MetadataArtifactLoaderFile> list2;
			if (!this.TryGetListForSpace(spaceToGet, out list2))
			{
				return list;
			}
			foreach (MetadataArtifactLoaderFile metadataArtifactLoaderFile in list2)
			{
				list.AddRange(metadataArtifactLoaderFile.CreateReaders(spaceToGet));
			}
			return list;
		}

		// Token: 0x06003D57 RID: 15703 RVA: 0x000CA1D4 File Offset: 0x000C83D4
		private static List<MetadataArtifactLoaderFile> GetArtifactsInDirectory(string directory, string extension, ICollection<string> uriRegistry)
		{
			List<MetadataArtifactLoaderFile> list = new List<MetadataArtifactLoaderFile>();
			foreach (string text in Directory.GetFiles(directory, MetadataArtifactLoader.wildcard + extension, SearchOption.TopDirectoryOnly))
			{
				string text2 = global::System.IO.Path.Combine(directory, text);
				if (!uriRegistry.Contains(text2) && text.EndsWith(extension, StringComparison.OrdinalIgnoreCase))
				{
					list.Add(new MetadataArtifactLoaderFile(text2, uriRegistry));
				}
			}
			return list;
		}

		// Token: 0x040014F1 RID: 5361
		private ReadOnlyCollection<MetadataArtifactLoaderFile> _csdlChildren;

		// Token: 0x040014F2 RID: 5362
		private ReadOnlyCollection<MetadataArtifactLoaderFile> _ssdlChildren;

		// Token: 0x040014F3 RID: 5363
		private ReadOnlyCollection<MetadataArtifactLoaderFile> _mslChildren;

		// Token: 0x040014F4 RID: 5364
		private readonly string _path;

		// Token: 0x040014F5 RID: 5365
		private readonly ICollection<string> _uriRegistry;
	}
}
