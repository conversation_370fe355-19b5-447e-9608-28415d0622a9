﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000018 RID: 24
	public class TS_UltimateLuxuryLimitBuy
	{
		// Token: 0x170000A1 RID: 161
		// (get) Token: 0x06000158 RID: 344 RVA: 0x00002BDA File Offset: 0x00000DDA
		// (set) Token: 0x06000159 RID: 345 RVA: 0x00002BE2 File Offset: 0x00000DE2
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int ID { get; set; }

		// Token: 0x170000A2 RID: 162
		// (get) Token: 0x0600015A RID: 346 RVA: 0x00002BEB File Offset: 0x00000DEB
		// (set) Token: 0x0600015B RID: 347 RVA: 0x00002BF3 File Offset: 0x00000DF3
		public int Round { get; set; }

		// Token: 0x170000A3 RID: 163
		// (get) Token: 0x0600015C RID: 348 RVA: 0x00002BFC File Offset: 0x00000DFC
		// (set) Token: 0x0600015D RID: 349 RVA: 0x00002C04 File Offset: 0x00000E04
		public int ItemID { get; set; }

		// Token: 0x170000A4 RID: 164
		// (get) Token: 0x0600015E RID: 350 RVA: 0x00002C0D File Offset: 0x00000E0D
		// (set) Token: 0x0600015F RID: 351 RVA: 0x00002C15 File Offset: 0x00000E15
		public int Validay { get; set; }

		// Token: 0x170000A5 RID: 165
		// (get) Token: 0x06000160 RID: 352 RVA: 0x00002C1E File Offset: 0x00000E1E
		// (set) Token: 0x06000161 RID: 353 RVA: 0x00002C26 File Offset: 0x00000E26
		public int Count { get; set; }

		// Token: 0x170000A6 RID: 166
		// (get) Token: 0x06000162 RID: 354 RVA: 0x00002C2F File Offset: 0x00000E2F
		// (set) Token: 0x06000163 RID: 355 RVA: 0x00002C37 File Offset: 0x00000E37
		public bool IsBind { get; set; }

		// Token: 0x170000A7 RID: 167
		// (get) Token: 0x06000164 RID: 356 RVA: 0x00002C40 File Offset: 0x00000E40
		// (set) Token: 0x06000165 RID: 357 RVA: 0x00002C48 File Offset: 0x00000E48
		public int OriginalPrice { get; set; }

		// Token: 0x170000A8 RID: 168
		// (get) Token: 0x06000166 RID: 358 RVA: 0x00002C51 File Offset: 0x00000E51
		// (set) Token: 0x06000167 RID: 359 RVA: 0x00002C59 File Offset: 0x00000E59
		public int DiscountedPrice { get; set; }

		// Token: 0x170000A9 RID: 169
		// (get) Token: 0x06000168 RID: 360 RVA: 0x00002C62 File Offset: 0x00000E62
		// (set) Token: 0x06000169 RID: 361 RVA: 0x00002C6A File Offset: 0x00000E6A
		public int LimitBuy { get; set; }
	}
}
