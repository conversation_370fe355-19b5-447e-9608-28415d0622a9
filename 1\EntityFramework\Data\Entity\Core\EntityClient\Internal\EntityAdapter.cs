﻿using System;
using System.Data.Common;
using System.Data.Entity.Core.Mapping.Update.Internal;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Resources;
using System.Threading;
using System.Threading.Tasks;

namespace System.Data.Entity.Core.EntityClient.Internal
{
	// Token: 0x020005E5 RID: 1509
	internal class EntityAdapter : IEntityAdapter
	{
		// Token: 0x060049CD RID: 18893 RVA: 0x00104E32 File Offset: 0x00103032
		public EntityAdapter(ObjectContext context)
			: this(context, (EntityAdapter a) => new UpdateTranslator(a))
		{
		}

		// Token: 0x060049CE RID: 18894 RVA: 0x00104E5A File Offset: 0x0010305A
		protected EntityAdapter(ObjectContext context, Func<EntityAdapter, UpdateTranslator> updateTranslatorFactory)
		{
			this._context = context;
			this._updateTranslatorFactory = updateTranslatorFactory;
		}

		// Token: 0x17000E9B RID: 3739
		// (get) Token: 0x060049CF RID: 18895 RVA: 0x00104E77 File Offset: 0x00103077
		public ObjectContext Context
		{
			get
			{
				return this._context;
			}
		}

		// Token: 0x17000E9C RID: 3740
		// (get) Token: 0x060049D0 RID: 18896 RVA: 0x00104E7F File Offset: 0x0010307F
		// (set) Token: 0x060049D1 RID: 18897 RVA: 0x00104E87 File Offset: 0x00103087
		DbConnection IEntityAdapter.Connection
		{
			get
			{
				return this.Connection;
			}
			set
			{
				this.Connection = (EntityConnection)value;
			}
		}

		// Token: 0x17000E9D RID: 3741
		// (get) Token: 0x060049D2 RID: 18898 RVA: 0x00104E95 File Offset: 0x00103095
		// (set) Token: 0x060049D3 RID: 18899 RVA: 0x00104E9D File Offset: 0x0010309D
		public EntityConnection Connection
		{
			get
			{
				return this._connection;
			}
			set
			{
				this._connection = value;
			}
		}

		// Token: 0x17000E9E RID: 3742
		// (get) Token: 0x060049D4 RID: 18900 RVA: 0x00104EA6 File Offset: 0x001030A6
		// (set) Token: 0x060049D5 RID: 18901 RVA: 0x00104EAE File Offset: 0x001030AE
		public bool AcceptChangesDuringUpdate
		{
			get
			{
				return this._acceptChangesDuringUpdate;
			}
			set
			{
				this._acceptChangesDuringUpdate = value;
			}
		}

		// Token: 0x17000E9F RID: 3743
		// (get) Token: 0x060049D6 RID: 18902 RVA: 0x00104EB7 File Offset: 0x001030B7
		// (set) Token: 0x060049D7 RID: 18903 RVA: 0x00104EBF File Offset: 0x001030BF
		public int? CommandTimeout { get; set; }

		// Token: 0x060049D8 RID: 18904 RVA: 0x00104EC8 File Offset: 0x001030C8
		public int Update()
		{
			return this.Update<int>(0, (UpdateTranslator ut) => ut.Update());
		}

		// Token: 0x060049D9 RID: 18905 RVA: 0x00104EF0 File Offset: 0x001030F0
		public Task<int> UpdateAsync(CancellationToken cancellationToken)
		{
			return this.Update<Task<int>>(Task.FromResult<int>(0), (UpdateTranslator ut) => ut.UpdateAsync(cancellationToken));
		}

		// Token: 0x060049DA RID: 18906 RVA: 0x00104F24 File Offset: 0x00103124
		private T Update<T>(T noChangesResult, Func<UpdateTranslator, T> updateFunction)
		{
			if (!EntityAdapter.IsStateManagerDirty(this._context.ObjectStateManager))
			{
				return noChangesResult;
			}
			if (this._connection == null)
			{
				throw Error.EntityClient_NoConnectionForAdapter();
			}
			if (this._connection.StoreProviderFactory == null || this._connection.StoreConnection == null)
			{
				throw Error.EntityClient_NoStoreConnectionForUpdate();
			}
			if (ConnectionState.Open != this._connection.State)
			{
				throw Error.EntityClient_ClosedConnectionForUpdate();
			}
			UpdateTranslator updateTranslator = this._updateTranslatorFactory(this);
			return updateFunction(updateTranslator);
		}

		// Token: 0x060049DB RID: 18907 RVA: 0x00104F9B File Offset: 0x0010319B
		private static bool IsStateManagerDirty(ObjectStateManager entityCache)
		{
			return entityCache.HasChanges();
		}

		// Token: 0x04001A0A RID: 6666
		private bool _acceptChangesDuringUpdate = true;

		// Token: 0x04001A0B RID: 6667
		private EntityConnection _connection;

		// Token: 0x04001A0C RID: 6668
		private readonly ObjectContext _context;

		// Token: 0x04001A0D RID: 6669
		private readonly Func<EntityAdapter, UpdateTranslator> _updateTranslatorFactory;
	}
}
