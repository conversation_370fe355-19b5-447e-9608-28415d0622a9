﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000028 RID: 40
	public class Sys_Users_DevilTurn
	{
		// Token: 0x17000132 RID: 306
		// (get) Token: 0x0600028B RID: 651 RVA: 0x00003614 File Offset: 0x00001814
		// (set) Token: 0x0600028C RID: 652 RVA: 0x0000361C File Offset: 0x0000181C
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000133 RID: 307
		// (get) Token: 0x0600028D RID: 653 RVA: 0x00003625 File Offset: 0x00001825
		// (set) Token: 0x0600028E RID: 654 RVA: 0x0000362D File Offset: 0x0000182D
		public int UserID { get; set; }

		// Token: 0x17000134 RID: 308
		// (get) Token: 0x0600028F RID: 655 RVA: 0x00003636 File Offset: 0x00001836
		// (set) Token: 0x06000290 RID: 656 RVA: 0x0000363E File Offset: 0x0000183E
		public string Name { get; set; }

		// Token: 0x17000135 RID: 309
		// (get) Token: 0x06000291 RID: 657 RVA: 0x00003647 File Offset: 0x00001847
		// (set) Token: 0x06000292 RID: 658 RVA: 0x0000364F File Offset: 0x0000184F
		public int lotteryCount { get; set; }

		// Token: 0x17000136 RID: 310
		// (get) Token: 0x06000293 RID: 659 RVA: 0x00003658 File Offset: 0x00001858
		// (set) Token: 0x06000294 RID: 660 RVA: 0x00003660 File Offset: 0x00001860
		public int myRankScore { get; set; }

		// Token: 0x17000137 RID: 311
		// (get) Token: 0x06000295 RID: 661 RVA: 0x00003669 File Offset: 0x00001869
		// (set) Token: 0x06000296 RID: 662 RVA: 0x00003671 File Offset: 0x00001871
		public int beadCount1 { get; set; }

		// Token: 0x17000138 RID: 312
		// (get) Token: 0x06000297 RID: 663 RVA: 0x0000367A File Offset: 0x0000187A
		// (set) Token: 0x06000298 RID: 664 RVA: 0x00003682 File Offset: 0x00001882
		public int beadCount2 { get; set; }

		// Token: 0x17000139 RID: 313
		// (get) Token: 0x06000299 RID: 665 RVA: 0x0000368B File Offset: 0x0000188B
		// (set) Token: 0x0600029A RID: 666 RVA: 0x00003693 File Offset: 0x00001893
		public int beadCount3 { get; set; }

		// Token: 0x1700013A RID: 314
		// (get) Token: 0x0600029B RID: 667 RVA: 0x0000369C File Offset: 0x0000189C
		// (set) Token: 0x0600029C RID: 668 RVA: 0x000036A4 File Offset: 0x000018A4
		public int beadCount4 { get; set; }

		// Token: 0x1700013B RID: 315
		// (get) Token: 0x0600029D RID: 669 RVA: 0x000036AD File Offset: 0x000018AD
		// (set) Token: 0x0600029E RID: 670 RVA: 0x000036B5 File Offset: 0x000018B5
		public int beadCount5 { get; set; }

		// Token: 0x1700013C RID: 316
		// (get) Token: 0x0600029F RID: 671 RVA: 0x000036BE File Offset: 0x000018BE
		// (set) Token: 0x060002A0 RID: 672 RVA: 0x000036C6 File Offset: 0x000018C6
		public string getGiftProgress { get; set; }

		// Token: 0x1700013D RID: 317
		// (get) Token: 0x060002A1 RID: 673 RVA: 0x000036CF File Offset: 0x000018CF
		// (set) Token: 0x060002A2 RID: 674 RVA: 0x000036D7 File Offset: 0x000018D7
		public int freeTimes { get; set; }

		// Token: 0x1700013E RID: 318
		// (get) Token: 0x060002A3 RID: 675 RVA: 0x000036E0 File Offset: 0x000018E0
		// (set) Token: 0x060002A4 RID: 676 RVA: 0x000036E8 File Offset: 0x000018E8
		public int HasCount { get; set; }

		// Token: 0x1700013F RID: 319
		// (get) Token: 0x060002A5 RID: 677 RVA: 0x000036F1 File Offset: 0x000018F1
		// (set) Token: 0x060002A6 RID: 678 RVA: 0x000036F9 File Offset: 0x000018F9
		public DateTime freeDate { get; set; }
	}
}
