﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x0200002D RID: 45
	public class Sys_Users_UiTimateLuxuryCarnival
	{
		// Token: 0x17000161 RID: 353
		// (get) Token: 0x060002EE RID: 750 RVA: 0x00003960 File Offset: 0x00001B60
		// (set) Token: 0x060002EF RID: 751 RVA: 0x00003968 File Offset: 0x00001B68
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000162 RID: 354
		// (get) Token: 0x060002F0 RID: 752 RVA: 0x00003971 File Offset: 0x00001B71
		// (set) Token: 0x060002F1 RID: 753 RVA: 0x00003979 File Offset: 0x00001B79
		public int UserID { get; set; }

		// Token: 0x17000163 RID: 355
		// (get) Token: 0x060002F2 RID: 754 RVA: 0x00003982 File Offset: 0x00001B82
		// (set) Token: 0x060002F3 RID: 755 RVA: 0x0000398A File Offset: 0x00001B8A
		public int CarnivalID { get; set; }

		// Token: 0x17000164 RID: 356
		// (get) Token: 0x060002F4 RID: 756 RVA: 0x00003993 File Offset: 0x00001B93
		// (set) Token: 0x060002F5 RID: 757 RVA: 0x0000399B File Offset: 0x00001B9B
		public int Status { get; set; }

		// Token: 0x17000165 RID: 357
		// (get) Token: 0x060002F6 RID: 758 RVA: 0x000039A4 File Offset: 0x00001BA4
		// (set) Token: 0x060002F7 RID: 759 RVA: 0x000039AC File Offset: 0x00001BAC
		public int LimitBuyCount { get; set; }
	}
}
