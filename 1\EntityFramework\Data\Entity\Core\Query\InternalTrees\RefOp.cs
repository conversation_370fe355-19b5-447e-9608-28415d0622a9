﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003D3 RID: 979
	internal sealed class RefOp : ScalarOp
	{
		// Token: 0x06002EB6 RID: 11958 RVA: 0x00093E83 File Offset: 0x00092083
		internal RefOp(EntitySet entitySet, TypeUsage type)
			: base(OpType.Ref, type)
		{
			this.m_entitySet = entitySet;
		}

		// Token: 0x06002EB7 RID: 11959 RVA: 0x00093E95 File Offset: 0x00092095
		private RefOp()
			: base(OpType.Ref)
		{
		}

		// Token: 0x17000926 RID: 2342
		// (get) Token: 0x06002EB8 RID: 11960 RVA: 0x00093E9F File Offset: 0x0009209F
		internal override int Arity
		{
			get
			{
				return 1;
			}
		}

		// Token: 0x17000927 RID: 2343
		// (get) Token: 0x06002EB9 RID: 11961 RVA: 0x00093EA2 File Offset: 0x000920A2
		internal EntitySet EntitySet
		{
			get
			{
				return this.m_entitySet;
			}
		}

		// Token: 0x06002EBA RID: 11962 RVA: 0x00093EAA File Offset: 0x000920AA
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002EBB RID: 11963 RVA: 0x00093EB4 File Offset: 0x000920B4
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000FC1 RID: 4033
		private readonly EntitySet m_entitySet;

		// Token: 0x04000FC2 RID: 4034
		internal static readonly RefOp Pattern = new RefOp();
	}
}
