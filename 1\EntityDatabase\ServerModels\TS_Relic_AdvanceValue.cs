﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000013 RID: 19
	public class TS_Relic_AdvanceValue
	{
		// Token: 0x17000078 RID: 120
		// (get) Token: 0x06000101 RID: 257 RVA: 0x000028F4 File Offset: 0x00000AF4
		// (set) Token: 0x06000102 RID: 258 RVA: 0x000028FC File Offset: 0x00000AFC
		[Key]
		public int ID { get; set; }

		// Token: 0x17000079 RID: 121
		// (get) Token: 0x06000103 RID: 259 RVA: 0x00002905 File Offset: 0x00000B05
		// (set) Token: 0x06000104 RID: 260 RVA: 0x0000290D File Offset: 0x00000B0D
		public int Quality { get; set; }

		// Token: 0x1700007A RID: 122
		// (get) Token: 0x06000105 RID: 261 RVA: 0x00002916 File Offset: 0x00000B16
		// (set) Token: 0x06000106 RID: 262 RVA: 0x0000291E File Offset: 0x00000B1E
		public int Type { get; set; }

		// Token: 0x1700007B RID: 123
		// (get) Token: 0x06000107 RID: 263 RVA: 0x00002927 File Offset: 0x00000B27
		// (set) Token: 0x06000108 RID: 264 RVA: 0x0000292F File Offset: 0x00000B2F
		public int MinValue { get; set; }

		// Token: 0x1700007C RID: 124
		// (get) Token: 0x06000109 RID: 265 RVA: 0x00002938 File Offset: 0x00000B38
		// (set) Token: 0x0600010A RID: 266 RVA: 0x00002940 File Offset: 0x00000B40
		public int MaxValue { get; set; }
	}
}
