﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace System.Data.Entity.Core.Common.CommandTrees.Internal
{
	// Token: 0x020006EB RID: 1771
	internal abstract class DbExpressionRuleProcessingVisitor : DefaultExpressionVisitor
	{
		// Token: 0x06005225 RID: 21029
		protected abstract IEnumerable<DbExpressionRule> GetRules();

		// Token: 0x06005226 RID: 21030 RVA: 0x00125144 File Offset: 0x00123344
		private static Tuple<DbExpression, DbExpressionRule.ProcessedAction> ProcessRules(DbExpression expression, List<DbExpressionRule> rules)
		{
			for (int i = 0; i < rules.Count; i++)
			{
				DbExpressionRule dbExpressionRule = rules[i];
				DbExpression dbExpression;
				if (dbExpressionRule.ShouldProcess(expression) && dbExpressionRule.TryProcess(expression, out dbExpression))
				{
					if (dbExpressionRule.OnExpressionProcessed != DbExpressionRule.ProcessedAction.Continue)
					{
						return Tuple.Create<DbExpression, DbExpressionRule.ProcessedAction>(dbExpression, dbExpressionRule.OnExpressionProcessed);
					}
					expression = dbExpression;
				}
			}
			return Tuple.Create<DbExpression, DbExpressionRule.ProcessedAction>(expression, DbExpressionRule.ProcessedAction.Continue);
		}

		// Token: 0x06005227 RID: 21031 RVA: 0x001251A0 File Offset: 0x001233A0
		private DbExpression ApplyRules(DbExpression expression)
		{
			List<DbExpressionRule> list = this.GetRules().ToList<DbExpressionRule>();
			Tuple<DbExpression, DbExpressionRule.ProcessedAction> tuple = DbExpressionRuleProcessingVisitor.ProcessRules(expression, list);
			while (tuple.Item2 == DbExpressionRule.ProcessedAction.Reset)
			{
				list = this.GetRules().ToList<DbExpressionRule>();
				tuple = DbExpressionRuleProcessingVisitor.ProcessRules(tuple.Item1, list);
			}
			if (tuple.Item2 == DbExpressionRule.ProcessedAction.Stop)
			{
				this._stopped = true;
			}
			return tuple.Item1;
		}

		// Token: 0x06005228 RID: 21032 RVA: 0x001251FC File Offset: 0x001233FC
		protected override DbExpression VisitExpression(DbExpression expression)
		{
			DbExpression dbExpression = this.ApplyRules(expression);
			if (this._stopped)
			{
				return dbExpression;
			}
			dbExpression = base.VisitExpression(dbExpression);
			if (this._stopped)
			{
				return dbExpression;
			}
			return this.ApplyRules(dbExpression);
		}

		// Token: 0x04001DE3 RID: 7651
		private bool _stopped;
	}
}
