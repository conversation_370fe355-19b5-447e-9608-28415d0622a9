﻿using System;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Text;
using System.Text.RegularExpressions;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.CqlGeneration
{
	// Token: 0x020005BC RID: 1468
	internal static class CqlWriter
	{
		// Token: 0x06004751 RID: 18257 RVA: 0x000FAF9D File Offset: 0x000F919D
		internal static string GetQualifiedName(string blockName, string field)
		{
			return StringUtil.FormatInvariant("{0}.{1}", new object[] { blockName, field });
		}

		// Token: 0x06004752 RID: 18258 RVA: 0x000FAFB7 File Offset: 0x000F91B7
		internal static void AppendEscapedTypeName(StringBuilder builder, EdmType type)
		{
			CqlWriter.AppendEscapedName(builder, CqlWriter.GetQualifiedName(type.NamespaceName, type.Name));
		}

		// Token: 0x06004753 RID: 18259 RVA: 0x000FAFD0 File Offset: 0x000F91D0
		internal static void AppendEscapedQualifiedName(StringBuilder builder, string name1, string name2)
		{
			CqlWriter.AppendEscapedName(builder, name1);
			builder.Append('.');
			CqlWriter.AppendEscapedName(builder, name2);
		}

		// Token: 0x06004754 RID: 18260 RVA: 0x000FAFEC File Offset: 0x000F91EC
		internal static void AppendEscapedName(StringBuilder builder, string name)
		{
			if (CqlWriter._wordIdentifierRegex.IsMatch(name) && !ExternalCalls.IsReservedKeyword(name))
			{
				builder.Append(name);
				return;
			}
			string text = name.Replace("]", "]]");
			builder.Append('[').Append(text).Append(']');
		}

		// Token: 0x0400194A RID: 6474
		private static readonly Regex _wordIdentifierRegex = new Regex("^[_A-Za-z]\\w*$", RegexOptions.Compiled | RegexOptions.ECMAScript);
	}
}
