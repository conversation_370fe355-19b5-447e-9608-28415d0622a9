﻿using System;

namespace System.Data.Entity.Core.Mapping.Update.Internal
{
	// Token: 0x020005C5 RID: 1477
	internal struct ExtractedStateEntry
	{
		// Token: 0x06004789 RID: 18313 RVA: 0x000FC14D File Offset: 0x000FA34D
		internal ExtractedStateEntry(EntityState state, PropagatorResult original, PropagatorResult current, IEntityStateEntry source)
		{
			this.State = state;
			this.Original = original;
			this.Current = current;
			this.Source = source;
		}

		// Token: 0x0600478A RID: 18314 RVA: 0x000FC16C File Offset: 0x000FA36C
		internal ExtractedStateEntry(UpdateTranslator translator, IEntityStateEntry stateEntry)
		{
			this.State = stateEntry.State;
			this.Source = stateEntry;
			EntityState state = stateEntry.State;
			if (state <= EntityState.Added)
			{
				if (state == EntityState.Unchanged)
				{
					this.Original = translator.RecordConverter.ConvertOriginalValuesToPropagatorResult(stateEntry, ModifiedPropertiesBehavior.NoneModified);
					this.Current = translator.RecordConverter.ConvertCurrentValuesToPropagatorResult(stateEntry, ModifiedPropertiesBehavior.NoneModified);
					return;
				}
				if (state == EntityState.Added)
				{
					this.Original = null;
					this.Current = translator.RecordConverter.ConvertCurrentValuesToPropagatorResult(stateEntry, ModifiedPropertiesBehavior.AllModified);
					return;
				}
			}
			else
			{
				if (state == EntityState.Deleted)
				{
					this.Original = translator.RecordConverter.ConvertOriginalValuesToPropagatorResult(stateEntry, ModifiedPropertiesBehavior.AllModified);
					this.Current = null;
					return;
				}
				if (state == EntityState.Modified)
				{
					this.Original = translator.RecordConverter.ConvertOriginalValuesToPropagatorResult(stateEntry, ModifiedPropertiesBehavior.SomeModified);
					this.Current = translator.RecordConverter.ConvertCurrentValuesToPropagatorResult(stateEntry, ModifiedPropertiesBehavior.SomeModified);
					return;
				}
			}
			this.Original = null;
			this.Current = null;
		}

		// Token: 0x04001963 RID: 6499
		internal readonly EntityState State;

		// Token: 0x04001964 RID: 6500
		internal readonly PropagatorResult Original;

		// Token: 0x04001965 RID: 6501
		internal readonly PropagatorResult Current;

		// Token: 0x04001966 RID: 6502
		internal readonly IEntityStateEntry Source;
	}
}
