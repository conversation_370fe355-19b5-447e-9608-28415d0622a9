﻿using System;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004E2 RID: 1250
	internal class ModelPerspective : Perspective
	{
		// Token: 0x06003E61 RID: 15969 RVA: 0x000CE4AD File Offset: 0x000CC6AD
		internal ModelPerspective(MetadataWorkspace metadataWorkspace)
			: base(metadataWorkspace, DataSpace.CSpace)
		{
		}

		// Token: 0x06003E62 RID: 15970 RVA: 0x000CE4B8 File Offset: 0x000CC6B8
		internal override bool TryGetTypeByName(string fullName, bool ignoreCase, out TypeUsage typeUsage)
		{
			Check.NotEmpty(fullName, "fullName");
			typeUsage = null;
			EdmType edmType = null;
			if (base.MetadataWorkspace.TryGetItem<EdmType>(fullName, ignoreCase, base.TargetDataspace, out edmType))
			{
				if (Helper.IsPrimitiveType(edmType))
				{
					typeUsage = MetadataWorkspace.GetCanonicalModelTypeUsage(((PrimitiveType)edmType).PrimitiveTypeKind);
				}
				else
				{
					typeUsage = TypeUsage.Create(edmType);
				}
			}
			return typeUsage != null;
		}
	}
}
