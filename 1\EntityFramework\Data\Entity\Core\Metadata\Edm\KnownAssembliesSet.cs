﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x0200050C RID: 1292
	internal class KnownAssembliesSet
	{
		// Token: 0x06003FD1 RID: 16337 RVA: 0x000D38C0 File Offset: 0x000D1AC0
		internal KnownAssembliesSet()
		{
			this._assemblies = new Dictionary<Assembly, KnownAssemblyEntry>();
		}

		// Token: 0x06003FD2 RID: 16338 RVA: 0x000D38D3 File Offset: 0x000D1AD3
		internal KnownAssembliesSet(KnownAssembliesSet set)
		{
			this._assemblies = new Dictionary<Assembly, KnownAssemblyEntry>(set._assemblies);
		}

		// Token: 0x06003FD3 RID: 16339 RVA: 0x000D38EC File Offset: 0x000D1AEC
		internal virtual bool TryGetKnownAssembly(Assembly assembly, object loaderCookie, EdmItemCollection itemCollection, out KnownAssemblyEntry entry)
		{
			return this._assemblies.TryGetValue(assembly, out entry) && entry.HaveSeenInCompatibleContext(loaderCookie, itemCollection);
		}

		// Token: 0x17000C7C RID: 3196
		// (get) Token: 0x06003FD4 RID: 16340 RVA: 0x000D390F File Offset: 0x000D1B0F
		internal IEnumerable<Assembly> Assemblies
		{
			get
			{
				return this._assemblies.Keys;
			}
		}

		// Token: 0x06003FD5 RID: 16341 RVA: 0x000D391C File Offset: 0x000D1B1C
		public IEnumerable<KnownAssemblyEntry> GetEntries(object loaderCookie, EdmItemCollection itemCollection)
		{
			return this._assemblies.Values.Where((KnownAssemblyEntry e) => e.HaveSeenInCompatibleContext(loaderCookie, itemCollection));
		}

		// Token: 0x06003FD6 RID: 16342 RVA: 0x000D395C File Offset: 0x000D1B5C
		internal bool Contains(Assembly assembly, object loaderCookie, EdmItemCollection itemCollection)
		{
			KnownAssemblyEntry knownAssemblyEntry;
			return this.TryGetKnownAssembly(assembly, loaderCookie, itemCollection, out knownAssemblyEntry);
		}

		// Token: 0x06003FD7 RID: 16343 RVA: 0x000D3974 File Offset: 0x000D1B74
		internal void Add(Assembly assembly, KnownAssemblyEntry knownAssemblyEntry)
		{
			KnownAssemblyEntry knownAssemblyEntry2;
			if (this._assemblies.TryGetValue(assembly, out knownAssemblyEntry2))
			{
				this._assemblies[assembly] = knownAssemblyEntry;
				return;
			}
			this._assemblies.Add(assembly, knownAssemblyEntry);
		}

		// Token: 0x04001645 RID: 5701
		private readonly Dictionary<Assembly, KnownAssemblyEntry> _assemblies;
	}
}
