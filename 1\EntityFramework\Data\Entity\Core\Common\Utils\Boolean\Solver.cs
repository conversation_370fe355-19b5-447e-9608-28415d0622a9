﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace System.Data.Entity.Core.Common.Utils.Boolean
{
	// Token: 0x0200061F RID: 1567
	internal sealed class Solver
	{
		// Token: 0x06004BE7 RID: 19431 RVA: 0x0010AC14 File Offset: 0x00108E14
		internal int CreateVariable()
		{
			int num = this._variableCount + 1;
			this._variableCount = num;
			return num;
		}

		// Token: 0x06004BE8 RID: 19432 RVA: 0x0010AC32 File Offset: 0x00108E32
		internal Vertex Not(Vertex vertex)
		{
			return this.IfThenElse(vertex, Vertex.Zero, Vertex.One);
		}

		// Token: 0x06004BE9 RID: 19433 RVA: 0x0010AC45 File Offset: 0x00108E45
		internal Vertex And(IEnumerable<Vertex> children)
		{
			return children.OrderByDescending((Vertex child) => child.Variable).Aggregate(Vertex.One, (Vertex left, Vertex right) => this.IfThenElse(left, right, Vertex.Zero));
		}

		// Token: 0x06004BEA RID: 19434 RVA: 0x0010AC82 File Offset: 0x00108E82
		internal Vertex And(Vertex left, Vertex right)
		{
			return this.IfThenElse(left, right, Vertex.Zero);
		}

		// Token: 0x06004BEB RID: 19435 RVA: 0x0010AC91 File Offset: 0x00108E91
		internal Vertex Or(IEnumerable<Vertex> children)
		{
			return children.OrderByDescending((Vertex child) => child.Variable).Aggregate(Vertex.Zero, (Vertex left, Vertex right) => this.IfThenElse(left, Vertex.One, right));
		}

		// Token: 0x06004BEC RID: 19436 RVA: 0x0010ACCE File Offset: 0x00108ECE
		internal Vertex CreateLeafVertex(int variable, Vertex[] children)
		{
			return this.GetUniqueVertex(variable, children);
		}

		// Token: 0x06004BED RID: 19437 RVA: 0x0010ACD8 File Offset: 0x00108ED8
		private Vertex GetUniqueVertex(int variable, Vertex[] children)
		{
			Vertex vertex = new Vertex(variable, children);
			Vertex vertex2;
			if (this._knownVertices.TryGetValue(vertex, out vertex2))
			{
				return vertex2;
			}
			this._knownVertices.Add(vertex, vertex);
			return vertex;
		}

		// Token: 0x06004BEE RID: 19438 RVA: 0x0010AD10 File Offset: 0x00108F10
		private Vertex IfThenElse(Vertex condition, Vertex then, Vertex @else)
		{
			if (condition.IsOne())
			{
				return then;
			}
			if (condition.IsZero())
			{
				return @else;
			}
			if (then.IsOne() && @else.IsZero())
			{
				return condition;
			}
			if (then.Equals(@else))
			{
				return then;
			}
			Triple<Vertex, Vertex, Vertex> triple = new Triple<Vertex, Vertex, Vertex>(condition, then, @else);
			Vertex uniqueVertex;
			if (this._computedIfThenElseValues.TryGetValue(triple, out uniqueVertex))
			{
				return uniqueVertex;
			}
			int num2;
			int num = Solver.DetermineTopVariable(condition, then, @else, out num2);
			Vertex[] array = new Vertex[num2];
			bool flag = true;
			for (int i = 0; i < num2; i++)
			{
				array[i] = this.IfThenElse(Solver.EvaluateFor(condition, num, i), Solver.EvaluateFor(then, num, i), Solver.EvaluateFor(@else, num, i));
				if (i > 0 && flag && !array[i].Equals(array[0]))
				{
					flag = false;
				}
			}
			if (flag)
			{
				return array[0];
			}
			uniqueVertex = this.GetUniqueVertex(num, array);
			this._computedIfThenElseValues.Add(triple, uniqueVertex);
			return uniqueVertex;
		}

		// Token: 0x06004BEF RID: 19439 RVA: 0x0010ADF4 File Offset: 0x00108FF4
		private static int DetermineTopVariable(Vertex condition, Vertex then, Vertex @else, out int topVariableDomainCount)
		{
			int num;
			if (condition.Variable < then.Variable)
			{
				num = condition.Variable;
				topVariableDomainCount = condition.Children.Length;
			}
			else
			{
				num = then.Variable;
				topVariableDomainCount = then.Children.Length;
			}
			if (@else.Variable < num)
			{
				num = @else.Variable;
				topVariableDomainCount = @else.Children.Length;
			}
			return num;
		}

		// Token: 0x06004BF0 RID: 19440 RVA: 0x0010AE4E File Offset: 0x0010904E
		private static Vertex EvaluateFor(Vertex vertex, int variable, int variableAssignment)
		{
			if (variable < vertex.Variable)
			{
				return vertex;
			}
			return vertex.Children[variableAssignment];
		}

		// Token: 0x06004BF1 RID: 19441 RVA: 0x0010AE64 File Offset: 0x00109064
		[Conditional("DEBUG")]
		private void AssertVerticesValid(IEnumerable<Vertex> vertices)
		{
			foreach (Vertex vertex in vertices)
			{
			}
		}

		// Token: 0x06004BF2 RID: 19442 RVA: 0x0010AEA8 File Offset: 0x001090A8
		[Conditional("DEBUG")]
		private void AssertVertexValid(Vertex vertex)
		{
			vertex.IsSink();
		}

		// Token: 0x04001A83 RID: 6787
		private readonly Dictionary<Triple<Vertex, Vertex, Vertex>, Vertex> _computedIfThenElseValues = new Dictionary<Triple<Vertex, Vertex, Vertex>, Vertex>();

		// Token: 0x04001A84 RID: 6788
		private readonly Dictionary<Vertex, Vertex> _knownVertices = new Dictionary<Vertex, Vertex>(Solver.VertexValueComparer.Instance);

		// Token: 0x04001A85 RID: 6789
		private int _variableCount;

		// Token: 0x04001A86 RID: 6790
		internal static readonly Vertex[] BooleanVariableChildren = new Vertex[]
		{
			Vertex.One,
			Vertex.Zero
		};

		// Token: 0x02000C59 RID: 3161
		private class VertexValueComparer : IEqualityComparer<Vertex>
		{
			// Token: 0x06006ACA RID: 27338 RVA: 0x0016BD1A File Offset: 0x00169F1A
			private VertexValueComparer()
			{
			}

			// Token: 0x06006ACB RID: 27339 RVA: 0x0016BD24 File Offset: 0x00169F24
			public bool Equals(Vertex x, Vertex y)
			{
				if (x.IsSink())
				{
					return x.Equals(y);
				}
				if (x.Variable != y.Variable || x.Children.Length != y.Children.Length)
				{
					return false;
				}
				for (int i = 0; i < x.Children.Length; i++)
				{
					if (!x.Children[i].Equals(y.Children[i]))
					{
						return false;
					}
				}
				return true;
			}

			// Token: 0x06006ACC RID: 27340 RVA: 0x0016BD90 File Offset: 0x00169F90
			public int GetHashCode(Vertex vertex)
			{
				if (vertex.IsSink())
				{
					return vertex.GetHashCode();
				}
				return (vertex.Children[0].GetHashCode() << 5) + 1 + vertex.Children[1].GetHashCode();
			}

			// Token: 0x040030E4 RID: 12516
			internal static readonly Solver.VertexValueComparer Instance = new Solver.VertexValueComparer();
		}
	}
}
