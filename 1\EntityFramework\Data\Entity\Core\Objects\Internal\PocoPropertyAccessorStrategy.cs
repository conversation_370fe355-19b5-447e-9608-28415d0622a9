﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects.DataClasses;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq.Expressions;
using System.Reflection;

namespace System.Data.Entity.Core.Objects.Internal
{
	// Token: 0x02000455 RID: 1109
	internal sealed class PocoPropertyAccessorStrategy : IPropertyAccessorStrategy
	{
		// Token: 0x0600362C RID: 13868 RVA: 0x000AD826 File Offset: 0x000ABA26
		public PocoPropertyAccessorStrategy(object entity)
		{
			this._entity = entity;
		}

		// Token: 0x0600362D RID: 13869 RVA: 0x000AD838 File Offset: 0x000ABA38
		public object GetNavigationPropertyValue(RelatedEnd relatedEnd)
		{
			object obj = null;
			if (relatedEnd != null)
			{
				if (relatedEnd.TargetAccessor.ValueGetter == null)
				{
					Type declaringType = PocoPropertyAccessorStrategy.GetDeclaringType(relatedEnd);
					PropertyInfo topProperty = declaringType.GetTopProperty(relatedEnd.TargetAccessor.PropertyName);
					if (topProperty == null)
					{
						throw new EntityException(Strings.PocoEntityWrapper_UnableToSetFieldOrProperty(relatedEnd.TargetAccessor.PropertyName, declaringType.FullName));
					}
					EntityProxyFactory entityProxyFactory = new EntityProxyFactory();
					relatedEnd.TargetAccessor.ValueGetter = entityProxyFactory.CreateBaseGetter(topProperty.DeclaringType, topProperty);
				}
				bool flag = relatedEnd.DisableLazyLoading();
				try
				{
					obj = relatedEnd.TargetAccessor.ValueGetter(this._entity);
				}
				catch (Exception ex)
				{
					throw new EntityException(Strings.PocoEntityWrapper_UnableToSetFieldOrProperty(relatedEnd.TargetAccessor.PropertyName, this._entity.GetType().FullName), ex);
				}
				finally
				{
					relatedEnd.ResetLazyLoading(flag);
				}
			}
			return obj;
		}

		// Token: 0x0600362E RID: 13870 RVA: 0x000AD928 File Offset: 0x000ABB28
		public void SetNavigationPropertyValue(RelatedEnd relatedEnd, object value)
		{
			if (relatedEnd != null)
			{
				if (relatedEnd.TargetAccessor.ValueSetter == null)
				{
					Type declaringType = PocoPropertyAccessorStrategy.GetDeclaringType(relatedEnd);
					PropertyInfo topProperty = declaringType.GetTopProperty(relatedEnd.TargetAccessor.PropertyName);
					if (topProperty == null)
					{
						throw new EntityException(Strings.PocoEntityWrapper_UnableToSetFieldOrProperty(relatedEnd.TargetAccessor.PropertyName, declaringType.FullName));
					}
					EntityProxyFactory entityProxyFactory = new EntityProxyFactory();
					relatedEnd.TargetAccessor.ValueSetter = entityProxyFactory.CreateBaseSetter(topProperty.DeclaringType, topProperty);
				}
				try
				{
					relatedEnd.TargetAccessor.ValueSetter(this._entity, value);
				}
				catch (Exception ex)
				{
					throw new EntityException(Strings.PocoEntityWrapper_UnableToSetFieldOrProperty(relatedEnd.TargetAccessor.PropertyName, this._entity.GetType().FullName), ex);
				}
			}
		}

		// Token: 0x0600362F RID: 13871 RVA: 0x000AD9F8 File Offset: 0x000ABBF8
		private static Type GetDeclaringType(RelatedEnd relatedEnd)
		{
			if (relatedEnd.NavigationProperty != null)
			{
				return Util.GetObjectMapping((EntityType)relatedEnd.NavigationProperty.DeclaringType, relatedEnd.WrappedOwner.Context.MetadataWorkspace).ClrType.ClrType;
			}
			return relatedEnd.WrappedOwner.IdentityType;
		}

		// Token: 0x06003630 RID: 13872 RVA: 0x000ADA48 File Offset: 0x000ABC48
		private static Type GetNavigationPropertyType(Type entityType, string propertyName)
		{
			PropertyInfo topProperty = entityType.GetTopProperty(propertyName);
			Type type;
			if (topProperty != null)
			{
				type = topProperty.PropertyType;
			}
			else
			{
				FieldInfo field = entityType.GetField(propertyName);
				if (!(field != null))
				{
					throw new EntityException(Strings.PocoEntityWrapper_UnableToSetFieldOrProperty(propertyName, entityType.FullName));
				}
				type = field.FieldType;
			}
			return type;
		}

		// Token: 0x06003631 RID: 13873 RVA: 0x000ADA9C File Offset: 0x000ABC9C
		public void CollectionAdd(RelatedEnd relatedEnd, object value)
		{
			object entity = this._entity;
			try
			{
				object obj = this.GetNavigationPropertyValue(relatedEnd);
				if (obj == null)
				{
					obj = this.CollectionCreate(relatedEnd);
					this.SetNavigationPropertyValue(relatedEnd, obj);
				}
				if (obj != relatedEnd)
				{
					if (relatedEnd.TargetAccessor.CollectionAdd == null)
					{
						relatedEnd.TargetAccessor.CollectionAdd = PocoPropertyAccessorStrategy.CreateCollectionAddFunction(PocoPropertyAccessorStrategy.GetDeclaringType(relatedEnd), relatedEnd.TargetAccessor.PropertyName);
					}
					relatedEnd.TargetAccessor.CollectionAdd(obj, value);
				}
			}
			catch (Exception ex)
			{
				throw new EntityException(Strings.PocoEntityWrapper_UnableToSetFieldOrProperty(relatedEnd.TargetAccessor.PropertyName, entity.GetType().FullName), ex);
			}
		}

		// Token: 0x06003632 RID: 13874 RVA: 0x000ADB48 File Offset: 0x000ABD48
		private static Action<object, object> CreateCollectionAddFunction(Type type, string propertyName)
		{
			Type collectionElementType = EntityUtil.GetCollectionElementType(PocoPropertyAccessorStrategy.GetNavigationPropertyType(type, propertyName));
			return (Action<object, object>)PocoPropertyAccessorStrategy.AddToCollectionGeneric.MakeGenericMethod(new Type[] { collectionElementType }).Invoke(null, null);
		}

		// Token: 0x06003633 RID: 13875 RVA: 0x000ADB82 File Offset: 0x000ABD82
		private static Action<object, object> AddToCollection<T>()
		{
			return delegate(object collectionArg, object item)
			{
				ICollection<T> collection = (ICollection<T>)collectionArg;
				Array array = collection as Array;
				if (array != null && array.IsFixedSize)
				{
					throw new InvalidOperationException(Strings.RelatedEnd_CannotAddToFixedSizeArray(array.GetType()));
				}
				collection.Add((T)((object)item));
			};
		}

		// Token: 0x06003634 RID: 13876 RVA: 0x000ADBA4 File Offset: 0x000ABDA4
		public bool CollectionRemove(RelatedEnd relatedEnd, object value)
		{
			object entity = this._entity;
			try
			{
				object navigationPropertyValue = this.GetNavigationPropertyValue(relatedEnd);
				if (navigationPropertyValue != null)
				{
					if (navigationPropertyValue == relatedEnd)
					{
						return true;
					}
					if (relatedEnd.TargetAccessor.CollectionRemove == null)
					{
						relatedEnd.TargetAccessor.CollectionRemove = PocoPropertyAccessorStrategy.CreateCollectionRemoveFunction(PocoPropertyAccessorStrategy.GetDeclaringType(relatedEnd), relatedEnd.TargetAccessor.PropertyName);
					}
					return relatedEnd.TargetAccessor.CollectionRemove(navigationPropertyValue, value);
				}
			}
			catch (Exception ex)
			{
				throw new EntityException(Strings.PocoEntityWrapper_UnableToSetFieldOrProperty(relatedEnd.TargetAccessor.PropertyName, entity.GetType().FullName), ex);
			}
			return false;
		}

		// Token: 0x06003635 RID: 13877 RVA: 0x000ADC48 File Offset: 0x000ABE48
		private static Func<object, object, bool> CreateCollectionRemoveFunction(Type type, string propertyName)
		{
			Type collectionElementType = EntityUtil.GetCollectionElementType(PocoPropertyAccessorStrategy.GetNavigationPropertyType(type, propertyName));
			return (Func<object, object, bool>)PocoPropertyAccessorStrategy.RemoveFromCollectionGeneric.MakeGenericMethod(new Type[] { collectionElementType }).Invoke(null, null);
		}

		// Token: 0x06003636 RID: 13878 RVA: 0x000ADC82 File Offset: 0x000ABE82
		private static Func<object, object, bool> RemoveFromCollection<T>()
		{
			return delegate(object collectionArg, object item)
			{
				ICollection<T> collection = (ICollection<T>)collectionArg;
				Array array = collection as Array;
				if (array != null && array.IsFixedSize)
				{
					throw new InvalidOperationException(Strings.RelatedEnd_CannotRemoveFromFixedSizeArray(array.GetType()));
				}
				return collection.Remove((T)((object)item));
			};
		}

		// Token: 0x06003637 RID: 13879 RVA: 0x000ADCA4 File Offset: 0x000ABEA4
		public object CollectionCreate(RelatedEnd relatedEnd)
		{
			if (this._entity is IEntityWithRelationships)
			{
				return relatedEnd;
			}
			if (relatedEnd.TargetAccessor.CollectionCreate == null)
			{
				Type declaringType = PocoPropertyAccessorStrategy.GetDeclaringType(relatedEnd);
				string propertyName = relatedEnd.TargetAccessor.PropertyName;
				Type navigationPropertyType = PocoPropertyAccessorStrategy.GetNavigationPropertyType(declaringType, propertyName);
				relatedEnd.TargetAccessor.CollectionCreate = PocoPropertyAccessorStrategy.CreateCollectionCreateDelegate(navigationPropertyType, propertyName);
			}
			return relatedEnd.TargetAccessor.CollectionCreate();
		}

		// Token: 0x06003638 RID: 13880 RVA: 0x000ADD08 File Offset: 0x000ABF08
		private static Func<object> CreateCollectionCreateDelegate(Type navigationPropertyType, string propName)
		{
			Type type = EntityUtil.DetermineCollectionType(navigationPropertyType);
			if (type == null)
			{
				throw new EntityException(Strings.PocoEntityWrapper_UnableToMaterializeArbitaryNavPropType(propName, navigationPropertyType));
			}
			return Expression.Lambda<Func<object>>(DelegateFactory.GetNewExpressionForCollectionType(type), new ParameterExpression[0]).Compile();
		}

		// Token: 0x0400117F RID: 4479
		internal static readonly MethodInfo AddToCollectionGeneric = typeof(PocoPropertyAccessorStrategy).GetOnlyDeclaredMethod("AddToCollection");

		// Token: 0x04001180 RID: 4480
		internal static readonly MethodInfo RemoveFromCollectionGeneric = typeof(PocoPropertyAccessorStrategy).GetOnlyDeclaredMethod("RemoveFromCollection");

		// Token: 0x04001181 RID: 4481
		private readonly object _entity;
	}
}
