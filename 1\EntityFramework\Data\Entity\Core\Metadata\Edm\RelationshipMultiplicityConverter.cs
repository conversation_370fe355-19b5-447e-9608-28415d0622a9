﻿using System;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004F4 RID: 1268
	internal static class RelationshipMultiplicityConverter
	{
		// Token: 0x06003F03 RID: 16131 RVA: 0x000D0A16 File Offset: 0x000CEC16
		internal static string MultiplicityToString(RelationshipMultiplicity multiplicity)
		{
			switch (multiplicity)
			{
			case RelationshipMultiplicity.ZeroOrOne:
				return "0..1";
			case RelationshipMultiplicity.One:
				return "1";
			case RelationshipMultiplicity.Many:
				return "*";
			default:
				return string.Empty;
			}
		}

		// Token: 0x06003F04 RID: 16132 RVA: 0x000D0A44 File Offset: 0x000CEC44
		internal static bool TryParseMultiplicity(string value, out RelationshipMultiplicity multiplicity)
		{
			if (value != null)
			{
				if (value == "*")
				{
					multiplicity = RelationshipMultiplicity.Many;
					return true;
				}
				if (value == "1")
				{
					multiplicity = RelationshipMultiplicity.One;
					return true;
				}
				if (value == "0..1")
				{
					multiplicity = RelationshipMultiplicity.ZeroOrOne;
					return true;
				}
			}
			multiplicity = (RelationshipMultiplicity)(-1);
			return false;
		}
	}
}
