﻿using System;
using System.ComponentModel;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Runtime.Serialization;
using System.Xml.Serialization;

namespace System.Data.Entity.Core.Objects.DataClasses
{
	// Token: 0x02000477 RID: 1143
	[DataContract(IsReference = true)]
	[Serializable]
	public abstract class EntityObject : StructuralObject, IEntityWithKey, IEntityWithChangeTracker, IEntityWithRelationships
	{
		// Token: 0x17000ABF RID: 2751
		// (get) Token: 0x060037FF RID: 14335 RVA: 0x000B661F File Offset: 0x000B481F
		// (set) Token: 0x06003800 RID: 14336 RVA: 0x000B663A File Offset: 0x000B483A
		private IEntityChangeTracker EntityChangeTracker
		{
			get
			{
				if (this._entityChangeTracker == null)
				{
					this._entityChangeTracker = EntityObject._detachedEntityChangeTracker;
				}
				return this._entityChangeTracker;
			}
			set
			{
				this._entityChangeTracker = value;
			}
		}

		// Token: 0x17000AC0 RID: 2752
		// (get) Token: 0x06003801 RID: 14337 RVA: 0x000B6643 File Offset: 0x000B4843
		[Browsable(false)]
		[XmlIgnore]
		public EntityState EntityState
		{
			get
			{
				return this.EntityChangeTracker.EntityState;
			}
		}

		// Token: 0x17000AC1 RID: 2753
		// (get) Token: 0x06003802 RID: 14338 RVA: 0x000B6650 File Offset: 0x000B4850
		// (set) Token: 0x06003803 RID: 14339 RVA: 0x000B6658 File Offset: 0x000B4858
		[Browsable(false)]
		[DataMember]
		public EntityKey EntityKey
		{
			get
			{
				return this._entityKey;
			}
			set
			{
				this.EntityChangeTracker.EntityMemberChanging("-EntityKey-");
				this._entityKey = value;
				this.EntityChangeTracker.EntityMemberChanged("-EntityKey-");
			}
		}

		// Token: 0x06003804 RID: 14340 RVA: 0x000B6684 File Offset: 0x000B4884
		void IEntityWithChangeTracker.SetChangeTracker(IEntityChangeTracker changeTracker)
		{
			if (changeTracker != null && this.EntityChangeTracker != EntityObject._detachedEntityChangeTracker && changeTracker != this.EntityChangeTracker)
			{
				EntityEntry entityEntry = this.EntityChangeTracker as EntityEntry;
				if (entityEntry == null || !entityEntry.ObjectStateManager.IsDisposed)
				{
					throw new InvalidOperationException(Strings.Entity_EntityCantHaveMultipleChangeTrackers);
				}
			}
			this.EntityChangeTracker = changeTracker;
		}

		// Token: 0x17000AC2 RID: 2754
		// (get) Token: 0x06003805 RID: 14341 RVA: 0x000B66D8 File Offset: 0x000B48D8
		RelationshipManager IEntityWithRelationships.RelationshipManager
		{
			get
			{
				if (this._relationships == null)
				{
					this._relationships = RelationshipManager.Create(this);
				}
				return this._relationships;
			}
		}

		// Token: 0x06003806 RID: 14342 RVA: 0x000B66F4 File Offset: 0x000B48F4
		protected sealed override void ReportPropertyChanging(string property)
		{
			Check.NotEmpty(property, "property");
			base.ReportPropertyChanging(property);
			this.EntityChangeTracker.EntityMemberChanging(property);
		}

		// Token: 0x06003807 RID: 14343 RVA: 0x000B6715 File Offset: 0x000B4915
		protected sealed override void ReportPropertyChanged(string property)
		{
			Check.NotEmpty(property, "property");
			this.EntityChangeTracker.EntityMemberChanged(property);
			base.ReportPropertyChanged(property);
		}

		// Token: 0x17000AC3 RID: 2755
		// (get) Token: 0x06003808 RID: 14344 RVA: 0x000B6736 File Offset: 0x000B4936
		internal sealed override bool IsChangeTracked
		{
			get
			{
				return this.EntityState != EntityState.Detached;
			}
		}

		// Token: 0x06003809 RID: 14345 RVA: 0x000B6744 File Offset: 0x000B4944
		internal sealed override void ReportComplexPropertyChanging(string entityMemberName, ComplexObject complexObject, string complexMemberName)
		{
			this.EntityChangeTracker.EntityComplexMemberChanging(entityMemberName, complexObject, complexMemberName);
		}

		// Token: 0x0600380A RID: 14346 RVA: 0x000B6754 File Offset: 0x000B4954
		internal sealed override void ReportComplexPropertyChanged(string entityMemberName, ComplexObject complexObject, string complexMemberName)
		{
			this.EntityChangeTracker.EntityComplexMemberChanged(entityMemberName, complexObject, complexMemberName);
		}

		// Token: 0x040012E6 RID: 4838
		private RelationshipManager _relationships;

		// Token: 0x040012E7 RID: 4839
		private EntityKey _entityKey;

		// Token: 0x040012E8 RID: 4840
		[NonSerialized]
		private IEntityChangeTracker _entityChangeTracker = EntityObject._detachedEntityChangeTracker;

		// Token: 0x040012E9 RID: 4841
		[NonSerialized]
		private static readonly EntityObject.DetachedEntityChangeTracker _detachedEntityChangeTracker = new EntityObject.DetachedEntityChangeTracker();

		// Token: 0x02000AB5 RID: 2741
		private class DetachedEntityChangeTracker : IEntityChangeTracker
		{
			// Token: 0x060062E0 RID: 25312 RVA: 0x00156443 File Offset: 0x00154643
			void IEntityChangeTracker.EntityMemberChanging(string entityMemberName)
			{
			}

			// Token: 0x060062E1 RID: 25313 RVA: 0x00156445 File Offset: 0x00154645
			void IEntityChangeTracker.EntityMemberChanged(string entityMemberName)
			{
			}

			// Token: 0x060062E2 RID: 25314 RVA: 0x00156447 File Offset: 0x00154647
			void IEntityChangeTracker.EntityComplexMemberChanging(string entityMemberName, object complexObject, string complexMemberName)
			{
			}

			// Token: 0x060062E3 RID: 25315 RVA: 0x00156449 File Offset: 0x00154649
			void IEntityChangeTracker.EntityComplexMemberChanged(string entityMemberName, object complexObject, string complexMemberName)
			{
			}

			// Token: 0x170010CD RID: 4301
			// (get) Token: 0x060062E4 RID: 25316 RVA: 0x0015644B File Offset: 0x0015464B
			EntityState IEntityChangeTracker.EntityState
			{
				get
				{
					return EntityState.Detached;
				}
			}
		}
	}
}
