﻿using System;
using System.ComponentModel;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Data.Entity.Spatial;
using System.Globalization;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006B6 RID: 1718
	public abstract class DbExpression
	{
		// Token: 0x06005057 RID: 20567 RVA: 0x001213BA File Offset: 0x0011F5BA
		internal DbExpression()
		{
		}

		// Token: 0x06005058 RID: 20568 RVA: 0x001213C4 File Offset: 0x0011F5C4
		internal DbExpression(DbExpressionKind kind, TypeUsage type, bool forceNullable = true)
		{
			DbExpression.CheckExpressionKind(kind);
			this._kind = kind;
			if (forceNullable && !TypeSemantics.IsNullable(type))
			{
				type = type.ShallowCopy(new FacetValues
				{
					Nullable = new bool?(true)
				});
			}
			this._type = type;
		}

		// Token: 0x17000FAA RID: 4010
		// (get) Token: 0x06005059 RID: 20569 RVA: 0x00121414 File Offset: 0x0011F614
		public virtual TypeUsage ResultType
		{
			get
			{
				return this._type;
			}
		}

		// Token: 0x17000FAB RID: 4011
		// (get) Token: 0x0600505A RID: 20570 RVA: 0x0012141C File Offset: 0x0011F61C
		public virtual DbExpressionKind ExpressionKind
		{
			get
			{
				return this._kind;
			}
		}

		// Token: 0x0600505B RID: 20571
		public abstract void Accept(DbExpressionVisitor visitor);

		// Token: 0x0600505C RID: 20572
		public abstract TResultType Accept<TResultType>(DbExpressionVisitor<TResultType> visitor);

		// Token: 0x0600505D RID: 20573 RVA: 0x00121424 File Offset: 0x0011F624
		[EditorBrowsable(EditorBrowsableState.Never)]
		public override bool Equals(object obj)
		{
			return base.Equals(obj);
		}

		// Token: 0x0600505E RID: 20574 RVA: 0x0012142D File Offset: 0x0011F62D
		public override int GetHashCode()
		{
			return base.GetHashCode();
		}

		// Token: 0x0600505F RID: 20575 RVA: 0x00121435 File Offset: 0x0011F635
		public static DbExpression FromBinary(byte[] value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.Binary);
			}
			return DbExpressionBuilder.Constant(value);
		}

		// Token: 0x06005060 RID: 20576 RVA: 0x00121447 File Offset: 0x0011F647
		public static implicit operator DbExpression(byte[] value)
		{
			return DbExpression.FromBinary(value);
		}

		// Token: 0x06005061 RID: 20577 RVA: 0x0012144F File Offset: 0x0011F64F
		public static DbExpression FromBoolean(bool? value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.Boolean);
			}
			if (!value.Value)
			{
				return DbExpressionBuilder.False;
			}
			return DbExpressionBuilder.True;
		}

		// Token: 0x06005062 RID: 20578 RVA: 0x00121475 File Offset: 0x0011F675
		public static implicit operator DbExpression(bool? value)
		{
			return DbExpression.FromBoolean(value);
		}

		// Token: 0x06005063 RID: 20579 RVA: 0x0012147D File Offset: 0x0011F67D
		public static DbExpression FromByte(byte? value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.Byte);
			}
			return DbExpressionBuilder.Constant(value.Value);
		}

		// Token: 0x06005064 RID: 20580 RVA: 0x001214A0 File Offset: 0x0011F6A0
		public static implicit operator DbExpression(byte? value)
		{
			return DbExpression.FromByte(value);
		}

		// Token: 0x06005065 RID: 20581 RVA: 0x001214A8 File Offset: 0x0011F6A8
		public static DbExpression FromDateTime(DateTime? value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.DateTime);
			}
			return DbExpressionBuilder.Constant(value.Value);
		}

		// Token: 0x06005066 RID: 20582 RVA: 0x001214CB File Offset: 0x0011F6CB
		public static implicit operator DbExpression(DateTime? value)
		{
			return DbExpression.FromDateTime(value);
		}

		// Token: 0x06005067 RID: 20583 RVA: 0x001214D3 File Offset: 0x0011F6D3
		public static DbExpression FromDateTimeOffset(DateTimeOffset? value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.DateTimeOffset);
			}
			return DbExpressionBuilder.Constant(value.Value);
		}

		// Token: 0x06005068 RID: 20584 RVA: 0x001214F7 File Offset: 0x0011F6F7
		public static implicit operator DbExpression(DateTimeOffset? value)
		{
			return DbExpression.FromDateTimeOffset(value);
		}

		// Token: 0x06005069 RID: 20585 RVA: 0x001214FF File Offset: 0x0011F6FF
		public static DbExpression FromDecimal(decimal? value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.Decimal);
			}
			return DbExpressionBuilder.Constant(value.Value);
		}

		// Token: 0x0600506A RID: 20586 RVA: 0x00121522 File Offset: 0x0011F722
		public static implicit operator DbExpression(decimal? value)
		{
			return DbExpression.FromDecimal(value);
		}

		// Token: 0x0600506B RID: 20587 RVA: 0x0012152A File Offset: 0x0011F72A
		public static DbExpression FromDouble(double? value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.Double);
			}
			return DbExpressionBuilder.Constant(value.Value);
		}

		// Token: 0x0600506C RID: 20588 RVA: 0x0012154D File Offset: 0x0011F74D
		public static implicit operator DbExpression(double? value)
		{
			return DbExpression.FromDouble(value);
		}

		// Token: 0x0600506D RID: 20589 RVA: 0x00121555 File Offset: 0x0011F755
		public static DbExpression FromGeography(DbGeography value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.Geography);
			}
			return DbExpressionBuilder.Constant(value);
		}

		// Token: 0x0600506E RID: 20590 RVA: 0x00121568 File Offset: 0x0011F768
		public static implicit operator DbExpression(DbGeography value)
		{
			return DbExpression.FromGeography(value);
		}

		// Token: 0x0600506F RID: 20591 RVA: 0x00121570 File Offset: 0x0011F770
		public static DbExpression FromGeometry(DbGeometry value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.Geometry);
			}
			return DbExpressionBuilder.Constant(value);
		}

		// Token: 0x06005070 RID: 20592 RVA: 0x00121583 File Offset: 0x0011F783
		public static implicit operator DbExpression(DbGeometry value)
		{
			return DbExpression.FromGeometry(value);
		}

		// Token: 0x06005071 RID: 20593 RVA: 0x0012158B File Offset: 0x0011F78B
		public static DbExpression FromGuid(Guid? value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.Guid);
			}
			return DbExpressionBuilder.Constant(value.Value);
		}

		// Token: 0x06005072 RID: 20594 RVA: 0x001215AE File Offset: 0x0011F7AE
		public static implicit operator DbExpression(Guid? value)
		{
			return DbExpression.FromGuid(value);
		}

		// Token: 0x06005073 RID: 20595 RVA: 0x001215B6 File Offset: 0x0011F7B6
		public static DbExpression FromInt16(short? value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.Int16);
			}
			return DbExpressionBuilder.Constant(value.Value);
		}

		// Token: 0x06005074 RID: 20596 RVA: 0x001215DA File Offset: 0x0011F7DA
		public static implicit operator DbExpression(short? value)
		{
			return DbExpression.FromInt16(value);
		}

		// Token: 0x06005075 RID: 20597 RVA: 0x001215E2 File Offset: 0x0011F7E2
		public static DbExpression FromInt32(int? value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.Int32);
			}
			return DbExpressionBuilder.Constant(value.Value);
		}

		// Token: 0x06005076 RID: 20598 RVA: 0x00121606 File Offset: 0x0011F806
		public static implicit operator DbExpression(int? value)
		{
			return DbExpression.FromInt32(value);
		}

		// Token: 0x06005077 RID: 20599 RVA: 0x0012160E File Offset: 0x0011F80E
		public static DbExpression FromInt64(long? value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.Int64);
			}
			return DbExpressionBuilder.Constant(value.Value);
		}

		// Token: 0x06005078 RID: 20600 RVA: 0x00121632 File Offset: 0x0011F832
		public static implicit operator DbExpression(long? value)
		{
			return DbExpression.FromInt64(value);
		}

		// Token: 0x06005079 RID: 20601 RVA: 0x0012163A File Offset: 0x0011F83A
		public static DbExpression FromSingle(float? value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.Single);
			}
			return DbExpressionBuilder.Constant(value.Value);
		}

		// Token: 0x0600507A RID: 20602 RVA: 0x0012165D File Offset: 0x0011F85D
		public static implicit operator DbExpression(float? value)
		{
			return DbExpression.FromSingle(value);
		}

		// Token: 0x0600507B RID: 20603 RVA: 0x00121665 File Offset: 0x0011F865
		public static DbExpression FromString(string value)
		{
			if (value == null)
			{
				return DbExpressionBuilder.CreatePrimitiveNullExpression(PrimitiveTypeKind.String);
			}
			return DbExpressionBuilder.Constant(value);
		}

		// Token: 0x0600507C RID: 20604 RVA: 0x00121678 File Offset: 0x0011F878
		public static implicit operator DbExpression(string value)
		{
			return DbExpression.FromString(value);
		}

		// Token: 0x0600507D RID: 20605 RVA: 0x00121680 File Offset: 0x0011F880
		internal static void CheckExpressionKind(DbExpressionKind kind)
		{
			if (kind < DbExpressionKind.All || DbExpressionKindHelper.Last < kind)
			{
				string name = typeof(DbExpressionKind).Name;
				int num = (int)kind;
				throw new ArgumentOutOfRangeException(name, Strings.ADP_InvalidEnumerationValue(name, num.ToString(CultureInfo.InvariantCulture)));
			}
		}

		// Token: 0x04001D56 RID: 7510
		private readonly TypeUsage _type;

		// Token: 0x04001D57 RID: 7511
		private readonly DbExpressionKind _kind;
	}
}
