﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005A6 RID: 1446
	internal class MemberDomainMap : InternalBase
	{
		// Token: 0x06004646 RID: 17990 RVA: 0x000F6DE2 File Offset: 0x000F4FE2
		private MemberDomainMap(Dictionary<MemberPath, Set<Constant>> domainMap, Dictionary<MemberPath, Set<Constant>> nonConditionDomainMap, EdmItemCollection edmItemCollection)
		{
			this.m_conditionDomainMap = domainMap;
			this.m_nonConditionDomainMap = nonConditionDomainMap;
			this.m_edmItemCollection = edmItemCollection;
		}

		// Token: 0x06004647 RID: 17991 RVA: 0x000F6E0C File Offset: 0x000F500C
		internal MemberDomainMap(ViewTarget viewTarget, bool isValidationEnabled, IEnumerable<Cell> extentCells, EdmItemCollection edmItemCollection, ConfigViewGenerator config, Dictionary<EntityType, Set<EntityType>> inheritanceGraph)
		{
			this.m_conditionDomainMap = new Dictionary<MemberPath, Set<Constant>>(MemberPath.EqualityComparer);
			this.m_edmItemCollection = edmItemCollection;
			Dictionary<MemberPath, Set<Constant>> dictionary = null;
			if (viewTarget == ViewTarget.UpdateView)
			{
				dictionary = Domain.ComputeConstantDomainSetsForSlotsInUpdateViews(extentCells, this.m_edmItemCollection);
			}
			else
			{
				dictionary = Domain.ComputeConstantDomainSetsForSlotsInQueryViews(extentCells, this.m_edmItemCollection, isValidationEnabled);
			}
			foreach (Cell cell in extentCells)
			{
				foreach (MemberRestriction memberRestriction in cell.GetLeftQuery(viewTarget).GetConjunctsFromWhereClause())
				{
					MemberPath memberPath = memberRestriction.RestrictedMemberSlot.MemberPath;
					Set<Constant> set;
					if (!dictionary.TryGetValue(memberPath, out set))
					{
						set = Domain.DeriveDomainFromMemberPath(memberPath, edmItemCollection, isValidationEnabled);
					}
					if (!set.Contains(Constant.Null))
					{
						if (memberRestriction.Domain.Values.All((Constant conditionConstant) => conditionConstant.Equals(Constant.NotNull)))
						{
							continue;
						}
					}
					if (set.Count <= 0 || (!set.Contains(Constant.Null) && memberRestriction.Domain.Values.Contains(Constant.Null)))
					{
						string text = Strings.ViewGen_InvalidCondition(memberPath.PathToString(new bool?(false)));
						ExceptionHelpers.ThrowMappingException(new ErrorLog.Record(ViewGenErrorCode.InvalidCondition, text, cell, string.Empty), config);
					}
					if (!memberPath.IsAlwaysDefined(inheritanceGraph))
					{
						set.Add(Constant.Undefined);
					}
					this.AddToDomainMap(memberPath, set);
				}
			}
			this.m_nonConditionDomainMap = new Dictionary<MemberPath, Set<Constant>>(MemberPath.EqualityComparer);
			foreach (Cell cell2 in extentCells)
			{
				foreach (MemberProjectedSlot memberProjectedSlot in cell2.GetLeftQuery(viewTarget).GetAllQuerySlots())
				{
					MemberPath memberPath2 = memberProjectedSlot.MemberPath;
					if (!this.m_conditionDomainMap.ContainsKey(memberPath2) && !this.m_nonConditionDomainMap.ContainsKey(memberPath2))
					{
						Set<Constant> set2 = Domain.DeriveDomainFromMemberPath(memberPath2, this.m_edmItemCollection, true);
						if (!memberPath2.IsAlwaysDefined(inheritanceGraph))
						{
							set2.Add(Constant.Undefined);
						}
						set2 = Domain.ExpandNegationsInDomain(set2, set2);
						this.m_nonConditionDomainMap.Add(memberPath2, new MemberDomainMap.CellConstantSetInfo(set2));
					}
				}
			}
		}

		// Token: 0x06004648 RID: 17992 RVA: 0x000F70E8 File Offset: 0x000F52E8
		internal bool IsProjectedConditionMember(MemberPath memberPath)
		{
			return this.m_projectedConditionMembers.Contains(memberPath);
		}

		// Token: 0x06004649 RID: 17993 RVA: 0x000F70F8 File Offset: 0x000F52F8
		internal MemberDomainMap GetOpenDomain()
		{
			Dictionary<MemberPath, Set<Constant>> dictionary = this.m_conditionDomainMap.ToDictionary((KeyValuePair<MemberPath, Set<Constant>> p) => p.Key, (KeyValuePair<MemberPath, Set<Constant>> p) => new Set<Constant>(p.Value, Constant.EqualityComparer));
			this.ExpandDomainsIfNeeded(dictionary);
			return new MemberDomainMap(dictionary, this.m_nonConditionDomainMap, this.m_edmItemCollection);
		}

		// Token: 0x0600464A RID: 17994 RVA: 0x000F7168 File Offset: 0x000F5368
		internal MemberDomainMap MakeCopy()
		{
			return new MemberDomainMap(this.m_conditionDomainMap.ToDictionary((KeyValuePair<MemberPath, Set<Constant>> p) => p.Key, (KeyValuePair<MemberPath, Set<Constant>> p) => new Set<Constant>(p.Value, Constant.EqualityComparer)), this.m_nonConditionDomainMap, this.m_edmItemCollection);
		}

		// Token: 0x0600464B RID: 17995 RVA: 0x000F71CF File Offset: 0x000F53CF
		internal void ExpandDomainsToIncludeAllPossibleValues()
		{
			this.ExpandDomainsIfNeeded(this.m_conditionDomainMap);
		}

		// Token: 0x0600464C RID: 17996 RVA: 0x000F71E0 File Offset: 0x000F53E0
		private void ExpandDomainsIfNeeded(Dictionary<MemberPath, Set<Constant>> domainMapForMembers)
		{
			foreach (MemberPath memberPath in domainMapForMembers.Keys)
			{
				Set<Constant> set = domainMapForMembers[memberPath];
				if (memberPath.IsScalarType())
				{
					if (!set.Any((Constant c) => c is NegatedConstant))
					{
						if (MetadataHelper.HasDiscreteDomain(memberPath.EdmType))
						{
							Set<Constant> set2 = Domain.DeriveDomainFromMemberPath(memberPath, this.m_edmItemCollection, true);
							set.Unite(set2);
						}
						else
						{
							NegatedConstant negatedConstant = new NegatedConstant(set);
							set.Add(negatedConstant);
						}
					}
				}
			}
		}

		// Token: 0x0600464D RID: 17997 RVA: 0x000F7298 File Offset: 0x000F5498
		internal void ReduceEnumerableDomainToEnumeratedValues(ConfigViewGenerator config)
		{
			MemberDomainMap.ReduceEnumerableDomainToEnumeratedValues(this.m_conditionDomainMap, config, this.m_edmItemCollection);
			MemberDomainMap.ReduceEnumerableDomainToEnumeratedValues(this.m_nonConditionDomainMap, config, this.m_edmItemCollection);
		}

		// Token: 0x0600464E RID: 17998 RVA: 0x000F72C0 File Offset: 0x000F54C0
		private static void ReduceEnumerableDomainToEnumeratedValues(Dictionary<MemberPath, Set<Constant>> domainMap, ConfigViewGenerator config, EdmItemCollection edmItemCollection)
		{
			foreach (MemberPath memberPath in domainMap.Keys)
			{
				if (MetadataHelper.HasDiscreteDomain(memberPath.EdmType))
				{
					Set<Constant> set = Domain.DeriveDomainFromMemberPath(memberPath, edmItemCollection, true);
					Set<Constant> set2 = domainMap[memberPath].Difference(set);
					set2.Remove(Constant.Undefined);
					if (set2.Count > 0)
					{
						if (config.IsNormalTracing)
						{
							Helpers.FormatTraceLine("Changed domain of {0} from {1} - subtract {2}", new object[]
							{
								memberPath,
								domainMap[memberPath],
								set2
							});
						}
						domainMap[memberPath].Subtract(set2);
					}
				}
			}
		}

		// Token: 0x0600464F RID: 17999 RVA: 0x000F737C File Offset: 0x000F557C
		internal static void PropagateUpdateDomainToQueryDomain(IEnumerable<Cell> cells, MemberDomainMap queryDomainMap, MemberDomainMap updateDomainMap)
		{
			foreach (Cell cell in cells)
			{
				CellQuery cquery = cell.CQuery;
				CellQuery squery = cell.SQuery;
				for (int i = 0; i < cquery.NumProjectedSlots; i++)
				{
					MemberProjectedSlot memberProjectedSlot = cquery.ProjectedSlotAt(i) as MemberProjectedSlot;
					MemberProjectedSlot memberProjectedSlot2 = squery.ProjectedSlotAt(i) as MemberProjectedSlot;
					if (memberProjectedSlot != null && memberProjectedSlot2 != null)
					{
						MemberPath memberPath = memberProjectedSlot.MemberPath;
						MemberPath memberPath2 = memberProjectedSlot2.MemberPath;
						Set<Constant> domainInternal = queryDomainMap.GetDomainInternal(memberPath);
						Set<Constant> domainInternal2 = updateDomainMap.GetDomainInternal(memberPath2);
						domainInternal.Unite(domainInternal2.Where((Constant constant) => !constant.IsNull() && !(constant is NegatedConstant)));
						if (updateDomainMap.IsConditionMember(memberPath2) && !queryDomainMap.IsConditionMember(memberPath))
						{
							queryDomainMap.m_projectedConditionMembers.Add(memberPath);
						}
					}
				}
			}
			MemberDomainMap.ExpandNegationsInDomainMap(queryDomainMap.m_conditionDomainMap);
			MemberDomainMap.ExpandNegationsInDomainMap(queryDomainMap.m_nonConditionDomainMap);
		}

		// Token: 0x06004650 RID: 18000 RVA: 0x000F7490 File Offset: 0x000F5690
		private static void ExpandNegationsInDomainMap(Dictionary<MemberPath, Set<Constant>> domainMap)
		{
			foreach (MemberPath memberPath in domainMap.Keys.ToArray<MemberPath>())
			{
				domainMap[memberPath] = Domain.ExpandNegationsInDomain(domainMap[memberPath]);
			}
		}

		// Token: 0x06004651 RID: 18001 RVA: 0x000F74CE File Offset: 0x000F56CE
		internal bool IsConditionMember(MemberPath path)
		{
			return this.m_conditionDomainMap.ContainsKey(path);
		}

		// Token: 0x06004652 RID: 18002 RVA: 0x000F74DC File Offset: 0x000F56DC
		internal IEnumerable<MemberPath> ConditionMembers(EntitySetBase extent)
		{
			foreach (MemberPath memberPath in this.m_conditionDomainMap.Keys)
			{
				if (memberPath.Extent.Equals(extent))
				{
					yield return memberPath;
				}
			}
			Dictionary<MemberPath, Set<Constant>>.KeyCollection.Enumerator enumerator = default(Dictionary<MemberPath, Set<Constant>>.KeyCollection.Enumerator);
			yield break;
			yield break;
		}

		// Token: 0x06004653 RID: 18003 RVA: 0x000F74F3 File Offset: 0x000F56F3
		internal IEnumerable<MemberPath> NonConditionMembers(EntitySetBase extent)
		{
			foreach (MemberPath memberPath in this.m_nonConditionDomainMap.Keys)
			{
				if (memberPath.Extent.Equals(extent))
				{
					yield return memberPath;
				}
			}
			Dictionary<MemberPath, Set<Constant>>.KeyCollection.Enumerator enumerator = default(Dictionary<MemberPath, Set<Constant>>.KeyCollection.Enumerator);
			yield break;
			yield break;
		}

		// Token: 0x06004654 RID: 18004 RVA: 0x000F750A File Offset: 0x000F570A
		internal void AddSentinel(MemberPath path)
		{
			this.GetDomainInternal(path).Add(Constant.AllOtherConstants);
		}

		// Token: 0x06004655 RID: 18005 RVA: 0x000F751D File Offset: 0x000F571D
		internal void RemoveSentinel(MemberPath path)
		{
			this.GetDomainInternal(path).Remove(Constant.AllOtherConstants);
		}

		// Token: 0x06004656 RID: 18006 RVA: 0x000F7530 File Offset: 0x000F5730
		internal IEnumerable<Constant> GetDomain(MemberPath path)
		{
			return this.GetDomainInternal(path);
		}

		// Token: 0x06004657 RID: 18007 RVA: 0x000F753C File Offset: 0x000F573C
		private Set<Constant> GetDomainInternal(MemberPath path)
		{
			Set<Constant> set;
			if (!this.m_conditionDomainMap.TryGetValue(path, out set))
			{
				set = this.m_nonConditionDomainMap[path];
			}
			return set;
		}

		// Token: 0x06004658 RID: 18008 RVA: 0x000F7567 File Offset: 0x000F5767
		internal void UpdateConditionMemberDomain(MemberPath path, IEnumerable<Constant> domainValues)
		{
			Set<Constant> set = this.m_conditionDomainMap[path];
			set.Clear();
			set.Unite(domainValues);
		}

		// Token: 0x06004659 RID: 18009 RVA: 0x000F7584 File Offset: 0x000F5784
		private void AddToDomainMap(MemberPath member, IEnumerable<Constant> domainValues)
		{
			Set<Constant> set;
			if (!this.m_conditionDomainMap.TryGetValue(member, out set))
			{
				set = new Set<Constant>(Constant.EqualityComparer);
			}
			set.Unite(domainValues);
			this.m_conditionDomainMap[member] = Domain.ExpandNegationsInDomain(set, set);
		}

		// Token: 0x0600465A RID: 18010 RVA: 0x000F75C8 File Offset: 0x000F57C8
		internal override void ToCompactString(StringBuilder builder)
		{
			foreach (MemberPath memberPath in this.m_conditionDomainMap.Keys)
			{
				builder.Append('(');
				memberPath.ToCompactString(builder);
				IEnumerable<Constant> domain = this.GetDomain(memberPath);
				builder.Append(": ");
				StringUtil.ToCommaSeparatedStringSorted(builder, domain);
				builder.Append(") ");
			}
		}

		// Token: 0x0400191A RID: 6426
		private readonly Dictionary<MemberPath, Set<Constant>> m_conditionDomainMap;

		// Token: 0x0400191B RID: 6427
		private readonly Dictionary<MemberPath, Set<Constant>> m_nonConditionDomainMap;

		// Token: 0x0400191C RID: 6428
		private readonly Set<MemberPath> m_projectedConditionMembers = new Set<MemberPath>();

		// Token: 0x0400191D RID: 6429
		private readonly EdmItemCollection m_edmItemCollection;

		// Token: 0x02000BD6 RID: 3030
		private class CellConstantSetInfo : Set<Constant>
		{
			// Token: 0x06006849 RID: 26697 RVA: 0x001629E7 File Offset: 0x00160BE7
			internal CellConstantSetInfo(Set<Constant> iconstants)
				: base(iconstants)
			{
			}
		}
	}
}
