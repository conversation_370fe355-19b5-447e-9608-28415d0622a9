﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects.DataClasses;
using System.Linq;

namespace System.Data.Entity.Core.Objects.Internal
{
	// Token: 0x02000445 RID: 1093
	internal class ForeignKeyFactory
	{
		// Token: 0x06003564 RID: 13668 RVA: 0x000AAD35 File Offset: 0x000A8F35
		public static bool IsConceptualNullKey(EntityKey key)
		{
			return !(key == null) && string.Equals(key.EntityContainerName, "EntityHasNullForeignKey") && string.Equals(key.EntitySetName, "EntityHasNullForeignKey");
		}

		// Token: 0x06003565 RID: 13669 RVA: 0x000AAD66 File Offset: 0x000A8F66
		public static bool IsConceptualNullKeyChanged(EntityKey conceptualNullKey, EntityKey realKey)
		{
			return realKey == null || !EntityKey.InternalEquals(conceptualNullKey, realKey, false);
		}

		// Token: 0x06003566 RID: 13670 RVA: 0x000AAD7E File Offset: 0x000A8F7E
		public static EntityKey CreateConceptualNullKey(EntityKey originalKey)
		{
			return new EntityKey("EntityHasNullForeignKey.EntityHasNullForeignKey", originalKey.EntityKeyValues);
		}

		// Token: 0x06003567 RID: 13671 RVA: 0x000AAD90 File Offset: 0x000A8F90
		public static EntityKey CreateKeyFromForeignKeyValues(EntityEntry dependentEntry, RelatedEnd relatedEnd)
		{
			ReferentialConstraint referentialConstraint = ((AssociationType)relatedEnd.RelationMetadata).ReferentialConstraints.First<ReferentialConstraint>();
			return ForeignKeyFactory.CreateKeyFromForeignKeyValues(dependentEntry, referentialConstraint, relatedEnd.GetTargetEntitySetFromRelationshipSet(), false);
		}

		// Token: 0x06003568 RID: 13672 RVA: 0x000AADC4 File Offset: 0x000A8FC4
		public static EntityKey CreateKeyFromForeignKeyValues(EntityEntry dependentEntry, ReferentialConstraint constraint, EntitySet principalEntitySet, bool useOriginalValues)
		{
			ReadOnlyMetadataCollection<EdmProperty> toProperties = constraint.ToProperties;
			int count = toProperties.Count;
			if (count != 1)
			{
				string[] keyMemberNames = principalEntitySet.ElementType.KeyMemberNames;
				object[] array = new object[count];
				ReadOnlyMetadataCollection<EdmProperty> fromProperties = constraint.FromProperties;
				for (int i = 0; i < count; i++)
				{
					object obj = (useOriginalValues ? dependentEntry.GetOriginalEntityValue(toProperties[i].Name) : dependentEntry.GetCurrentEntityValue(toProperties[i].Name));
					if (obj == DBNull.Value)
					{
						return null;
					}
					int num = Array.IndexOf<string>(keyMemberNames, fromProperties[i].Name);
					array[num] = obj;
				}
				return new EntityKey(principalEntitySet, array);
			}
			object obj2 = (useOriginalValues ? dependentEntry.GetOriginalEntityValue(toProperties.First<EdmProperty>().Name) : dependentEntry.GetCurrentEntityValue(toProperties.First<EdmProperty>().Name));
			if (obj2 != DBNull.Value)
			{
				return new EntityKey(principalEntitySet, obj2);
			}
			return null;
		}

		// Token: 0x04001143 RID: 4419
		private const string s_NullPart = "EntityHasNullForeignKey";

		// Token: 0x04001144 RID: 4420
		private const string s_NullForeignKey = "EntityHasNullForeignKey.EntityHasNullForeignKey";
	}
}
