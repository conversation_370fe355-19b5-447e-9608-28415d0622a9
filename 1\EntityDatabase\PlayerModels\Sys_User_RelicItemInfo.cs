﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000033 RID: 51
	public class Sys_User_RelicItemInfo
	{
		// Token: 0x1700018E RID: 398
		// (get) Token: 0x0600034E RID: 846 RVA: 0x00003C93 File Offset: 0x00001E93
		// (set) Token: 0x0600034F RID: 847 RVA: 0x00003C9B File Offset: 0x00001E9B
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x1700018F RID: 399
		// (get) Token: 0x06000350 RID: 848 RVA: 0x00003CA4 File Offset: 0x00001EA4
		// (set) Token: 0x06000351 RID: 849 RVA: 0x00003CAC File Offset: 0x00001EAC
		public int UserID { get; set; }

		// Token: 0x17000190 RID: 400
		// (get) Token: 0x06000352 RID: 850 RVA: 0x00003CB5 File Offset: 0x00001EB5
		// (set) Token: 0x06000353 RID: 851 RVA: 0x00003CBD File Offset: 0x00001EBD
		public int XiuLianNum { get; set; }

		// Token: 0x17000191 RID: 401
		// (get) Token: 0x06000354 RID: 852 RVA: 0x00003CC6 File Offset: 0x00001EC6
		// (set) Token: 0x06000355 RID: 853 RVA: 0x00003CCE File Offset: 0x00001ECE
		public int ZFNum { get; set; }

		// Token: 0x17000192 RID: 402
		// (get) Token: 0x06000356 RID: 854 RVA: 0x00003CD7 File Offset: 0x00001ED7
		// (set) Token: 0x06000357 RID: 855 RVA: 0x00003CDF File Offset: 0x00001EDF
		public int GJZFNum { get; set; }

		// Token: 0x17000193 RID: 403
		// (get) Token: 0x06000358 RID: 856 RVA: 0x00003CE8 File Offset: 0x00001EE8
		// (set) Token: 0x06000359 RID: 857 RVA: 0x00003CF0 File Offset: 0x00001EF0
		public int AdvanceNum { get; set; }

		// Token: 0x17000194 RID: 404
		// (get) Token: 0x0600035A RID: 858 RVA: 0x00003CF9 File Offset: 0x00001EF9
		// (set) Token: 0x0600035B RID: 859 RVA: 0x00003D01 File Offset: 0x00001F01
		public int GJAdvanceNum { get; set; }

		// Token: 0x17000195 RID: 405
		// (get) Token: 0x0600035C RID: 860 RVA: 0x00003D0A File Offset: 0x00001F0A
		// (set) Token: 0x0600035D RID: 861 RVA: 0x00003D12 File Offset: 0x00001F12
		public int shopScore { get; set; }

		// Token: 0x17000196 RID: 406
		// (get) Token: 0x0600035E RID: 862 RVA: 0x00003D1B File Offset: 0x00001F1B
		// (set) Token: 0x0600035F RID: 863 RVA: 0x00003D23 File Offset: 0x00001F23
		public string RelicInfo { get; set; }

		// Token: 0x17000197 RID: 407
		// (get) Token: 0x06000360 RID: 864 RVA: 0x00003D2C File Offset: 0x00001F2C
		// (set) Token: 0x06000361 RID: 865 RVA: 0x00003D34 File Offset: 0x00001F34
		public string ManualInfo { get; set; }

		// Token: 0x17000198 RID: 408
		// (get) Token: 0x06000362 RID: 866 RVA: 0x00003D3D File Offset: 0x00001F3D
		// (set) Token: 0x06000363 RID: 867 RVA: 0x00003D45 File Offset: 0x00001F45
		public int OpenCount { get; set; }
	}
}
