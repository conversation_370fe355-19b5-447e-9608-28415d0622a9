﻿using System;

namespace System.Data.Entity.Core.Objects.Internal
{
	// Token: 0x02000454 RID: 1108
	internal sealed class PocoEntityKeyStrategy : IEntityKeyStrategy
	{
		// Token: 0x06003628 RID: 13864 RVA: 0x000AD80A File Offset: 0x000ABA0A
		public EntityKey GetEntityKey()
		{
			return this._key;
		}

		// Token: 0x06003629 RID: 13865 RVA: 0x000AD812 File Offset: 0x000ABA12
		public void SetEntityKey(EntityKey key)
		{
			this._key = key;
		}

		// Token: 0x0600362A RID: 13866 RVA: 0x000AD81B File Offset: 0x000ABA1B
		public EntityKey GetEntityKeyFromEntity()
		{
			return null;
		}

		// Token: 0x0400117E RID: 4478
		private EntityKey _key;
	}
}
