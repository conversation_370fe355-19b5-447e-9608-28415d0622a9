﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;
using System.Reflection;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004BF RID: 1215
	public class EnumType : SimpleType
	{
		// Token: 0x06003C2F RID: 15407 RVA: 0x000C68A6 File Offset: 0x000C4AA6
		internal EnumType()
		{
			this._underlyingType = PrimitiveType.GetEdmPrimitiveType(PrimitiveTypeKind.Int32);
			this._isFlags = false;
		}

		// Token: 0x06003C30 RID: 15408 RVA: 0x000C68D2 File Offset: 0x000C4AD2
		internal EnumType(string name, string namespaceName, PrimitiveType underlyingType, bool isFlags, DataSpace dataSpace)
			: base(name, namespaceName, dataSpace)
		{
			this._isFlags = isFlags;
			this._underlyingType = underlyingType;
		}

		// Token: 0x06003C31 RID: 15409 RVA: 0x000C6900 File Offset: 0x000C4B00
		internal EnumType(Type clrType)
			: base(clrType.Name, clrType.NestingNamespace() ?? string.Empty, DataSpace.OSpace)
		{
			ClrProviderManifest.Instance.TryGetPrimitiveType(clrType.GetEnumUnderlyingType(), out this._underlyingType);
			this._isFlags = clrType.GetCustomAttributes(false).Any<FlagsAttribute>();
			foreach (string text in Enum.GetNames(clrType))
			{
				this.AddMember(new EnumMember(text, Convert.ChangeType(Enum.Parse(clrType, text), clrType.GetEnumUnderlyingType(), CultureInfo.InvariantCulture)));
			}
		}

		// Token: 0x17000BC2 RID: 3010
		// (get) Token: 0x06003C32 RID: 15410 RVA: 0x000C699E File Offset: 0x000C4B9E
		public override BuiltInTypeKind BuiltInTypeKind
		{
			get
			{
				return BuiltInTypeKind.EnumType;
			}
		}

		// Token: 0x17000BC3 RID: 3011
		// (get) Token: 0x06003C33 RID: 15411 RVA: 0x000C69A2 File Offset: 0x000C4BA2
		[MetadataProperty(BuiltInTypeKind.EnumMember, true)]
		public ReadOnlyMetadataCollection<EnumMember> Members
		{
			get
			{
				return this._members;
			}
		}

		// Token: 0x17000BC4 RID: 3012
		// (get) Token: 0x06003C34 RID: 15412 RVA: 0x000C69AA File Offset: 0x000C4BAA
		// (set) Token: 0x06003C35 RID: 15413 RVA: 0x000C69B2 File Offset: 0x000C4BB2
		[MetadataProperty(PrimitiveTypeKind.Boolean, false)]
		public bool IsFlags
		{
			get
			{
				return this._isFlags;
			}
			internal set
			{
				Util.ThrowIfReadOnly(this);
				this._isFlags = value;
			}
		}

		// Token: 0x17000BC5 RID: 3013
		// (get) Token: 0x06003C36 RID: 15414 RVA: 0x000C69C1 File Offset: 0x000C4BC1
		// (set) Token: 0x06003C37 RID: 15415 RVA: 0x000C69C9 File Offset: 0x000C4BC9
		[MetadataProperty(BuiltInTypeKind.PrimitiveType, false)]
		public PrimitiveType UnderlyingType
		{
			get
			{
				return this._underlyingType;
			}
			internal set
			{
				Util.ThrowIfReadOnly(this);
				this._underlyingType = value;
			}
		}

		// Token: 0x06003C38 RID: 15416 RVA: 0x000C69D8 File Offset: 0x000C4BD8
		internal override void SetReadOnly()
		{
			if (!base.IsReadOnly)
			{
				base.SetReadOnly();
				this.Members.Source.SetReadOnly();
			}
		}

		// Token: 0x06003C39 RID: 15417 RVA: 0x000C69F9 File Offset: 0x000C4BF9
		internal void AddMember(EnumMember enumMember)
		{
			this.Members.Source.Add(enumMember);
		}

		// Token: 0x06003C3A RID: 15418 RVA: 0x000C6A0C File Offset: 0x000C4C0C
		public static EnumType Create(string name, string namespaceName, PrimitiveType underlyingType, bool isFlags, IEnumerable<EnumMember> members, IEnumerable<MetadataProperty> metadataProperties)
		{
			Check.NotEmpty(name, "name");
			Check.NotEmpty(namespaceName, "namespaceName");
			Check.NotNull<PrimitiveType>(underlyingType, "underlyingType");
			if (!Helper.IsSupportedEnumUnderlyingType(underlyingType.PrimitiveTypeKind))
			{
				throw new ArgumentException(Strings.InvalidEnumUnderlyingType, "underlyingType");
			}
			EnumType enumType = new EnumType(name, namespaceName, underlyingType, isFlags, DataSpace.CSpace);
			if (members != null)
			{
				foreach (EnumMember enumMember in members)
				{
					if (!Helper.IsEnumMemberValueInRange(underlyingType.PrimitiveTypeKind, Convert.ToInt64(enumMember.Value, CultureInfo.InvariantCulture)))
					{
						throw new ArgumentException(Strings.EnumMemberValueOutOfItsUnderylingTypeRange(enumMember.Value, enumMember.Name, underlyingType.Name), "members");
					}
					enumType.AddMember(enumMember);
				}
			}
			if (metadataProperties != null)
			{
				enumType.AddMetadataProperties(metadataProperties);
			}
			enumType.SetReadOnly();
			return enumType;
		}

		// Token: 0x040014B4 RID: 5300
		private readonly ReadOnlyMetadataCollection<EnumMember> _members = new ReadOnlyMetadataCollection<EnumMember>(new MetadataCollection<EnumMember>());

		// Token: 0x040014B5 RID: 5301
		private PrimitiveType _underlyingType;

		// Token: 0x040014B6 RID: 5302
		private bool _isFlags;
	}
}
