﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000008 RID: 8
	public class DevilTurnOpenBox
	{
		// Token: 0x17000011 RID: 17
		// (get) Token: 0x06000027 RID: 39 RVA: 0x0000219D File Offset: 0x0000039D
		// (set) Token: 0x06000028 RID: 40 RVA: 0x000021A5 File Offset: 0x000003A5
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int ID { get; set; }

		// Token: 0x17000012 RID: 18
		// (get) Token: 0x06000029 RID: 41 RVA: 0x000021AE File Offset: 0x000003AE
		// (set) Token: 0x0600002A RID: 42 RVA: 0x000021B6 File Offset: 0x000003B6
		public int BoxId { get; set; }

		// Token: 0x17000013 RID: 19
		// (get) Token: 0x0600002B RID: 43 RVA: 0x000021BF File Offset: 0x000003BF
		// (set) Token: 0x0600002C RID: 44 RVA: 0x000021C7 File Offset: 0x000003C7
		public int BoxIndex { get; set; }

		// Token: 0x17000014 RID: 20
		// (get) Token: 0x0600002D RID: 45 RVA: 0x000021D0 File Offset: 0x000003D0
		// (set) Token: 0x0600002E RID: 46 RVA: 0x000021D8 File Offset: 0x000003D8
		public int NeedMoney { get; set; }
	}
}
