﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Utilities;
using System.Reflection;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004C0 RID: 1216
	internal class ExpensiveOSpaceLoader
	{
		// Token: 0x06003C3B RID: 15419 RVA: 0x000C6AF8 File Offset: 0x000C4CF8
		public virtual Dictionary<string, EdmType> LoadTypesExpensiveWay(Assembly assembly)
		{
			KnownAssembliesSet knownAssembliesSet = new KnownAssembliesSet();
			Dictionary<string, EdmType> dictionary;
			List<EdmItemError> list;
			AssemblyCache.LoadAssembly(assembly, false, knownAssembliesSet, out dictionary, out list);
			if (list.Count != 0)
			{
				throw EntityUtil.InvalidSchemaEncountered(Helper.CombineErrorMessage(list));
			}
			return dictionary;
		}

		// Token: 0x06003C3C RID: 15420 RVA: 0x000C6B2C File Offset: 0x000C4D2C
		public virtual AssociationType GetRelationshipTypeExpensiveWay(Type entityClrType, string relationshipName)
		{
			Dictionary<string, EdmType> dictionary = this.LoadTypesExpensiveWay(entityClrType.Assembly());
			EdmType edmType;
			if (dictionary != null && dictionary.TryGetValue(relationshipName, out edmType) && Helper.IsRelationshipType(edmType))
			{
				return (AssociationType)edmType;
			}
			return null;
		}

		// Token: 0x06003C3D RID: 15421 RVA: 0x000C6B64 File Offset: 0x000C4D64
		public virtual IEnumerable<AssociationType> GetAllRelationshipTypesExpensiveWay(Assembly assembly)
		{
			Dictionary<string, EdmType> dictionary = this.LoadTypesExpensiveWay(assembly);
			if (dictionary != null)
			{
				foreach (EdmType edmType in dictionary.Values)
				{
					if (Helper.IsAssociationType(edmType))
					{
						yield return (AssociationType)edmType;
					}
				}
				Dictionary<string, EdmType>.ValueCollection.Enumerator enumerator = default(Dictionary<string, EdmType>.ValueCollection.Enumerator);
			}
			yield break;
			yield break;
		}
	}
}
