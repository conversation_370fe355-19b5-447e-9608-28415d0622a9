﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005A2 RID: 1442
	internal class Domain : InternalBase
	{
		// Token: 0x060045F5 RID: 17909 RVA: 0x000F5A13 File Offset: 0x000F3C13
		internal Domain(Constant value, IEnumerable<Constant> possibleDiscreteValues)
			: this(new Constant[] { value }, possibleDiscreteValues)
		{
		}

		// Token: 0x060045F6 RID: 17910 RVA: 0x000F5A26 File Offset: 0x000F3C26
		internal Domain(IEnumerable<Constant> values, IEnumerable<Constant> possibleDiscreteValues)
		{
			this.m_possibleValues = Domain.DeterminePossibleValues(values, possibleDiscreteValues);
			this.m_domain = Domain.ExpandNegationsInDomain(values, this.m_possibleValues);
			this.AssertInvariant();
		}

		// Token: 0x060045F7 RID: 17911 RVA: 0x000F5A53 File Offset: 0x000F3C53
		internal Domain(Domain domain)
		{
			this.m_domain = new Set<Constant>(domain.m_domain, Constant.EqualityComparer);
			this.m_possibleValues = new Set<Constant>(domain.m_possibleValues, Constant.EqualityComparer);
			this.AssertInvariant();
		}

		// Token: 0x17000DD3 RID: 3539
		// (get) Token: 0x060045F8 RID: 17912 RVA: 0x000F5A8D File Offset: 0x000F3C8D
		internal IEnumerable<Constant> AllPossibleValues
		{
			get
			{
				return this.AllPossibleValuesInternal;
			}
		}

		// Token: 0x17000DD4 RID: 3540
		// (get) Token: 0x060045F9 RID: 17913 RVA: 0x000F5A98 File Offset: 0x000F3C98
		private Set<Constant> AllPossibleValuesInternal
		{
			get
			{
				NegatedConstant negatedConstant = new NegatedConstant(this.m_possibleValues);
				return this.m_possibleValues.Union(new Constant[] { negatedConstant });
			}
		}

		// Token: 0x17000DD5 RID: 3541
		// (get) Token: 0x060045FA RID: 17914 RVA: 0x000F5AC6 File Offset: 0x000F3CC6
		internal int Count
		{
			get
			{
				return this.m_domain.Count;
			}
		}

		// Token: 0x17000DD6 RID: 3542
		// (get) Token: 0x060045FB RID: 17915 RVA: 0x000F5AD3 File Offset: 0x000F3CD3
		internal IEnumerable<Constant> Values
		{
			get
			{
				return this.m_domain;
			}
		}

		// Token: 0x060045FC RID: 17916 RVA: 0x000F5ADC File Offset: 0x000F3CDC
		internal static Set<Constant> DeriveDomainFromMemberPath(MemberPath memberPath, EdmItemCollection edmItemCollection, bool leaveDomainUnbounded)
		{
			Set<Constant> set = Domain.DeriveDomainFromType(memberPath.EdmType, edmItemCollection, leaveDomainUnbounded);
			if (memberPath.IsNullable)
			{
				set.Add(Constant.Null);
			}
			return set;
		}

		// Token: 0x060045FD RID: 17917 RVA: 0x000F5B0C File Offset: 0x000F3D0C
		private static Set<Constant> DeriveDomainFromType(EdmType type, EdmItemCollection edmItemCollection, bool leaveDomainUnbounded)
		{
			Set<Constant> set;
			if (Helper.IsScalarType(type))
			{
				if (MetadataHelper.HasDiscreteDomain(type))
				{
					set = new Set<Constant>(Domain.CreateList(true, false), Constant.EqualityComparer);
				}
				else
				{
					set = new Set<Constant>(Constant.EqualityComparer);
					if (leaveDomainUnbounded)
					{
						set.Add(Constant.NotNull);
					}
				}
			}
			else
			{
				if (Helper.IsRefType(type))
				{
					type = ((RefType)type).ElementType;
				}
				List<Constant> list = new List<Constant>();
				foreach (EdmType edmType in MetadataHelper.GetTypeAndSubtypesOf(type, edmItemCollection, false))
				{
					TypeConstant typeConstant = new TypeConstant(edmType);
					list.Add(typeConstant);
				}
				set = new Set<Constant>(list, Constant.EqualityComparer);
			}
			return set;
		}

		// Token: 0x060045FE RID: 17918 RVA: 0x000F5BD4 File Offset: 0x000F3DD4
		internal static bool TryGetDefaultValueForMemberPath(MemberPath memberPath, out Constant defaultConstant)
		{
			object defaultValue = memberPath.DefaultValue;
			defaultConstant = Constant.Null;
			if (defaultValue != null)
			{
				defaultConstant = new ScalarConstant(defaultValue);
				return true;
			}
			return memberPath.IsNullable || memberPath.IsComputed;
		}

		// Token: 0x060045FF RID: 17919 RVA: 0x000F5C10 File Offset: 0x000F3E10
		internal static Constant GetDefaultValueForMemberPath(MemberPath memberPath, IEnumerable<LeftCellWrapper> wrappersForErrorReporting, ConfigViewGenerator config)
		{
			Constant constant = null;
			if (!Domain.TryGetDefaultValueForMemberPath(memberPath, out constant))
			{
				string text = Strings.ViewGen_No_Default_Value(memberPath.Extent.Name, memberPath.PathToString(new bool?(false)));
				ExceptionHelpers.ThrowMappingException(new ErrorLog.Record(ViewGenErrorCode.NoDefaultValue, text, wrappersForErrorReporting, string.Empty), config);
			}
			return constant;
		}

		// Token: 0x06004600 RID: 17920 RVA: 0x000F5C60 File Offset: 0x000F3E60
		internal int GetHash()
		{
			int num = 0;
			foreach (Constant constant in this.m_domain)
			{
				num ^= Constant.EqualityComparer.GetHashCode(constant);
			}
			return num;
		}

		// Token: 0x06004601 RID: 17921 RVA: 0x000F5CC0 File Offset: 0x000F3EC0
		internal bool IsEqualTo(Domain second)
		{
			return this.m_domain.SetEquals(second.m_domain);
		}

		// Token: 0x06004602 RID: 17922 RVA: 0x000F5CD4 File Offset: 0x000F3ED4
		internal bool ContainsNotNull()
		{
			NegatedConstant negatedConstant = Domain.GetNegatedConstant(this.m_domain);
			return negatedConstant != null && negatedConstant.Contains(Constant.Null);
		}

		// Token: 0x06004603 RID: 17923 RVA: 0x000F5CFD File Offset: 0x000F3EFD
		internal bool Contains(Constant constant)
		{
			return this.m_domain.Contains(constant);
		}

		// Token: 0x06004604 RID: 17924 RVA: 0x000F5D0C File Offset: 0x000F3F0C
		internal static Set<Constant> ExpandNegationsInDomain(IEnumerable<Constant> domain, IEnumerable<Constant> otherPossibleValues)
		{
			Set<Constant> set = Domain.DeterminePossibleValues(domain, otherPossibleValues);
			Set<Constant> set2 = new Set<Constant>(Constant.EqualityComparer);
			foreach (Constant constant in domain)
			{
				NegatedConstant negatedConstant = constant as NegatedConstant;
				if (negatedConstant != null)
				{
					set2.Add(new NegatedConstant(set));
					Set<Constant> set3 = set.Difference(negatedConstant.Elements);
					set2.AddRange(set3);
				}
				else
				{
					set2.Add(constant);
				}
			}
			return set2;
		}

		// Token: 0x06004605 RID: 17925 RVA: 0x000F5D9C File Offset: 0x000F3F9C
		internal static Set<Constant> ExpandNegationsInDomain(IEnumerable<Constant> domain)
		{
			return Domain.ExpandNegationsInDomain(domain, domain);
		}

		// Token: 0x06004606 RID: 17926 RVA: 0x000F5DA8 File Offset: 0x000F3FA8
		private static Set<Constant> DeterminePossibleValues(IEnumerable<Constant> domain)
		{
			Set<Constant> set = new Set<Constant>(Constant.EqualityComparer);
			foreach (Constant constant in domain)
			{
				NegatedConstant negatedConstant = constant as NegatedConstant;
				if (negatedConstant != null)
				{
					using (IEnumerator<Constant> enumerator2 = negatedConstant.Elements.GetEnumerator())
					{
						while (enumerator2.MoveNext())
						{
							Constant constant2 = enumerator2.Current;
							set.Add(constant2);
						}
						continue;
					}
				}
				set.Add(constant);
			}
			return set;
		}

		// Token: 0x06004607 RID: 17927 RVA: 0x000F5E4C File Offset: 0x000F404C
		internal static Dictionary<MemberPath, Set<Constant>> ComputeConstantDomainSetsForSlotsInQueryViews(IEnumerable<Cell> cells, EdmItemCollection edmItemCollection, bool isValidationEnabled)
		{
			Dictionary<MemberPath, Set<Constant>> dictionary = new Dictionary<MemberPath, Set<Constant>>(MemberPath.EqualityComparer);
			foreach (Cell cell in cells)
			{
				foreach (MemberRestriction memberRestriction in cell.CQuery.GetConjunctsFromWhereClause())
				{
					MemberProjectedSlot restrictedMemberSlot = memberRestriction.RestrictedMemberSlot;
					Set<Constant> set = Domain.DeriveDomainFromMemberPath(restrictedMemberSlot.MemberPath, edmItemCollection, isValidationEnabled);
					set.AddRange(memberRestriction.Domain.Values.Where((Constant c) => !c.Equals(Constant.Null) && !c.Equals(Constant.NotNull)));
					Set<Constant> set2;
					if (!dictionary.TryGetValue(restrictedMemberSlot.MemberPath, out set2))
					{
						dictionary[restrictedMemberSlot.MemberPath] = set;
					}
					else
					{
						set2.AddRange(set);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x06004608 RID: 17928 RVA: 0x000F5F58 File Offset: 0x000F4158
		private static bool GetRestrictedOrUnrestrictedDomain(MemberProjectedSlot slot, CellQuery cellQuery, EdmItemCollection edmItemCollection, out Set<Constant> domain)
		{
			return Domain.TryGetDomainRestrictedByWhereClause(Domain.DeriveDomainFromMemberPath(slot.MemberPath, edmItemCollection, true), slot, cellQuery, out domain);
		}

		// Token: 0x06004609 RID: 17929 RVA: 0x000F5F70 File Offset: 0x000F4170
		internal static Dictionary<MemberPath, Set<Constant>> ComputeConstantDomainSetsForSlotsInUpdateViews(IEnumerable<Cell> cells, EdmItemCollection edmItemCollection)
		{
			Dictionary<MemberPath, Set<Constant>> dictionary = new Dictionary<MemberPath, Set<Constant>>(MemberPath.EqualityComparer);
			foreach (Cell cell in cells)
			{
				CellQuery cquery = cell.CQuery;
				CellQuery squery = cell.SQuery;
				foreach (MemberProjectedSlot memberProjectedSlot in from oneOfConst in squery.GetConjunctsFromWhereClause()
					select oneOfConst.RestrictedMemberSlot)
				{
					Set<Constant> set;
					if (!Domain.GetRestrictedOrUnrestrictedDomain(memberProjectedSlot, squery, edmItemCollection, out set))
					{
						int projectedPosition = squery.GetProjectedPosition(memberProjectedSlot);
						if (projectedPosition >= 0 && !Domain.GetRestrictedOrUnrestrictedDomain(cquery.ProjectedSlotAt(projectedPosition) as MemberProjectedSlot, cquery, edmItemCollection, out set))
						{
							continue;
						}
					}
					MemberPath memberPath = memberProjectedSlot.MemberPath;
					Constant constant;
					if (Domain.TryGetDefaultValueForMemberPath(memberPath, out constant))
					{
						set.Add(constant);
					}
					Set<Constant> set2;
					if (!dictionary.TryGetValue(memberPath, out set2))
					{
						dictionary[memberPath] = set;
					}
					else
					{
						set2.AddRange(set);
					}
				}
			}
			return dictionary;
		}

		// Token: 0x0600460A RID: 17930 RVA: 0x000F60A4 File Offset: 0x000F42A4
		private static bool TryGetDomainRestrictedByWhereClause(IEnumerable<Constant> domain, MemberProjectedSlot slot, CellQuery cellQuery, out Set<Constant> result)
		{
			IEnumerable<Set<Constant>> enumerable = from restriction in cellQuery.GetConjunctsFromWhereClause()
				where MemberPath.EqualityComparer.Equals(restriction.RestrictedMemberSlot.MemberPath, slot.MemberPath)
				select new Set<Constant>(restriction.Domain.Values, Constant.EqualityComparer);
			if (!enumerable.Any<Set<Constant>>())
			{
				result = new Set<Constant>(domain);
				return false;
			}
			Set<Constant> set = Domain.DeterminePossibleValues(enumerable.SelectMany((Set<Constant> m) => m.Select((Constant c) => c)), domain);
			Domain domain2 = new Domain(domain, set);
			foreach (Set<Constant> set2 in enumerable)
			{
				domain2 = domain2.Intersect(new Domain(set2, set));
			}
			result = new Set<Constant>(domain2.Values, Constant.EqualityComparer);
			return !domain.SequenceEqual(result);
		}

		// Token: 0x0600460B RID: 17931 RVA: 0x000F61A8 File Offset: 0x000F43A8
		private Domain Intersect(Domain second)
		{
			Domain domain = new Domain(this);
			domain.m_domain.Intersect(second.m_domain);
			return domain;
		}

		// Token: 0x0600460C RID: 17932 RVA: 0x000F61C4 File Offset: 0x000F43C4
		private static NegatedConstant GetNegatedConstant(IEnumerable<Constant> constants)
		{
			NegatedConstant negatedConstant = null;
			foreach (Constant constant in constants)
			{
				NegatedConstant negatedConstant2 = constant as NegatedConstant;
				if (negatedConstant2 != null)
				{
					negatedConstant = negatedConstant2;
				}
			}
			return negatedConstant;
		}

		// Token: 0x0600460D RID: 17933 RVA: 0x000F6214 File Offset: 0x000F4414
		private static Set<Constant> DeterminePossibleValues(IEnumerable<Constant> domain1, IEnumerable<Constant> domain2)
		{
			return Domain.DeterminePossibleValues(new Set<Constant>(domain1, Constant.EqualityComparer).Union(domain2));
		}

		// Token: 0x0600460E RID: 17934 RVA: 0x000F622C File Offset: 0x000F442C
		[Conditional("DEBUG")]
		private static void CheckTwoDomainInvariants(Domain domain1, Domain domain2)
		{
			domain1.AssertInvariant();
			domain2.AssertInvariant();
		}

		// Token: 0x0600460F RID: 17935 RVA: 0x000F623A File Offset: 0x000F443A
		private static IEnumerable<Constant> CreateList(object value1, object value2)
		{
			yield return new ScalarConstant(value1);
			yield return new ScalarConstant(value2);
			yield break;
		}

		// Token: 0x06004610 RID: 17936 RVA: 0x000F6251 File Offset: 0x000F4451
		internal void AssertInvariant()
		{
			Domain.GetNegatedConstant(this.m_domain);
			Domain.GetNegatedConstant(this.m_possibleValues);
		}

		// Token: 0x06004611 RID: 17937 RVA: 0x000F626C File Offset: 0x000F446C
		internal string ToUserString()
		{
			StringBuilder stringBuilder = new StringBuilder();
			bool flag = true;
			foreach (Constant constant in this.m_domain)
			{
				if (!flag)
				{
					stringBuilder.Append(", ");
				}
				stringBuilder.Append(constant.ToUserString());
				flag = false;
			}
			return stringBuilder.ToString();
		}

		// Token: 0x06004612 RID: 17938 RVA: 0x000F62E4 File Offset: 0x000F44E4
		internal override void ToCompactString(StringBuilder builder)
		{
			builder.Append(this.ToUserString());
		}

		// Token: 0x0400190A RID: 6410
		private readonly Set<Constant> m_domain;

		// Token: 0x0400190B RID: 6411
		private readonly Set<Constant> m_possibleValues;
	}
}
