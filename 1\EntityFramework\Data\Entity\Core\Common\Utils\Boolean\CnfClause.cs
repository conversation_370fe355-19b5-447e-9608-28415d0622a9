﻿using System;

namespace System.Data.Entity.Core.Common.Utils.Boolean
{
	// Token: 0x02000608 RID: 1544
	internal sealed class CnfClause<T_Identifier> : Clause<T_Identifier>, IEquatable<CnfClause<T_Identifier>>
	{
		// Token: 0x06004B83 RID: 19331 RVA: 0x00109CFB File Offset: 0x00107EFB
		internal CnfClause(Set<Literal<T_Identifier>> literals)
			: base(literals, ExprType.Or)
		{
		}

		// Token: 0x06004B84 RID: 19332 RVA: 0x00109D05 File Offset: 0x00107F05
		public bool Equals(CnfClause<T_Identifier> other)
		{
			return other != null && other.Literals.SetEquals(base.Literals);
		}
	}
}
