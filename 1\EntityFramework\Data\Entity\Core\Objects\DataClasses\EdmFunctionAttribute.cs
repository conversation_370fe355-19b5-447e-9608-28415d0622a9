﻿using System;

namespace System.Data.Entity.Core.Objects.DataClasses
{
	// Token: 0x0200046F RID: 1135
	[Obsolete("This attribute has been replaced by System.Data.Entity.DbFunctionAttribute.")]
	[AttributeUsage(AttributeTargets.Method, Inherited = false, AllowMultiple = false)]
	public sealed class EdmFunctionAttribute : DbFunctionAttribute
	{
		// Token: 0x060037AF RID: 14255 RVA: 0x000B58BC File Offset: 0x000B3ABC
		public EdmFunctionAttribute(string namespaceName, string functionName)
			: base(namespaceName, functionName)
		{
		}
	}
}
