﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x0200054A RID: 1354
	public abstract class MappingItemCollection : ItemCollection
	{
		// Token: 0x06004256 RID: 16982 RVA: 0x000E016B File Offset: 0x000DE36B
		internal MappingItemCollection(DataSpace dataSpace)
			: base(dataSpace)
		{
		}

		// Token: 0x06004257 RID: 16983 RVA: 0x000E0174 File Offset: 0x000DE374
		internal virtual bool TryGetMap(string identity, DataSpace typeSpace, out MappingBase map)
		{
			throw Error.NotSupported();
		}

		// Token: 0x06004258 RID: 16984 RVA: 0x000E017B File Offset: 0x000DE37B
		internal virtual MappingBase GetMap(GlobalItem item)
		{
			throw Error.NotSupported();
		}

		// Token: 0x06004259 RID: 16985 RVA: 0x000E0182 File Offset: 0x000DE382
		internal virtual bool TryGetMap(GlobalItem item, out MappingBase map)
		{
			throw Error.NotSupported();
		}

		// Token: 0x0600425A RID: 16986 RVA: 0x000E0189 File Offset: 0x000DE389
		internal virtual MappingBase GetMap(string identity, DataSpace typeSpace, bool ignoreCase)
		{
			throw Error.NotSupported();
		}

		// Token: 0x0600425B RID: 16987 RVA: 0x000E0190 File Offset: 0x000DE390
		internal virtual bool TryGetMap(string identity, DataSpace typeSpace, bool ignoreCase, out MappingBase map)
		{
			throw Error.NotSupported();
		}

		// Token: 0x0600425C RID: 16988 RVA: 0x000E0197 File Offset: 0x000DE397
		internal virtual MappingBase GetMap(string identity, DataSpace typeSpace)
		{
			throw Error.NotSupported();
		}
	}
}
