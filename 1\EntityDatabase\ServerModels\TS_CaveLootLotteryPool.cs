﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x0200000C RID: 12
	public class TS_CaveLootLotteryPool
	{
		// Token: 0x17000037 RID: 55
		// (get) Token: 0x06000078 RID: 120 RVA: 0x00002464 File Offset: 0x00000664
		// (set) Token: 0x06000079 RID: 121 RVA: 0x0000246C File Offset: 0x0000066C
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int Id { get; set; }

		// Token: 0x17000038 RID: 56
		// (get) Token: 0x0600007A RID: 122 RVA: 0x00002475 File Offset: 0x00000675
		// (set) Token: 0x0600007B RID: 123 RVA: 0x0000247D File Offset: 0x0000067D
		public int ItemId { get; set; }

		// Token: 0x17000039 RID: 57
		// (get) Token: 0x0600007C RID: 124 RVA: 0x00002486 File Offset: 0x00000686
		// (set) Token: 0x0600007D RID: 125 RVA: 0x0000248E File Offset: 0x0000068E
		public int Type { get; set; }

		// Token: 0x1700003A RID: 58
		// (get) Token: 0x0600007E RID: 126 RVA: 0x00002497 File Offset: 0x00000697
		// (set) Token: 0x0600007F RID: 127 RVA: 0x0000249F File Offset: 0x0000069F
		public int Count { get; set; }

		// Token: 0x1700003B RID: 59
		// (get) Token: 0x06000080 RID: 128 RVA: 0x000024A8 File Offset: 0x000006A8
		// (set) Token: 0x06000081 RID: 129 RVA: 0x000024B0 File Offset: 0x000006B0
		public bool IsBind { get; set; }

		// Token: 0x1700003C RID: 60
		// (get) Token: 0x06000082 RID: 130 RVA: 0x000024B9 File Offset: 0x000006B9
		// (set) Token: 0x06000083 RID: 131 RVA: 0x000024C1 File Offset: 0x000006C1
		public int ValidDate { get; set; }

		// Token: 0x1700003D RID: 61
		// (get) Token: 0x06000084 RID: 132 RVA: 0x000024CA File Offset: 0x000006CA
		// (set) Token: 0x06000085 RID: 133 RVA: 0x000024D2 File Offset: 0x000006D2
		public int IsTips { get; set; }

		// Token: 0x1700003E RID: 62
		// (get) Token: 0x06000086 RID: 134 RVA: 0x000024DB File Offset: 0x000006DB
		// (set) Token: 0x06000087 RID: 135 RVA: 0x000024E3 File Offset: 0x000006E3
		public int Discount { get; set; }

		// Token: 0x1700003F RID: 63
		// (get) Token: 0x06000088 RID: 136 RVA: 0x000024EC File Offset: 0x000006EC
		// (set) Token: 0x06000089 RID: 137 RVA: 0x000024F4 File Offset: 0x000006F4
		public int Protect { get; set; }

		// Token: 0x17000040 RID: 64
		// (get) Token: 0x0600008A RID: 138 RVA: 0x000024FD File Offset: 0x000006FD
		// (set) Token: 0x0600008B RID: 139 RVA: 0x00002505 File Offset: 0x00000705
		public string Desc { get; set; }

		// Token: 0x17000041 RID: 65
		// (get) Token: 0x0600008C RID: 140 RVA: 0x0000250E File Offset: 0x0000070E
		// (set) Token: 0x0600008D RID: 141 RVA: 0x00002516 File Offset: 0x00000716
		public int Random { get; set; }
	}
}
