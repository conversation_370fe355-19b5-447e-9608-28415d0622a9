﻿using System;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004AB RID: 1195
	internal abstract class EdmModelRuleSet : DataModelValidationRuleSet
	{
		// Token: 0x06003AE8 RID: 15080 RVA: 0x000C1554 File Offset: 0x000BF754
		public static EdmModelRuleSet CreateEdmModelRuleSet(double version, bool validateSyntax)
		{
			if (object.Equals(version, 1.0))
			{
				return new EdmModelRuleSet.V1RuleSet(validateSyntax);
			}
			if (object.Equals(version, 1.1))
			{
				return new EdmModelRuleSet.V1_1RuleSet(validateSyntax);
			}
			if (object.Equals(version, 2.0))
			{
				return new EdmModelRuleSet.V2RuleSet(validateSyntax);
			}
			if (object.Equals(version, 3.0))
			{
				return new EdmModelRuleSet.V3RuleSet(validateSyntax);
			}
			return null;
		}

		// Token: 0x06003AE9 RID: 15081 RVA: 0x000C15EC File Offset: 0x000BF7EC
		private EdmModelRuleSet(bool validateSyntax)
		{
			if (validateSyntax)
			{
				base.AddRule(EdmModelSyntacticValidationRules.EdmAssociationConstraint_DependentEndMustNotBeNull);
				base.AddRule(EdmModelSyntacticValidationRules.EdmAssociationConstraint_DependentPropertiesMustNotBeEmpty);
				base.AddRule(EdmModelSyntacticValidationRules.EdmAssociationEnd_EntityTypeMustNotBeNull);
				base.AddRule(EdmModelSyntacticValidationRules.EdmAssociationSet_ElementTypeMustNotBeNull);
				base.AddRule(EdmModelSyntacticValidationRules.EdmAssociationSet_SourceSetMustNotBeNull);
				base.AddRule(EdmModelSyntacticValidationRules.EdmAssociationSet_TargetSetMustNotBeNull);
				base.AddRule(EdmModelSyntacticValidationRules.EdmAssociationType_AssociationEndMustNotBeNull);
				base.AddRule(EdmModelSyntacticValidationRules.EdmEntitySet_ElementTypeMustNotBeNull);
				base.AddRule(EdmModelSyntacticValidationRules.EdmModel_NameMustNotBeEmptyOrWhiteSpace);
				base.AddRule(EdmModelSyntacticValidationRules.EdmModel_NameIsTooLong);
				base.AddRule(EdmModelSyntacticValidationRules.EdmModel_NameIsNotAllowed);
				base.AddRule(EdmModelSyntacticValidationRules.EdmNavigationProperty_AssociationMustNotBeNull);
				base.AddRule(EdmModelSyntacticValidationRules.EdmNavigationProperty_ResultEndMustNotBeNull);
				base.AddRule(EdmModelSyntacticValidationRules.EdmTypeReference_TypeNotValid);
			}
			base.AddRule(EdmModelSemanticValidationRules.EdmType_SystemNamespaceEncountered);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntityContainer_SimilarRelationshipEnd);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntityContainer_InvalidEntitySetNameReference);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntityContainer_ConcurrencyRedefinedOnSubTypeOfEntitySetType);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntityContainer_DuplicateEntityContainerMemberName);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntityContainer_DuplicateEntitySetTable);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntitySet_EntitySetTypeHasNoKeys);
			base.AddRule(EdmModelSemanticValidationRules.EdmAssociationSet_DuplicateEndName);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntityType_EntityKeyMustBeScalar);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntityType_DuplicatePropertyNameSpecifiedInEntityKey);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntityType_InvalidKeyNullablePart);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntityType_InvalidKeyKeyDefinedInBaseClass);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntityType_KeyMissingOnEntityType);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntityType_InvalidMemberNameMatchesTypeName);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntityType_PropertyNameAlreadyDefinedDuplicate);
			base.AddRule(EdmModelSemanticValidationRules.EdmEntityType_CycleInTypeHierarchy);
			base.AddRule(EdmModelSemanticValidationRules.EdmNavigationProperty_BadNavigationPropertyUndefinedRole);
			base.AddRule(EdmModelSemanticValidationRules.EdmNavigationProperty_BadNavigationPropertyRolesCannotBeTheSame);
			base.AddRule(EdmModelSemanticValidationRules.EdmNavigationProperty_BadNavigationPropertyBadFromRoleType);
			base.AddRule(EdmModelSemanticValidationRules.EdmAssociationType_InvalidOperationMultipleEndsInAssociation);
			base.AddRule(EdmModelSemanticValidationRules.EdmAssociationType_EndWithManyMultiplicityCannotHaveOperationsSpecified);
			base.AddRule(EdmModelSemanticValidationRules.EdmAssociationType_EndNameAlreadyDefinedDuplicate);
			base.AddRule(EdmModelSemanticValidationRules.EdmAssociationType_InvalidPropertyInRelationshipConstraint);
			base.AddRule(EdmModelSemanticValidationRules.EdmAssociationType_SameRoleReferredInReferentialConstraint);
			base.AddRule(EdmModelSemanticValidationRules.EdmAssociationType_ValidateReferentialConstraint);
			base.AddRule(EdmModelSemanticValidationRules.EdmComplexType_InvalidMemberNameMatchesTypeName);
			base.AddRule(EdmModelSemanticValidationRules.EdmNamespace_TypeNameAlreadyDefinedDuplicate);
			base.AddRule(EdmModelSemanticValidationRules.EdmFunction_DuplicateParameterName);
		}

		// Token: 0x02000AD4 RID: 2772
		private abstract class NonV1_1RuleSet : EdmModelRuleSet
		{
			// Token: 0x0600635E RID: 25438 RVA: 0x0015743C File Offset: 0x0015563C
			protected NonV1_1RuleSet(bool validateSyntax)
				: base(validateSyntax)
			{
				base.AddRule(EdmModelSemanticValidationRules.EdmProperty_NullableComplexType);
				base.AddRule(EdmModelSemanticValidationRules.EdmProperty_InvalidCollectionKind);
				base.AddRule(EdmModelSemanticValidationRules.EdmComplexType_PropertyNameAlreadyDefinedDuplicate);
				base.AddRule(EdmModelSemanticValidationRules.EdmComplexType_InvalidIsAbstract);
				base.AddRule(EdmModelSemanticValidationRules.EdmComplexType_InvalidIsPolymorphic);
				base.AddRule(EdmModelSemanticValidationRules.EdmFunction_ComposableFunctionImportsNotAllowed_V1_V2);
			}
		}

		// Token: 0x02000AD5 RID: 2773
		private sealed class V1RuleSet : EdmModelRuleSet.NonV1_1RuleSet
		{
			// Token: 0x0600635F RID: 25439 RVA: 0x00157492 File Offset: 0x00155692
			internal V1RuleSet(bool validateSyntax)
				: base(validateSyntax)
			{
				base.AddRule(EdmModelSemanticValidationRules.EdmProperty_InvalidPropertyType);
			}
		}

		// Token: 0x02000AD6 RID: 2774
		private sealed class V1_1RuleSet : EdmModelRuleSet
		{
			// Token: 0x06006360 RID: 25440 RVA: 0x001574A6 File Offset: 0x001556A6
			internal V1_1RuleSet(bool validateSyntax)
				: base(validateSyntax)
			{
				base.AddRule(EdmModelSemanticValidationRules.EdmComplexType_PropertyNameAlreadyDefinedDuplicate_V1_1);
				base.AddRule(EdmModelSemanticValidationRules.EdmComplexType_CycleInTypeHierarchy_V1_1);
				base.AddRule(EdmModelSemanticValidationRules.EdmProperty_InvalidCollectionKind_V1_1);
				base.AddRule(EdmModelSemanticValidationRules.EdmProperty_InvalidPropertyType_V1_1);
			}
		}

		// Token: 0x02000AD7 RID: 2775
		private class V2RuleSet : EdmModelRuleSet.NonV1_1RuleSet
		{
			// Token: 0x06006361 RID: 25441 RVA: 0x001574DB File Offset: 0x001556DB
			internal V2RuleSet(bool validateSyntax)
				: base(validateSyntax)
			{
				base.AddRule(EdmModelSemanticValidationRules.EdmProperty_InvalidPropertyType);
			}
		}

		// Token: 0x02000AD8 RID: 2776
		private sealed class V3RuleSet : EdmModelRuleSet.V2RuleSet
		{
			// Token: 0x06006362 RID: 25442 RVA: 0x001574EF File Offset: 0x001556EF
			internal V3RuleSet(bool validateSyntax)
				: base(validateSyntax)
			{
				base.RemoveRule(EdmModelSemanticValidationRules.EdmProperty_InvalidPropertyType);
				base.AddRule(EdmModelSemanticValidationRules.EdmProperty_InvalidPropertyType_V3);
				base.RemoveRule(EdmModelSemanticValidationRules.EdmFunction_ComposableFunctionImportsNotAllowed_V1_V2);
			}
		}
	}
}
