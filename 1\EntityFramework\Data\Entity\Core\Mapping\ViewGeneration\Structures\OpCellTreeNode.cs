﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.CqlGeneration;
using System.Data.Entity.Core.Mapping.ViewGeneration.QueryRewriting;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Resources;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005AD RID: 1453
	internal class OpCellTreeNode : CellTreeNode
	{
		// Token: 0x060046C5 RID: 18117 RVA: 0x000F8D17 File Offset: 0x000F6F17
		internal OpCellTreeNode(ViewgenContext context, CellTreeOpType opType)
			: base(context)
		{
			this.m_opType = opType;
			this.m_attrs = new Set<MemberPath>(MemberPath.EqualityComparer);
			this.m_children = new List<CellTreeNode>();
		}

		// Token: 0x060046C6 RID: 18118 RVA: 0x000F8D42 File Offset: 0x000F6F42
		internal OpCellTreeNode(ViewgenContext context, CellTreeOpType opType, params CellTreeNode[] children)
			: this(context, opType, children)
		{
		}

		// Token: 0x060046C7 RID: 18119 RVA: 0x000F8D50 File Offset: 0x000F6F50
		internal OpCellTreeNode(ViewgenContext context, CellTreeOpType opType, IEnumerable<CellTreeNode> children)
			: this(context, opType)
		{
			foreach (CellTreeNode cellTreeNode in children)
			{
				this.Add(cellTreeNode);
			}
		}

		// Token: 0x17000E05 RID: 3589
		// (get) Token: 0x060046C8 RID: 18120 RVA: 0x000F8DA0 File Offset: 0x000F6FA0
		internal override CellTreeOpType OpType
		{
			get
			{
				return this.m_opType;
			}
		}

		// Token: 0x17000E06 RID: 3590
		// (get) Token: 0x060046C9 RID: 18121 RVA: 0x000F8DA8 File Offset: 0x000F6FA8
		internal override FragmentQuery LeftFragmentQuery
		{
			get
			{
				if (this.m_leftFragmentQuery == null)
				{
					this.m_leftFragmentQuery = OpCellTreeNode.GenerateFragmentQuery(this.Children, true, base.ViewgenContext, this.OpType);
				}
				return this.m_leftFragmentQuery;
			}
		}

		// Token: 0x17000E07 RID: 3591
		// (get) Token: 0x060046CA RID: 18122 RVA: 0x000F8DD6 File Offset: 0x000F6FD6
		internal override FragmentQuery RightFragmentQuery
		{
			get
			{
				if (this.m_rightFragmentQuery == null)
				{
					this.m_rightFragmentQuery = OpCellTreeNode.GenerateFragmentQuery(this.Children, false, base.ViewgenContext, this.OpType);
				}
				return this.m_rightFragmentQuery;
			}
		}

		// Token: 0x17000E08 RID: 3592
		// (get) Token: 0x060046CB RID: 18123 RVA: 0x000F8E04 File Offset: 0x000F7004
		internal override MemberDomainMap RightDomainMap
		{
			get
			{
				return this.m_children[0].RightDomainMap;
			}
		}

		// Token: 0x17000E09 RID: 3593
		// (get) Token: 0x060046CC RID: 18124 RVA: 0x000F8E17 File Offset: 0x000F7017
		internal override Set<MemberPath> Attributes
		{
			get
			{
				return this.m_attrs;
			}
		}

		// Token: 0x17000E0A RID: 3594
		// (get) Token: 0x060046CD RID: 18125 RVA: 0x000F8E1F File Offset: 0x000F701F
		internal override List<CellTreeNode> Children
		{
			get
			{
				return this.m_children;
			}
		}

		// Token: 0x17000E0B RID: 3595
		// (get) Token: 0x060046CE RID: 18126 RVA: 0x000F8E27 File Offset: 0x000F7027
		internal override int NumProjectedSlots
		{
			get
			{
				return this.m_children[0].NumProjectedSlots;
			}
		}

		// Token: 0x17000E0C RID: 3596
		// (get) Token: 0x060046CF RID: 18127 RVA: 0x000F8E3A File Offset: 0x000F703A
		internal override int NumBoolSlots
		{
			get
			{
				return this.m_children[0].NumBoolSlots;
			}
		}

		// Token: 0x060046D0 RID: 18128 RVA: 0x000F8E4D File Offset: 0x000F704D
		internal override TOutput Accept<TInput, TOutput>(CellTreeNode.SimpleCellTreeVisitor<TInput, TOutput> visitor, TInput param)
		{
			return visitor.VisitOpNode(this, param);
		}

		// Token: 0x060046D1 RID: 18129 RVA: 0x000F8E58 File Offset: 0x000F7058
		internal override TOutput Accept<TInput, TOutput>(CellTreeNode.CellTreeVisitor<TInput, TOutput> visitor, TInput param)
		{
			switch (this.OpType)
			{
			case CellTreeOpType.Union:
				return visitor.VisitUnion(this, param);
			case CellTreeOpType.FOJ:
				return visitor.VisitFullOuterJoin(this, param);
			case CellTreeOpType.LOJ:
				return visitor.VisitLeftOuterJoin(this, param);
			case CellTreeOpType.IJ:
				return visitor.VisitInnerJoin(this, param);
			case CellTreeOpType.LASJ:
				return visitor.VisitLeftAntiSemiJoin(this, param);
			default:
				return visitor.VisitInnerJoin(this, param);
			}
		}

		// Token: 0x060046D2 RID: 18130 RVA: 0x000F8EBF File Offset: 0x000F70BF
		internal void Add(CellTreeNode child)
		{
			this.Insert(this.m_children.Count, child);
		}

		// Token: 0x060046D3 RID: 18131 RVA: 0x000F8ED3 File Offset: 0x000F70D3
		internal void AddFirst(CellTreeNode child)
		{
			this.Insert(0, child);
		}

		// Token: 0x060046D4 RID: 18132 RVA: 0x000F8EDD File Offset: 0x000F70DD
		private void Insert(int index, CellTreeNode child)
		{
			this.m_attrs.Unite(child.Attributes);
			this.m_children.Insert(index, child);
			this.m_leftFragmentQuery = null;
			this.m_rightFragmentQuery = null;
		}

		// Token: 0x060046D5 RID: 18133 RVA: 0x000F8F0C File Offset: 0x000F710C
		internal override CqlBlock ToCqlBlock(bool[] requiredSlots, CqlIdentifiers identifiers, ref int blockAliasNum, ref List<WithRelationship> withRelationships)
		{
			CqlBlock cqlBlock;
			if (this.OpType == CellTreeOpType.Union)
			{
				cqlBlock = this.UnionToCqlBlock(requiredSlots, identifiers, ref blockAliasNum, ref withRelationships);
			}
			else
			{
				cqlBlock = this.JoinToCqlBlock(requiredSlots, identifiers, ref blockAliasNum, ref withRelationships);
			}
			return cqlBlock;
		}

		// Token: 0x060046D6 RID: 18134 RVA: 0x000F8F40 File Offset: 0x000F7140
		internal override bool IsProjectedSlot(int slot)
		{
			using (List<CellTreeNode>.Enumerator enumerator = this.Children.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					if (enumerator.Current.IsProjectedSlot(slot))
					{
						return true;
					}
				}
			}
			return false;
		}

		// Token: 0x060046D7 RID: 18135 RVA: 0x000F8F9C File Offset: 0x000F719C
		private CqlBlock UnionToCqlBlock(bool[] requiredSlots, CqlIdentifiers identifiers, ref int blockAliasNum, ref List<WithRelationship> withRelationships)
		{
			List<CqlBlock> list = new List<CqlBlock>();
			List<Tuple<CqlBlock, SlotInfo>> list2 = new List<Tuple<CqlBlock, SlotInfo>>();
			int num = requiredSlots.Length;
			foreach (CellTreeNode cellTreeNode in this.Children)
			{
				bool[] projectedSlots = cellTreeNode.GetProjectedSlots();
				OpCellTreeNode.AndWith(projectedSlots, requiredSlots);
				CqlBlock cqlBlock = cellTreeNode.ToCqlBlock(projectedSlots, identifiers, ref blockAliasNum, ref withRelationships);
				for (int i = projectedSlots.Length; i < cqlBlock.Slots.Count; i++)
				{
					list2.Add(Tuple.Create<CqlBlock, SlotInfo>(cqlBlock, cqlBlock.Slots[i]));
				}
				SlotInfo[] array = new SlotInfo[cqlBlock.Slots.Count];
				for (int j = 0; j < num; j++)
				{
					if (requiredSlots[j] && !projectedSlots[j])
					{
						if (base.IsBoolSlot(j))
						{
							array[j] = new SlotInfo(true, true, new BooleanProjectedSlot(BoolExpression.False, identifiers, base.SlotToBoolIndex(j)), null);
						}
						else
						{
							MemberPath memberPath = cqlBlock.MemberPath(j);
							array[j] = new SlotInfo(true, true, new ConstantProjectedSlot(Constant.Null), memberPath);
						}
					}
					else
					{
						array[j] = cqlBlock.Slots[j];
					}
				}
				cqlBlock.Slots = new ReadOnlyCollection<SlotInfo>(array);
				list.Add(cqlBlock);
			}
			if (list2.Count != 0)
			{
				foreach (CqlBlock cqlBlock2 in list)
				{
					SlotInfo[] array2 = new SlotInfo[num + list2.Count];
					cqlBlock2.Slots.CopyTo(array2, 0);
					int num2 = num;
					foreach (Tuple<CqlBlock, SlotInfo> tuple in list2)
					{
						SlotInfo item = tuple.Item2;
						if (tuple.Item1.Equals(cqlBlock2))
						{
							array2[num2] = new SlotInfo(true, true, item.SlotValue, item.OutputMember);
						}
						else
						{
							array2[num2] = new SlotInfo(true, true, new ConstantProjectedSlot(Constant.Null), item.OutputMember);
						}
						num2++;
					}
					cqlBlock2.Slots = new ReadOnlyCollection<SlotInfo>(array2);
				}
			}
			SlotInfo[] array3 = new SlotInfo[num + list2.Count];
			CqlBlock cqlBlock3 = list[0];
			for (int k = 0; k < num; k++)
			{
				SlotInfo slotInfo = cqlBlock3.Slots[k];
				bool flag = requiredSlots[k];
				array3[k] = new SlotInfo(flag, flag, slotInfo.SlotValue, slotInfo.OutputMember);
			}
			for (int l = num; l < num + list2.Count; l++)
			{
				SlotInfo slotInfo2 = cqlBlock3.Slots[l];
				array3[l] = new SlotInfo(true, true, slotInfo2.SlotValue, slotInfo2.OutputMember);
			}
			SlotInfo[] array4 = array3;
			List<CqlBlock> list3 = list;
			int num3 = blockAliasNum + 1;
			blockAliasNum = num3;
			return new UnionCqlBlock(array4, list3, identifiers, num3);
		}

		// Token: 0x060046D8 RID: 18136 RVA: 0x000F92D4 File Offset: 0x000F74D4
		private static void AndWith(bool[] boolArray, bool[] another)
		{
			for (int i = 0; i < boolArray.Length; i++)
			{
				boolArray[i] &= another[i];
			}
		}

		// Token: 0x060046D9 RID: 18137 RVA: 0x000F9300 File Offset: 0x000F7500
		private CqlBlock JoinToCqlBlock(bool[] requiredSlots, CqlIdentifiers identifiers, ref int blockAliasNum, ref List<WithRelationship> withRelationships)
		{
			int num = requiredSlots.Length;
			List<CqlBlock> list = new List<CqlBlock>();
			List<Tuple<QualifiedSlot, MemberPath>> list2 = new List<Tuple<QualifiedSlot, MemberPath>>();
			foreach (CellTreeNode cellTreeNode in this.Children)
			{
				bool[] projectedSlots = cellTreeNode.GetProjectedSlots();
				OpCellTreeNode.AndWith(projectedSlots, requiredSlots);
				CqlBlock cqlBlock = cellTreeNode.ToCqlBlock(projectedSlots, identifiers, ref blockAliasNum, ref withRelationships);
				list.Add(cqlBlock);
				for (int i = projectedSlots.Length; i < cqlBlock.Slots.Count; i++)
				{
					list2.Add(Tuple.Create<QualifiedSlot, MemberPath>(cqlBlock.QualifySlotWithBlockAlias(i), cqlBlock.MemberPath(i)));
				}
			}
			SlotInfo[] array = new SlotInfo[num + list2.Count];
			for (int j = 0; j < num; j++)
			{
				SlotInfo joinSlotInfo = this.GetJoinSlotInfo(this.OpType, requiredSlots[j], list, j, identifiers);
				array[j] = joinSlotInfo;
			}
			int num2 = 0;
			int k = num;
			while (k < num + list2.Count)
			{
				array[k] = new SlotInfo(true, true, list2[num2].Item1, list2[num2].Item2);
				k++;
				num2++;
			}
			List<JoinCqlBlock.OnClause> list3 = new List<JoinCqlBlock.OnClause>();
			for (int l = 1; l < list.Count; l++)
			{
				CqlBlock cqlBlock2 = list[l];
				JoinCqlBlock.OnClause onClause = new JoinCqlBlock.OnClause();
				foreach (int num3 in base.KeySlots)
				{
					if (!base.ViewgenContext.Config.IsValidationEnabled && (!cqlBlock2.IsProjected(num3) || !list[0].IsProjected(num3)))
					{
						ErrorLog errorLog = new ErrorLog();
						errorLog.AddEntry(new ErrorLog.Record(ViewGenErrorCode.NoJoinKeyOrFKProvidedInMapping, Strings.Viewgen_NoJoinKeyOrFK, base.ViewgenContext.AllWrappersForExtent, string.Empty));
						ExceptionHelpers.ThrowMappingException(errorLog, base.ViewgenContext.Config);
					}
					QualifiedSlot qualifiedSlot = list[0].QualifySlotWithBlockAlias(num3);
					QualifiedSlot qualifiedSlot2 = cqlBlock2.QualifySlotWithBlockAlias(num3);
					MemberPath outputMember = array[num3].OutputMember;
					onClause.Add(qualifiedSlot, outputMember, qualifiedSlot2, outputMember);
				}
				list3.Add(onClause);
			}
			CellTreeOpType opType = this.OpType;
			SlotInfo[] array2 = array;
			List<CqlBlock> list4 = list;
			List<JoinCqlBlock.OnClause> list5 = list3;
			int num4 = blockAliasNum + 1;
			blockAliasNum = num4;
			return new JoinCqlBlock(opType, array2, list4, list5, identifiers, num4);
		}

		// Token: 0x060046DA RID: 18138 RVA: 0x000F9570 File Offset: 0x000F7770
		private SlotInfo GetJoinSlotInfo(CellTreeOpType opType, bool isRequiredSlot, List<CqlBlock> children, int slotNum, CqlIdentifiers identifiers)
		{
			if (!isRequiredSlot)
			{
				return new SlotInfo(false, false, null, base.GetMemberPath(slotNum));
			}
			int num = -1;
			CaseStatement caseStatement = null;
			for (int i = 0; i < children.Count; i++)
			{
				CqlBlock cqlBlock = children[i];
				if (cqlBlock.IsProjected(slotNum))
				{
					if (base.IsKeySlot(slotNum))
					{
						num = i;
						break;
					}
					if (opType == CellTreeOpType.IJ)
					{
						num = OpCellTreeNode.GetInnerJoinChildForSlot(children, slotNum);
						break;
					}
					if (num != -1)
					{
						if (caseStatement == null)
						{
							caseStatement = new CaseStatement(base.GetMemberPath(slotNum));
							this.AddCaseForOuterJoins(caseStatement, children[num], slotNum, identifiers);
						}
						this.AddCaseForOuterJoins(caseStatement, cqlBlock, slotNum, identifiers);
					}
					num = i;
				}
			}
			MemberPath memberPath = base.GetMemberPath(slotNum);
			ProjectedSlot projectedSlot;
			if (caseStatement != null && (caseStatement.Clauses.Count > 0 || caseStatement.ElseValue != null))
			{
				caseStatement.Simplify();
				projectedSlot = new CaseStatementProjectedSlot(caseStatement, null);
			}
			else if (num >= 0)
			{
				projectedSlot = children[num].QualifySlotWithBlockAlias(slotNum);
			}
			else if (base.IsBoolSlot(slotNum))
			{
				projectedSlot = new BooleanProjectedSlot(BoolExpression.False, identifiers, base.SlotToBoolIndex(slotNum));
			}
			else
			{
				projectedSlot = new ConstantProjectedSlot(Domain.GetDefaultValueForMemberPath(memberPath, base.GetLeaves(), base.ViewgenContext.Config));
			}
			bool flag = base.IsBoolSlot(slotNum) && ((opType == CellTreeOpType.LOJ && num > 0) || opType == CellTreeOpType.FOJ);
			return new SlotInfo(true, true, projectedSlot, memberPath, flag);
		}

		// Token: 0x060046DB RID: 18139 RVA: 0x000F96C4 File Offset: 0x000F78C4
		private static int GetInnerJoinChildForSlot(List<CqlBlock> children, int slotNum)
		{
			int num = -1;
			for (int i = 0; i < children.Count; i++)
			{
				CqlBlock cqlBlock = children[i];
				if (cqlBlock.IsProjected(slotNum))
				{
					ProjectedSlot projectedSlot = cqlBlock.SlotValue(slotNum);
					ConstantProjectedSlot constantProjectedSlot = projectedSlot as ConstantProjectedSlot;
					if (projectedSlot is MemberProjectedSlot)
					{
						num = i;
					}
					else if (constantProjectedSlot != null && constantProjectedSlot.CellConstant.IsNull())
					{
						if (num == -1)
						{
							num = i;
						}
					}
					else
					{
						num = i;
					}
				}
			}
			return num;
		}

		// Token: 0x060046DC RID: 18140 RVA: 0x000F972C File Offset: 0x000F792C
		private void AddCaseForOuterJoins(CaseStatement caseForOuterJoins, CqlBlock child, int slotNum, CqlIdentifiers identifiers)
		{
			ConstantProjectedSlot constantProjectedSlot = child.SlotValue(slotNum) as ConstantProjectedSlot;
			if (constantProjectedSlot != null && constantProjectedSlot.CellConstant.IsNull())
			{
				return;
			}
			BoolExpression boolExpression = BoolExpression.False;
			for (int i = 0; i < this.NumBoolSlots; i++)
			{
				int num = base.BoolIndexToSlot(i);
				if (child.IsProjected(num))
				{
					QualifiedCellIdBoolean qualifiedCellIdBoolean = new QualifiedCellIdBoolean(child, identifiers, i);
					boolExpression = BoolExpression.CreateOr(new BoolExpression[]
					{
						boolExpression,
						BoolExpression.CreateLiteral(qualifiedCellIdBoolean, this.RightDomainMap)
					});
				}
			}
			QualifiedSlot qualifiedSlot = child.QualifySlotWithBlockAlias(slotNum);
			caseForOuterJoins.AddWhenThen(boolExpression, qualifiedSlot);
		}

		// Token: 0x060046DD RID: 18141 RVA: 0x000F97BC File Offset: 0x000F79BC
		private static FragmentQuery GenerateFragmentQuery(IEnumerable<CellTreeNode> children, bool isLeft, ViewgenContext context, CellTreeOpType OpType)
		{
			FragmentQuery fragmentQuery = (isLeft ? children.First<CellTreeNode>().LeftFragmentQuery : children.First<CellTreeNode>().RightFragmentQuery);
			FragmentQueryProcessor fragmentQueryProcessor = (isLeft ? context.LeftFragmentQP : context.RightFragmentQP);
			foreach (CellTreeNode cellTreeNode in children.Skip(1))
			{
				FragmentQuery fragmentQuery2 = (isLeft ? cellTreeNode.LeftFragmentQuery : cellTreeNode.RightFragmentQuery);
				switch (OpType)
				{
				case CellTreeOpType.LOJ:
					break;
				case CellTreeOpType.IJ:
					fragmentQuery = fragmentQueryProcessor.Intersect(fragmentQuery, fragmentQuery2);
					break;
				case CellTreeOpType.LASJ:
					fragmentQuery = fragmentQueryProcessor.Difference(fragmentQuery, fragmentQuery2);
					break;
				default:
					fragmentQuery = fragmentQueryProcessor.Union(fragmentQuery, fragmentQuery2);
					break;
				}
			}
			return fragmentQuery;
		}

		// Token: 0x060046DE RID: 18142 RVA: 0x000F9880 File Offset: 0x000F7A80
		internal static string OpToEsql(CellTreeOpType opType)
		{
			switch (opType)
			{
			case CellTreeOpType.Union:
				return "UNION ALL";
			case CellTreeOpType.FOJ:
				return "FULL OUTER JOIN";
			case CellTreeOpType.LOJ:
				return "LEFT OUTER JOIN";
			case CellTreeOpType.IJ:
				return "INNER JOIN";
			default:
				return null;
			}
		}

		// Token: 0x060046DF RID: 18143 RVA: 0x000F98B8 File Offset: 0x000F7AB8
		internal override void ToCompactString(StringBuilder stringBuilder)
		{
			stringBuilder.Append("(");
			for (int i = 0; i < this.m_children.Count; i++)
			{
				this.m_children[i].ToCompactString(stringBuilder);
				if (i != this.m_children.Count - 1)
				{
					StringUtil.FormatStringBuilder(stringBuilder, " {0} ", new object[] { this.OpType });
				}
			}
			stringBuilder.Append(")");
		}

		// Token: 0x0400192C RID: 6444
		private readonly Set<MemberPath> m_attrs;

		// Token: 0x0400192D RID: 6445
		private readonly List<CellTreeNode> m_children;

		// Token: 0x0400192E RID: 6446
		private readonly CellTreeOpType m_opType;

		// Token: 0x0400192F RID: 6447
		private FragmentQuery m_leftFragmentQuery;

		// Token: 0x04001930 RID: 6448
		private FragmentQuery m_rightFragmentQuery;
	}
}
