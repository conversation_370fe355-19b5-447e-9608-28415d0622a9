﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004A0 RID: 1184
	internal class DefaultAssemblyResolver : MetadataArtifactAssemblyResolver
	{
		// Token: 0x06003A46 RID: 14918 RVA: 0x000BFC1E File Offset: 0x000BDE1E
		internal override bool TryResolveAssemblyReference(AssemblyName referenceName, out Assembly assembly)
		{
			assembly = this.ResolveAssembly(referenceName);
			return assembly != null;
		}

		// Token: 0x06003A47 RID: 14919 RVA: 0x000BFC31 File Offset: 0x000BDE31
		internal override IEnumerable<Assembly> GetWildcardAssemblies()
		{
			return DefaultAssemblyResolver.GetAllDiscoverableAssemblies();
		}

		// Token: 0x06003A48 RID: 14920 RVA: 0x000BFC38 File Offset: 0x000BDE38
		internal virtual Assembly ResolveAssembly(AssemblyName referenceName)
		{
			Assembly assembly = null;
			foreach (Assembly assembly2 in DefaultAssemblyResolver.GetAlreadyLoadedNonSystemAssemblies())
			{
				if (AssemblyName.ReferenceMatchesDefinition(referenceName, new AssemblyName(assembly2.FullName)))
				{
					return assembly2;
				}
			}
			if (assembly == null)
			{
				assembly = MetadataAssemblyHelper.SafeLoadReferencedAssembly(referenceName);
				if (assembly != null)
				{
					return assembly;
				}
			}
			DefaultAssemblyResolver.TryFindWildcardAssemblyMatch(referenceName, out assembly);
			return assembly;
		}

		// Token: 0x06003A49 RID: 14921 RVA: 0x000BFCC0 File Offset: 0x000BDEC0
		private static bool TryFindWildcardAssemblyMatch(AssemblyName referenceName, out Assembly assembly)
		{
			foreach (Assembly assembly2 in DefaultAssemblyResolver.GetAllDiscoverableAssemblies())
			{
				if (AssemblyName.ReferenceMatchesDefinition(referenceName, new AssemblyName(assembly2.FullName)))
				{
					assembly = assembly2;
					return true;
				}
			}
			assembly = null;
			return false;
		}

		// Token: 0x06003A4A RID: 14922 RVA: 0x000BFD28 File Offset: 0x000BDF28
		private static IEnumerable<Assembly> GetAlreadyLoadedNonSystemAssemblies()
		{
			return from a in AppDomain.CurrentDomain.GetAssemblies()
				where a != null && !MetadataAssemblyHelper.ShouldFilterAssembly(a)
				select a;
		}

		// Token: 0x06003A4B RID: 14923 RVA: 0x000BFD58 File Offset: 0x000BDF58
		private static IEnumerable<Assembly> GetAllDiscoverableAssemblies()
		{
			Assembly entryAssembly = Assembly.GetEntryAssembly();
			HashSet<Assembly> hashSet = new HashSet<Assembly>(DefaultAssemblyResolver.AssemblyComparer.Instance);
			foreach (Assembly assembly in DefaultAssemblyResolver.GetAlreadyLoadedNonSystemAssemblies())
			{
				hashSet.Add(assembly);
			}
			AspProxy aspProxy = new AspProxy();
			if (aspProxy.IsAspNetEnvironment())
			{
				if (aspProxy.HasBuildManagerType())
				{
					IEnumerable<Assembly> buildManagerReferencedAssemblies = aspProxy.GetBuildManagerReferencedAssemblies();
					if (buildManagerReferencedAssemblies != null)
					{
						foreach (Assembly assembly2 in buildManagerReferencedAssemblies)
						{
							if (!MetadataAssemblyHelper.ShouldFilterAssembly(assembly2))
							{
								hashSet.Add(assembly2);
							}
						}
					}
				}
				return hashSet.Where((Assembly a) => a != null);
			}
			if (entryAssembly == null)
			{
				return hashSet;
			}
			hashSet.Add(entryAssembly);
			foreach (Assembly assembly3 in MetadataAssemblyHelper.GetNonSystemReferencedAssemblies(entryAssembly))
			{
				hashSet.Add(assembly3);
			}
			return hashSet;
		}

		// Token: 0x02000AC9 RID: 2761
		internal sealed class AssemblyComparer : IEqualityComparer<Assembly>
		{
			// Token: 0x06006330 RID: 25392 RVA: 0x001571D7 File Offset: 0x001553D7
			private AssemblyComparer()
			{
			}

			// Token: 0x170010D6 RID: 4310
			// (get) Token: 0x06006331 RID: 25393 RVA: 0x001571DF File Offset: 0x001553DF
			public static DefaultAssemblyResolver.AssemblyComparer Instance
			{
				get
				{
					return DefaultAssemblyResolver.AssemblyComparer._instance;
				}
			}

			// Token: 0x06006332 RID: 25394 RVA: 0x001571E8 File Offset: 0x001553E8
			public bool Equals(Assembly x, Assembly y)
			{
				AssemblyName assemblyName = new AssemblyName(x.FullName);
				AssemblyName assemblyName2 = new AssemblyName(y.FullName);
				return x == y || (AssemblyName.ReferenceMatchesDefinition(assemblyName, assemblyName2) && AssemblyName.ReferenceMatchesDefinition(assemblyName2, assemblyName));
			}

			// Token: 0x06006333 RID: 25395 RVA: 0x00157225 File Offset: 0x00155425
			public int GetHashCode(Assembly assembly)
			{
				return assembly.FullName.GetHashCode();
			}

			// Token: 0x04002BAD RID: 11181
			private static readonly DefaultAssemblyResolver.AssemblyComparer _instance = new DefaultAssemblyResolver.AssemblyComparer();
		}
	}
}
