﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using System.Threading;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x0200048D RID: 1165
	internal sealed class ClrEntityType : EntityType
	{
		// Token: 0x060039DC RID: 14812 RVA: 0x000BD934 File Offset: 0x000BBB34
		internal ClrEntityType(Type type, string cspaceNamespaceName, string cspaceTypeName)
			: base(Check.NotNull<Type>(type, "type").Name, type.NestingNamespace() ?? string.Empty, DataSpace.OSpace)
		{
			this._type = type;
			this._cspaceNamespaceName = cspaceNamespaceName;
			this._cspaceTypeName = cspaceNamespaceName + "." + cspaceTypeName;
			base.Abstract = type.IsAbstract();
		}

		// Token: 0x17000B0C RID: 2828
		// (get) Token: 0x060039DD RID: 14813 RVA: 0x000BD993 File Offset: 0x000BBB93
		// (set) Token: 0x060039DE RID: 14814 RVA: 0x000BD99B File Offset: 0x000BBB9B
		internal Func<object> Constructor
		{
			get
			{
				return this._constructor;
			}
			set
			{
				Interlocked.CompareExchange<Func<object>>(ref this._constructor, value, null);
			}
		}

		// Token: 0x17000B0D RID: 2829
		// (get) Token: 0x060039DF RID: 14815 RVA: 0x000BD9AB File Offset: 0x000BBBAB
		internal override Type ClrType
		{
			get
			{
				return this._type;
			}
		}

		// Token: 0x17000B0E RID: 2830
		// (get) Token: 0x060039E0 RID: 14816 RVA: 0x000BD9B3 File Offset: 0x000BBBB3
		internal string CSpaceTypeName
		{
			get
			{
				return this._cspaceTypeName;
			}
		}

		// Token: 0x17000B0F RID: 2831
		// (get) Token: 0x060039E1 RID: 14817 RVA: 0x000BD9BB File Offset: 0x000BBBBB
		internal string CSpaceNamespaceName
		{
			get
			{
				return this._cspaceNamespaceName;
			}
		}

		// Token: 0x17000B10 RID: 2832
		// (get) Token: 0x060039E2 RID: 14818 RVA: 0x000BD9C3 File Offset: 0x000BBBC3
		internal string HashedDescription
		{
			get
			{
				if (this._hash == null)
				{
					Interlocked.CompareExchange<string>(ref this._hash, this.BuildEntityTypeHash(), null);
				}
				return this._hash;
			}
		}

		// Token: 0x060039E3 RID: 14819 RVA: 0x000BD9E8 File Offset: 0x000BBBE8
		private string BuildEntityTypeHash()
		{
			string text;
			using (SHA256 sha = MetadataHelper.CreateSHA256HashAlgorithm())
			{
				byte[] array = sha.ComputeHash(Encoding.ASCII.GetBytes(this.BuildEntityTypeDescription()));
				StringBuilder stringBuilder = new StringBuilder(array.Length * 2);
				foreach (byte b in array)
				{
					stringBuilder.Append(b.ToString("X2", CultureInfo.InvariantCulture));
				}
				text = stringBuilder.ToString();
			}
			return text;
		}

		// Token: 0x060039E4 RID: 14820 RVA: 0x000BDA70 File Offset: 0x000BBC70
		private string BuildEntityTypeDescription()
		{
			StringBuilder stringBuilder = new StringBuilder(512);
			stringBuilder.Append("CLR:").Append(this.ClrType.FullName);
			stringBuilder.Append("Conceptual:").Append(this.CSpaceTypeName);
			SortedSet<string> sortedSet = new SortedSet<string>();
			foreach (NavigationProperty navigationProperty in base.NavigationProperties)
			{
				sortedSet.Add(string.Concat(new string[]
				{
					navigationProperty.Name,
					"*",
					navigationProperty.FromEndMember.Name,
					"*",
					navigationProperty.FromEndMember.RelationshipMultiplicity.ToString(),
					"*",
					navigationProperty.ToEndMember.Name,
					"*",
					navigationProperty.ToEndMember.RelationshipMultiplicity.ToString(),
					"*"
				}));
			}
			stringBuilder.Append("NavProps:");
			foreach (string text in sortedSet)
			{
				stringBuilder.Append(text);
			}
			SortedSet<string> sortedSet2 = new SortedSet<string>();
			foreach (string text2 in this.KeyMemberNames)
			{
				sortedSet2.Add(text2);
			}
			stringBuilder.Append("Keys:");
			foreach (string text3 in sortedSet2)
			{
				stringBuilder.Append(text3 + "*");
			}
			SortedSet<string> sortedSet3 = new SortedSet<string>();
			foreach (EdmMember edmMember in base.Members)
			{
				if (!sortedSet2.Contains(edmMember.Name))
				{
					sortedSet3.Add(edmMember.Name + "*");
				}
			}
			stringBuilder.Append("Scalars:");
			foreach (string text4 in sortedSet3)
			{
				stringBuilder.Append(text4 + "*");
			}
			return stringBuilder.ToString();
		}

		// Token: 0x0400134C RID: 4940
		private readonly Type _type;

		// Token: 0x0400134D RID: 4941
		private Func<object> _constructor;

		// Token: 0x0400134E RID: 4942
		private readonly string _cspaceTypeName;

		// Token: 0x0400134F RID: 4943
		private readonly string _cspaceNamespaceName;

		// Token: 0x04001350 RID: 4944
		private string _hash;
	}
}
