﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000012 RID: 18
	public class TS_Relic_AdvanceTemplate
	{
		// Token: 0x17000073 RID: 115
		// (get) Token: 0x060000F6 RID: 246 RVA: 0x00002896 File Offset: 0x00000A96
		// (set) Token: 0x060000F7 RID: 247 RVA: 0x0000289E File Offset: 0x00000A9E
		[Key]
		public int ID { get; set; }

		// Token: 0x17000074 RID: 116
		// (get) Token: 0x060000F8 RID: 248 RVA: 0x000028A7 File Offset: 0x00000AA7
		// (set) Token: 0x060000F9 RID: 249 RVA: 0x000028AF File Offset: 0x00000AAF
		public int RelicID { get; set; }

		// Token: 0x17000075 RID: 117
		// (get) Token: 0x060000FA RID: 250 RVA: 0x000028B8 File Offset: 0x00000AB8
		// (set) Token: 0x060000FB RID: 251 RVA: 0x000028C0 File Offset: 0x00000AC0
		public int Level { get; set; }

		// Token: 0x17000076 RID: 118
		// (get) Token: 0x060000FC RID: 252 RVA: 0x000028C9 File Offset: 0x00000AC9
		// (set) Token: 0x060000FD RID: 253 RVA: 0x000028D1 File Offset: 0x00000AD1
		public string ItemCost { get; set; }

		// Token: 0x17000077 RID: 119
		// (get) Token: 0x060000FE RID: 254 RVA: 0x000028DA File Offset: 0x00000ADA
		// (set) Token: 0x060000FF RID: 255 RVA: 0x000028E2 File Offset: 0x00000AE2
		public string Skill { get; set; }
	}
}
