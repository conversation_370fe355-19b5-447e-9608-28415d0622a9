﻿using System;
using System.Data.Entity.ModelConfiguration.Edm;
using System.Linq;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x02000509 RID: 1289
	internal class CodeFirstOSpaceLoader
	{
		// Token: 0x06003FC4 RID: 16324 RVA: 0x000D36C0 File Offset: 0x000D18C0
		public CodeFirstOSpaceLoader(CodeFirstOSpaceTypeFactory typeFactory = null)
		{
			this._typeFactory = typeFactory ?? new CodeFirstOSpaceTypeFactory();
		}

		// Token: 0x06003FC5 RID: 16325 RVA: 0x000D36D8 File Offset: 0x000D18D8
		public void LoadTypes(EdmItemCollection edmItemCollection, ObjectItemCollection objectItemCollection)
		{
			foreach (EdmType edmType in from t in edmItemCollection.OfType<EdmType>()
				where t.BuiltInTypeKind == BuiltInTypeKind.EntityType || t.BuiltInTypeKind == BuiltInTypeKind.EnumType || t.BuiltInTypeKind == BuiltInTypeKind.ComplexType
				select t)
			{
				Type clrType = edmType.GetClrType();
				if (clrType != null)
				{
					EdmType edmType2 = this._typeFactory.TryCreateType(clrType, edmType);
					if (edmType2 != null)
					{
						this._typeFactory.CspaceToOspace.Add(edmType, edmType2);
					}
				}
			}
			this._typeFactory.CreateRelationships(edmItemCollection);
			foreach (Action action in this._typeFactory.ReferenceResolutions)
			{
				action();
			}
			foreach (EdmType edmType3 in this._typeFactory.LoadedTypes.Values)
			{
				edmType3.SetReadOnly();
			}
			objectItemCollection.AddLoadedTypes(this._typeFactory.LoadedTypes);
			objectItemCollection.OSpaceTypesLoaded = true;
		}

		// Token: 0x0400163F RID: 5695
		private readonly CodeFirstOSpaceTypeFactory _typeFactory;
	}
}
