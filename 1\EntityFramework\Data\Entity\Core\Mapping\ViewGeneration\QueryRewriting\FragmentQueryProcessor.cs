﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Common.Utils.Boolean;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Globalization;
using System.Linq;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.QueryRewriting
{
	// Token: 0x02000588 RID: 1416
	internal class FragmentQueryProcessor : TileQueryProcessor<FragmentQuery>
	{
		// Token: 0x06004487 RID: 17543 RVA: 0x000F0668 File Offset: 0x000EE868
		public FragmentQueryProcessor(FragmentQueryKBChaseSupport kb)
		{
			this._kb = kb;
		}

		// Token: 0x06004488 RID: 17544 RVA: 0x000F0677 File Offset: 0x000EE877
		internal static FragmentQueryProcessor Merge(FragmentQueryProcessor qp1, FragmentQueryProcessor qp2)
		{
			FragmentQueryKBChaseSupport fragmentQueryKBChaseSupport = new FragmentQueryKBChaseSupport();
			fragmentQueryKBChaseSupport.AddKnowledgeBase(qp1.KnowledgeBase);
			fragmentQueryKBChaseSupport.AddKnowledgeBase(qp2.KnowledgeBase);
			return new FragmentQueryProcessor(fragmentQueryKBChaseSupport);
		}

		// Token: 0x17000D8F RID: 3471
		// (get) Token: 0x06004489 RID: 17545 RVA: 0x000F069B File Offset: 0x000EE89B
		internal FragmentQueryKB KnowledgeBase
		{
			get
			{
				return this._kb;
			}
		}

		// Token: 0x0600448A RID: 17546 RVA: 0x000F06A4 File Offset: 0x000EE8A4
		internal override FragmentQuery Union(FragmentQuery q1, FragmentQuery q2)
		{
			HashSet<MemberPath> hashSet = new HashSet<MemberPath>(q1.Attributes);
			hashSet.IntersectWith(q2.Attributes);
			BoolExpression boolExpression = BoolExpression.CreateOr(new BoolExpression[] { q1.Condition, q2.Condition });
			return FragmentQuery.Create(hashSet, boolExpression);
		}

		// Token: 0x0600448B RID: 17547 RVA: 0x000F06EE File Offset: 0x000EE8EE
		internal bool IsDisjointFrom(FragmentQuery q1, FragmentQuery q2)
		{
			return !this.IsSatisfiable(this.Intersect(q1, q2));
		}

		// Token: 0x0600448C RID: 17548 RVA: 0x000F0701 File Offset: 0x000EE901
		internal bool IsContainedIn(FragmentQuery q1, FragmentQuery q2)
		{
			return !this.IsSatisfiable(this.Difference(q1, q2));
		}

		// Token: 0x0600448D RID: 17549 RVA: 0x000F0714 File Offset: 0x000EE914
		internal bool IsEquivalentTo(FragmentQuery q1, FragmentQuery q2)
		{
			return this.IsContainedIn(q1, q2) && this.IsContainedIn(q2, q1);
		}

		// Token: 0x0600448E RID: 17550 RVA: 0x000F072C File Offset: 0x000EE92C
		internal override FragmentQuery Intersect(FragmentQuery q1, FragmentQuery q2)
		{
			HashSet<MemberPath> hashSet = new HashSet<MemberPath>(q1.Attributes);
			hashSet.IntersectWith(q2.Attributes);
			BoolExpression boolExpression = BoolExpression.CreateAnd(new BoolExpression[] { q1.Condition, q2.Condition });
			return FragmentQuery.Create(hashSet, boolExpression);
		}

		// Token: 0x0600448F RID: 17551 RVA: 0x000F0776 File Offset: 0x000EE976
		internal override FragmentQuery Difference(FragmentQuery qA, FragmentQuery qB)
		{
			return FragmentQuery.Create(qA.Attributes, BoolExpression.CreateAndNot(qA.Condition, qB.Condition));
		}

		// Token: 0x06004490 RID: 17552 RVA: 0x000F0794 File Offset: 0x000EE994
		internal override bool IsSatisfiable(FragmentQuery query)
		{
			return this.IsSatisfiable(query.Condition);
		}

		// Token: 0x06004491 RID: 17553 RVA: 0x000F07A2 File Offset: 0x000EE9A2
		private bool IsSatisfiable(BoolExpression condition)
		{
			return this._kb.IsSatisfiable(condition.Tree);
		}

		// Token: 0x06004492 RID: 17554 RVA: 0x000F07B8 File Offset: 0x000EE9B8
		internal override FragmentQuery CreateDerivedViewBySelectingConstantAttributes(FragmentQuery view)
		{
			HashSet<MemberPath> hashSet = new HashSet<MemberPath>();
			foreach (DomainVariable<BoolLiteral, Constant> domainVariable in view.Condition.Variables)
			{
				MemberRestriction memberRestriction = domainVariable.Identifier as MemberRestriction;
				if (memberRestriction != null)
				{
					MemberPath memberPath = memberRestriction.RestrictedMemberSlot.MemberPath;
					Domain domain = memberRestriction.Domain;
					if (!view.Attributes.Contains(memberPath))
					{
						if (!domain.AllPossibleValues.Any((Constant it) => it.HasNotNull()))
						{
							foreach (Constant constant in domain.Values)
							{
								DomainConstraint<BoolLiteral, Constant> domainConstraint = new DomainConstraint<BoolLiteral, Constant>(domainVariable, new Set<Constant>(new Constant[] { constant }, Constant.EqualityComparer));
								BoolExpression boolExpression = view.Condition.Create(new AndExpr<DomainConstraint<BoolLiteral, Constant>>(new BoolExpr<DomainConstraint<BoolLiteral, Constant>>[]
								{
									view.Condition.Tree,
									new NotExpr<DomainConstraint<BoolLiteral, Constant>>(new TermExpr<DomainConstraint<BoolLiteral, Constant>>(domainConstraint))
								}));
								if (!this.IsSatisfiable(boolExpression))
								{
									hashSet.Add(memberPath);
								}
							}
						}
					}
				}
			}
			if (hashSet.Count > 0)
			{
				hashSet.UnionWith(view.Attributes);
				return new FragmentQuery(string.Format(CultureInfo.InvariantCulture, "project({0})", new object[] { view.Description }), view.FromVariable, hashSet, view.Condition);
			}
			return null;
		}

		// Token: 0x06004493 RID: 17555 RVA: 0x000F097C File Offset: 0x000EEB7C
		public override string ToString()
		{
			return this._kb.ToString();
		}

		// Token: 0x040018AA RID: 6314
		private readonly FragmentQueryKBChaseSupport _kb;

		// Token: 0x02000B93 RID: 2963
		private class AttributeSetComparator : IEqualityComparer<HashSet<MemberPath>>
		{
			// Token: 0x060066CC RID: 26316 RVA: 0x0015F8D7 File Offset: 0x0015DAD7
			public bool Equals(HashSet<MemberPath> x, HashSet<MemberPath> y)
			{
				return x.SetEquals(y);
			}

			// Token: 0x060066CD RID: 26317 RVA: 0x0015F8E0 File Offset: 0x0015DAE0
			public int GetHashCode(HashSet<MemberPath> attrs)
			{
				int num = 123;
				foreach (MemberPath memberPath in attrs)
				{
					num += MemberPath.EqualityComparer.GetHashCode(memberPath) * 7;
				}
				return num;
			}
		}
	}
}
