﻿using System;
using System.Collections.ObjectModel;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x0200055E RID: 1374
	public abstract class StructuralTypeMapping : MappingItem
	{
		// Token: 0x17000D57 RID: 3415
		// (get) Token: 0x06004341 RID: 17217
		public abstract ReadOnlyCollection<PropertyMapping> PropertyMappings { get; }

		// Token: 0x17000D58 RID: 3416
		// (get) Token: 0x06004342 RID: 17218
		public abstract ReadOnlyCollection<ConditionPropertyMapping> Conditions { get; }

		// Token: 0x06004343 RID: 17219
		public abstract void AddPropertyMapping(PropertyMapping propertyMapping);

		// Token: 0x06004344 RID: 17220
		public abstract void RemovePropertyMapping(PropertyMapping propertyMapping);

		// Token: 0x06004345 RID: 17221
		public abstract void AddCondition(ConditionPropertyMapping condition);

		// Token: 0x06004346 RID: 17222
		public abstract void RemoveCondition(ConditionPropertyMapping condition);
	}
}
