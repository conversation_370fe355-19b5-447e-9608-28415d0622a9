﻿using System;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder.Spatial
{
	// Token: 0x020006FA RID: 1786
	public static class SpatialEdmFunctions
	{
		// Token: 0x060053F0 RID: 21488 RVA: 0x0012CDEC File Offset: 0x0012AFEC
		public static DbFunctionExpression GeometryFromText(DbExpression wellKnownText)
		{
			Check.NotNull<DbExpression>(wellKnownText, "wellKnownText");
			return EdmFunctions.InvokeCanonicalFunction("GeometryFromText", new DbExpression[] { wellKnownText });
		}

		// Token: 0x060053F1 RID: 21489 RVA: 0x0012CE0E File Offset: 0x0012B00E
		public static DbFunctionExpression GeometryFromText(DbExpression wellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(wellKnownText, "wellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryFromText", new DbExpression[] { wellKnownText, coordinateSystemId });
		}

		// Token: 0x060053F2 RID: 21490 RVA: 0x0012CE40 File Offset: 0x0012B040
		public static DbFunctionExpression GeometryPointFromText(DbExpression pointWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(pointWellKnownText, "pointWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryPointFromText", new DbExpression[] { pointWellKnownText, coordinateSystemId });
		}

		// Token: 0x060053F3 RID: 21491 RVA: 0x0012CE72 File Offset: 0x0012B072
		public static DbFunctionExpression GeometryLineFromText(DbExpression lineWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(lineWellKnownText, "lineWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryLineFromText", new DbExpression[] { lineWellKnownText, coordinateSystemId });
		}

		// Token: 0x060053F4 RID: 21492 RVA: 0x0012CEA4 File Offset: 0x0012B0A4
		public static DbFunctionExpression GeometryPolygonFromText(DbExpression polygonWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(polygonWellKnownText, "polygonWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryPolygonFromText", new DbExpression[] { polygonWellKnownText, coordinateSystemId });
		}

		// Token: 0x060053F5 RID: 21493 RVA: 0x0012CED6 File Offset: 0x0012B0D6
		public static DbFunctionExpression GeometryMultiPointFromText(DbExpression multiPointWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(multiPointWellKnownText, "multiPointWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryMultiPointFromText", new DbExpression[] { multiPointWellKnownText, coordinateSystemId });
		}

		// Token: 0x060053F6 RID: 21494 RVA: 0x0012CF08 File Offset: 0x0012B108
		public static DbFunctionExpression GeometryMultiLineFromText(DbExpression multiLineWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(multiLineWellKnownText, "multiLineWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryMultiLineFromText", new DbExpression[] { multiLineWellKnownText, coordinateSystemId });
		}

		// Token: 0x060053F7 RID: 21495 RVA: 0x0012CF3A File Offset: 0x0012B13A
		public static DbFunctionExpression GeometryMultiPolygonFromText(DbExpression multiPolygonWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(multiPolygonWellKnownText, "multiPolygonWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryMultiPolygonFromText", new DbExpression[] { multiPolygonWellKnownText, coordinateSystemId });
		}

		// Token: 0x060053F8 RID: 21496 RVA: 0x0012CF6C File Offset: 0x0012B16C
		public static DbFunctionExpression GeometryCollectionFromText(DbExpression geometryCollectionWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(geometryCollectionWellKnownText, "geometryCollectionWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryCollectionFromText", new DbExpression[] { geometryCollectionWellKnownText, coordinateSystemId });
		}

		// Token: 0x060053F9 RID: 21497 RVA: 0x0012CF9E File Offset: 0x0012B19E
		public static DbFunctionExpression GeometryFromBinary(DbExpression wellKnownBinaryValue)
		{
			Check.NotNull<DbExpression>(wellKnownBinaryValue, "wellKnownBinaryValue");
			return EdmFunctions.InvokeCanonicalFunction("GeometryFromBinary", new DbExpression[] { wellKnownBinaryValue });
		}

		// Token: 0x060053FA RID: 21498 RVA: 0x0012CFC0 File Offset: 0x0012B1C0
		public static DbFunctionExpression GeometryFromBinary(DbExpression wellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(wellKnownBinaryValue, "wellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryFromBinary", new DbExpression[] { wellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x060053FB RID: 21499 RVA: 0x0012CFF2 File Offset: 0x0012B1F2
		public static DbFunctionExpression GeometryPointFromBinary(DbExpression pointWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(pointWellKnownBinaryValue, "pointWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryPointFromBinary", new DbExpression[] { pointWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x060053FC RID: 21500 RVA: 0x0012D024 File Offset: 0x0012B224
		public static DbFunctionExpression GeometryLineFromBinary(DbExpression lineWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(lineWellKnownBinaryValue, "lineWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryLineFromBinary", new DbExpression[] { lineWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x060053FD RID: 21501 RVA: 0x0012D056 File Offset: 0x0012B256
		public static DbFunctionExpression GeometryPolygonFromBinary(DbExpression polygonWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(polygonWellKnownBinaryValue, "polygonWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryPolygonFromBinary", new DbExpression[] { polygonWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x060053FE RID: 21502 RVA: 0x0012D088 File Offset: 0x0012B288
		public static DbFunctionExpression GeometryMultiPointFromBinary(DbExpression multiPointWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(multiPointWellKnownBinaryValue, "multiPointWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryMultiPointFromBinary", new DbExpression[] { multiPointWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x060053FF RID: 21503 RVA: 0x0012D0BA File Offset: 0x0012B2BA
		public static DbFunctionExpression GeometryMultiLineFromBinary(DbExpression multiLineWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(multiLineWellKnownBinaryValue, "multiLineWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryMultiLineFromBinary", new DbExpression[] { multiLineWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x06005400 RID: 21504 RVA: 0x0012D0EC File Offset: 0x0012B2EC
		public static DbFunctionExpression GeometryMultiPolygonFromBinary(DbExpression multiPolygonWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(multiPolygonWellKnownBinaryValue, "multiPolygonWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryMultiPolygonFromBinary", new DbExpression[] { multiPolygonWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x06005401 RID: 21505 RVA: 0x0012D11E File Offset: 0x0012B31E
		public static DbFunctionExpression GeometryCollectionFromBinary(DbExpression geometryCollectionWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(geometryCollectionWellKnownBinaryValue, "geometryCollectionWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryCollectionFromBinary", new DbExpression[] { geometryCollectionWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x06005402 RID: 21506 RVA: 0x0012D150 File Offset: 0x0012B350
		public static DbFunctionExpression GeometryFromGml(DbExpression geometryMarkup)
		{
			Check.NotNull<DbExpression>(geometryMarkup, "geometryMarkup");
			return EdmFunctions.InvokeCanonicalFunction("GeometryFromGml", new DbExpression[] { geometryMarkup });
		}

		// Token: 0x06005403 RID: 21507 RVA: 0x0012D172 File Offset: 0x0012B372
		public static DbFunctionExpression GeometryFromGml(DbExpression geometryMarkup, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(geometryMarkup, "geometryMarkup");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeometryFromGml", new DbExpression[] { geometryMarkup, coordinateSystemId });
		}

		// Token: 0x06005404 RID: 21508 RVA: 0x0012D1A4 File Offset: 0x0012B3A4
		public static DbFunctionExpression GeographyFromText(DbExpression wellKnownText)
		{
			Check.NotNull<DbExpression>(wellKnownText, "wellKnownText");
			return EdmFunctions.InvokeCanonicalFunction("GeographyFromText", new DbExpression[] { wellKnownText });
		}

		// Token: 0x06005405 RID: 21509 RVA: 0x0012D1C6 File Offset: 0x0012B3C6
		public static DbFunctionExpression GeographyFromText(DbExpression wellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(wellKnownText, "wellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyFromText", new DbExpression[] { wellKnownText, coordinateSystemId });
		}

		// Token: 0x06005406 RID: 21510 RVA: 0x0012D1F8 File Offset: 0x0012B3F8
		public static DbFunctionExpression GeographyPointFromText(DbExpression pointWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(pointWellKnownText, "pointWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyPointFromText", new DbExpression[] { pointWellKnownText, coordinateSystemId });
		}

		// Token: 0x06005407 RID: 21511 RVA: 0x0012D22A File Offset: 0x0012B42A
		public static DbFunctionExpression GeographyLineFromText(DbExpression lineWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(lineWellKnownText, "lineWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyLineFromText", new DbExpression[] { lineWellKnownText, coordinateSystemId });
		}

		// Token: 0x06005408 RID: 21512 RVA: 0x0012D25C File Offset: 0x0012B45C
		public static DbFunctionExpression GeographyPolygonFromText(DbExpression polygonWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(polygonWellKnownText, "polygonWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyPolygonFromText", new DbExpression[] { polygonWellKnownText, coordinateSystemId });
		}

		// Token: 0x06005409 RID: 21513 RVA: 0x0012D28E File Offset: 0x0012B48E
		public static DbFunctionExpression GeographyMultiPointFromText(DbExpression multiPointWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(multiPointWellKnownText, "multiPointWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyMultiPointFromText", new DbExpression[] { multiPointWellKnownText, coordinateSystemId });
		}

		// Token: 0x0600540A RID: 21514 RVA: 0x0012D2C0 File Offset: 0x0012B4C0
		public static DbFunctionExpression GeographyMultiLineFromText(DbExpression multiLineWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(multiLineWellKnownText, "multiLineWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyMultiLineFromText", new DbExpression[] { multiLineWellKnownText, coordinateSystemId });
		}

		// Token: 0x0600540B RID: 21515 RVA: 0x0012D2F2 File Offset: 0x0012B4F2
		public static DbFunctionExpression GeographyMultiPolygonFromText(DbExpression multiPolygonWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(multiPolygonWellKnownText, "multiPolygonWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyMultiPolygonFromText", new DbExpression[] { multiPolygonWellKnownText, coordinateSystemId });
		}

		// Token: 0x0600540C RID: 21516 RVA: 0x0012D324 File Offset: 0x0012B524
		public static DbFunctionExpression GeographyCollectionFromText(DbExpression geographyCollectionWellKnownText, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(geographyCollectionWellKnownText, "geographyCollectionWellKnownText");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyCollectionFromText", new DbExpression[] { geographyCollectionWellKnownText, coordinateSystemId });
		}

		// Token: 0x0600540D RID: 21517 RVA: 0x0012D356 File Offset: 0x0012B556
		public static DbFunctionExpression GeographyFromBinary(DbExpression wellKnownBinaryValue)
		{
			Check.NotNull<DbExpression>(wellKnownBinaryValue, "wellKnownBinaryValue");
			return EdmFunctions.InvokeCanonicalFunction("GeographyFromBinary", new DbExpression[] { wellKnownBinaryValue });
		}

		// Token: 0x0600540E RID: 21518 RVA: 0x0012D378 File Offset: 0x0012B578
		public static DbFunctionExpression GeographyFromBinary(DbExpression wellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(wellKnownBinaryValue, "wellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyFromBinary", new DbExpression[] { wellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x0600540F RID: 21519 RVA: 0x0012D3AA File Offset: 0x0012B5AA
		public static DbFunctionExpression GeographyPointFromBinary(DbExpression pointWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(pointWellKnownBinaryValue, "pointWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyPointFromBinary", new DbExpression[] { pointWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x06005410 RID: 21520 RVA: 0x0012D3DC File Offset: 0x0012B5DC
		public static DbFunctionExpression GeographyLineFromBinary(DbExpression lineWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(lineWellKnownBinaryValue, "lineWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyLineFromBinary", new DbExpression[] { lineWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x06005411 RID: 21521 RVA: 0x0012D40E File Offset: 0x0012B60E
		public static DbFunctionExpression GeographyPolygonFromBinary(DbExpression polygonWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(polygonWellKnownBinaryValue, "polygonWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyPolygonFromBinary", new DbExpression[] { polygonWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x06005412 RID: 21522 RVA: 0x0012D440 File Offset: 0x0012B640
		public static DbFunctionExpression GeographyMultiPointFromBinary(DbExpression multiPointWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(multiPointWellKnownBinaryValue, "multiPointWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyMultiPointFromBinary", new DbExpression[] { multiPointWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x06005413 RID: 21523 RVA: 0x0012D472 File Offset: 0x0012B672
		public static DbFunctionExpression GeographyMultiLineFromBinary(DbExpression multiLineWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(multiLineWellKnownBinaryValue, "multiLineWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyMultiLineFromBinary", new DbExpression[] { multiLineWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x06005414 RID: 21524 RVA: 0x0012D4A4 File Offset: 0x0012B6A4
		public static DbFunctionExpression GeographyMultiPolygonFromBinary(DbExpression multiPolygonWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(multiPolygonWellKnownBinaryValue, "multiPolygonWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyMultiPolygonFromBinary", new DbExpression[] { multiPolygonWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x06005415 RID: 21525 RVA: 0x0012D4D6 File Offset: 0x0012B6D6
		public static DbFunctionExpression GeographyCollectionFromBinary(DbExpression geographyCollectionWellKnownBinaryValue, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(geographyCollectionWellKnownBinaryValue, "geographyCollectionWellKnownBinaryValue");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyCollectionFromBinary", new DbExpression[] { geographyCollectionWellKnownBinaryValue, coordinateSystemId });
		}

		// Token: 0x06005416 RID: 21526 RVA: 0x0012D508 File Offset: 0x0012B708
		public static DbFunctionExpression GeographyFromGml(DbExpression geographyMarkup)
		{
			Check.NotNull<DbExpression>(geographyMarkup, "geographyMarkup");
			return EdmFunctions.InvokeCanonicalFunction("GeographyFromGml", new DbExpression[] { geographyMarkup });
		}

		// Token: 0x06005417 RID: 21527 RVA: 0x0012D52A File Offset: 0x0012B72A
		public static DbFunctionExpression GeographyFromGml(DbExpression geographyMarkup, DbExpression coordinateSystemId)
		{
			Check.NotNull<DbExpression>(geographyMarkup, "geographyMarkup");
			Check.NotNull<DbExpression>(coordinateSystemId, "coordinateSystemId");
			return EdmFunctions.InvokeCanonicalFunction("GeographyFromGml", new DbExpression[] { geographyMarkup, coordinateSystemId });
		}

		// Token: 0x06005418 RID: 21528 RVA: 0x0012D55C File Offset: 0x0012B75C
		public static DbFunctionExpression CoordinateSystemId(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("CoordinateSystemId", new DbExpression[] { spatialValue });
		}

		// Token: 0x06005419 RID: 21529 RVA: 0x0012D57E File Offset: 0x0012B77E
		public static DbFunctionExpression SpatialTypeName(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("SpatialTypeName", new DbExpression[] { spatialValue });
		}

		// Token: 0x0600541A RID: 21530 RVA: 0x0012D5A0 File Offset: 0x0012B7A0
		public static DbFunctionExpression SpatialDimension(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("SpatialDimension", new DbExpression[] { spatialValue });
		}

		// Token: 0x0600541B RID: 21531 RVA: 0x0012D5C2 File Offset: 0x0012B7C2
		public static DbFunctionExpression SpatialEnvelope(this DbExpression geometryValue)
		{
			Check.NotNull<DbExpression>(geometryValue, "geometryValue");
			return EdmFunctions.InvokeCanonicalFunction("SpatialEnvelope", new DbExpression[] { geometryValue });
		}

		// Token: 0x0600541C RID: 21532 RVA: 0x0012D5E4 File Offset: 0x0012B7E4
		public static DbFunctionExpression AsBinary(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("AsBinary", new DbExpression[] { spatialValue });
		}

		// Token: 0x0600541D RID: 21533 RVA: 0x0012D606 File Offset: 0x0012B806
		public static DbFunctionExpression AsGml(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("AsGml", new DbExpression[] { spatialValue });
		}

		// Token: 0x0600541E RID: 21534 RVA: 0x0012D628 File Offset: 0x0012B828
		public static DbFunctionExpression AsText(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("AsText", new DbExpression[] { spatialValue });
		}

		// Token: 0x0600541F RID: 21535 RVA: 0x0012D64A File Offset: 0x0012B84A
		public static DbFunctionExpression IsEmptySpatial(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("IsEmptySpatial", new DbExpression[] { spatialValue });
		}

		// Token: 0x06005420 RID: 21536 RVA: 0x0012D66C File Offset: 0x0012B86C
		public static DbFunctionExpression IsSimpleGeometry(this DbExpression geometryValue)
		{
			Check.NotNull<DbExpression>(geometryValue, "geometryValue");
			return EdmFunctions.InvokeCanonicalFunction("IsSimpleGeometry", new DbExpression[] { geometryValue });
		}

		// Token: 0x06005421 RID: 21537 RVA: 0x0012D68E File Offset: 0x0012B88E
		public static DbFunctionExpression SpatialBoundary(this DbExpression geometryValue)
		{
			Check.NotNull<DbExpression>(geometryValue, "geometryValue");
			return EdmFunctions.InvokeCanonicalFunction("SpatialBoundary", new DbExpression[] { geometryValue });
		}

		// Token: 0x06005422 RID: 21538 RVA: 0x0012D6B0 File Offset: 0x0012B8B0
		public static DbFunctionExpression IsValidGeometry(this DbExpression geometryValue)
		{
			Check.NotNull<DbExpression>(geometryValue, "geometryValue");
			return EdmFunctions.InvokeCanonicalFunction("IsValidGeometry", new DbExpression[] { geometryValue });
		}

		// Token: 0x06005423 RID: 21539 RVA: 0x0012D6D2 File Offset: 0x0012B8D2
		public static DbFunctionExpression SpatialEquals(this DbExpression spatialValue1, DbExpression spatialValue2)
		{
			Check.NotNull<DbExpression>(spatialValue1, "spatialValue1");
			Check.NotNull<DbExpression>(spatialValue2, "spatialValue2");
			return EdmFunctions.InvokeCanonicalFunction("SpatialEquals", new DbExpression[] { spatialValue1, spatialValue2 });
		}

		// Token: 0x06005424 RID: 21540 RVA: 0x0012D704 File Offset: 0x0012B904
		public static DbFunctionExpression SpatialDisjoint(this DbExpression spatialValue1, DbExpression spatialValue2)
		{
			Check.NotNull<DbExpression>(spatialValue1, "spatialValue1");
			Check.NotNull<DbExpression>(spatialValue2, "spatialValue2");
			return EdmFunctions.InvokeCanonicalFunction("SpatialDisjoint", new DbExpression[] { spatialValue1, spatialValue2 });
		}

		// Token: 0x06005425 RID: 21541 RVA: 0x0012D736 File Offset: 0x0012B936
		public static DbFunctionExpression SpatialIntersects(this DbExpression spatialValue1, DbExpression spatialValue2)
		{
			Check.NotNull<DbExpression>(spatialValue1, "spatialValue1");
			Check.NotNull<DbExpression>(spatialValue2, "spatialValue2");
			return EdmFunctions.InvokeCanonicalFunction("SpatialIntersects", new DbExpression[] { spatialValue1, spatialValue2 });
		}

		// Token: 0x06005426 RID: 21542 RVA: 0x0012D768 File Offset: 0x0012B968
		public static DbFunctionExpression SpatialTouches(this DbExpression geometryValue1, DbExpression geometryValue2)
		{
			Check.NotNull<DbExpression>(geometryValue1, "geometryValue1");
			Check.NotNull<DbExpression>(geometryValue2, "geometryValue2");
			return EdmFunctions.InvokeCanonicalFunction("SpatialTouches", new DbExpression[] { geometryValue1, geometryValue2 });
		}

		// Token: 0x06005427 RID: 21543 RVA: 0x0012D79A File Offset: 0x0012B99A
		public static DbFunctionExpression SpatialCrosses(this DbExpression geometryValue1, DbExpression geometryValue2)
		{
			Check.NotNull<DbExpression>(geometryValue1, "geometryValue1");
			Check.NotNull<DbExpression>(geometryValue2, "geometryValue2");
			return EdmFunctions.InvokeCanonicalFunction("SpatialCrosses", new DbExpression[] { geometryValue1, geometryValue2 });
		}

		// Token: 0x06005428 RID: 21544 RVA: 0x0012D7CC File Offset: 0x0012B9CC
		public static DbFunctionExpression SpatialWithin(this DbExpression geometryValue1, DbExpression geometryValue2)
		{
			Check.NotNull<DbExpression>(geometryValue1, "geometryValue1");
			Check.NotNull<DbExpression>(geometryValue2, "geometryValue2");
			return EdmFunctions.InvokeCanonicalFunction("SpatialWithin", new DbExpression[] { geometryValue1, geometryValue2 });
		}

		// Token: 0x06005429 RID: 21545 RVA: 0x0012D7FE File Offset: 0x0012B9FE
		public static DbFunctionExpression SpatialContains(this DbExpression geometryValue1, DbExpression geometryValue2)
		{
			Check.NotNull<DbExpression>(geometryValue1, "geometryValue1");
			Check.NotNull<DbExpression>(geometryValue2, "geometryValue2");
			return EdmFunctions.InvokeCanonicalFunction("SpatialContains", new DbExpression[] { geometryValue1, geometryValue2 });
		}

		// Token: 0x0600542A RID: 21546 RVA: 0x0012D830 File Offset: 0x0012BA30
		public static DbFunctionExpression SpatialOverlaps(this DbExpression geometryValue1, DbExpression geometryValue2)
		{
			Check.NotNull<DbExpression>(geometryValue1, "geometryValue1");
			Check.NotNull<DbExpression>(geometryValue2, "geometryValue2");
			return EdmFunctions.InvokeCanonicalFunction("SpatialOverlaps", new DbExpression[] { geometryValue1, geometryValue2 });
		}

		// Token: 0x0600542B RID: 21547 RVA: 0x0012D864 File Offset: 0x0012BA64
		public static DbFunctionExpression SpatialRelate(this DbExpression geometryValue1, DbExpression geometryValue2, DbExpression intersectionPatternMatrix)
		{
			Check.NotNull<DbExpression>(geometryValue1, "geometryValue1");
			Check.NotNull<DbExpression>(geometryValue2, "geometryValue2");
			Check.NotNull<DbExpression>(intersectionPatternMatrix, "intersectionPatternMatrix");
			return EdmFunctions.InvokeCanonicalFunction("SpatialRelate", new DbExpression[] { geometryValue1, geometryValue2, intersectionPatternMatrix });
		}

		// Token: 0x0600542C RID: 21548 RVA: 0x0012D8B1 File Offset: 0x0012BAB1
		public static DbFunctionExpression SpatialBuffer(this DbExpression spatialValue, DbExpression distance)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			Check.NotNull<DbExpression>(distance, "distance");
			return EdmFunctions.InvokeCanonicalFunction("SpatialBuffer", new DbExpression[] { spatialValue, distance });
		}

		// Token: 0x0600542D RID: 21549 RVA: 0x0012D8E3 File Offset: 0x0012BAE3
		public static DbFunctionExpression Distance(this DbExpression spatialValue1, DbExpression spatialValue2)
		{
			Check.NotNull<DbExpression>(spatialValue1, "spatialValue1");
			Check.NotNull<DbExpression>(spatialValue2, "spatialValue2");
			return EdmFunctions.InvokeCanonicalFunction("Distance", new DbExpression[] { spatialValue1, spatialValue2 });
		}

		// Token: 0x0600542E RID: 21550 RVA: 0x0012D915 File Offset: 0x0012BB15
		public static DbFunctionExpression SpatialConvexHull(this DbExpression geometryValue)
		{
			Check.NotNull<DbExpression>(geometryValue, "geometryValue");
			return EdmFunctions.InvokeCanonicalFunction("SpatialConvexHull", new DbExpression[] { geometryValue });
		}

		// Token: 0x0600542F RID: 21551 RVA: 0x0012D937 File Offset: 0x0012BB37
		public static DbFunctionExpression SpatialIntersection(this DbExpression spatialValue1, DbExpression spatialValue2)
		{
			Check.NotNull<DbExpression>(spatialValue1, "spatialValue1");
			Check.NotNull<DbExpression>(spatialValue2, "spatialValue2");
			return EdmFunctions.InvokeCanonicalFunction("SpatialIntersection", new DbExpression[] { spatialValue1, spatialValue2 });
		}

		// Token: 0x06005430 RID: 21552 RVA: 0x0012D969 File Offset: 0x0012BB69
		public static DbFunctionExpression SpatialUnion(this DbExpression spatialValue1, DbExpression spatialValue2)
		{
			Check.NotNull<DbExpression>(spatialValue1, "spatialValue1");
			Check.NotNull<DbExpression>(spatialValue2, "spatialValue2");
			return EdmFunctions.InvokeCanonicalFunction("SpatialUnion", new DbExpression[] { spatialValue1, spatialValue2 });
		}

		// Token: 0x06005431 RID: 21553 RVA: 0x0012D99B File Offset: 0x0012BB9B
		public static DbFunctionExpression SpatialDifference(this DbExpression spatialValue1, DbExpression spatialValue2)
		{
			Check.NotNull<DbExpression>(spatialValue1, "spatialValue1");
			Check.NotNull<DbExpression>(spatialValue2, "spatialValue2");
			return EdmFunctions.InvokeCanonicalFunction("SpatialDifference", new DbExpression[] { spatialValue1, spatialValue2 });
		}

		// Token: 0x06005432 RID: 21554 RVA: 0x0012D9CD File Offset: 0x0012BBCD
		public static DbFunctionExpression SpatialSymmetricDifference(this DbExpression spatialValue1, DbExpression spatialValue2)
		{
			Check.NotNull<DbExpression>(spatialValue1, "spatialValue1");
			Check.NotNull<DbExpression>(spatialValue2, "spatialValue2");
			return EdmFunctions.InvokeCanonicalFunction("SpatialSymmetricDifference", new DbExpression[] { spatialValue1, spatialValue2 });
		}

		// Token: 0x06005433 RID: 21555 RVA: 0x0012D9FF File Offset: 0x0012BBFF
		public static DbFunctionExpression SpatialElementCount(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("SpatialElementCount", new DbExpression[] { spatialValue });
		}

		// Token: 0x06005434 RID: 21556 RVA: 0x0012DA21 File Offset: 0x0012BC21
		public static DbFunctionExpression SpatialElementAt(this DbExpression spatialValue, DbExpression indexValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			Check.NotNull<DbExpression>(indexValue, "indexValue");
			return EdmFunctions.InvokeCanonicalFunction("SpatialElementAt", new DbExpression[] { spatialValue, indexValue });
		}

		// Token: 0x06005435 RID: 21557 RVA: 0x0012DA53 File Offset: 0x0012BC53
		public static DbFunctionExpression XCoordinate(this DbExpression geometryValue)
		{
			Check.NotNull<DbExpression>(geometryValue, "geometryValue");
			return EdmFunctions.InvokeCanonicalFunction("XCoordinate", new DbExpression[] { geometryValue });
		}

		// Token: 0x06005436 RID: 21558 RVA: 0x0012DA75 File Offset: 0x0012BC75
		public static DbFunctionExpression YCoordinate(this DbExpression geometryValue)
		{
			Check.NotNull<DbExpression>(geometryValue, "geometryValue");
			return EdmFunctions.InvokeCanonicalFunction("YCoordinate", new DbExpression[] { geometryValue });
		}

		// Token: 0x06005437 RID: 21559 RVA: 0x0012DA97 File Offset: 0x0012BC97
		public static DbFunctionExpression Elevation(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("Elevation", new DbExpression[] { spatialValue });
		}

		// Token: 0x06005438 RID: 21560 RVA: 0x0012DAB9 File Offset: 0x0012BCB9
		public static DbFunctionExpression Measure(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("Measure", new DbExpression[] { spatialValue });
		}

		// Token: 0x06005439 RID: 21561 RVA: 0x0012DADB File Offset: 0x0012BCDB
		public static DbFunctionExpression Latitude(this DbExpression geographyValue)
		{
			Check.NotNull<DbExpression>(geographyValue, "geographyValue");
			return EdmFunctions.InvokeCanonicalFunction("Latitude", new DbExpression[] { geographyValue });
		}

		// Token: 0x0600543A RID: 21562 RVA: 0x0012DAFD File Offset: 0x0012BCFD
		public static DbFunctionExpression Longitude(this DbExpression geographyValue)
		{
			Check.NotNull<DbExpression>(geographyValue, "geographyValue");
			return EdmFunctions.InvokeCanonicalFunction("Longitude", new DbExpression[] { geographyValue });
		}

		// Token: 0x0600543B RID: 21563 RVA: 0x0012DB1F File Offset: 0x0012BD1F
		public static DbFunctionExpression SpatialLength(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("SpatialLength", new DbExpression[] { spatialValue });
		}

		// Token: 0x0600543C RID: 21564 RVA: 0x0012DB41 File Offset: 0x0012BD41
		public static DbFunctionExpression StartPoint(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("StartPoint", new DbExpression[] { spatialValue });
		}

		// Token: 0x0600543D RID: 21565 RVA: 0x0012DB63 File Offset: 0x0012BD63
		public static DbFunctionExpression EndPoint(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("EndPoint", new DbExpression[] { spatialValue });
		}

		// Token: 0x0600543E RID: 21566 RVA: 0x0012DB85 File Offset: 0x0012BD85
		public static DbFunctionExpression IsClosedSpatial(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("IsClosedSpatial", new DbExpression[] { spatialValue });
		}

		// Token: 0x0600543F RID: 21567 RVA: 0x0012DBA7 File Offset: 0x0012BDA7
		public static DbFunctionExpression IsRing(this DbExpression geometryValue)
		{
			Check.NotNull<DbExpression>(geometryValue, "geometryValue");
			return EdmFunctions.InvokeCanonicalFunction("IsRing", new DbExpression[] { geometryValue });
		}

		// Token: 0x06005440 RID: 21568 RVA: 0x0012DBC9 File Offset: 0x0012BDC9
		public static DbFunctionExpression PointCount(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("PointCount", new DbExpression[] { spatialValue });
		}

		// Token: 0x06005441 RID: 21569 RVA: 0x0012DBEB File Offset: 0x0012BDEB
		public static DbFunctionExpression PointAt(this DbExpression spatialValue, DbExpression indexValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			Check.NotNull<DbExpression>(indexValue, "indexValue");
			return EdmFunctions.InvokeCanonicalFunction("PointAt", new DbExpression[] { spatialValue, indexValue });
		}

		// Token: 0x06005442 RID: 21570 RVA: 0x0012DC1D File Offset: 0x0012BE1D
		public static DbFunctionExpression Area(this DbExpression spatialValue)
		{
			Check.NotNull<DbExpression>(spatialValue, "spatialValue");
			return EdmFunctions.InvokeCanonicalFunction("Area", new DbExpression[] { spatialValue });
		}

		// Token: 0x06005443 RID: 21571 RVA: 0x0012DC3F File Offset: 0x0012BE3F
		public static DbFunctionExpression Centroid(this DbExpression geometryValue)
		{
			Check.NotNull<DbExpression>(geometryValue, "geometryValue");
			return EdmFunctions.InvokeCanonicalFunction("Centroid", new DbExpression[] { geometryValue });
		}

		// Token: 0x06005444 RID: 21572 RVA: 0x0012DC61 File Offset: 0x0012BE61
		public static DbFunctionExpression PointOnSurface(this DbExpression geometryValue)
		{
			Check.NotNull<DbExpression>(geometryValue, "geometryValue");
			return EdmFunctions.InvokeCanonicalFunction("PointOnSurface", new DbExpression[] { geometryValue });
		}

		// Token: 0x06005445 RID: 21573 RVA: 0x0012DC83 File Offset: 0x0012BE83
		public static DbFunctionExpression ExteriorRing(this DbExpression geometryValue)
		{
			Check.NotNull<DbExpression>(geometryValue, "geometryValue");
			return EdmFunctions.InvokeCanonicalFunction("ExteriorRing", new DbExpression[] { geometryValue });
		}

		// Token: 0x06005446 RID: 21574 RVA: 0x0012DCA5 File Offset: 0x0012BEA5
		public static DbFunctionExpression InteriorRingCount(this DbExpression geometryValue)
		{
			Check.NotNull<DbExpression>(geometryValue, "geometryValue");
			return EdmFunctions.InvokeCanonicalFunction("InteriorRingCount", new DbExpression[] { geometryValue });
		}

		// Token: 0x06005447 RID: 21575 RVA: 0x0012DCC7 File Offset: 0x0012BEC7
		public static DbFunctionExpression InteriorRingAt(this DbExpression geometryValue, DbExpression indexValue)
		{
			Check.NotNull<DbExpression>(geometryValue, "geometryValue");
			Check.NotNull<DbExpression>(indexValue, "indexValue");
			return EdmFunctions.InvokeCanonicalFunction("InteriorRingAt", new DbExpression[] { geometryValue, indexValue });
		}
	}
}
