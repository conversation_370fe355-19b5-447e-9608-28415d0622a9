﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Query.InternalTrees;

namespace System.Data.Entity.Core.Query.PlanCompiler
{
	// Token: 0x02000378 RID: 888
	internal class VarInfoMap
	{
		// Token: 0x06002AE3 RID: 10979 RVA: 0x0008BE9D File Offset: 0x0008A09D
		internal VarInfoMap()
		{
			this.m_map = new Dictionary<Var, VarInfo>();
		}

		// Token: 0x06002AE4 RID: 10980 RVA: 0x0008BEB0 File Offset: 0x0008A0B0
		internal VarInfo CreateStructuredVarInfo(Var v, RowType newType, List<Var> newVars, List<EdmProperty> newProperties, bool newVarsIncludeNullSentinelVar)
		{
			VarInfo varInfo = new StructuredVarInfo(newType, newVars, newProperties, newVarsIncludeNullSentinelVar);
			this.m_map.Add(v, varInfo);
			return varInfo;
		}

		// Token: 0x06002AE5 RID: 10981 RVA: 0x0008BED7 File Offset: 0x0008A0D7
		internal VarInfo CreateStructuredVarInfo(Var v, RowType newType, List<Var> newVars, List<EdmProperty> newProperties)
		{
			return this.CreateStructuredVarInfo(v, newType, newVars, newProperties, false);
		}

		// Token: 0x06002AE6 RID: 10982 RVA: 0x0008BEE8 File Offset: 0x0008A0E8
		internal VarInfo CreateCollectionVarInfo(Var v, Var newVar)
		{
			VarInfo varInfo = new CollectionVarInfo(newVar);
			this.m_map.Add(v, varInfo);
			return varInfo;
		}

		// Token: 0x06002AE7 RID: 10983 RVA: 0x0008BF0C File Offset: 0x0008A10C
		internal VarInfo CreatePrimitiveTypeVarInfo(Var v, Var newVar)
		{
			PlanCompiler.Assert(TypeSemantics.IsScalarType(v.Type), "The current variable should be of primitive or enum type.");
			PlanCompiler.Assert(TypeSemantics.IsScalarType(newVar.Type), "The new variable should be of primitive or enum type.");
			VarInfo varInfo = new PrimitiveTypeVarInfo(newVar);
			this.m_map.Add(v, varInfo);
			return varInfo;
		}

		// Token: 0x06002AE8 RID: 10984 RVA: 0x0008BF58 File Offset: 0x0008A158
		internal bool TryGetVarInfo(Var v, out VarInfo varInfo)
		{
			return this.m_map.TryGetValue(v, out varInfo);
		}

		// Token: 0x04000ED4 RID: 3796
		private readonly Dictionary<Var, VarInfo> m_map;
	}
}
