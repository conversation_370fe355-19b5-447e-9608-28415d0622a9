﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000029 RID: 41
	public class Sys_Users_DevilTurn_Rank
	{
		// Token: 0x17000140 RID: 320
		// (get) Token: 0x060002A8 RID: 680 RVA: 0x0000370B File Offset: 0x0000190B
		// (set) Token: 0x060002A9 RID: 681 RVA: 0x00003713 File Offset: 0x00001913
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000141 RID: 321
		// (get) Token: 0x060002AA RID: 682 RVA: 0x0000371C File Offset: 0x0000191C
		// (set) Token: 0x060002AB RID: 683 RVA: 0x00003724 File Offset: 0x00001924
		public int UserID { get; set; }

		// Token: 0x17000142 RID: 322
		// (get) Token: 0x060002AC RID: 684 RVA: 0x0000372D File Offset: 0x0000192D
		// (set) Token: 0x060002AD RID: 685 RVA: 0x00003735 File Offset: 0x00001935
		public string NickName { get; set; }

		// Token: 0x17000143 RID: 323
		// (get) Token: 0x060002AE RID: 686 RVA: 0x0000373E File Offset: 0x0000193E
		// (set) Token: 0x060002AF RID: 687 RVA: 0x00003746 File Offset: 0x00001946
		public int Score { get; set; }

		// Token: 0x17000144 RID: 324
		// (get) Token: 0x060002B0 RID: 688 RVA: 0x0000374F File Offset: 0x0000194F
		// (set) Token: 0x060002B1 RID: 689 RVA: 0x00003757 File Offset: 0x00001957
		public int ServerId { get; set; }

		// Token: 0x17000145 RID: 325
		// (get) Token: 0x060002B2 RID: 690 RVA: 0x00003760 File Offset: 0x00001960
		// (set) Token: 0x060002B3 RID: 691 RVA: 0x00003768 File Offset: 0x00001968
		public string ServerName { get; set; }
	}
}
