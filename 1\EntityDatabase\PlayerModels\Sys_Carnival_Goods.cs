﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x0200001E RID: 30
	public class Sys_Carnival_Goods
	{
		// Token: 0x170000E4 RID: 228
		// (get) Token: 0x060001E5 RID: 485 RVA: 0x0000308C File Offset: 0x0000128C
		// (set) Token: 0x060001E6 RID: 486 RVA: 0x00003094 File Offset: 0x00001294
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x170000E5 RID: 229
		// (get) Token: 0x060001E7 RID: 487 RVA: 0x0000309D File Offset: 0x0000129D
		// (set) Token: 0x060001E8 RID: 488 RVA: 0x000030A5 File Offset: 0x000012A5
		public int UserID { get; set; }

		// Token: 0x170000E6 RID: 230
		// (get) Token: 0x060001E9 RID: 489 RVA: 0x000030AE File Offset: 0x000012AE
		// (set) Token: 0x060001EA RID: 490 RVA: 0x000030B6 File Offset: 0x000012B6
		public string NickName { get; set; }

		// Token: 0x170000E7 RID: 231
		// (get) Token: 0x060001EB RID: 491 RVA: 0x000030BF File Offset: 0x000012BF
		// (set) Token: 0x060001EC RID: 492 RVA: 0x000030C7 File Offset: 0x000012C7
		public string AreaName { get; set; }

		// Token: 0x170000E8 RID: 232
		// (get) Token: 0x060001ED RID: 493 RVA: 0x000030D0 File Offset: 0x000012D0
		// (set) Token: 0x060001EE RID: 494 RVA: 0x000030D8 File Offset: 0x000012D8
		public int RewardType { get; set; }

		// Token: 0x170000E9 RID: 233
		// (get) Token: 0x060001EF RID: 495 RVA: 0x000030E1 File Offset: 0x000012E1
		// (set) Token: 0x060001F0 RID: 496 RVA: 0x000030E9 File Offset: 0x000012E9
		public int ItemID { get; set; }

		// Token: 0x170000EA RID: 234
		// (get) Token: 0x060001F1 RID: 497 RVA: 0x000030F2 File Offset: 0x000012F2
		// (set) Token: 0x060001F2 RID: 498 RVA: 0x000030FA File Offset: 0x000012FA
		public int ItemCount { get; set; }
	}
}
