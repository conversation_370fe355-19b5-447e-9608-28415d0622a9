﻿using System;
using System.Collections.Generic;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003B8 RID: 952
	internal class MultiStreamNestOp : NestBaseOp
	{
		// Token: 0x06002DBD RID: 11709 RVA: 0x00091468 File Offset: 0x0008F668
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002DBE RID: 11710 RVA: 0x00091472 File Offset: 0x0008F672
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x06002DBF RID: 11711 RVA: 0x0009147C File Offset: 0x0008F67C
		internal MultiStreamNestOp(List<SortKey> prefixSortKeys, VarVec outputVars, List<CollectionInfo> collectionInfoList)
			: base(OpType.MultiStreamNest, prefixSortKeys, outputVars, collectionInfoList)
		{
		}
	}
}
