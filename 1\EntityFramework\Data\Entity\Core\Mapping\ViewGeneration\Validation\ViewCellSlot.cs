﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Validation
{
	// Token: 0x02000582 RID: 1410
	internal class ViewCellSlot : ProjectedSlot
	{
		// Token: 0x0600444B RID: 17483 RVA: 0x000EF208 File Offset: 0x000ED408
		internal ViewCellSlot(int slotNum, MemberProjectedSlot cSlot, MemberProjectedSlot sSlot)
		{
			this.m_slotNum = slotNum;
			this.m_cSlot = cSlot;
			this.m_sSlot = sSlot;
		}

		// Token: 0x17000D83 RID: 3459
		// (get) Token: 0x0600444C RID: 17484 RVA: 0x000EF225 File Offset: 0x000ED425
		internal MemberProjectedSlot CSlot
		{
			get
			{
				return this.m_cSlot;
			}
		}

		// Token: 0x17000D84 RID: 3460
		// (get) Token: 0x0600444D RID: 17485 RVA: 0x000EF22D File Offset: 0x000ED42D
		internal MemberProjectedSlot SSlot
		{
			get
			{
				return this.m_sSlot;
			}
		}

		// Token: 0x0600444E RID: 17486 RVA: 0x000EF238 File Offset: 0x000ED438
		protected override bool IsEqualTo(ProjectedSlot right)
		{
			ViewCellSlot viewCellSlot = right as ViewCellSlot;
			return viewCellSlot != null && (this.m_slotNum == viewCellSlot.m_slotNum && ProjectedSlot.EqualityComparer.Equals(this.m_cSlot, viewCellSlot.m_cSlot)) && ProjectedSlot.EqualityComparer.Equals(this.m_sSlot, viewCellSlot.m_sSlot);
		}

		// Token: 0x0600444F RID: 17487 RVA: 0x000EF28F File Offset: 0x000ED48F
		protected override int GetHash()
		{
			return ProjectedSlot.EqualityComparer.GetHashCode(this.m_cSlot) ^ ProjectedSlot.EqualityComparer.GetHashCode(this.m_sSlot) ^ this.m_slotNum;
		}

		// Token: 0x06004450 RID: 17488 RVA: 0x000EF2BC File Offset: 0x000ED4BC
		internal static string SlotsToUserString(IEnumerable<ViewCellSlot> slots, bool isFromCside)
		{
			StringBuilder stringBuilder = new StringBuilder();
			bool flag = true;
			foreach (ViewCellSlot viewCellSlot in slots)
			{
				if (!flag)
				{
					stringBuilder.Append(", ");
				}
				stringBuilder.Append(ViewCellSlot.SlotToUserString(viewCellSlot, isFromCside));
				flag = false;
			}
			return stringBuilder.ToString();
		}

		// Token: 0x06004451 RID: 17489 RVA: 0x000EF32C File Offset: 0x000ED52C
		internal static string SlotToUserString(ViewCellSlot slot, bool isFromCside)
		{
			MemberProjectedSlot memberProjectedSlot = (isFromCside ? slot.CSlot : slot.SSlot);
			return StringUtil.FormatInvariant("{0}", new object[] { memberProjectedSlot });
		}

		// Token: 0x06004452 RID: 17490 RVA: 0x000EF35F File Offset: 0x000ED55F
		internal override string GetCqlFieldAlias(MemberPath outputMember)
		{
			return null;
		}

		// Token: 0x06004453 RID: 17491 RVA: 0x000EF362 File Offset: 0x000ED562
		internal override StringBuilder AsEsql(StringBuilder builder, MemberPath outputMember, string blockAlias, int indentLevel)
		{
			return null;
		}

		// Token: 0x06004454 RID: 17492 RVA: 0x000EF365 File Offset: 0x000ED565
		internal override DbExpression AsCqt(DbExpression row, MemberPath outputMember)
		{
			return null;
		}

		// Token: 0x06004455 RID: 17493 RVA: 0x000EF368 File Offset: 0x000ED568
		internal override void ToCompactString(StringBuilder builder)
		{
			builder.Append('<');
			StringUtil.FormatStringBuilder(builder, "{0}", new object[] { this.m_slotNum });
			builder.Append(':');
			this.m_cSlot.ToCompactString(builder);
			builder.Append('-');
			this.m_sSlot.ToCompactString(builder);
			builder.Append('>');
		}

		// Token: 0x0400189C RID: 6300
		private readonly int m_slotNum;

		// Token: 0x0400189D RID: 6301
		private readonly MemberProjectedSlot m_cSlot;

		// Token: 0x0400189E RID: 6302
		private readonly MemberProjectedSlot m_sSlot;
	}
}
