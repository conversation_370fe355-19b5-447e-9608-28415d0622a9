﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x02000492 RID: 1170
	public class ComplexType : StructuralType
	{
		// Token: 0x060039F6 RID: 14838 RVA: 0x000BDF9D File Offset: 0x000BC19D
		internal ComplexType(string name, string namespaceName, DataSpace dataSpace)
			: base(name, namespaceName, dataSpace)
		{
		}

		// Token: 0x060039F7 RID: 14839 RVA: 0x000BDFA8 File Offset: 0x000BC1A8
		internal ComplexType()
		{
		}

		// Token: 0x060039F8 RID: 14840 RVA: 0x000BDFB0 File Offset: 0x000BC1B0
		internal ComplexType(string name)
			: this(name, "Transient", DataSpace.CSpace)
		{
		}

		// Token: 0x17000B15 RID: 2837
		// (get) Token: 0x060039F9 RID: 14841 RVA: 0x000BDFBF File Offset: 0x000BC1BF
		public override BuiltInTypeKind BuiltInTypeKind
		{
			get
			{
				return BuiltInTypeKind.ComplexType;
			}
		}

		// Token: 0x17000B16 RID: 2838
		// (get) Token: 0x060039FA RID: 14842 RVA: 0x000BDFC2 File Offset: 0x000BC1C2
		public virtual ReadOnlyMetadataCollection<EdmProperty> Properties
		{
			get
			{
				return new FilteredReadOnlyMetadataCollection<EdmProperty, EdmMember>(base.Members, new Predicate<EdmMember>(Helper.IsEdmProperty));
			}
		}

		// Token: 0x060039FB RID: 14843 RVA: 0x000BDFDB File Offset: 0x000BC1DB
		internal override void ValidateMemberForAdd(EdmMember member)
		{
		}

		// Token: 0x060039FC RID: 14844 RVA: 0x000BDFE0 File Offset: 0x000BC1E0
		public static ComplexType Create(string name, string namespaceName, DataSpace dataSpace, IEnumerable<EdmMember> members, IEnumerable<MetadataProperty> metadataProperties)
		{
			Check.NotEmpty(name, "name");
			Check.NotEmpty(namespaceName, "namespaceName");
			Check.NotNull<IEnumerable<EdmMember>>(members, "members");
			ComplexType complexType = new ComplexType(name, namespaceName, dataSpace);
			foreach (EdmMember edmMember in members)
			{
				complexType.AddMember(edmMember);
			}
			if (metadataProperties != null)
			{
				complexType.AddMetadataProperties(metadataProperties);
			}
			complexType.SetReadOnly();
			return complexType;
		}
	}
}
