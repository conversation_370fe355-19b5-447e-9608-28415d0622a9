﻿using System;
using System.Data.Entity;
using System.Data.Entity.ModelConfiguration.Conventions;

namespace EntityDatabase.ServerModels
{
	// Token: 0x0200000A RID: 10
	public class ServerData : DbContext
	{
		// Token: 0x17000018 RID: 24
		// (get) Token: 0x06000037 RID: 55 RVA: 0x00002226 File Offset: 0x00000426
		// (set) Token: 0x06000038 RID: 56 RVA: 0x0000222E File Offset: 0x0000042E
		public virtual DbSet<TS_Carnival_Items> TS_Carnival_Items { get; set; }

		// Token: 0x17000019 RID: 25
		// (get) Token: 0x06000039 RID: 57 RVA: 0x00002237 File Offset: 0x00000437
		// (set) Token: 0x0600003A RID: 58 RVA: 0x0000223F File Offset: 0x0000043F
		public virtual DbSet<TS_Upgrade> TS_Upgrade { get; set; }

		// Token: 0x1700001A RID: 26
		// (get) Token: 0x0600003B RID: 59 RVA: 0x00002248 File Offset: 0x00000448
		// (set) Token: 0x0600003C RID: 60 RVA: 0x00002250 File Offset: 0x00000450
		public virtual DbSet<TS_FirstPayShopTemp> TS_FirstPayShopTemp { get; set; }

		// Token: 0x1700001B RID: 27
		// (get) Token: 0x0600003D RID: 61 RVA: 0x00002259 File Offset: 0x00000459
		// (set) Token: 0x0600003E RID: 62 RVA: 0x00002261 File Offset: 0x00000461
		public virtual DbSet<TS_FirstPayCode> TS_FirstPayCode { get; set; }

		// Token: 0x1700001C RID: 28
		// (get) Token: 0x0600003F RID: 63 RVA: 0x0000226A File Offset: 0x0000046A
		// (set) Token: 0x06000040 RID: 64 RVA: 0x00002272 File Offset: 0x00000472
		public virtual DbSet<TS_FirstCopy> TS_FirstCopy { get; set; }

		// Token: 0x1700001D RID: 29
		// (get) Token: 0x06000041 RID: 65 RVA: 0x0000227B File Offset: 0x0000047B
		// (set) Token: 0x06000042 RID: 66 RVA: 0x00002283 File Offset: 0x00000483
		public virtual DbSet<TS_UltimateLuxuryCarnival> TS_UltimateLuxuryCarnival { get; set; }

		// Token: 0x1700001E RID: 30
		// (get) Token: 0x06000043 RID: 67 RVA: 0x0000228C File Offset: 0x0000048C
		// (set) Token: 0x06000044 RID: 68 RVA: 0x00002294 File Offset: 0x00000494
		public virtual DbSet<TS_UltimateLuxuryLimitBuy> TS_UltimateLuxuryLimitBuy { get; set; }

		// Token: 0x1700001F RID: 31
		// (get) Token: 0x06000045 RID: 69 RVA: 0x0000229D File Offset: 0x0000049D
		// (set) Token: 0x06000046 RID: 70 RVA: 0x000022A5 File Offset: 0x000004A5
		public virtual DbSet<TS_UltimateLuxuryRecharge> TS_UltimateLuxuryRecharge { get; set; }

		// Token: 0x17000020 RID: 32
		// (get) Token: 0x06000047 RID: 71 RVA: 0x000022AE File Offset: 0x000004AE
		// (set) Token: 0x06000048 RID: 72 RVA: 0x000022B6 File Offset: 0x000004B6
		public virtual DbSet<TS_UltimateLuxuryTurntable> TS_UltimateLuxuryTurntable { get; set; }

		// Token: 0x17000021 RID: 33
		// (get) Token: 0x06000049 RID: 73 RVA: 0x000022BF File Offset: 0x000004BF
		// (set) Token: 0x0600004A RID: 74 RVA: 0x000022C7 File Offset: 0x000004C7
		public virtual DbSet<TS_WarPass_QuestTemplate> TS_WarPass_QuestTemplate { get; set; }

		// Token: 0x17000022 RID: 34
		// (get) Token: 0x0600004B RID: 75 RVA: 0x000022D0 File Offset: 0x000004D0
		// (set) Token: 0x0600004C RID: 76 RVA: 0x000022D8 File Offset: 0x000004D8
		public virtual DbSet<TS_CaveLootLotteryPool> TS_CaveLootLotteryPool { get; set; }

		// Token: 0x17000023 RID: 35
		// (get) Token: 0x0600004D RID: 77 RVA: 0x000022E1 File Offset: 0x000004E1
		// (set) Token: 0x0600004E RID: 78 RVA: 0x000022E9 File Offset: 0x000004E9
		public virtual DbSet<TS_CaveLootProgressAward> TS_CaveLootProgressAward { get; set; }

		// Token: 0x17000024 RID: 36
		// (get) Token: 0x0600004F RID: 79 RVA: 0x000022F2 File Offset: 0x000004F2
		// (set) Token: 0x06000050 RID: 80 RVA: 0x000022FA File Offset: 0x000004FA
		public virtual DbSet<DevilTurnOpenBox> DevilTurnOpenBox { get; set; }

		// Token: 0x17000025 RID: 37
		// (get) Token: 0x06000051 RID: 81 RVA: 0x00002303 File Offset: 0x00000503
		// (set) Token: 0x06000052 RID: 82 RVA: 0x0000230B File Offset: 0x0000050B
		public virtual DbSet<DevilturnRankAward> DevilturnRankAward { get; set; }

		// Token: 0x17000026 RID: 38
		// (get) Token: 0x06000053 RID: 83 RVA: 0x00002314 File Offset: 0x00000514
		// (set) Token: 0x06000054 RID: 84 RVA: 0x0000231C File Offset: 0x0000051C
		public virtual DbSet<DevilTreasItemList> DevilTreasItemList { get; set; }

		// Token: 0x17000027 RID: 39
		// (get) Token: 0x06000055 RID: 85 RVA: 0x00002325 File Offset: 0x00000525
		// (set) Token: 0x06000056 RID: 86 RVA: 0x0000232D File Offset: 0x0000052D
		public virtual DbSet<DevilTreasPointsList> DevilTreasPointsList { get; set; }

		// Token: 0x17000028 RID: 40
		// (get) Token: 0x06000057 RID: 87 RVA: 0x00002336 File Offset: 0x00000536
		// (set) Token: 0x06000058 RID: 88 RVA: 0x0000233E File Offset: 0x0000053E
		public virtual DbSet<DevilTreasRankRewardList> DevilTreasRankRewardList { get; set; }

		// Token: 0x17000029 RID: 41
		// (get) Token: 0x06000059 RID: 89 RVA: 0x00002347 File Offset: 0x00000547
		// (set) Token: 0x0600005A RID: 90 RVA: 0x0000234F File Offset: 0x0000054F
		public virtual DbSet<DevilTreasSarahToBoxList> DevilTreasSarahToBoxList { get; set; }

		// Token: 0x1700002A RID: 42
		// (get) Token: 0x0600005B RID: 91 RVA: 0x00002358 File Offset: 0x00000558
		// (set) Token: 0x0600005C RID: 92 RVA: 0x00002360 File Offset: 0x00000560
		public virtual DbSet<TS_Relic_AdvanceTemplate> RelicAdvanceTemplate { get; set; }

		// Token: 0x1700002B RID: 43
		// (get) Token: 0x0600005D RID: 93 RVA: 0x00002369 File Offset: 0x00000569
		// (set) Token: 0x0600005E RID: 94 RVA: 0x00002371 File Offset: 0x00000571
		public virtual DbSet<TS_Relic_AdvanceValue> RelicAdvanceValue { get; set; }

		// Token: 0x1700002C RID: 44
		// (get) Token: 0x0600005F RID: 95 RVA: 0x0000237A File Offset: 0x0000057A
		// (set) Token: 0x06000060 RID: 96 RVA: 0x00002382 File Offset: 0x00000582
		public virtual DbSet<TS_Relic_DegreeTemplate> RelicDegreeTemplate { get; set; }

		// Token: 0x1700002D RID: 45
		// (get) Token: 0x06000061 RID: 97 RVA: 0x0000238B File Offset: 0x0000058B
		// (set) Token: 0x06000062 RID: 98 RVA: 0x00002393 File Offset: 0x00000593
		public virtual DbSet<TS_Relic_ItemTemplate> RelicItemTemplate { get; set; }

		// Token: 0x1700002E RID: 46
		// (get) Token: 0x06000063 RID: 99 RVA: 0x0000239C File Offset: 0x0000059C
		// (set) Token: 0x06000064 RID: 100 RVA: 0x000023A4 File Offset: 0x000005A4
		public virtual DbSet<TS_Relic_UpgradeTemplate> RelicUpgradeTemplate { get; set; }

		// Token: 0x06000065 RID: 101 RVA: 0x000023AD File Offset: 0x000005AD
		public ServerData()
			: base("name = ServerData")
		{
		}

		// Token: 0x06000066 RID: 102 RVA: 0x000023BC File Offset: 0x000005BC
		protected override void OnModelCreating(DbModelBuilder modelBuilder)
		{
			base.OnModelCreating(modelBuilder);
			modelBuilder.Conventions.Remove<PluralizingTableNameConvention>();
		}
	}
}
