﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Common;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Common.QueryCache;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Core.SchemaObjectModel;
using System.Data.Entity.Infrastructure.DependencyResolution;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Text;
using System.Xml;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004FD RID: 1277
	public class StoreItemCollection : ItemCollection
	{
		// Token: 0x06003F25 RID: 16165 RVA: 0x000D1045 File Offset: 0x000CF245
		internal StoreItemCollection()
			: base(DataSpace.SSpace)
		{
		}

		// Token: 0x06003F26 RID: 16166 RVA: 0x000D1064 File Offset: 0x000CF264
		internal StoreItemCollection(DbProviderFactory factory, DbProviderManifest manifest, string providerInvariantName, string providerManifestToken)
			: base(DataSpace.SSpace)
		{
			this._providerFactory = factory;
			this._providerManifest = manifest;
			this._providerInvariantName = providerInvariantName;
			this._providerManifestToken = providerManifestToken;
			this._cachedCTypeFunction = new Memoizer<EdmFunction, EdmFunction>(new Func<EdmFunction, EdmFunction>(StoreItemCollection.ConvertFunctionSignatureToCType), null);
			this.LoadProviderManifest(this._providerManifest);
		}

		// Token: 0x06003F27 RID: 16167 RVA: 0x000D10D0 File Offset: 0x000CF2D0
		private StoreItemCollection(IEnumerable<XmlReader> xmlReaders, ReadOnlyCollection<string> filePaths, IDbDependencyResolver resolver, out IList<EdmSchemaError> errors)
			: base(DataSpace.SSpace)
		{
			errors = this.Init(xmlReaders, filePaths, false, resolver, out this._providerManifest, out this._providerFactory, out this._providerInvariantName, out this._providerManifestToken, out this._cachedCTypeFunction);
		}

		// Token: 0x06003F28 RID: 16168 RVA: 0x000D1128 File Offset: 0x000CF328
		internal StoreItemCollection(IEnumerable<XmlReader> xmlReaders, IEnumerable<string> filePaths)
			: base(DataSpace.SSpace)
		{
			EntityUtil.CheckArgumentEmpty<XmlReader>(ref xmlReaders, new Func<string, string>(Strings.StoreItemCollectionMustHaveOneArtifact), "xmlReader");
			this.Init(xmlReaders, filePaths, true, null, out this._providerManifest, out this._providerFactory, out this._providerInvariantName, out this._providerManifestToken, out this._cachedCTypeFunction);
		}

		// Token: 0x06003F29 RID: 16169 RVA: 0x000D1194 File Offset: 0x000CF394
		public StoreItemCollection(IEnumerable<XmlReader> xmlReaders)
			: base(DataSpace.SSpace)
		{
			Check.NotNull<IEnumerable<XmlReader>>(xmlReaders, "xmlReaders");
			EntityUtil.CheckArgumentEmpty<XmlReader>(ref xmlReaders, new Func<string, string>(Strings.StoreItemCollectionMustHaveOneArtifact), "xmlReader");
			MetadataArtifactLoader metadataArtifactLoader = MetadataArtifactLoader.CreateCompositeFromXmlReaders(xmlReaders);
			this.Init(metadataArtifactLoader.GetReaders(), metadataArtifactLoader.GetPaths(), true, null, out this._providerManifest, out this._providerFactory, out this._providerInvariantName, out this._providerManifestToken, out this._cachedCTypeFunction);
		}

		// Token: 0x06003F2A RID: 16170 RVA: 0x000D1220 File Offset: 0x000CF420
		public StoreItemCollection(EdmModel model)
			: base(DataSpace.SSpace)
		{
			Check.NotNull<EdmModel>(model, "model");
			this._providerManifest = model.ProviderManifest;
			this._providerInvariantName = model.ProviderInfo.ProviderInvariantName;
			this._providerFactory = DbConfiguration.DependencyResolver.GetService(this._providerInvariantName);
			this._providerManifestToken = model.ProviderInfo.ProviderManifestToken;
			this._cachedCTypeFunction = new Memoizer<EdmFunction, EdmFunction>(new Func<EdmFunction, EdmFunction>(StoreItemCollection.ConvertFunctionSignatureToCType), null);
			this.LoadProviderManifest(this._providerManifest);
			this._schemaVersion = model.SchemaVersion;
			model.Validate();
			foreach (GlobalItem globalItem in model.GlobalItems)
			{
				globalItem.SetReadOnly();
				base.AddInternal(globalItem);
			}
		}

		// Token: 0x06003F2B RID: 16171 RVA: 0x000D1318 File Offset: 0x000CF518
		public StoreItemCollection(params string[] filePaths)
			: base(DataSpace.SSpace)
		{
			Check.NotNull<string[]>(filePaths, "filePaths");
			IEnumerable<string> enumerable = filePaths;
			EntityUtil.CheckArgumentEmpty<string>(ref enumerable, new Func<string, string>(Strings.StoreItemCollectionMustHaveOneArtifact), "filePaths");
			List<XmlReader> list = null;
			try
			{
				MetadataArtifactLoader metadataArtifactLoader = MetadataArtifactLoader.CreateCompositeFromFilePaths(enumerable, ".ssdl");
				list = metadataArtifactLoader.CreateReaders(DataSpace.SSpace);
				IEnumerable<XmlReader> enumerable2 = list.AsEnumerable<XmlReader>();
				EntityUtil.CheckArgumentEmpty<XmlReader>(ref enumerable2, new Func<string, string>(Strings.StoreItemCollectionMustHaveOneArtifact), "filePaths");
				this.Init(list, metadataArtifactLoader.GetPaths(DataSpace.SSpace), true, null, out this._providerManifest, out this._providerFactory, out this._providerInvariantName, out this._providerManifestToken, out this._cachedCTypeFunction);
			}
			finally
			{
				if (list != null)
				{
					Helper.DisposeXmlReaders(list);
				}
			}
		}

		// Token: 0x06003F2C RID: 16172 RVA: 0x000D13EC File Offset: 0x000CF5EC
		private IList<EdmSchemaError> Init(IEnumerable<XmlReader> xmlReaders, IEnumerable<string> filePaths, bool throwOnError, IDbDependencyResolver resolver, out DbProviderManifest providerManifest, out DbProviderFactory providerFactory, out string providerInvariantName, out string providerManifestToken, out Memoizer<EdmFunction, EdmFunction> cachedCTypeFunction)
		{
			cachedCTypeFunction = new Memoizer<EdmFunction, EdmFunction>(new Func<EdmFunction, EdmFunction>(StoreItemCollection.ConvertFunctionSignatureToCType), null);
			StoreItemCollection.Loader loader = new StoreItemCollection.Loader(xmlReaders, filePaths, throwOnError, resolver);
			providerFactory = loader.ProviderFactory;
			providerManifest = loader.ProviderManifest;
			providerManifestToken = loader.ProviderManifestToken;
			providerInvariantName = loader.ProviderInvariantName;
			if (!loader.HasNonWarningErrors)
			{
				this.LoadProviderManifest(loader.ProviderManifest);
				List<EdmSchemaError> list = EdmItemCollection.LoadItems(this._providerManifest, loader.Schemas, this);
				foreach (EdmSchemaError edmSchemaError in list)
				{
					loader.Errors.Add(edmSchemaError);
				}
				if (throwOnError && list.Count != 0)
				{
					loader.ThrowOnNonWarningErrors();
				}
			}
			return loader.Errors;
		}

		// Token: 0x17000C68 RID: 3176
		// (get) Token: 0x06003F2D RID: 16173 RVA: 0x000D14C4 File Offset: 0x000CF6C4
		internal QueryCacheManager QueryCacheManager
		{
			get
			{
				return this._queryCacheManager;
			}
		}

		// Token: 0x17000C69 RID: 3177
		// (get) Token: 0x06003F2E RID: 16174 RVA: 0x000D14CC File Offset: 0x000CF6CC
		public virtual DbProviderFactory ProviderFactory
		{
			get
			{
				return this._providerFactory;
			}
		}

		// Token: 0x17000C6A RID: 3178
		// (get) Token: 0x06003F2F RID: 16175 RVA: 0x000D14D4 File Offset: 0x000CF6D4
		public virtual DbProviderManifest ProviderManifest
		{
			get
			{
				return this._providerManifest;
			}
		}

		// Token: 0x17000C6B RID: 3179
		// (get) Token: 0x06003F30 RID: 16176 RVA: 0x000D14DC File Offset: 0x000CF6DC
		public virtual string ProviderManifestToken
		{
			get
			{
				return this._providerManifestToken;
			}
		}

		// Token: 0x17000C6C RID: 3180
		// (get) Token: 0x06003F31 RID: 16177 RVA: 0x000D14E4 File Offset: 0x000CF6E4
		public virtual string ProviderInvariantName
		{
			get
			{
				return this._providerInvariantName;
			}
		}

		// Token: 0x17000C6D RID: 3181
		// (get) Token: 0x06003F32 RID: 16178 RVA: 0x000D14EC File Offset: 0x000CF6EC
		// (set) Token: 0x06003F33 RID: 16179 RVA: 0x000D14F4 File Offset: 0x000CF6F4
		public double StoreSchemaVersion
		{
			get
			{
				return this._schemaVersion;
			}
			internal set
			{
				this._schemaVersion = value;
			}
		}

		// Token: 0x06003F34 RID: 16180 RVA: 0x000D14FD File Offset: 0x000CF6FD
		public virtual ReadOnlyCollection<PrimitiveType> GetPrimitiveTypes()
		{
			return this._primitiveTypeMaps.GetTypes();
		}

		// Token: 0x06003F35 RID: 16181 RVA: 0x000D150C File Offset: 0x000CF70C
		internal override PrimitiveType GetMappedPrimitiveType(PrimitiveTypeKind primitiveTypeKind)
		{
			PrimitiveType primitiveType = null;
			this._primitiveTypeMaps.TryGetType(primitiveTypeKind, null, out primitiveType);
			return primitiveType;
		}

		// Token: 0x06003F36 RID: 16182 RVA: 0x000D152C File Offset: 0x000CF72C
		private void LoadProviderManifest(DbProviderManifest storeManifest)
		{
			foreach (PrimitiveType primitiveType in storeManifest.GetStoreTypes())
			{
				base.AddInternal(primitiveType);
				this._primitiveTypeMaps.Add(primitiveType);
			}
			foreach (EdmFunction edmFunction in storeManifest.GetStoreFunctions())
			{
				base.AddInternal(edmFunction);
			}
		}

		// Token: 0x06003F37 RID: 16183 RVA: 0x000D15C4 File Offset: 0x000CF7C4
		internal ReadOnlyCollection<EdmFunction> GetCTypeFunctions(string functionName, bool ignoreCase)
		{
			ReadOnlyCollection<EdmFunction> readOnlyCollection;
			if (!base.FunctionLookUpTable.TryGetValue(functionName, out readOnlyCollection))
			{
				return Helper.EmptyEdmFunctionReadOnlyCollection;
			}
			readOnlyCollection = this.ConvertToCTypeFunctions(readOnlyCollection);
			if (ignoreCase)
			{
				return readOnlyCollection;
			}
			return ItemCollection.GetCaseSensitiveFunctions(readOnlyCollection, functionName);
		}

		// Token: 0x06003F38 RID: 16184 RVA: 0x000D15FC File Offset: 0x000CF7FC
		private ReadOnlyCollection<EdmFunction> ConvertToCTypeFunctions(ReadOnlyCollection<EdmFunction> functionOverloads)
		{
			List<EdmFunction> list = new List<EdmFunction>();
			foreach (EdmFunction edmFunction in functionOverloads)
			{
				list.Add(this.ConvertToCTypeFunction(edmFunction));
			}
			return new ReadOnlyCollection<EdmFunction>(list);
		}

		// Token: 0x06003F39 RID: 16185 RVA: 0x000D1658 File Offset: 0x000CF858
		internal EdmFunction ConvertToCTypeFunction(EdmFunction sTypeFunction)
		{
			return this._cachedCTypeFunction.Evaluate(sTypeFunction);
		}

		// Token: 0x06003F3A RID: 16186 RVA: 0x000D1668 File Offset: 0x000CF868
		internal static EdmFunction ConvertFunctionSignatureToCType(EdmFunction sTypeFunction)
		{
			if (sTypeFunction.IsFromProviderManifest)
			{
				return sTypeFunction;
			}
			FunctionParameter functionParameter = null;
			if (sTypeFunction.ReturnParameter != null)
			{
				TypeUsage typeUsage = MetadataHelper.ConvertStoreTypeUsageToEdmTypeUsage(sTypeFunction.ReturnParameter.TypeUsage);
				functionParameter = new FunctionParameter(sTypeFunction.ReturnParameter.Name, typeUsage, sTypeFunction.ReturnParameter.GetParameterMode());
			}
			List<FunctionParameter> list = new List<FunctionParameter>();
			if (sTypeFunction.Parameters.Count > 0)
			{
				foreach (FunctionParameter functionParameter2 in sTypeFunction.Parameters)
				{
					TypeUsage typeUsage2 = MetadataHelper.ConvertStoreTypeUsageToEdmTypeUsage(functionParameter2.TypeUsage);
					FunctionParameter functionParameter3 = new FunctionParameter(functionParameter2.Name, typeUsage2, functionParameter2.GetParameterMode());
					list.Add(functionParameter3);
				}
			}
			FunctionParameter[] array;
			if (functionParameter != null)
			{
				(array = new FunctionParameter[1])[0] = functionParameter;
			}
			else
			{
				array = new FunctionParameter[0];
			}
			FunctionParameter[] array2 = array;
			EdmFunction edmFunction = new EdmFunction(sTypeFunction.Name, sTypeFunction.NamespaceName, DataSpace.CSpace, new EdmFunctionPayload
			{
				Schema = sTypeFunction.Schema,
				StoreFunctionName = sTypeFunction.StoreFunctionNameAttribute,
				CommandText = sTypeFunction.CommandTextAttribute,
				IsAggregate = new bool?(sTypeFunction.AggregateAttribute),
				IsBuiltIn = new bool?(sTypeFunction.BuiltInAttribute),
				IsNiladic = new bool?(sTypeFunction.NiladicFunctionAttribute),
				IsComposable = new bool?(sTypeFunction.IsComposableAttribute),
				IsFromProviderManifest = new bool?(sTypeFunction.IsFromProviderManifest),
				IsCachedStoreFunction = new bool?(true),
				IsFunctionImport = new bool?(sTypeFunction.IsFunctionImport),
				ReturnParameters = array2,
				Parameters = list.ToArray(),
				ParameterTypeSemantics = new ParameterTypeSemantics?(sTypeFunction.ParameterTypeSemanticsAttribute)
			});
			edmFunction.SetReadOnly();
			return edmFunction;
		}

		// Token: 0x06003F3B RID: 16187 RVA: 0x000D1828 File Offset: 0x000CFA28
		public static StoreItemCollection Create(IEnumerable<XmlReader> xmlReaders, ReadOnlyCollection<string> filePaths, IDbDependencyResolver resolver, out IList<EdmSchemaError> errors)
		{
			Check.NotNull<IEnumerable<XmlReader>>(xmlReaders, "xmlReaders");
			EntityUtil.CheckArgumentContainsNull<XmlReader>(ref xmlReaders, "xmlReaders");
			EntityUtil.CheckArgumentEmpty<XmlReader>(ref xmlReaders, new Func<string, string>(Strings.StoreItemCollectionMustHaveOneArtifact), "xmlReaders");
			StoreItemCollection storeItemCollection = new StoreItemCollection(xmlReaders, filePaths, resolver, out errors);
			if (errors == null || errors.Count <= 0)
			{
				return storeItemCollection;
			}
			return null;
		}

		// Token: 0x0400158B RID: 5515
		private double _schemaVersion;

		// Token: 0x0400158C RID: 5516
		private readonly CacheForPrimitiveTypes _primitiveTypeMaps = new CacheForPrimitiveTypes();

		// Token: 0x0400158D RID: 5517
		private readonly Memoizer<EdmFunction, EdmFunction> _cachedCTypeFunction;

		// Token: 0x0400158E RID: 5518
		private readonly DbProviderManifest _providerManifest;

		// Token: 0x0400158F RID: 5519
		private readonly string _providerInvariantName;

		// Token: 0x04001590 RID: 5520
		private readonly string _providerManifestToken;

		// Token: 0x04001591 RID: 5521
		private readonly DbProviderFactory _providerFactory;

		// Token: 0x04001592 RID: 5522
		private readonly QueryCacheManager _queryCacheManager = QueryCacheManager.Create();

		// Token: 0x02000B10 RID: 2832
		private class Loader
		{
			// Token: 0x06006470 RID: 25712 RVA: 0x0015A68C File Offset: 0x0015888C
			public Loader(IEnumerable<XmlReader> xmlReaders, IEnumerable<string> sourceFilePaths, bool throwOnError, IDbDependencyResolver resolver)
			{
				this._throwOnError = throwOnError;
				IDbDependencyResolver dbDependencyResolver2;
				if (resolver != null)
				{
					IDbDependencyResolver dbDependencyResolver = new CompositeResolver<IDbDependencyResolver, IDbDependencyResolver>(resolver, DbConfiguration.DependencyResolver);
					dbDependencyResolver2 = dbDependencyResolver;
				}
				else
				{
					dbDependencyResolver2 = DbConfiguration.DependencyResolver;
				}
				this._resolver = dbDependencyResolver2;
				this.LoadItems(xmlReaders, sourceFilePaths);
			}

			// Token: 0x170010ED RID: 4333
			// (get) Token: 0x06006471 RID: 25713 RVA: 0x0015A6CD File Offset: 0x001588CD
			public IList<EdmSchemaError> Errors
			{
				get
				{
					return this._errors;
				}
			}

			// Token: 0x170010EE RID: 4334
			// (get) Token: 0x06006472 RID: 25714 RVA: 0x0015A6D5 File Offset: 0x001588D5
			public IList<Schema> Schemas
			{
				get
				{
					return this._schemas;
				}
			}

			// Token: 0x170010EF RID: 4335
			// (get) Token: 0x06006473 RID: 25715 RVA: 0x0015A6DD File Offset: 0x001588DD
			public DbProviderManifest ProviderManifest
			{
				get
				{
					return this._providerManifest;
				}
			}

			// Token: 0x170010F0 RID: 4336
			// (get) Token: 0x06006474 RID: 25716 RVA: 0x0015A6E5 File Offset: 0x001588E5
			public DbProviderFactory ProviderFactory
			{
				get
				{
					return this._providerFactory;
				}
			}

			// Token: 0x170010F1 RID: 4337
			// (get) Token: 0x06006475 RID: 25717 RVA: 0x0015A6ED File Offset: 0x001588ED
			public string ProviderManifestToken
			{
				get
				{
					return this._providerManifestToken;
				}
			}

			// Token: 0x170010F2 RID: 4338
			// (get) Token: 0x06006476 RID: 25718 RVA: 0x0015A6F5 File Offset: 0x001588F5
			public string ProviderInvariantName
			{
				get
				{
					return this._provider;
				}
			}

			// Token: 0x170010F3 RID: 4339
			// (get) Token: 0x06006477 RID: 25719 RVA: 0x0015A6FD File Offset: 0x001588FD
			public bool HasNonWarningErrors
			{
				get
				{
					return !MetadataHelper.CheckIfAllErrorsAreWarnings(this._errors);
				}
			}

			// Token: 0x06006478 RID: 25720 RVA: 0x0015A710 File Offset: 0x00158910
			private void LoadItems(IEnumerable<XmlReader> xmlReaders, IEnumerable<string> sourceFilePaths)
			{
				this._errors = SchemaManager.ParseAndValidate(xmlReaders, sourceFilePaths, SchemaDataModelOption.ProviderDataModel, new AttributeValueNotification(this.OnProviderNotification), new AttributeValueNotification(this.OnProviderManifestTokenNotification), new ProviderManifestNeeded(this.OnProviderManifestNeeded), out this._schemas);
				if (this._throwOnError)
				{
					this.ThrowOnNonWarningErrors();
				}
			}

			// Token: 0x06006479 RID: 25721 RVA: 0x0015A763 File Offset: 0x00158963
			internal void ThrowOnNonWarningErrors()
			{
				if (!MetadataHelper.CheckIfAllErrorsAreWarnings(this._errors))
				{
					throw EntityUtil.InvalidSchemaEncountered(Helper.CombineErrorMessage(this._errors));
				}
			}

			// Token: 0x0600647A RID: 25722 RVA: 0x0015A784 File Offset: 0x00158984
			private void OnProviderNotification(string provider, Action<string, ErrorCode, EdmSchemaErrorSeverity> addError)
			{
				string provider2 = this._provider;
				if (this._provider == null)
				{
					this._provider = provider;
					this.InitializeProviderManifest(addError);
					return;
				}
				if (this._provider == provider)
				{
					return;
				}
				addError(Strings.AllArtifactsMustTargetSameProvider_InvariantName(provider2, this._provider), ErrorCode.InconsistentProvider, EdmSchemaErrorSeverity.Error);
			}

			// Token: 0x0600647B RID: 25723 RVA: 0x0015A7D8 File Offset: 0x001589D8
			private void InitializeProviderManifest(Action<string, ErrorCode, EdmSchemaErrorSeverity> addError)
			{
				if (this._providerManifest == null && this._providerManifestToken != null && this._provider != null)
				{
					DbProviderFactory dbProviderFactory = null;
					try
					{
						dbProviderFactory = DbConfiguration.DependencyResolver.GetService(this._provider);
					}
					catch (ArgumentException ex)
					{
						addError(ex.Message, ErrorCode.InvalidProvider, EdmSchemaErrorSeverity.Error);
						return;
					}
					try
					{
						DbProviderServices service = this._resolver.GetService(this._provider);
						this._providerManifest = service.GetProviderManifest(this._providerManifestToken);
						this._providerFactory = dbProviderFactory;
						if (this._providerManifest is EdmProviderManifest)
						{
							if (this._throwOnError)
							{
								throw new NotSupportedException(Strings.OnlyStoreConnectionsSupported);
							}
							addError(Strings.OnlyStoreConnectionsSupported, ErrorCode.InvalidProvider, EdmSchemaErrorSeverity.Error);
						}
					}
					catch (ProviderIncompatibleException ex2)
					{
						if (this._throwOnError)
						{
							throw;
						}
						StoreItemCollection.Loader.AddProviderIncompatibleError(ex2, addError);
					}
				}
			}

			// Token: 0x0600647C RID: 25724 RVA: 0x0015A8C0 File Offset: 0x00158AC0
			private void OnProviderManifestTokenNotification(string token, Action<string, ErrorCode, EdmSchemaErrorSeverity> addError)
			{
				if (this._providerManifestToken == null)
				{
					this._providerManifestToken = token;
					this.InitializeProviderManifest(addError);
					return;
				}
				if (this._providerManifestToken != token)
				{
					addError(Strings.AllArtifactsMustTargetSameProvider_ManifestToken(token, this._providerManifestToken), ErrorCode.ProviderManifestTokenMismatch, EdmSchemaErrorSeverity.Error);
				}
			}

			// Token: 0x0600647D RID: 25725 RVA: 0x0015A8FF File Offset: 0x00158AFF
			private DbProviderManifest OnProviderManifestNeeded(Action<string, ErrorCode, EdmSchemaErrorSeverity> addError)
			{
				if (this._providerManifest == null)
				{
					addError(Strings.ProviderManifestTokenNotFound, ErrorCode.ProviderManifestTokenNotFound, EdmSchemaErrorSeverity.Error);
				}
				return this._providerManifest;
			}

			// Token: 0x0600647E RID: 25726 RVA: 0x0015A920 File Offset: 0x00158B20
			private static void AddProviderIncompatibleError(ProviderIncompatibleException provEx, Action<string, ErrorCode, EdmSchemaErrorSeverity> addError)
			{
				StringBuilder stringBuilder = new StringBuilder(provEx.Message);
				if (provEx.InnerException != null && !string.IsNullOrEmpty(provEx.InnerException.Message))
				{
					stringBuilder.AppendFormat(" {0}", provEx.InnerException.Message);
				}
				addError(stringBuilder.ToString(), ErrorCode.FailedToRetrieveProviderManifest, EdmSchemaErrorSeverity.Error);
			}

			// Token: 0x04002CA8 RID: 11432
			private string _provider;

			// Token: 0x04002CA9 RID: 11433
			private string _providerManifestToken;

			// Token: 0x04002CAA RID: 11434
			private DbProviderManifest _providerManifest;

			// Token: 0x04002CAB RID: 11435
			private DbProviderFactory _providerFactory;

			// Token: 0x04002CAC RID: 11436
			private IList<EdmSchemaError> _errors;

			// Token: 0x04002CAD RID: 11437
			private IList<Schema> _schemas;

			// Token: 0x04002CAE RID: 11438
			private readonly bool _throwOnError;

			// Token: 0x04002CAF RID: 11439
			private readonly IDbDependencyResolver _resolver;
		}
	}
}
