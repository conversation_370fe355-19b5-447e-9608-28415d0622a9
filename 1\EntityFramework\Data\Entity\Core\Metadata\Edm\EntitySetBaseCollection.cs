﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004BB RID: 1211
	internal sealed class EntitySetBaseCollection : MetadataCollection<EntitySetBase>
	{
		// Token: 0x06003BFC RID: 15356 RVA: 0x000C5FFC File Offset: 0x000C41FC
		internal EntitySetBaseCollection(EntityContainer entityContainer)
			: this(entityContainer, null)
		{
		}

		// Token: 0x06003BFD RID: 15357 RVA: 0x000C6006 File Offset: 0x000C4206
		internal EntitySetBaseCollection(EntityContainer entityContainer, IEnumerable<EntitySetBase> items)
			: base(items)
		{
			Check.NotNull<EntityContainer>(entityContainer, "entityContainer");
			this._entityContainer = entityContainer;
		}

		// Token: 0x17000BB2 RID: 2994
		public override EntitySetBase this[int index]
		{
			get
			{
				return base[index];
			}
			set
			{
				throw new InvalidOperationException(Strings.OperationOnReadOnlyCollection);
			}
		}

		// Token: 0x17000BB3 RID: 2995
		public override EntitySetBase this[string identity]
		{
			get
			{
				return base[identity];
			}
			set
			{
				throw new InvalidOperationException(Strings.OperationOnReadOnlyCollection);
			}
		}

		// Token: 0x06003C02 RID: 15362 RVA: 0x000C604C File Offset: 0x000C424C
		public override void Add(EntitySetBase item)
		{
			Check.NotNull<EntitySetBase>(item, "item");
			EntitySetBaseCollection.ThrowIfItHasEntityContainer(item, "item");
			base.Add(item);
			item.ChangeEntityContainerWithoutCollectionFixup(this._entityContainer);
		}

		// Token: 0x06003C03 RID: 15363 RVA: 0x000C6078 File Offset: 0x000C4278
		private static void ThrowIfItHasEntityContainer(EntitySetBase entitySet, string argumentName)
		{
			Check.NotNull<EntitySetBase>(entitySet, argumentName);
			if (entitySet.EntityContainer != null)
			{
				throw new ArgumentException(Strings.EntitySetInAnotherContainer, argumentName);
			}
		}

		// Token: 0x040014A7 RID: 5287
		private readonly EntityContainer _entityContainer;
	}
}
