﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Globalization;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005A3 RID: 1443
	internal class ErrorLog : InternalBase
	{
		// Token: 0x06004613 RID: 17939 RVA: 0x000F62F3 File Offset: 0x000F44F3
		internal ErrorLog()
		{
			this.m_log = new List<ErrorLog.Record>();
		}

		// Token: 0x17000DD7 RID: 3543
		// (get) Token: 0x06004614 RID: 17940 RVA: 0x000F6306 File Offset: 0x000F4506
		internal int Count
		{
			get
			{
				return this.m_log.Count;
			}
		}

		// Token: 0x17000DD8 RID: 3544
		// (get) Token: 0x06004615 RID: 17941 RVA: 0x000F6313 File Offset: 0x000F4513
		internal IEnumerable<EdmSchemaError> Errors
		{
			get
			{
				foreach (ErrorLog.Record record in this.m_log)
				{
					yield return record.Error;
				}
				List<ErrorLog.Record>.Enumerator enumerator = default(List<ErrorLog.Record>.Enumerator);
				yield break;
				yield break;
			}
		}

		// Token: 0x06004616 RID: 17942 RVA: 0x000F6323 File Offset: 0x000F4523
		internal void AddEntry(ErrorLog.Record record)
		{
			this.m_log.Add(record);
		}

		// Token: 0x06004617 RID: 17943 RVA: 0x000F6334 File Offset: 0x000F4534
		internal void Merge(ErrorLog log)
		{
			foreach (ErrorLog.Record record in log.m_log)
			{
				this.m_log.Add(record);
			}
		}

		// Token: 0x06004618 RID: 17944 RVA: 0x000F638C File Offset: 0x000F458C
		internal void PrintTrace()
		{
			StringBuilder stringBuilder = new StringBuilder();
			this.ToCompactString(stringBuilder);
			Helpers.StringTraceLine(stringBuilder.ToString());
		}

		// Token: 0x06004619 RID: 17945 RVA: 0x000F63B4 File Offset: 0x000F45B4
		internal override void ToCompactString(StringBuilder builder)
		{
			foreach (ErrorLog.Record record in this.m_log)
			{
				record.ToCompactString(builder);
			}
		}

		// Token: 0x0600461A RID: 17946 RVA: 0x000F6408 File Offset: 0x000F4608
		internal string ToUserString()
		{
			StringBuilder stringBuilder = new StringBuilder();
			foreach (ErrorLog.Record record in this.m_log)
			{
				string text = record.ToUserString();
				stringBuilder.AppendLine(text);
			}
			return stringBuilder.ToString();
		}

		// Token: 0x0400190C RID: 6412
		private readonly List<ErrorLog.Record> m_log;

		// Token: 0x02000BCC RID: 3020
		internal class Record : InternalBase
		{
			// Token: 0x0600681B RID: 26651 RVA: 0x00162234 File Offset: 0x00160434
			internal Record(ViewGenErrorCode errorCode, string message, IEnumerable<LeftCellWrapper> wrappers, string debugMessage)
			{
				IEnumerable<Cell> inputCellsForWrappers = LeftCellWrapper.GetInputCellsForWrappers(wrappers);
				this.Init(errorCode, message, inputCellsForWrappers, debugMessage);
			}

			// Token: 0x0600681C RID: 26652 RVA: 0x00162259 File Offset: 0x00160459
			internal Record(ViewGenErrorCode errorCode, string message, Cell sourceCell, string debugMessage)
			{
				this.Init(errorCode, message, new Cell[] { sourceCell }, debugMessage);
			}

			// Token: 0x0600681D RID: 26653 RVA: 0x00162275 File Offset: 0x00160475
			internal Record(ViewGenErrorCode errorCode, string message, IEnumerable<Cell> sourceCells, string debugMessage)
			{
				this.Init(errorCode, message, sourceCells, debugMessage);
			}

			// Token: 0x0600681E RID: 26654 RVA: 0x00162288 File Offset: 0x00160488
			internal Record(EdmSchemaError error)
			{
				this.m_debugMessage = error.ToString();
				this.m_mappingError = error;
			}

			// Token: 0x0600681F RID: 26655 RVA: 0x001622A4 File Offset: 0x001604A4
			private void Init(ViewGenErrorCode errorCode, string message, IEnumerable<Cell> sourceCells, string debugMessage)
			{
				this.m_sourceCells = new List<Cell>(sourceCells);
				CellLabel cellLabel = this.m_sourceCells[0].CellLabel;
				string sourceLocation = cellLabel.SourceLocation;
				int startLineNumber = cellLabel.StartLineNumber;
				int startLinePosition = cellLabel.StartLinePosition;
				string text = ErrorLog.Record.InternalToString(message, debugMessage, this.m_sourceCells, errorCode, false);
				this.m_debugMessage = ErrorLog.Record.InternalToString(message, debugMessage, this.m_sourceCells, errorCode, true);
				this.m_mappingError = new EdmSchemaError(text, (int)errorCode, EdmSchemaErrorSeverity.Error, sourceLocation, startLineNumber, startLinePosition);
			}

			// Token: 0x1700112C RID: 4396
			// (get) Token: 0x06006820 RID: 26656 RVA: 0x0016231A File Offset: 0x0016051A
			internal EdmSchemaError Error
			{
				get
				{
					return this.m_mappingError;
				}
			}

			// Token: 0x06006821 RID: 26657 RVA: 0x00162322 File Offset: 0x00160522
			internal override void ToCompactString(StringBuilder builder)
			{
				builder.Append(this.m_debugMessage);
			}

			// Token: 0x06006822 RID: 26658 RVA: 0x00162334 File Offset: 0x00160534
			private static void GetUserLinesFromCells(IEnumerable<Cell> sourceCells, StringBuilder lineBuilder, bool isInvariant)
			{
				IEnumerable<Cell> enumerable = sourceCells.OrderBy((Cell cell) => cell.CellLabel.StartLineNumber, Comparer<int>.Default);
				bool flag = true;
				foreach (Cell cell2 in enumerable)
				{
					if (!flag)
					{
						lineBuilder.Append(isInvariant ? EntityRes.GetString("ViewGen_CommaBlank") : ", ");
					}
					flag = false;
					lineBuilder.AppendFormat(CultureInfo.InvariantCulture, "{0}", new object[] { cell2.CellLabel.StartLineNumber });
				}
			}

			// Token: 0x06006823 RID: 26659 RVA: 0x001623EC File Offset: 0x001605EC
			private static string InternalToString(string message, string debugMessage, List<Cell> sourceCells, ViewGenErrorCode errorCode, bool isInvariant)
			{
				StringBuilder stringBuilder = new StringBuilder();
				if (isInvariant)
				{
					stringBuilder.AppendLine(debugMessage);
					stringBuilder.Append(isInvariant ? "ERROR" : Strings.ViewGen_Error);
					StringUtil.FormatStringBuilder(stringBuilder, " ({0}): ", new object[] { (int)errorCode });
				}
				StringBuilder stringBuilder2 = new StringBuilder();
				ErrorLog.Record.GetUserLinesFromCells(sourceCells, stringBuilder2, isInvariant);
				if (isInvariant)
				{
					if (sourceCells.Count > 1)
					{
						StringUtil.FormatStringBuilder(stringBuilder, "Problem in Mapping Fragments starting at lines {0}: ", new object[] { stringBuilder2.ToString() });
					}
					else
					{
						StringUtil.FormatStringBuilder(stringBuilder, "Problem in Mapping Fragment starting at line {0}: ", new object[] { stringBuilder2.ToString() });
					}
				}
				else if (sourceCells.Count > 1)
				{
					stringBuilder.Append(Strings.ViewGen_ErrorLog2(stringBuilder2.ToString()));
				}
				else
				{
					stringBuilder.Append(Strings.ViewGen_ErrorLog(stringBuilder2.ToString()));
				}
				stringBuilder.AppendLine(message);
				return stringBuilder.ToString();
			}

			// Token: 0x06006824 RID: 26660 RVA: 0x001624D0 File Offset: 0x001606D0
			internal string ToUserString()
			{
				return this.m_mappingError.ToString();
			}

			// Token: 0x04002EC4 RID: 11972
			private EdmSchemaError m_mappingError;

			// Token: 0x04002EC5 RID: 11973
			private List<Cell> m_sourceCells;

			// Token: 0x04002EC6 RID: 11974
			private string m_debugMessage;
		}
	}
}
