﻿using System;
using System.Collections.Generic;

namespace System.Data.Entity.Core.Common.Utils
{
	// Token: 0x020005FF RID: 1535
	internal class TrailingSpaceComparer : IEqualityComparer<object>
	{
		// Token: 0x06004B43 RID: 19267 RVA: 0x001096A2 File Offset: 0x001078A2
		private TrailingSpaceComparer()
		{
		}

		// Token: 0x06004B44 RID: 19268 RVA: 0x001096AC File Offset: 0x001078AC
		bool IEqualityComparer<object>.Equals(object x, object y)
		{
			string text = x as string;
			if (text != null)
			{
				string text2 = y as string;
				if (text2 != null)
				{
					return TrailingSpaceStringComparer.Instance.Equals(text, text2);
				}
			}
			return TrailingSpaceComparer._template.Equals(x, y);
		}

		// Token: 0x06004B45 RID: 19269 RVA: 0x001096E8 File Offset: 0x001078E8
		int IEqualityComparer<object>.GetHashCode(object obj)
		{
			string text = obj as string;
			if (text != null)
			{
				return TrailingSpaceStringComparer.Instance.GetHashCode(text);
			}
			return TrailingSpaceComparer._template.GetHashCode(obj);
		}

		// Token: 0x04001A52 RID: 6738
		internal static readonly TrailingSpaceComparer Instance = new TrailingSpaceComparer();

		// Token: 0x04001A53 RID: 6739
		private static readonly IEqualityComparer<object> _template = EqualityComparer<object>.Default;
	}
}
