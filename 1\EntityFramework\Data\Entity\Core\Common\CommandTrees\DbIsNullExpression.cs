﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006C7 RID: 1735
	public class DbIsNullExpression : DbUnaryExpression
	{
		// Token: 0x06005116 RID: 20758 RVA: 0x00121B96 File Offset: 0x0011FD96
		internal DbIsNullExpression()
		{
		}

		// Token: 0x06005117 RID: 20759 RVA: 0x00121B9E File Offset: 0x0011FD9E
		internal DbIsNullExpression(TypeUsage booleanResultType, DbExpression arg)
			: base(DbExpressionKind.IsNull, booleanResultType, arg)
		{
		}

		// Token: 0x06005118 RID: 20760 RVA: 0x00121BAA File Offset: 0x0011FDAA
		public override void Accept(DbExpressionVisitor visitor)
		{
			Check.NotNull<DbExpressionVisitor>(visitor, "visitor");
			visitor.Visit(this);
		}

		// Token: 0x06005119 RID: 20761 RVA: 0x00121BBF File Offset: 0x0011FDBF
		public override TResultType Accept<TResultType>(DbExpressionVisitor<TResultType> visitor)
		{
			Check.NotNull<DbExpressionVisitor<TResultType>>(visitor, "visitor");
			return visitor.Visit(this);
		}
	}
}
