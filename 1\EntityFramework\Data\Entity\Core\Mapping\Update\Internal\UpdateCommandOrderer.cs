﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Resources;
using System.Linq;

namespace System.Data.Entity.Core.Mapping.Update.Internal
{
	// Token: 0x020005D6 RID: 1494
	internal class UpdateCommandOrderer : Graph<UpdateCommand>
	{
		// Token: 0x06004815 RID: 18453 RVA: 0x000FEA40 File Offset: 0x000FCC40
		internal UpdateCommandOrderer(IEnumerable<UpdateCommand> commands, UpdateTranslator translator)
			: base(EqualityComparer<UpdateCommand>.Default)
		{
			this._translator = translator;
			this._keyComparer = new UpdateCommandOrderer.ForeignKeyValueComparer(this._translator.KeyComparer);
			HashSet<EntitySet> hashSet = new HashSet<EntitySet>();
			HashSet<EntityContainer> hashSet2 = new HashSet<EntityContainer>();
			foreach (UpdateCommand updateCommand in commands)
			{
				if (updateCommand.Table != null)
				{
					hashSet.Add(updateCommand.Table);
					hashSet2.Add(updateCommand.Table.EntityContainer);
				}
				base.AddVertex(updateCommand);
				if (updateCommand.Kind == UpdateCommandKind.Function)
				{
					this._hasFunctionCommands = true;
				}
			}
			UpdateCommandOrderer.InitializeForeignKeyMaps(hashSet2, hashSet, out this._sourceMap, out this._targetMap);
			this.AddServerGenDependencies();
			this.AddForeignKeyDependencies();
			if (this._hasFunctionCommands)
			{
				this.AddModelDependencies();
			}
		}

		// Token: 0x06004816 RID: 18454 RVA: 0x000FEB20 File Offset: 0x000FCD20
		private static void InitializeForeignKeyMaps(HashSet<EntityContainer> containers, HashSet<EntitySet> tables, out KeyToListMap<EntitySetBase, ReferentialConstraint> sourceMap, out KeyToListMap<EntitySetBase, ReferentialConstraint> targetMap)
		{
			sourceMap = new KeyToListMap<EntitySetBase, ReferentialConstraint>(EqualityComparer<EntitySetBase>.Default);
			targetMap = new KeyToListMap<EntitySetBase, ReferentialConstraint>(EqualityComparer<EntitySetBase>.Default);
			foreach (EntityContainer entityContainer in containers)
			{
				foreach (EntitySetBase entitySetBase in entityContainer.BaseEntitySets)
				{
					AssociationSet associationSet = entitySetBase as AssociationSet;
					if (associationSet != null)
					{
						AssociationSetEnd associationSetEnd = null;
						AssociationSetEnd associationSetEnd2 = null;
						ReadOnlyMetadataCollection<AssociationSetEnd> associationSetEnds = associationSet.AssociationSetEnds;
						if (2 == associationSetEnds.Count)
						{
							AssociationType elementType = associationSet.ElementType;
							bool flag = false;
							ReferentialConstraint referentialConstraint = null;
							foreach (ReferentialConstraint referentialConstraint2 in elementType.ReferentialConstraints)
							{
								if (!flag)
								{
									flag = true;
								}
								associationSetEnd = associationSet.AssociationSetEnds[referentialConstraint2.ToRole.Name];
								associationSetEnd2 = associationSet.AssociationSetEnds[referentialConstraint2.FromRole.Name];
								referentialConstraint = referentialConstraint2;
							}
							if (associationSetEnd2 != null && associationSetEnd != null && tables.Contains(associationSetEnd2.EntitySet) && tables.Contains(associationSetEnd.EntitySet))
							{
								sourceMap.Add(associationSetEnd.EntitySet, referentialConstraint);
								targetMap.Add(associationSetEnd2.EntitySet, referentialConstraint);
							}
						}
					}
				}
			}
		}

		// Token: 0x06004817 RID: 18455 RVA: 0x000FECD4 File Offset: 0x000FCED4
		private void AddServerGenDependencies()
		{
			Dictionary<int, UpdateCommand> dictionary = new Dictionary<int, UpdateCommand>();
			foreach (UpdateCommand updateCommand in base.Vertices)
			{
				foreach (int num in updateCommand.OutputIdentifiers)
				{
					try
					{
						dictionary.Add(num, updateCommand);
					}
					catch (ArgumentException ex)
					{
						throw new UpdateException(Strings.Update_AmbiguousServerGenIdentifier, ex, updateCommand.GetStateEntries(this._translator).Cast<ObjectStateEntry>().Distinct<ObjectStateEntry>());
					}
				}
			}
			foreach (UpdateCommand updateCommand2 in base.Vertices)
			{
				foreach (int num2 in updateCommand2.InputIdentifiers)
				{
					UpdateCommand updateCommand3;
					if (dictionary.TryGetValue(num2, out updateCommand3))
					{
						base.AddEdge(updateCommand3, updateCommand2);
					}
				}
			}
		}

		// Token: 0x06004818 RID: 18456 RVA: 0x000FEE18 File Offset: 0x000FD018
		private void AddForeignKeyDependencies()
		{
			KeyToListMap<UpdateCommandOrderer.ForeignKeyValue, UpdateCommand> keyToListMap = this.DetermineForeignKeyPredecessors();
			this.AddForeignKeyEdges(keyToListMap);
		}

		// Token: 0x06004819 RID: 18457 RVA: 0x000FEE34 File Offset: 0x000FD034
		private void AddForeignKeyEdges(KeyToListMap<UpdateCommandOrderer.ForeignKeyValue, UpdateCommand> predecessors)
		{
			foreach (DynamicUpdateCommand dynamicUpdateCommand in base.Vertices.OfType<DynamicUpdateCommand>())
			{
				if (dynamicUpdateCommand.Operator == ModificationOperator.Update || ModificationOperator.Insert == dynamicUpdateCommand.Operator)
				{
					foreach (ReferentialConstraint referentialConstraint in this._sourceMap.EnumerateValues(dynamicUpdateCommand.Table))
					{
						UpdateCommandOrderer.ForeignKeyValue foreignKeyValue;
						UpdateCommandOrderer.ForeignKeyValue foreignKeyValue2;
						if (UpdateCommandOrderer.ForeignKeyValue.TryCreateSourceKey(referentialConstraint, dynamicUpdateCommand.CurrentValues, true, out foreignKeyValue) && (dynamicUpdateCommand.Operator != ModificationOperator.Update || !UpdateCommandOrderer.ForeignKeyValue.TryCreateSourceKey(referentialConstraint, dynamicUpdateCommand.OriginalValues, true, out foreignKeyValue2) || !this._keyComparer.Equals(foreignKeyValue2, foreignKeyValue)))
						{
							foreach (UpdateCommand updateCommand in predecessors.EnumerateValues(foreignKeyValue))
							{
								if (updateCommand != dynamicUpdateCommand)
								{
									base.AddEdge(updateCommand, dynamicUpdateCommand);
								}
							}
						}
					}
				}
				if (dynamicUpdateCommand.Operator == ModificationOperator.Update || ModificationOperator.Delete == dynamicUpdateCommand.Operator)
				{
					foreach (ReferentialConstraint referentialConstraint2 in this._targetMap.EnumerateValues(dynamicUpdateCommand.Table))
					{
						UpdateCommandOrderer.ForeignKeyValue foreignKeyValue3;
						UpdateCommandOrderer.ForeignKeyValue foreignKeyValue4;
						if (UpdateCommandOrderer.ForeignKeyValue.TryCreateTargetKey(referentialConstraint2, dynamicUpdateCommand.OriginalValues, false, out foreignKeyValue3) && (dynamicUpdateCommand.Operator != ModificationOperator.Update || !UpdateCommandOrderer.ForeignKeyValue.TryCreateTargetKey(referentialConstraint2, dynamicUpdateCommand.CurrentValues, false, out foreignKeyValue4) || !this._keyComparer.Equals(foreignKeyValue4, foreignKeyValue3)))
						{
							foreach (UpdateCommand updateCommand2 in predecessors.EnumerateValues(foreignKeyValue3))
							{
								if (updateCommand2 != dynamicUpdateCommand)
								{
									base.AddEdge(updateCommand2, dynamicUpdateCommand);
								}
							}
						}
					}
				}
			}
		}

		// Token: 0x0600481A RID: 18458 RVA: 0x000FF084 File Offset: 0x000FD284
		private KeyToListMap<UpdateCommandOrderer.ForeignKeyValue, UpdateCommand> DetermineForeignKeyPredecessors()
		{
			KeyToListMap<UpdateCommandOrderer.ForeignKeyValue, UpdateCommand> keyToListMap = new KeyToListMap<UpdateCommandOrderer.ForeignKeyValue, UpdateCommand>(this._keyComparer);
			foreach (DynamicUpdateCommand dynamicUpdateCommand in base.Vertices.OfType<DynamicUpdateCommand>())
			{
				if (dynamicUpdateCommand.Operator == ModificationOperator.Update || ModificationOperator.Insert == dynamicUpdateCommand.Operator)
				{
					foreach (ReferentialConstraint referentialConstraint in this._targetMap.EnumerateValues(dynamicUpdateCommand.Table))
					{
						UpdateCommandOrderer.ForeignKeyValue foreignKeyValue;
						UpdateCommandOrderer.ForeignKeyValue foreignKeyValue2;
						if (UpdateCommandOrderer.ForeignKeyValue.TryCreateTargetKey(referentialConstraint, dynamicUpdateCommand.CurrentValues, true, out foreignKeyValue) && (dynamicUpdateCommand.Operator != ModificationOperator.Update || !UpdateCommandOrderer.ForeignKeyValue.TryCreateTargetKey(referentialConstraint, dynamicUpdateCommand.OriginalValues, true, out foreignKeyValue2) || !this._keyComparer.Equals(foreignKeyValue2, foreignKeyValue)))
						{
							keyToListMap.Add(foreignKeyValue, dynamicUpdateCommand);
						}
					}
				}
				if (dynamicUpdateCommand.Operator == ModificationOperator.Update || ModificationOperator.Delete == dynamicUpdateCommand.Operator)
				{
					foreach (ReferentialConstraint referentialConstraint2 in this._sourceMap.EnumerateValues(dynamicUpdateCommand.Table))
					{
						UpdateCommandOrderer.ForeignKeyValue foreignKeyValue3;
						UpdateCommandOrderer.ForeignKeyValue foreignKeyValue4;
						if (UpdateCommandOrderer.ForeignKeyValue.TryCreateSourceKey(referentialConstraint2, dynamicUpdateCommand.OriginalValues, false, out foreignKeyValue3) && (dynamicUpdateCommand.Operator != ModificationOperator.Update || !UpdateCommandOrderer.ForeignKeyValue.TryCreateSourceKey(referentialConstraint2, dynamicUpdateCommand.CurrentValues, false, out foreignKeyValue4) || !this._keyComparer.Equals(foreignKeyValue4, foreignKeyValue3)))
						{
							keyToListMap.Add(foreignKeyValue3, dynamicUpdateCommand);
						}
					}
				}
			}
			return keyToListMap;
		}

		// Token: 0x0600481B RID: 18459 RVA: 0x000FF238 File Offset: 0x000FD438
		private void AddModelDependencies()
		{
			KeyToListMap<EntityKey, UpdateCommand> keyToListMap = new KeyToListMap<EntityKey, UpdateCommand>(EqualityComparer<EntityKey>.Default);
			KeyToListMap<EntityKey, UpdateCommand> keyToListMap2 = new KeyToListMap<EntityKey, UpdateCommand>(EqualityComparer<EntityKey>.Default);
			KeyToListMap<EntityKey, UpdateCommand> keyToListMap3 = new KeyToListMap<EntityKey, UpdateCommand>(EqualityComparer<EntityKey>.Default);
			KeyToListMap<EntityKey, UpdateCommand> keyToListMap4 = new KeyToListMap<EntityKey, UpdateCommand>(EqualityComparer<EntityKey>.Default);
			foreach (UpdateCommand updateCommand in base.Vertices)
			{
				updateCommand.GetRequiredAndProducedEntities(this._translator, keyToListMap, keyToListMap2, keyToListMap3, keyToListMap4);
			}
			this.AddModelDependencies(keyToListMap, keyToListMap3);
			this.AddModelDependencies(keyToListMap4, keyToListMap2);
		}

		// Token: 0x0600481C RID: 18460 RVA: 0x000FF2D0 File Offset: 0x000FD4D0
		private void AddModelDependencies(KeyToListMap<EntityKey, UpdateCommand> producedMap, KeyToListMap<EntityKey, UpdateCommand> requiredMap)
		{
			foreach (KeyValuePair<EntityKey, List<UpdateCommand>> keyValuePair in requiredMap.KeyValuePairs)
			{
				EntityKey key = keyValuePair.Key;
				List<UpdateCommand> value = keyValuePair.Value;
				foreach (UpdateCommand updateCommand in producedMap.EnumerateValues(key))
				{
					foreach (UpdateCommand updateCommand2 in value)
					{
						if (updateCommand != updateCommand2 && (updateCommand.Kind == UpdateCommandKind.Function || updateCommand2.Kind == UpdateCommandKind.Function))
						{
							base.AddEdge(updateCommand, updateCommand2);
						}
					}
				}
			}
		}

		// Token: 0x0400199F RID: 6559
		private readonly UpdateCommandOrderer.ForeignKeyValueComparer _keyComparer;

		// Token: 0x040019A0 RID: 6560
		private readonly KeyToListMap<EntitySetBase, ReferentialConstraint> _sourceMap;

		// Token: 0x040019A1 RID: 6561
		private readonly KeyToListMap<EntitySetBase, ReferentialConstraint> _targetMap;

		// Token: 0x040019A2 RID: 6562
		private readonly bool _hasFunctionCommands;

		// Token: 0x040019A3 RID: 6563
		private readonly UpdateTranslator _translator;

		// Token: 0x02000C0F RID: 3087
		private struct ForeignKeyValue
		{
			// Token: 0x06006971 RID: 26993 RVA: 0x001678C8 File Offset: 0x00165AC8
			private ForeignKeyValue(ReferentialConstraint metadata, PropagatorResult record, bool isTarget, bool isInsert)
			{
				this.Metadata = metadata;
				IList<EdmProperty> list = (isTarget ? metadata.FromProperties : metadata.ToProperties);
				PropagatorResult[] array = new PropagatorResult[list.Count];
				bool flag = false;
				for (int i = 0; i < array.Length; i++)
				{
					array[i] = record.GetMemberValue(list[i]);
					if (array[i].IsNull)
					{
						flag = true;
						break;
					}
				}
				if (flag)
				{
					this.Key = null;
				}
				else
				{
					this.Key = new CompositeKey(array);
				}
				this.IsInsert = isInsert;
			}

			// Token: 0x06006972 RID: 26994 RVA: 0x00167948 File Offset: 0x00165B48
			internal static bool TryCreateTargetKey(ReferentialConstraint metadata, PropagatorResult record, bool isInsert, out UpdateCommandOrderer.ForeignKeyValue key)
			{
				key = new UpdateCommandOrderer.ForeignKeyValue(metadata, record, true, isInsert);
				return key.Key != null;
			}

			// Token: 0x06006973 RID: 26995 RVA: 0x00167964 File Offset: 0x00165B64
			internal static bool TryCreateSourceKey(ReferentialConstraint metadata, PropagatorResult record, bool isInsert, out UpdateCommandOrderer.ForeignKeyValue key)
			{
				key = new UpdateCommandOrderer.ForeignKeyValue(metadata, record, false, isInsert);
				return key.Key != null;
			}

			// Token: 0x04002FBC RID: 12220
			internal readonly ReferentialConstraint Metadata;

			// Token: 0x04002FBD RID: 12221
			internal readonly CompositeKey Key;

			// Token: 0x04002FBE RID: 12222
			internal readonly bool IsInsert;
		}

		// Token: 0x02000C10 RID: 3088
		private class ForeignKeyValueComparer : IEqualityComparer<UpdateCommandOrderer.ForeignKeyValue>
		{
			// Token: 0x06006974 RID: 26996 RVA: 0x00167980 File Offset: 0x00165B80
			internal ForeignKeyValueComparer(IEqualityComparer<CompositeKey> baseComparer)
			{
				this._baseComparer = baseComparer;
			}

			// Token: 0x06006975 RID: 26997 RVA: 0x0016798F File Offset: 0x00165B8F
			public bool Equals(UpdateCommandOrderer.ForeignKeyValue x, UpdateCommandOrderer.ForeignKeyValue y)
			{
				return x.IsInsert == y.IsInsert && x.Metadata == y.Metadata && this._baseComparer.Equals(x.Key, y.Key);
			}

			// Token: 0x06006976 RID: 26998 RVA: 0x001679C6 File Offset: 0x00165BC6
			public int GetHashCode(UpdateCommandOrderer.ForeignKeyValue obj)
			{
				return this._baseComparer.GetHashCode(obj.Key);
			}

			// Token: 0x04002FBF RID: 12223
			private readonly IEqualityComparer<CompositeKey> _baseComparer;
		}
	}
}
