﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Objects.Internal;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Common.Internal.Materialization
{
	// Token: 0x0200063B RID: 1595
	internal class Coordinator<T> : Coordinator
	{
		// Token: 0x17000EE2 RID: 3810
		// (get) Token: 0x06004CD7 RID: 19671 RVA: 0x0010E6DB File Offset: 0x0010C8DB
		internal virtual T Current
		{
			get
			{
				return this._current;
			}
		}

		// Token: 0x06004CD8 RID: 19672 RVA: 0x0010E6E4 File Offset: 0x0010C8E4
		internal Coordinator(CoordinatorFactory<T> coordinatorFactory, Coordinator parent, Coordinator next)
			: base(coordinatorFactory, parent, next)
		{
			this.TypedCoordinatorFactory = coordinatorFactory;
			Coordinator coordinator = null;
			foreach (CoordinatorFactory coordinatorFactory2 in coordinatorFactory.NestedCoordinators.Reverse<CoordinatorFactory>())
			{
				base.Child = coordinatorFactory2.CreateCoordinator(this, coordinator);
				coordinator = base.Child;
			}
			this.IsUsingElementCollection = !base.IsRoot && typeof(T) != typeof(RecordState);
		}

		// Token: 0x06004CD9 RID: 19673 RVA: 0x0010E780 File Offset: 0x0010C980
		internal override void ResetCollection(Shaper shaper)
		{
			if (this._handleClose != null)
			{
				this._handleClose(shaper, this._wrappedElements);
				this._handleClose = null;
			}
			base.IsEntered = false;
			if (this.IsUsingElementCollection)
			{
				this._elements = this.TypedCoordinatorFactory.InitializeCollection(shaper);
				this._wrappedElements = new List<IEntityWrapper>();
			}
			if (base.Child != null)
			{
				base.Child.ResetCollection(shaper);
			}
			if (this.Next != null)
			{
				this.Next.ResetCollection(shaper);
			}
		}

		// Token: 0x06004CDA RID: 19674 RVA: 0x0010E808 File Offset: 0x0010CA08
		internal override void ReadNextElement(Shaper shaper)
		{
			IEntityWrapper entityWrapper = null;
			T t;
			try
			{
				if (this.TypedCoordinatorFactory.WrappedElement == null)
				{
					t = this.TypedCoordinatorFactory.Element(shaper);
				}
				else
				{
					entityWrapper = this.TypedCoordinatorFactory.WrappedElement(shaper);
					t = (T)((object)entityWrapper.Entity);
				}
			}
			catch (Exception ex)
			{
				if (ex.IsCatchableExceptionType() && !shaper.Reader.IsClosed)
				{
					this.ResetCollection(shaper);
					t = this.TypedCoordinatorFactory.ElementWithErrorHandling(shaper);
				}
				throw;
			}
			if (this.IsUsingElementCollection)
			{
				this._elements.Add(t);
				if (entityWrapper != null)
				{
					this._wrappedElements.Add(entityWrapper);
					return;
				}
			}
			else
			{
				this._current = t;
			}
		}

		// Token: 0x06004CDB RID: 19675 RVA: 0x0010E8C4 File Offset: 0x0010CAC4
		internal void RegisterCloseHandler(Action<Shaper, List<IEntityWrapper>> closeHandler)
		{
			this._handleClose = closeHandler;
		}

		// Token: 0x06004CDC RID: 19676 RVA: 0x0010E8CD File Offset: 0x0010CACD
		internal void SetCurrentToDefault()
		{
			this._current = default(T);
		}

		// Token: 0x06004CDD RID: 19677 RVA: 0x0010E8DB File Offset: 0x0010CADB
		private IEnumerable<T> GetElements()
		{
			return this._elements;
		}

		// Token: 0x04001B3C RID: 6972
		internal readonly CoordinatorFactory<T> TypedCoordinatorFactory;

		// Token: 0x04001B3D RID: 6973
		private T _current;

		// Token: 0x04001B3E RID: 6974
		private ICollection<T> _elements;

		// Token: 0x04001B3F RID: 6975
		private List<IEntityWrapper> _wrappedElements;

		// Token: 0x04001B40 RID: 6976
		private Action<Shaper, List<IEntityWrapper>> _handleClose;

		// Token: 0x04001B41 RID: 6977
		private readonly bool IsUsingElementCollection;
	}
}
