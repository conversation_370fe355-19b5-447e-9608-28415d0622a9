﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Objects.Internal;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;

namespace System.Data.Entity.Core.Objects.ELinq
{
	// Token: 0x02000465 RID: 1125
	internal class ObjectQueryProvider : IQueryProvider, IDbAsyncQueryProvider
	{
		// Token: 0x06003777 RID: 14199 RVA: 0x000B38F1 File Offset: 0x000B1AF1
		internal ObjectQueryProvider(ObjectContext context)
		{
			this._context = context;
		}

		// Token: 0x06003778 RID: 14200 RVA: 0x000B3900 File Offset: 0x000B1B00
		internal ObjectQueryProvider(ObjectQuery query)
			: this(query.Context)
		{
			this._query = query;
		}

		// Token: 0x06003779 RID: 14201 RVA: 0x000B3915 File Offset: 0x000B1B15
		internal virtual ObjectQuery<TElement> CreateQuery<TElement>(Expression expression)
		{
			return this.GetObjectQueryState(this._query, expression, typeof(TElement)).CreateObjectQuery<TElement>();
		}

		// Token: 0x0600377A RID: 14202 RVA: 0x000B3933 File Offset: 0x000B1B33
		internal virtual ObjectQuery CreateQuery(Expression expression, Type ofType)
		{
			return this.GetObjectQueryState(this._query, expression, ofType).CreateQuery();
		}

		// Token: 0x0600377B RID: 14203 RVA: 0x000B3948 File Offset: 0x000B1B48
		private ObjectQueryState GetObjectQueryState(ObjectQuery query, Expression expression, Type ofType)
		{
			if (query != null)
			{
				return new ELinqQueryState(ofType, this._query, expression, null);
			}
			return new ELinqQueryState(ofType, this._context, expression, null);
		}

		// Token: 0x0600377C RID: 14204 RVA: 0x000B396A File Offset: 0x000B1B6A
		IQueryable<TElement> IQueryProvider.CreateQuery<TElement>(Expression expression)
		{
			Check.NotNull<Expression>(expression, "expression");
			if (!typeof(IQueryable<TElement>).IsAssignableFrom(expression.Type))
			{
				throw new ArgumentException(Strings.ELinq_ExpressionMustBeIQueryable, "expression");
			}
			return this.CreateQuery<TElement>(expression);
		}

		// Token: 0x0600377D RID: 14205 RVA: 0x000B39A6 File Offset: 0x000B1BA6
		TResult IQueryProvider.Execute<TResult>(Expression expression)
		{
			Check.NotNull<Expression>(expression, "expression");
			return ObjectQueryProvider.ExecuteSingle<TResult>(this.CreateQuery<TResult>(expression), expression);
		}

		// Token: 0x0600377E RID: 14206 RVA: 0x000B39C4 File Offset: 0x000B1BC4
		IQueryable IQueryProvider.CreateQuery(Expression expression)
		{
			Check.NotNull<Expression>(expression, "expression");
			if (!typeof(IQueryable).IsAssignableFrom(expression.Type))
			{
				throw new ArgumentException(Strings.ELinq_ExpressionMustBeIQueryable, "expression");
			}
			Type elementType = TypeSystem.GetElementType(expression.Type);
			return this.CreateQuery(expression, elementType);
		}

		// Token: 0x0600377F RID: 14207 RVA: 0x000B3A18 File Offset: 0x000B1C18
		object IQueryProvider.Execute(Expression expression)
		{
			Check.NotNull<Expression>(expression, "expression");
			return ObjectQueryProvider.ExecuteSingle<object>(this.CreateQuery(expression, expression.Type).Cast<object>(), expression);
		}

		// Token: 0x06003780 RID: 14208 RVA: 0x000B3A3E File Offset: 0x000B1C3E
		Task<TResult> IDbAsyncQueryProvider.ExecuteAsync<TResult>(Expression expression, CancellationToken cancellationToken)
		{
			Check.NotNull<Expression>(expression, "expression");
			cancellationToken.ThrowIfCancellationRequested();
			return ObjectQueryProvider.ExecuteSingleAsync<TResult>(this.CreateQuery<TResult>(expression), expression, cancellationToken);
		}

		// Token: 0x06003781 RID: 14209 RVA: 0x000B3A61 File Offset: 0x000B1C61
		Task<object> IDbAsyncQueryProvider.ExecuteAsync(Expression expression, CancellationToken cancellationToken)
		{
			Check.NotNull<Expression>(expression, "expression");
			cancellationToken.ThrowIfCancellationRequested();
			return ObjectQueryProvider.ExecuteSingleAsync<object>(this.CreateQuery(expression, expression.Type).Cast<object>(), expression, cancellationToken);
		}

		// Token: 0x06003782 RID: 14210 RVA: 0x000B3A8F File Offset: 0x000B1C8F
		internal static TResult ExecuteSingle<TResult>(IEnumerable<TResult> query, Expression queryRoot)
		{
			return ObjectQueryProvider.GetElementFunction<TResult>(queryRoot)(query);
		}

		// Token: 0x06003783 RID: 14211 RVA: 0x000B3AA0 File Offset: 0x000B1CA0
		private static Func<IEnumerable<TResult>, TResult> GetElementFunction<TResult>(Expression queryRoot)
		{
			SequenceMethod sequenceMethod;
			if (ReflectionUtil.TryIdentifySequenceMethod(queryRoot, true, out sequenceMethod))
			{
				if (sequenceMethod - SequenceMethod.First <= 1)
				{
					return (IEnumerable<TResult> sequence) => sequence.First<TResult>();
				}
				if (sequenceMethod - SequenceMethod.FirstOrDefault <= 1)
				{
					return (IEnumerable<TResult> sequence) => sequence.FirstOrDefault<TResult>();
				}
				if (sequenceMethod - SequenceMethod.SingleOrDefault <= 1)
				{
					return (IEnumerable<TResult> sequence) => sequence.SingleOrDefault<TResult>();
				}
			}
			return (IEnumerable<TResult> sequence) => sequence.Single<TResult>();
		}

		// Token: 0x06003784 RID: 14212 RVA: 0x000B3B4E File Offset: 0x000B1D4E
		internal static Task<TResult> ExecuteSingleAsync<TResult>(IDbAsyncEnumerable<TResult> query, Expression queryRoot, CancellationToken cancellationToken)
		{
			return ObjectQueryProvider.GetAsyncElementFunction<TResult>(queryRoot)(query, cancellationToken);
		}

		// Token: 0x06003785 RID: 14213 RVA: 0x000B3B60 File Offset: 0x000B1D60
		private static Func<IDbAsyncEnumerable<TResult>, CancellationToken, Task<TResult>> GetAsyncElementFunction<TResult>(Expression queryRoot)
		{
			SequenceMethod sequenceMethod;
			if (ReflectionUtil.TryIdentifySequenceMethod(queryRoot, true, out sequenceMethod))
			{
				if (sequenceMethod - SequenceMethod.First <= 1)
				{
					return (IDbAsyncEnumerable<TResult> sequence, CancellationToken cancellationToken) => sequence.FirstAsync(cancellationToken);
				}
				if (sequenceMethod - SequenceMethod.FirstOrDefault <= 1)
				{
					return (IDbAsyncEnumerable<TResult> sequence, CancellationToken cancellationToken) => sequence.FirstOrDefaultAsync(cancellationToken);
				}
				if (sequenceMethod - SequenceMethod.SingleOrDefault <= 1)
				{
					return (IDbAsyncEnumerable<TResult> sequence, CancellationToken cancellationToken) => sequence.SingleOrDefaultAsync(cancellationToken);
				}
			}
			return (IDbAsyncEnumerable<TResult> sequence, CancellationToken cancellationToken) => sequence.SingleAsync(cancellationToken);
		}

		// Token: 0x0400121F RID: 4639
		private readonly ObjectContext _context;

		// Token: 0x04001220 RID: 4640
		private readonly ObjectQuery _query;
	}
}
