﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Validation
{
	// Token: 0x02000578 RID: 1400
	internal class BasicKeyConstraint : KeyConstraint<BasicCellRelation, MemberProjectedSlot>
	{
		// Token: 0x06004402 RID: 17410 RVA: 0x000ECA08 File Offset: 0x000EAC08
		internal BasicKeyConstraint(BasicCellRelation relation, IEnumerable<MemberProjectedSlot> keySlots)
			: base(relation, keySlots, ProjectedSlot.EqualityComparer)
		{
		}

		// Token: 0x06004403 RID: 17411 RVA: 0x000ECA18 File Offset: 0x000EAC18
		internal ViewKeyConstraint Propagate()
		{
			ViewCellRelation viewCellRelation = base.CellRelation.ViewCellRelation;
			List<ViewCellSlot> list = new List<ViewCellSlot>();
			foreach (MemberProjectedSlot memberProjectedSlot in base.KeySlots)
			{
				ViewCellSlot viewCellSlot = viewCellRelation.LookupViewSlot(memberProjectedSlot);
				if (viewCellSlot == null)
				{
					return null;
				}
				list.Add(viewCellSlot);
			}
			return new ViewKeyConstraint(viewCellRelation, list);
		}
	}
}
