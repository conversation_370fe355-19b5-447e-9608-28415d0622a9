﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.EntitySql;
using System.Data.Entity.Core.Common.QueryCache;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping;
using System.Data.Entity.Core.Mapping.Update.Internal;
using System.Data.Entity.Core.Mapping.ViewGeneration;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Core.Objects.DataClasses;
using System.Data.Entity.Core.Objects.ELinq;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Xml;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004E1 RID: 1249
	public class MetadataWorkspace
	{
		// Token: 0x06003E15 RID: 15893 RVA: 0x000CD268 File Offset: 0x000CB468
		public MetadataWorkspace()
		{
			this._itemsOSpace = new Lazy<ObjectItemCollection>(() => new ObjectItemCollection(), true);
			this.MetadataOptimization = new MetadataOptimization(this);
		}

		// Token: 0x06003E16 RID: 15894 RVA: 0x000CD2C8 File Offset: 0x000CB4C8
		public MetadataWorkspace(Func<EdmItemCollection> cSpaceLoader, Func<StoreItemCollection> sSpaceLoader, Func<StorageMappingItemCollection> csMappingLoader, Func<ObjectItemCollection> oSpaceLoader)
		{
			MetadataWorkspace <>4__this = this;
			Check.NotNull<Func<EdmItemCollection>>(cSpaceLoader, "cSpaceLoader");
			Check.NotNull<Func<StoreItemCollection>>(sSpaceLoader, "sSpaceLoader");
			Check.NotNull<Func<StorageMappingItemCollection>>(csMappingLoader, "csMappingLoader");
			Check.NotNull<Func<ObjectItemCollection>>(oSpaceLoader, "oSpaceLoader");
			this._itemsCSpace = new Lazy<EdmItemCollection>(() => <>4__this.LoadAndCheckItemCollection<EdmItemCollection>(cSpaceLoader), true);
			this._itemsSSpace = new Lazy<StoreItemCollection>(() => <>4__this.LoadAndCheckItemCollection<StoreItemCollection>(sSpaceLoader), true);
			this._itemsOSpace = new Lazy<ObjectItemCollection>(oSpaceLoader, true);
			this._itemsCSSpace = new Lazy<StorageMappingItemCollection>(() => <>4__this.LoadAndCheckItemCollection<StorageMappingItemCollection>(csMappingLoader), true);
			this._itemsOCSpace = new Lazy<DefaultObjectMappingItemCollection>(() => new DefaultObjectMappingItemCollection(<>4__this._itemsCSpace.Value, <>4__this._itemsOSpace.Value), true);
			this.MetadataOptimization = new MetadataOptimization(this);
		}

		// Token: 0x06003E17 RID: 15895 RVA: 0x000CD3D0 File Offset: 0x000CB5D0
		public MetadataWorkspace(Func<EdmItemCollection> cSpaceLoader, Func<StoreItemCollection> sSpaceLoader, Func<StorageMappingItemCollection> csMappingLoader)
		{
			MetadataWorkspace <>4__this = this;
			Check.NotNull<Func<EdmItemCollection>>(cSpaceLoader, "cSpaceLoader");
			Check.NotNull<Func<StoreItemCollection>>(sSpaceLoader, "sSpaceLoader");
			Check.NotNull<Func<StorageMappingItemCollection>>(csMappingLoader, "csMappingLoader");
			this._itemsCSpace = new Lazy<EdmItemCollection>(() => <>4__this.LoadAndCheckItemCollection<EdmItemCollection>(cSpaceLoader), true);
			this._itemsSSpace = new Lazy<StoreItemCollection>(() => <>4__this.LoadAndCheckItemCollection<StoreItemCollection>(sSpaceLoader), true);
			this._itemsOSpace = new Lazy<ObjectItemCollection>(() => new ObjectItemCollection(), true);
			this._itemsCSSpace = new Lazy<StorageMappingItemCollection>(() => <>4__this.LoadAndCheckItemCollection<StorageMappingItemCollection>(csMappingLoader), true);
			this._itemsOCSpace = new Lazy<DefaultObjectMappingItemCollection>(() => new DefaultObjectMappingItemCollection(<>4__this._itemsCSpace.Value, <>4__this._itemsOSpace.Value), true);
			this.MetadataOptimization = new MetadataOptimization(this);
		}

		// Token: 0x06003E18 RID: 15896 RVA: 0x000CD4E8 File Offset: 0x000CB6E8
		public MetadataWorkspace(IEnumerable<string> paths, IEnumerable<Assembly> assembliesToConsider)
		{
			Check.NotNull<IEnumerable<string>>(paths, "paths");
			Check.NotNull<IEnumerable<Assembly>>(assembliesToConsider, "assembliesToConsider");
			EntityUtil.CheckArgumentContainsNull<string>(ref paths, "paths");
			EntityUtil.CheckArgumentContainsNull<Assembly>(ref assembliesToConsider, "assembliesToConsider");
			Func<AssemblyName, Assembly> func = delegate(AssemblyName referenceName)
			{
				foreach (Assembly assembly in assembliesToConsider)
				{
					if (AssemblyName.ReferenceMatchesDefinition(referenceName, new AssemblyName(assembly.FullName)))
					{
						return assembly;
					}
				}
				throw new ArgumentException(Strings.AssemblyMissingFromAssembliesToConsider(referenceName.FullName), "assembliesToConsider");
			};
			this.CreateMetadataWorkspaceWithResolver(paths, () => assembliesToConsider, func);
			this.MetadataOptimization = new MetadataOptimization(this);
		}

		// Token: 0x06003E19 RID: 15897 RVA: 0x000CD588 File Offset: 0x000CB788
		private void CreateMetadataWorkspaceWithResolver(IEnumerable<string> paths, Func<IEnumerable<Assembly>> wildcardAssemblies, Func<AssemblyName, Assembly> resolveReference)
		{
			MetadataArtifactLoader metadataArtifactLoader = MetadataArtifactLoader.CreateCompositeFromFilePaths(paths.ToArray<string>(), "", new CustomAssemblyResolver(wildcardAssemblies, resolveReference));
			this._itemsOSpace = new Lazy<ObjectItemCollection>(() => new ObjectItemCollection(), true);
			using (DisposableCollectionWrapper<XmlReader> disposableCollectionWrapper = new DisposableCollectionWrapper<XmlReader>(metadataArtifactLoader.CreateReaders(DataSpace.CSpace)))
			{
				if (disposableCollectionWrapper.Any<XmlReader>())
				{
					EdmItemCollection itemCollection = new EdmItemCollection(disposableCollectionWrapper, metadataArtifactLoader.GetPaths(DataSpace.CSpace), false);
					this._itemsCSpace = new Lazy<EdmItemCollection>(() => itemCollection, true);
					this._itemsOCSpace = new Lazy<DefaultObjectMappingItemCollection>(() => new DefaultObjectMappingItemCollection(itemCollection, this._itemsOSpace.Value), true);
				}
			}
			using (DisposableCollectionWrapper<XmlReader> disposableCollectionWrapper2 = new DisposableCollectionWrapper<XmlReader>(metadataArtifactLoader.CreateReaders(DataSpace.SSpace)))
			{
				if (disposableCollectionWrapper2.Any<XmlReader>())
				{
					StoreItemCollection itemCollection2 = new StoreItemCollection(disposableCollectionWrapper2, metadataArtifactLoader.GetPaths(DataSpace.SSpace));
					this._itemsSSpace = new Lazy<StoreItemCollection>(() => itemCollection2, true);
				}
			}
			using (DisposableCollectionWrapper<XmlReader> disposableCollectionWrapper3 = new DisposableCollectionWrapper<XmlReader>(metadataArtifactLoader.CreateReaders(DataSpace.CSSpace)))
			{
				if (disposableCollectionWrapper3.Any<XmlReader>() && this._itemsCSpace != null && this._itemsSSpace != null)
				{
					StorageMappingItemCollection mapping = new StorageMappingItemCollection(this._itemsCSpace.Value, this._itemsSSpace.Value, disposableCollectionWrapper3, metadataArtifactLoader.GetPaths(DataSpace.CSSpace));
					this._itemsCSSpace = new Lazy<StorageMappingItemCollection>(() => mapping, true);
				}
			}
		}

		// Token: 0x17000C35 RID: 3125
		// (get) Token: 0x06003E1A RID: 15898 RVA: 0x000CD748 File Offset: 0x000CB948
		private static IEnumerable<double> SupportedEdmVersions
		{
			get
			{
				yield return 0.0;
				yield return 1.0;
				yield return 2.0;
				yield return 3.0;
				yield break;
			}
		}

		// Token: 0x17000C36 RID: 3126
		// (get) Token: 0x06003E1B RID: 15899 RVA: 0x000CD751 File Offset: 0x000CB951
		public static double MaximumEdmVersionSupported
		{
			get
			{
				return MetadataWorkspace._maximumEdmVersionSupported;
			}
		}

		// Token: 0x17000C37 RID: 3127
		// (get) Token: 0x06003E1C RID: 15900 RVA: 0x000CD758 File Offset: 0x000CB958
		internal virtual Guid MetadataWorkspaceId
		{
			get
			{
				return this._metadataWorkspaceId;
			}
		}

		// Token: 0x06003E1D RID: 15901 RVA: 0x000CD760 File Offset: 0x000CB960
		public virtual EntitySqlParser CreateEntitySqlParser()
		{
			return new EntitySqlParser(new ModelPerspective(this));
		}

		// Token: 0x06003E1E RID: 15902 RVA: 0x000CD76D File Offset: 0x000CB96D
		public virtual DbQueryCommandTree CreateQueryCommandTree(DbExpression query)
		{
			return new DbQueryCommandTree(this, DataSpace.CSpace, query);
		}

		// Token: 0x06003E1F RID: 15903 RVA: 0x000CD777 File Offset: 0x000CB977
		public virtual ItemCollection GetItemCollection(DataSpace dataSpace)
		{
			return this.GetItemCollection(dataSpace, true);
		}

		// Token: 0x06003E20 RID: 15904 RVA: 0x000CD784 File Offset: 0x000CB984
		[Obsolete("Construct MetadataWorkspace using constructor that accepts metadata loading delegates.")]
		public virtual void RegisterItemCollection(ItemCollection collection)
		{
			Check.NotNull<ItemCollection>(collection, "collection");
			try
			{
				switch (collection.DataSpace)
				{
				case DataSpace.OSpace:
					this._itemsOSpace = new Lazy<ObjectItemCollection>(() => (ObjectItemCollection)collection, true);
					if (this._itemsOCSpace == null && this._itemsCSpace != null)
					{
						this._itemsOCSpace = new Lazy<DefaultObjectMappingItemCollection>(() => new DefaultObjectMappingItemCollection(this._itemsCSpace.Value, this._itemsOSpace.Value));
						goto IL_0217;
					}
					goto IL_0217;
				case DataSpace.CSpace:
				{
					EdmItemCollection edmCollection = (EdmItemCollection)collection;
					if (!MetadataWorkspace.SupportedEdmVersions.Contains(edmCollection.EdmVersion))
					{
						throw new InvalidOperationException(Strings.EdmVersionNotSupportedByRuntime(edmCollection.EdmVersion, Helper.GetCommaDelimitedString(from e in MetadataWorkspace.SupportedEdmVersions
							where e != 0.0
							select e.ToString(CultureInfo.InvariantCulture))));
					}
					this.CheckAndSetItemCollectionVersionInWorkSpace(collection);
					this._itemsCSpace = new Lazy<EdmItemCollection>(() => edmCollection, true);
					if (this._itemsOCSpace == null)
					{
						this._itemsOCSpace = new Lazy<DefaultObjectMappingItemCollection>(() => new DefaultObjectMappingItemCollection(edmCollection, this._itemsOSpace.Value));
						goto IL_0217;
					}
					goto IL_0217;
				}
				case DataSpace.SSpace:
					this.CheckAndSetItemCollectionVersionInWorkSpace(collection);
					this._itemsSSpace = new Lazy<StoreItemCollection>(() => (StoreItemCollection)collection, true);
					goto IL_0217;
				case DataSpace.CSSpace:
					this.CheckAndSetItemCollectionVersionInWorkSpace(collection);
					this._itemsCSSpace = new Lazy<StorageMappingItemCollection>(() => (StorageMappingItemCollection)collection, true);
					goto IL_0217;
				}
				this._itemsOCSpace = new Lazy<DefaultObjectMappingItemCollection>(() => (DefaultObjectMappingItemCollection)collection, true);
				IL_0217:;
			}
			catch (InvalidCastException)
			{
				throw new MetadataException(Strings.InvalidCollectionForMapping(collection.DataSpace.ToString()));
			}
		}

		// Token: 0x06003E21 RID: 15905 RVA: 0x000CD9EC File Offset: 0x000CBBEC
		private T LoadAndCheckItemCollection<T>(Func<T> itemCollectionLoader) where T : ItemCollection
		{
			T t = itemCollectionLoader();
			if (t != null)
			{
				this.CheckAndSetItemCollectionVersionInWorkSpace(t);
			}
			return t;
		}

		// Token: 0x06003E22 RID: 15906 RVA: 0x000CDA18 File Offset: 0x000CBC18
		private void CheckAndSetItemCollectionVersionInWorkSpace(ItemCollection itemCollectionToRegister)
		{
			double num = 0.0;
			string text = null;
			switch (itemCollectionToRegister.DataSpace)
			{
			case DataSpace.CSpace:
				num = ((EdmItemCollection)itemCollectionToRegister).EdmVersion;
				text = "EdmItemCollection";
				break;
			case DataSpace.SSpace:
				num = ((StoreItemCollection)itemCollectionToRegister).StoreSchemaVersion;
				text = "StoreItemCollection";
				break;
			case DataSpace.CSSpace:
				num = ((StorageMappingItemCollection)itemCollectionToRegister).MappingVersion;
				text = "StorageMappingItemCollection";
				break;
			}
			object schemaVersionLock = this._schemaVersionLock;
			lock (schemaVersionLock)
			{
				if (num != this._schemaVersion && num != 0.0 && this._schemaVersion != 0.0)
				{
					throw new MetadataException(Strings.DifferentSchemaVersionInCollection(text, num, this._schemaVersion));
				}
				this._schemaVersion = num;
			}
		}

		// Token: 0x06003E23 RID: 15907 RVA: 0x000CDB08 File Offset: 0x000CBD08
		public virtual void LoadFromAssembly(Assembly assembly)
		{
			this.LoadFromAssembly(assembly, null);
		}

		// Token: 0x06003E24 RID: 15908 RVA: 0x000CDB14 File Offset: 0x000CBD14
		public virtual void LoadFromAssembly(Assembly assembly, Action<string> logLoadMessage)
		{
			Check.NotNull<Assembly>(assembly, "assembly");
			ObjectItemCollection objectItemCollection = (ObjectItemCollection)this.GetItemCollection(DataSpace.OSpace);
			this.ExplicitLoadFromAssembly(assembly, objectItemCollection, logLoadMessage);
		}

		// Token: 0x06003E25 RID: 15909 RVA: 0x000CDB44 File Offset: 0x000CBD44
		private void ExplicitLoadFromAssembly(Assembly assembly, ObjectItemCollection collection, Action<string> logLoadMessage)
		{
			ItemCollection itemCollection;
			if (!this.TryGetItemCollection(DataSpace.CSpace, out itemCollection))
			{
				itemCollection = null;
			}
			collection.ExplicitLoadFromAssembly(assembly, (EdmItemCollection)itemCollection, logLoadMessage);
		}

		// Token: 0x06003E26 RID: 15910 RVA: 0x000CDB6C File Offset: 0x000CBD6C
		private void ImplicitLoadFromAssembly(Assembly assembly, ObjectItemCollection collection)
		{
			if (!MetadataAssemblyHelper.ShouldFilterAssembly(assembly))
			{
				this.ExplicitLoadFromAssembly(assembly, collection, null);
			}
		}

		// Token: 0x06003E27 RID: 15911 RVA: 0x000CDB80 File Offset: 0x000CBD80
		internal virtual void ImplicitLoadAssemblyForType(Type type, Assembly callingAssembly)
		{
			ItemCollection itemCollection;
			if (this.TryGetItemCollection(DataSpace.OSpace, out itemCollection))
			{
				ObjectItemCollection objectItemCollection = (ObjectItemCollection)itemCollection;
				ItemCollection itemCollection2;
				this.TryGetItemCollection(DataSpace.CSpace, out itemCollection2);
				EdmItemCollection edmItemCollection = (EdmItemCollection)itemCollection2;
				if (!objectItemCollection.ImplicitLoadAssemblyForType(type, edmItemCollection) && null != callingAssembly)
				{
					if (ObjectItemAttributeAssemblyLoader.IsSchemaAttributePresent(callingAssembly) || this._foundAssemblyWithAttribute || MetadataAssemblyHelper.GetNonSystemReferencedAssemblies(callingAssembly).Any(new Func<Assembly, bool>(ObjectItemAttributeAssemblyLoader.IsSchemaAttributePresent)))
					{
						this._foundAssemblyWithAttribute = true;
						objectItemCollection.ImplicitLoadAllReferencedAssemblies(callingAssembly, edmItemCollection);
						return;
					}
					this.ImplicitLoadFromAssembly(callingAssembly, objectItemCollection);
				}
			}
		}

		// Token: 0x06003E28 RID: 15912 RVA: 0x000CDC04 File Offset: 0x000CBE04
		internal virtual void ImplicitLoadFromEntityType(EntityType type, Assembly callingAssembly)
		{
			MappingBase mappingBase;
			if (!this.TryGetMap(type, DataSpace.OCSpace, out mappingBase))
			{
				this.ImplicitLoadAssemblyForType(typeof(IEntityWithKey), callingAssembly);
				ObjectItemCollection objectItemCollection = this.GetItemCollection(DataSpace.OSpace) as ObjectItemCollection;
				EdmType edmType;
				if (objectItemCollection == null || !objectItemCollection.TryGetOSpaceType(type, out edmType))
				{
					throw new InvalidOperationException(Strings.Mapping_Object_InvalidType(type.Identity));
				}
			}
		}

		// Token: 0x06003E29 RID: 15913 RVA: 0x000CDC5A File Offset: 0x000CBE5A
		public virtual T GetItem<T>(string identity, DataSpace dataSpace) where T : GlobalItem
		{
			return this.GetItemCollection(dataSpace, true).GetItem<T>(identity, false);
		}

		// Token: 0x06003E2A RID: 15914 RVA: 0x000CDC6C File Offset: 0x000CBE6C
		public virtual bool TryGetItem<T>(string identity, DataSpace space, out T item) where T : GlobalItem
		{
			item = default(T);
			ItemCollection itemCollection = this.GetItemCollection(space, false);
			return itemCollection != null && itemCollection.TryGetItem<T>(identity, false, out item);
		}

		// Token: 0x06003E2B RID: 15915 RVA: 0x000CDC97 File Offset: 0x000CBE97
		public virtual T GetItem<T>(string identity, bool ignoreCase, DataSpace dataSpace) where T : GlobalItem
		{
			return this.GetItemCollection(dataSpace, true).GetItem<T>(identity, ignoreCase);
		}

		// Token: 0x06003E2C RID: 15916 RVA: 0x000CDCA8 File Offset: 0x000CBEA8
		public virtual bool TryGetItem<T>(string identity, bool ignoreCase, DataSpace dataSpace, out T item) where T : GlobalItem
		{
			item = default(T);
			ItemCollection itemCollection = this.GetItemCollection(dataSpace, false);
			return itemCollection != null && itemCollection.TryGetItem<T>(identity, ignoreCase, out item);
		}

		// Token: 0x06003E2D RID: 15917 RVA: 0x000CDCD5 File Offset: 0x000CBED5
		public virtual ReadOnlyCollection<T> GetItems<T>(DataSpace dataSpace) where T : GlobalItem
		{
			return this.GetItemCollection(dataSpace, true).GetItems<T>();
		}

		// Token: 0x06003E2E RID: 15918 RVA: 0x000CDCE4 File Offset: 0x000CBEE4
		public virtual EdmType GetType(string name, string namespaceName, DataSpace dataSpace)
		{
			return this.GetItemCollection(dataSpace, true).GetType(name, namespaceName, false);
		}

		// Token: 0x06003E2F RID: 15919 RVA: 0x000CDCF8 File Offset: 0x000CBEF8
		public virtual bool TryGetType(string name, string namespaceName, DataSpace dataSpace, out EdmType type)
		{
			type = null;
			ItemCollection itemCollection = this.GetItemCollection(dataSpace, false);
			return itemCollection != null && itemCollection.TryGetType(name, namespaceName, false, out type);
		}

		// Token: 0x06003E30 RID: 15920 RVA: 0x000CDD22 File Offset: 0x000CBF22
		public virtual EdmType GetType(string name, string namespaceName, bool ignoreCase, DataSpace dataSpace)
		{
			return this.GetItemCollection(dataSpace, true).GetType(name, namespaceName, ignoreCase);
		}

		// Token: 0x06003E31 RID: 15921 RVA: 0x000CDD38 File Offset: 0x000CBF38
		public virtual bool TryGetType(string name, string namespaceName, bool ignoreCase, DataSpace dataSpace, out EdmType type)
		{
			type = null;
			ItemCollection itemCollection = this.GetItemCollection(dataSpace, false);
			return itemCollection != null && itemCollection.TryGetType(name, namespaceName, ignoreCase, out type);
		}

		// Token: 0x06003E32 RID: 15922 RVA: 0x000CDD63 File Offset: 0x000CBF63
		public virtual EntityContainer GetEntityContainer(string name, DataSpace dataSpace)
		{
			return this.GetItemCollection(dataSpace, true).GetEntityContainer(name);
		}

		// Token: 0x06003E33 RID: 15923 RVA: 0x000CDD74 File Offset: 0x000CBF74
		public virtual bool TryGetEntityContainer(string name, DataSpace dataSpace, out EntityContainer entityContainer)
		{
			entityContainer = null;
			Check.NotNull<string>(name, "name");
			ItemCollection itemCollection = this.GetItemCollection(dataSpace, false);
			return itemCollection != null && itemCollection.TryGetEntityContainer(name, out entityContainer);
		}

		// Token: 0x06003E34 RID: 15924 RVA: 0x000CDDA6 File Offset: 0x000CBFA6
		public virtual EntityContainer GetEntityContainer(string name, bool ignoreCase, DataSpace dataSpace)
		{
			return this.GetItemCollection(dataSpace, true).GetEntityContainer(name, ignoreCase);
		}

		// Token: 0x06003E35 RID: 15925 RVA: 0x000CDDB8 File Offset: 0x000CBFB8
		public virtual bool TryGetEntityContainer(string name, bool ignoreCase, DataSpace dataSpace, out EntityContainer entityContainer)
		{
			entityContainer = null;
			Check.NotNull<string>(name, "name");
			ItemCollection itemCollection = this.GetItemCollection(dataSpace, false);
			return itemCollection != null && itemCollection.TryGetEntityContainer(name, ignoreCase, out entityContainer);
		}

		// Token: 0x06003E36 RID: 15926 RVA: 0x000CDDED File Offset: 0x000CBFED
		public virtual ReadOnlyCollection<EdmFunction> GetFunctions(string name, string namespaceName, DataSpace dataSpace)
		{
			return this.GetFunctions(name, namespaceName, dataSpace, false);
		}

		// Token: 0x06003E37 RID: 15927 RVA: 0x000CDDF9 File Offset: 0x000CBFF9
		public virtual ReadOnlyCollection<EdmFunction> GetFunctions(string name, string namespaceName, DataSpace dataSpace, bool ignoreCase)
		{
			Check.NotEmpty(name, "name");
			Check.NotEmpty(namespaceName, "namespaceName");
			return this.GetItemCollection(dataSpace, true).GetFunctions(namespaceName + "." + name, ignoreCase);
		}

		// Token: 0x06003E38 RID: 15928 RVA: 0x000CDE30 File Offset: 0x000CC030
		internal virtual bool TryGetFunction(string name, string namespaceName, TypeUsage[] parameterTypes, bool ignoreCase, DataSpace dataSpace, out EdmFunction function)
		{
			function = null;
			Check.NotNull<string>(name, "name");
			Check.NotNull<string>(namespaceName, "namespaceName");
			ItemCollection itemCollection = this.GetItemCollection(dataSpace, false);
			return itemCollection != null && itemCollection.TryGetFunction(namespaceName + "." + name, parameterTypes, ignoreCase, out function);
		}

		// Token: 0x06003E39 RID: 15929 RVA: 0x000CDE7F File Offset: 0x000CC07F
		public virtual ReadOnlyCollection<PrimitiveType> GetPrimitiveTypes(DataSpace dataSpace)
		{
			return this.GetItemCollection(dataSpace, true).GetItems<PrimitiveType>();
		}

		// Token: 0x06003E3A RID: 15930 RVA: 0x000CDE8E File Offset: 0x000CC08E
		public virtual ReadOnlyCollection<GlobalItem> GetItems(DataSpace dataSpace)
		{
			return this.GetItemCollection(dataSpace, true).GetItems<GlobalItem>();
		}

		// Token: 0x06003E3B RID: 15931 RVA: 0x000CDE9D File Offset: 0x000CC09D
		internal virtual PrimitiveType GetMappedPrimitiveType(PrimitiveTypeKind primitiveTypeKind, DataSpace dataSpace)
		{
			return this.GetItemCollection(dataSpace, true).GetMappedPrimitiveType(primitiveTypeKind);
		}

		// Token: 0x06003E3C RID: 15932 RVA: 0x000CDEB0 File Offset: 0x000CC0B0
		internal virtual bool TryGetMap(string typeIdentity, DataSpace typeSpace, bool ignoreCase, DataSpace mappingSpace, out MappingBase map)
		{
			map = null;
			ItemCollection itemCollection = this.GetItemCollection(mappingSpace, false);
			return itemCollection != null && ((MappingItemCollection)itemCollection).TryGetMap(typeIdentity, typeSpace, ignoreCase, out map);
		}

		// Token: 0x06003E3D RID: 15933 RVA: 0x000CDEE0 File Offset: 0x000CC0E0
		internal virtual MappingBase GetMap(string identity, DataSpace typeSpace, DataSpace dataSpace)
		{
			return ((MappingItemCollection)this.GetItemCollection(dataSpace, true)).GetMap(identity, typeSpace);
		}

		// Token: 0x06003E3E RID: 15934 RVA: 0x000CDEF6 File Offset: 0x000CC0F6
		internal virtual MappingBase GetMap(GlobalItem item, DataSpace dataSpace)
		{
			return ((MappingItemCollection)this.GetItemCollection(dataSpace, true)).GetMap(item);
		}

		// Token: 0x06003E3F RID: 15935 RVA: 0x000CDF0C File Offset: 0x000CC10C
		internal virtual bool TryGetMap(GlobalItem item, DataSpace dataSpace, out MappingBase map)
		{
			map = null;
			ItemCollection itemCollection = this.GetItemCollection(dataSpace, false);
			return itemCollection != null && ((MappingItemCollection)itemCollection).TryGetMap(item, out map);
		}

		// Token: 0x06003E40 RID: 15936 RVA: 0x000CDF37 File Offset: 0x000CC137
		public virtual bool TryGetItemCollection(DataSpace dataSpace, out ItemCollection collection)
		{
			collection = this.GetItemCollection(dataSpace, false);
			return collection != null;
		}

		// Token: 0x06003E41 RID: 15937 RVA: 0x000CDF48 File Offset: 0x000CC148
		internal virtual ItemCollection GetItemCollection(DataSpace dataSpace, bool required)
		{
			ItemCollection itemCollection;
			switch (dataSpace)
			{
			case DataSpace.OSpace:
				itemCollection = this._itemsOSpace.Value;
				break;
			case DataSpace.CSpace:
				itemCollection = ((this._itemsCSpace == null) ? null : this._itemsCSpace.Value);
				break;
			case DataSpace.SSpace:
				itemCollection = ((this._itemsSSpace == null) ? null : this._itemsSSpace.Value);
				break;
			case DataSpace.OCSpace:
				itemCollection = ((this._itemsOCSpace == null) ? null : this._itemsOCSpace.Value);
				break;
			case DataSpace.CSSpace:
				itemCollection = ((this._itemsCSSpace == null) ? null : this._itemsCSSpace.Value);
				break;
			default:
				itemCollection = null;
				break;
			}
			if (required && itemCollection == null)
			{
				throw new InvalidOperationException(Strings.NoCollectionForSpace(dataSpace.ToString()));
			}
			return itemCollection;
		}

		// Token: 0x06003E42 RID: 15938 RVA: 0x000CE006 File Offset: 0x000CC206
		public virtual StructuralType GetObjectSpaceType(StructuralType edmSpaceType)
		{
			return this.GetObjectSpaceType<StructuralType>(edmSpaceType);
		}

		// Token: 0x06003E43 RID: 15939 RVA: 0x000CE00F File Offset: 0x000CC20F
		public virtual bool TryGetObjectSpaceType(StructuralType edmSpaceType, out StructuralType objectSpaceType)
		{
			return this.TryGetObjectSpaceType<StructuralType>(edmSpaceType, out objectSpaceType);
		}

		// Token: 0x06003E44 RID: 15940 RVA: 0x000CE019 File Offset: 0x000CC219
		public virtual EnumType GetObjectSpaceType(EnumType edmSpaceType)
		{
			return this.GetObjectSpaceType<EnumType>(edmSpaceType);
		}

		// Token: 0x06003E45 RID: 15941 RVA: 0x000CE022 File Offset: 0x000CC222
		public virtual bool TryGetObjectSpaceType(EnumType edmSpaceType, out EnumType objectSpaceType)
		{
			return this.TryGetObjectSpaceType<EnumType>(edmSpaceType, out objectSpaceType);
		}

		// Token: 0x06003E46 RID: 15942 RVA: 0x000CE02C File Offset: 0x000CC22C
		private T GetObjectSpaceType<T>(T edmSpaceType) where T : EdmType
		{
			T t;
			if (!this.TryGetObjectSpaceType<T>(edmSpaceType, out t))
			{
				throw new ArgumentException(Strings.FailedToFindOSpaceTypeMapping(edmSpaceType.Identity));
			}
			return t;
		}

		// Token: 0x06003E47 RID: 15943 RVA: 0x000CE05C File Offset: 0x000CC25C
		private bool TryGetObjectSpaceType<T>(T edmSpaceType, out T objectSpaceType) where T : EdmType
		{
			if (edmSpaceType.DataSpace != DataSpace.CSpace)
			{
				throw new ArgumentException(Strings.ArgumentMustBeCSpaceType, "edmSpaceType");
			}
			objectSpaceType = default(T);
			MappingBase mappingBase;
			if (this.TryGetMap(edmSpaceType, DataSpace.OCSpace, out mappingBase))
			{
				ObjectTypeMapping objectTypeMapping = mappingBase as ObjectTypeMapping;
				if (objectTypeMapping != null)
				{
					objectSpaceType = (T)((object)objectTypeMapping.ClrType);
				}
			}
			return objectSpaceType != null;
		}

		// Token: 0x06003E48 RID: 15944 RVA: 0x000CE0C8 File Offset: 0x000CC2C8
		public virtual StructuralType GetEdmSpaceType(StructuralType objectSpaceType)
		{
			return this.GetEdmSpaceType<StructuralType>(objectSpaceType);
		}

		// Token: 0x06003E49 RID: 15945 RVA: 0x000CE0D1 File Offset: 0x000CC2D1
		public virtual bool TryGetEdmSpaceType(StructuralType objectSpaceType, out StructuralType edmSpaceType)
		{
			return this.TryGetEdmSpaceType<StructuralType>(objectSpaceType, out edmSpaceType);
		}

		// Token: 0x06003E4A RID: 15946 RVA: 0x000CE0DB File Offset: 0x000CC2DB
		public virtual EnumType GetEdmSpaceType(EnumType objectSpaceType)
		{
			return this.GetEdmSpaceType<EnumType>(objectSpaceType);
		}

		// Token: 0x06003E4B RID: 15947 RVA: 0x000CE0E4 File Offset: 0x000CC2E4
		public virtual bool TryGetEdmSpaceType(EnumType objectSpaceType, out EnumType edmSpaceType)
		{
			return this.TryGetEdmSpaceType<EnumType>(objectSpaceType, out edmSpaceType);
		}

		// Token: 0x06003E4C RID: 15948 RVA: 0x000CE0F0 File Offset: 0x000CC2F0
		private T GetEdmSpaceType<T>(T objectSpaceType) where T : EdmType
		{
			T t;
			if (!this.TryGetEdmSpaceType<T>(objectSpaceType, out t))
			{
				throw new ArgumentException(Strings.FailedToFindCSpaceTypeMapping(objectSpaceType.Identity));
			}
			return t;
		}

		// Token: 0x06003E4D RID: 15949 RVA: 0x000CE120 File Offset: 0x000CC320
		private bool TryGetEdmSpaceType<T>(T objectSpaceType, out T edmSpaceType) where T : EdmType
		{
			if (objectSpaceType.DataSpace != DataSpace.OSpace)
			{
				throw new ArgumentException(Strings.ArgumentMustBeOSpaceType, "objectSpaceType");
			}
			edmSpaceType = default(T);
			MappingBase mappingBase;
			if (this.TryGetMap(objectSpaceType, DataSpace.OCSpace, out mappingBase))
			{
				ObjectTypeMapping objectTypeMapping = mappingBase as ObjectTypeMapping;
				if (objectTypeMapping != null)
				{
					edmSpaceType = (T)((object)objectTypeMapping.EdmType);
				}
			}
			return edmSpaceType != null;
		}

		// Token: 0x06003E4E RID: 15950 RVA: 0x000CE18B File Offset: 0x000CC38B
		internal virtual DbQueryCommandTree GetCqtView(EntitySetBase extent)
		{
			return this.GetGeneratedView(extent).GetCommandTree();
		}

		// Token: 0x06003E4F RID: 15951 RVA: 0x000CE199 File Offset: 0x000CC399
		internal virtual GeneratedView GetGeneratedView(EntitySetBase extent)
		{
			return ((StorageMappingItemCollection)this.GetItemCollection(DataSpace.CSSpace, true)).GetGeneratedView(extent, this);
		}

		// Token: 0x06003E50 RID: 15952 RVA: 0x000CE1AF File Offset: 0x000CC3AF
		internal virtual bool TryGetGeneratedViewOfType(EntitySetBase extent, EntityTypeBase type, bool includeSubtypes, out GeneratedView generatedView)
		{
			return ((StorageMappingItemCollection)this.GetItemCollection(DataSpace.CSSpace, true)).TryGetGeneratedViewOfType(extent, type, includeSubtypes, out generatedView);
		}

		// Token: 0x06003E51 RID: 15953 RVA: 0x000CE1C8 File Offset: 0x000CC3C8
		internal virtual DbLambda GetGeneratedFunctionDefinition(EdmFunction function)
		{
			return ((EdmItemCollection)this.GetItemCollection(DataSpace.CSpace, true)).GetGeneratedFunctionDefinition(function);
		}

		// Token: 0x06003E52 RID: 15954 RVA: 0x000CE1E0 File Offset: 0x000CC3E0
		internal virtual bool TryGetFunctionImportMapping(EdmFunction functionImport, out FunctionImportMapping targetFunctionMapping)
		{
			using (IEnumerator<EntityContainerMapping> enumerator = this.GetItems<EntityContainerMapping>(DataSpace.CSSpace).GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					if (enumerator.Current.TryGetFunctionImportMapping(functionImport, out targetFunctionMapping))
					{
						return true;
					}
				}
			}
			targetFunctionMapping = null;
			return false;
		}

		// Token: 0x06003E53 RID: 15955 RVA: 0x000CE23C File Offset: 0x000CC43C
		internal virtual ViewLoader GetUpdateViewLoader()
		{
			if (this._itemsCSSpace == null || this._itemsCSSpace.Value == null)
			{
				return null;
			}
			return this._itemsCSSpace.Value.GetUpdateViewLoader();
		}

		// Token: 0x06003E54 RID: 15956 RVA: 0x000CE268 File Offset: 0x000CC468
		internal virtual TypeUsage GetOSpaceTypeUsage(TypeUsage edmSpaceTypeUsage)
		{
			EdmType edmType;
			if (Helper.IsPrimitiveType(edmSpaceTypeUsage.EdmType))
			{
				edmType = this.GetItemCollection(DataSpace.OSpace, true).GetMappedPrimitiveType(((PrimitiveType)edmSpaceTypeUsage.EdmType).PrimitiveTypeKind);
			}
			else
			{
				edmType = ((ObjectTypeMapping)((DefaultObjectMappingItemCollection)this.GetItemCollection(DataSpace.OCSpace, true)).GetMap(edmSpaceTypeUsage.EdmType)).ClrType;
			}
			return TypeUsage.Create(edmType, edmSpaceTypeUsage.Facets);
		}

		// Token: 0x06003E55 RID: 15957 RVA: 0x000CE2D4 File Offset: 0x000CC4D4
		internal virtual bool IsItemCollectionAlreadyRegistered(DataSpace dataSpace)
		{
			ItemCollection itemCollection;
			return this.TryGetItemCollection(dataSpace, out itemCollection);
		}

		// Token: 0x06003E56 RID: 15958 RVA: 0x000CE2EA File Offset: 0x000CC4EA
		internal virtual bool IsMetadataWorkspaceCSCompatible(MetadataWorkspace other)
		{
			return this.GetItemCollection(DataSpace.CSSpace, false).MetadataEquals(other.GetItemCollection(DataSpace.CSSpace, false));
		}

		// Token: 0x06003E57 RID: 15959 RVA: 0x000CE304 File Offset: 0x000CC504
		public static void ClearCache()
		{
			MetadataCache.Instance.Clear();
			using (LockedAssemblyCache lockedAssemblyCache = AssemblyCache.AcquireLockedAssemblyCache())
			{
				lockedAssemblyCache.Clear();
			}
		}

		// Token: 0x06003E58 RID: 15960 RVA: 0x000CE344 File Offset: 0x000CC544
		internal static TypeUsage GetCanonicalModelTypeUsage(PrimitiveTypeKind primitiveTypeKind)
		{
			return EdmProviderManifest.Instance.GetCanonicalModelTypeUsage(primitiveTypeKind);
		}

		// Token: 0x06003E59 RID: 15961 RVA: 0x000CE351 File Offset: 0x000CC551
		internal static PrimitiveType GetModelPrimitiveType(PrimitiveTypeKind primitiveTypeKind)
		{
			return EdmProviderManifest.Instance.GetPrimitiveType(primitiveTypeKind);
		}

		// Token: 0x06003E5A RID: 15962 RVA: 0x000CE35E File Offset: 0x000CC55E
		[Obsolete("Use MetadataWorkspace.GetRelevantMembersForUpdate(EntitySetBase, EntityTypeBase, bool) instead")]
		public virtual IEnumerable<EdmMember> GetRequiredOriginalValueMembers(EntitySetBase entitySet, EntityTypeBase entityType)
		{
			return this.GetInterestingMembers(entitySet, entityType, StorageMappingItemCollection.InterestingMembersKind.RequiredOriginalValueMembers);
		}

		// Token: 0x06003E5B RID: 15963 RVA: 0x000CE369 File Offset: 0x000CC569
		public virtual ReadOnlyCollection<EdmMember> GetRelevantMembersForUpdate(EntitySetBase entitySet, EntityTypeBase entityType, bool partialUpdateSupported)
		{
			return this.GetInterestingMembers(entitySet, entityType, partialUpdateSupported ? StorageMappingItemCollection.InterestingMembersKind.PartialUpdate : StorageMappingItemCollection.InterestingMembersKind.FullUpdate);
		}

		// Token: 0x06003E5C RID: 15964 RVA: 0x000CE37C File Offset: 0x000CC57C
		private ReadOnlyCollection<EdmMember> GetInterestingMembers(EntitySetBase entitySet, EntityTypeBase entityType, StorageMappingItemCollection.InterestingMembersKind interestingMembersKind)
		{
			AssociationSet associationSet = entitySet as AssociationSet;
			if (entitySet.EntityContainer.DataSpace != DataSpace.CSpace)
			{
				throw new ArgumentException(Strings.EntitySetNotInCSPace(entitySet.Name));
			}
			if (entitySet.ElementType.IsAssignableFrom(entityType))
			{
				return ((StorageMappingItemCollection)this.GetItemCollection(DataSpace.CSSpace, true)).GetInterestingMembers(entitySet, entityType, interestingMembersKind);
			}
			if (associationSet != null)
			{
				throw new ArgumentException(Strings.TypeNotInAssociationSet(entityType.FullName, entitySet.ElementType.FullName, entitySet.Name));
			}
			throw new ArgumentException(Strings.TypeNotInEntitySet(entityType.FullName, entitySet.ElementType.FullName, entitySet.Name));
		}

		// Token: 0x06003E5D RID: 15965 RVA: 0x000CE41B File Offset: 0x000CC61B
		internal virtual QueryCacheManager GetQueryCacheManager()
		{
			return this._itemsSSpace.Value.QueryCacheManager;
		}

		// Token: 0x06003E5E RID: 15966 RVA: 0x000CE42D File Offset: 0x000CC62D
		internal bool TryDetermineCSpaceModelType<T>(out EdmType modelEdmType)
		{
			return this.TryDetermineCSpaceModelType(typeof(T), out modelEdmType);
		}

		// Token: 0x06003E5F RID: 15967 RVA: 0x000CE440 File Offset: 0x000CC640
		internal virtual bool TryDetermineCSpaceModelType(Type type, out EdmType modelEdmType)
		{
			Type nonNullableType = TypeSystem.GetNonNullableType(type);
			this.ImplicitLoadAssemblyForType(nonNullableType, Assembly.GetCallingAssembly());
			EdmType edmType;
			MappingBase mappingBase;
			if (((ObjectItemCollection)this.GetItemCollection(DataSpace.OSpace)).TryGetItem<EdmType>(nonNullableType.FullNameWithNesting(), out edmType) && this.TryGetMap(edmType, DataSpace.OCSpace, out mappingBase))
			{
				ObjectTypeMapping objectTypeMapping = (ObjectTypeMapping)mappingBase;
				modelEdmType = objectTypeMapping.EdmType;
				return true;
			}
			modelEdmType = null;
			return false;
		}

		// Token: 0x04001523 RID: 5411
		private Lazy<EdmItemCollection> _itemsCSpace;

		// Token: 0x04001524 RID: 5412
		private Lazy<StoreItemCollection> _itemsSSpace;

		// Token: 0x04001525 RID: 5413
		private Lazy<ObjectItemCollection> _itemsOSpace;

		// Token: 0x04001526 RID: 5414
		private Lazy<StorageMappingItemCollection> _itemsCSSpace;

		// Token: 0x04001527 RID: 5415
		private Lazy<DefaultObjectMappingItemCollection> _itemsOCSpace;

		// Token: 0x04001528 RID: 5416
		private bool _foundAssemblyWithAttribute;

		// Token: 0x04001529 RID: 5417
		private double _schemaVersion;

		// Token: 0x0400152A RID: 5418
		private readonly object _schemaVersionLock = new object();

		// Token: 0x0400152B RID: 5419
		private readonly Guid _metadataWorkspaceId = Guid.NewGuid();

		// Token: 0x0400152C RID: 5420
		internal readonly MetadataOptimization MetadataOptimization;

		// Token: 0x0400152D RID: 5421
		private static readonly double _maximumEdmVersionSupported = MetadataWorkspace.SupportedEdmVersions.Last<double>();
	}
}
