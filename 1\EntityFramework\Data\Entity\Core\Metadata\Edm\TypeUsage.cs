﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x02000501 RID: 1281
	[DebuggerDisplay("EdmType={EdmType}, Facets.Count={Facets.Count}")]
	public class TypeUsage : MetadataItem
	{
		// Token: 0x06003F89 RID: 16265 RVA: 0x000D27B5 File Offset: 0x000D09B5
		internal TypeUsage()
		{
		}

		// Token: 0x06003F8A RID: 16266 RVA: 0x000D27BD File Offset: 0x000D09BD
		private TypeUsage(EdmType edmType)
			: base(MetadataItem.MetadataFlags.Readonly)
		{
			Check.NotNull<EdmType>(edmType, "edmType");
			this._edmType = edmType;
		}

		// Token: 0x06003F8B RID: 16267 RVA: 0x000D27DC File Offset: 0x000D09DC
		private TypeUsage(EdmType edmType, IEnumerable<Facet> facets)
			: this(edmType)
		{
			MetadataCollection<Facet> metadataCollection = MetadataCollection<Facet>.Wrap(facets.ToList<Facet>());
			metadataCollection.SetReadOnly();
			this._facets = metadataCollection.AsReadOnlyMetadataCollection();
		}

		// Token: 0x06003F8C RID: 16268 RVA: 0x000D280F File Offset: 0x000D0A0F
		internal static TypeUsage Create(EdmType edmType)
		{
			return new TypeUsage(edmType);
		}

		// Token: 0x06003F8D RID: 16269 RVA: 0x000D2817 File Offset: 0x000D0A17
		internal static TypeUsage Create(EdmType edmType, FacetValues values)
		{
			return new TypeUsage(edmType, TypeUsage.GetDefaultFacetDescriptionsAndOverrideFacetValues(edmType, values));
		}

		// Token: 0x06003F8E RID: 16270 RVA: 0x000D2826 File Offset: 0x000D0A26
		public static TypeUsage Create(EdmType edmType, IEnumerable<Facet> facets)
		{
			return new TypeUsage(edmType, facets);
		}

		// Token: 0x06003F8F RID: 16271 RVA: 0x000D282F File Offset: 0x000D0A2F
		internal TypeUsage ShallowCopy(FacetValues facetValues)
		{
			return TypeUsage.Create(this._edmType, TypeUsage.OverrideFacetValues(this.Facets, facetValues));
		}

		// Token: 0x06003F90 RID: 16272 RVA: 0x000D2848 File Offset: 0x000D0A48
		internal TypeUsage ShallowCopy(params Facet[] facetValues)
		{
			return TypeUsage.Create(this._edmType, TypeUsage.OverrideFacetValues(this.Facets, facetValues));
		}

		// Token: 0x06003F91 RID: 16273 RVA: 0x000D2861 File Offset: 0x000D0A61
		private static IEnumerable<Facet> OverrideFacetValues(IEnumerable<Facet> facets, IEnumerable<Facet> facetValues)
		{
			return facets.Except(facetValues, (Facet f1, Facet f2) => f1.EdmEquals(f2)).Union(facetValues);
		}

		// Token: 0x06003F92 RID: 16274 RVA: 0x000D288F File Offset: 0x000D0A8F
		public static TypeUsage CreateDefaultTypeUsage(EdmType edmType)
		{
			Check.NotNull<EdmType>(edmType, "edmType");
			return TypeUsage.Create(edmType);
		}

		// Token: 0x06003F93 RID: 16275 RVA: 0x000D28A4 File Offset: 0x000D0AA4
		public static TypeUsage CreateStringTypeUsage(PrimitiveType primitiveType, bool isUnicode, bool isFixedLength, int maxLength)
		{
			Check.NotNull<PrimitiveType>(primitiveType, "primitiveType");
			if (primitiveType.PrimitiveTypeKind != PrimitiveTypeKind.String)
			{
				throw new ArgumentException(Strings.NotStringTypeForTypeUsage);
			}
			TypeUsage.ValidateMaxLength(maxLength);
			return TypeUsage.Create(primitiveType, new FacetValues
			{
				MaxLength = new int?(maxLength),
				Unicode = new bool?(isUnicode),
				FixedLength = new bool?(isFixedLength)
			});
		}

		// Token: 0x06003F94 RID: 16276 RVA: 0x000D2918 File Offset: 0x000D0B18
		public static TypeUsage CreateStringTypeUsage(PrimitiveType primitiveType, bool isUnicode, bool isFixedLength)
		{
			Check.NotNull<PrimitiveType>(primitiveType, "primitiveType");
			if (primitiveType.PrimitiveTypeKind != PrimitiveTypeKind.String)
			{
				throw new ArgumentException(Strings.NotStringTypeForTypeUsage);
			}
			return TypeUsage.Create(primitiveType, new FacetValues
			{
				MaxLength = TypeUsage.DefaultMaxLengthFacetValue,
				Unicode = new bool?(isUnicode),
				FixedLength = new bool?(isFixedLength)
			});
		}

		// Token: 0x06003F95 RID: 16277 RVA: 0x000D2984 File Offset: 0x000D0B84
		public static TypeUsage CreateBinaryTypeUsage(PrimitiveType primitiveType, bool isFixedLength, int maxLength)
		{
			Check.NotNull<PrimitiveType>(primitiveType, "primitiveType");
			if (primitiveType.PrimitiveTypeKind != PrimitiveTypeKind.Binary)
			{
				throw new ArgumentException(Strings.NotBinaryTypeForTypeUsage);
			}
			TypeUsage.ValidateMaxLength(maxLength);
			return TypeUsage.Create(primitiveType, new FacetValues
			{
				MaxLength = new int?(maxLength),
				FixedLength = new bool?(isFixedLength)
			});
		}

		// Token: 0x06003F96 RID: 16278 RVA: 0x000D29E4 File Offset: 0x000D0BE4
		public static TypeUsage CreateBinaryTypeUsage(PrimitiveType primitiveType, bool isFixedLength)
		{
			Check.NotNull<PrimitiveType>(primitiveType, "primitiveType");
			if (primitiveType.PrimitiveTypeKind != PrimitiveTypeKind.Binary)
			{
				throw new ArgumentException(Strings.NotBinaryTypeForTypeUsage);
			}
			return TypeUsage.Create(primitiveType, new FacetValues
			{
				MaxLength = TypeUsage.DefaultMaxLengthFacetValue,
				FixedLength = new bool?(isFixedLength)
			});
		}

		// Token: 0x06003F97 RID: 16279 RVA: 0x000D2A3C File Offset: 0x000D0C3C
		public static TypeUsage CreateDateTimeTypeUsage(PrimitiveType primitiveType, byte? precision)
		{
			Check.NotNull<PrimitiveType>(primitiveType, "primitiveType");
			if (primitiveType.PrimitiveTypeKind != PrimitiveTypeKind.DateTime)
			{
				throw new ArgumentException(Strings.NotDateTimeTypeForTypeUsage);
			}
			return TypeUsage.Create(primitiveType, new FacetValues
			{
				Precision = precision
			});
		}

		// Token: 0x06003F98 RID: 16280 RVA: 0x000D2A75 File Offset: 0x000D0C75
		public static TypeUsage CreateDateTimeOffsetTypeUsage(PrimitiveType primitiveType, byte? precision)
		{
			Check.NotNull<PrimitiveType>(primitiveType, "primitiveType");
			if (primitiveType.PrimitiveTypeKind != PrimitiveTypeKind.DateTimeOffset)
			{
				throw new ArgumentException(Strings.NotDateTimeOffsetTypeForTypeUsage);
			}
			return TypeUsage.Create(primitiveType, new FacetValues
			{
				Precision = precision
			});
		}

		// Token: 0x06003F99 RID: 16281 RVA: 0x000D2AAF File Offset: 0x000D0CAF
		public static TypeUsage CreateTimeTypeUsage(PrimitiveType primitiveType, byte? precision)
		{
			Check.NotNull<PrimitiveType>(primitiveType, "primitiveType");
			if (primitiveType.PrimitiveTypeKind != PrimitiveTypeKind.Time)
			{
				throw new ArgumentException(Strings.NotTimeTypeForTypeUsage);
			}
			return TypeUsage.Create(primitiveType, new FacetValues
			{
				Precision = precision
			});
		}

		// Token: 0x06003F9A RID: 16282 RVA: 0x000D2AEC File Offset: 0x000D0CEC
		public static TypeUsage CreateDecimalTypeUsage(PrimitiveType primitiveType, byte precision, byte scale)
		{
			Check.NotNull<PrimitiveType>(primitiveType, "primitiveType");
			if (primitiveType.PrimitiveTypeKind != PrimitiveTypeKind.Decimal)
			{
				throw new ArgumentException(Strings.NotDecimalTypeForTypeUsage);
			}
			return TypeUsage.Create(primitiveType, new FacetValues
			{
				Precision = new byte?(precision),
				Scale = new byte?(scale)
			});
		}

		// Token: 0x06003F9B RID: 16283 RVA: 0x000D2B48 File Offset: 0x000D0D48
		public static TypeUsage CreateDecimalTypeUsage(PrimitiveType primitiveType)
		{
			Check.NotNull<PrimitiveType>(primitiveType, "primitiveType");
			if (primitiveType.PrimitiveTypeKind != PrimitiveTypeKind.Decimal)
			{
				throw new ArgumentException(Strings.NotDecimalTypeForTypeUsage);
			}
			return TypeUsage.Create(primitiveType, new FacetValues
			{
				Precision = TypeUsage.DefaultPrecisionFacetValue,
				Scale = TypeUsage.DefaultScaleFacetValue
			});
		}

		// Token: 0x17000C6F RID: 3183
		// (get) Token: 0x06003F9C RID: 16284 RVA: 0x000D2BA0 File Offset: 0x000D0DA0
		public override BuiltInTypeKind BuiltInTypeKind
		{
			get
			{
				return BuiltInTypeKind.TypeUsage;
			}
		}

		// Token: 0x17000C70 RID: 3184
		// (get) Token: 0x06003F9D RID: 16285 RVA: 0x000D2BA4 File Offset: 0x000D0DA4
		[MetadataProperty(BuiltInTypeKind.EdmType, false)]
		public virtual EdmType EdmType
		{
			get
			{
				return this._edmType;
			}
		}

		// Token: 0x17000C71 RID: 3185
		// (get) Token: 0x06003F9E RID: 16286 RVA: 0x000D2BAC File Offset: 0x000D0DAC
		[MetadataProperty(BuiltInTypeKind.Facet, true)]
		public virtual ReadOnlyMetadataCollection<Facet> Facets
		{
			get
			{
				if (this._facets == null)
				{
					MetadataCollection<Facet> metadataCollection = new MetadataCollection<Facet>(this.GetFacets());
					metadataCollection.SetReadOnly();
					Interlocked.CompareExchange<ReadOnlyMetadataCollection<Facet>>(ref this._facets, metadataCollection.AsReadOnlyMetadataCollection(), null);
				}
				return this._facets;
			}
		}

		// Token: 0x17000C72 RID: 3186
		// (get) Token: 0x06003F9F RID: 16287 RVA: 0x000D2BF0 File Offset: 0x000D0DF0
		public TypeUsage ModelTypeUsage
		{
			get
			{
				if (this._modelTypeUsage == null)
				{
					EdmType edmType = this.EdmType;
					if (edmType.DataSpace == DataSpace.CSpace || edmType.DataSpace == DataSpace.OSpace)
					{
						return this;
					}
					TypeUsage typeUsage;
					if (Helper.IsRowType(edmType))
					{
						RowType rowType = (RowType)edmType;
						EdmProperty[] array = new EdmProperty[rowType.Properties.Count];
						for (int i = 0; i < array.Length; i++)
						{
							EdmProperty edmProperty = rowType.Properties[i];
							TypeUsage modelTypeUsage = edmProperty.TypeUsage.ModelTypeUsage;
							array[i] = new EdmProperty(edmProperty.Name, modelTypeUsage);
						}
						typeUsage = TypeUsage.Create(new RowType(array, rowType.InitializerMetadata), this.Facets);
					}
					else if (Helper.IsCollectionType(edmType))
					{
						typeUsage = TypeUsage.Create(new CollectionType(((CollectionType)edmType).TypeUsage.ModelTypeUsage), this.Facets);
					}
					else if (Helper.IsPrimitiveType(edmType))
					{
						typeUsage = ((PrimitiveType)edmType).ProviderManifest.GetEdmType(this);
						if (typeUsage == null)
						{
							throw new ProviderIncompatibleException(Strings.Mapping_ProviderReturnsNullType(this.ToString()));
						}
						if (!TypeSemantics.IsNullable(this))
						{
							typeUsage = TypeUsage.Create(typeUsage.EdmType, TypeUsage.OverrideFacetValues(typeUsage.Facets, new FacetValues
							{
								Nullable = new bool?(false)
							}));
						}
					}
					else
					{
						if (!Helper.IsEntityTypeBase(edmType) && !Helper.IsComplexType(edmType))
						{
							return null;
						}
						typeUsage = this;
					}
					Interlocked.CompareExchange<TypeUsage>(ref this._modelTypeUsage, typeUsage, null);
				}
				return this._modelTypeUsage;
			}
		}

		// Token: 0x06003FA0 RID: 16288 RVA: 0x000D2D5A File Offset: 0x000D0F5A
		public bool IsSubtypeOf(TypeUsage typeUsage)
		{
			return this.EdmType != null && typeUsage != null && this.EdmType.IsSubtypeOf(typeUsage.EdmType);
		}

		// Token: 0x06003FA1 RID: 16289 RVA: 0x000D2D7A File Offset: 0x000D0F7A
		private IEnumerable<Facet> GetFacets()
		{
			return from facetDescription in this._edmType.GetAssociatedFacetDescriptions()
				select facetDescription.DefaultValueFacet;
		}

		// Token: 0x06003FA2 RID: 16290 RVA: 0x000D2DAB File Offset: 0x000D0FAB
		internal override void SetReadOnly()
		{
			base.SetReadOnly();
		}

		// Token: 0x17000C73 RID: 3187
		// (get) Token: 0x06003FA3 RID: 16291 RVA: 0x000D2DB4 File Offset: 0x000D0FB4
		internal override string Identity
		{
			get
			{
				if (this.Facets.Count == 0)
				{
					return this.EdmType.Identity;
				}
				if (this._identity == null)
				{
					StringBuilder stringBuilder = new StringBuilder(128);
					this.BuildIdentity(stringBuilder);
					string text = stringBuilder.ToString();
					Interlocked.CompareExchange<string>(ref this._identity, text, null);
				}
				return this._identity;
			}
		}

		// Token: 0x06003FA4 RID: 16292 RVA: 0x000D2E10 File Offset: 0x000D1010
		private static IEnumerable<Facet> GetDefaultFacetDescriptionsAndOverrideFacetValues(EdmType type, FacetValues values)
		{
			return TypeUsage.OverrideFacetValues<FacetDescription>(type.GetAssociatedFacetDescriptions(), (FacetDescription fd) => fd, (FacetDescription fd) => fd.DefaultValueFacet, values);
		}

		// Token: 0x06003FA5 RID: 16293 RVA: 0x000D2E68 File Offset: 0x000D1068
		private static IEnumerable<Facet> OverrideFacetValues(IEnumerable<Facet> facets, FacetValues values)
		{
			return TypeUsage.OverrideFacetValues<Facet>(facets, (Facet f) => f.Description, (Facet f) => f, values);
		}

		// Token: 0x06003FA6 RID: 16294 RVA: 0x000D2EBA File Offset: 0x000D10BA
		private static IEnumerable<Facet> OverrideFacetValues<T>(IEnumerable<T> facetThings, Func<T, FacetDescription> getDescription, Func<T, Facet> getFacet, FacetValues values)
		{
			foreach (T t in facetThings)
			{
				FacetDescription facetDescription = getDescription(t);
				Facet facet;
				if (!facetDescription.IsConstant && values.TryGetFacet(facetDescription, out facet))
				{
					yield return facet;
				}
				else
				{
					yield return getFacet(t);
				}
			}
			IEnumerator<T> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x06003FA7 RID: 16295 RVA: 0x000D2EE0 File Offset: 0x000D10E0
		internal override void BuildIdentity(StringBuilder builder)
		{
			if (this._identity != null)
			{
				builder.Append(this._identity);
				return;
			}
			builder.Append(this.EdmType.Identity);
			builder.Append("(");
			bool flag = true;
			for (int i = 0; i < this.Facets.Count; i++)
			{
				Facet facet = this.Facets[i];
				if (0 <= Array.BinarySearch<string>(TypeUsage._identityFacets, facet.Name, StringComparer.Ordinal))
				{
					if (flag)
					{
						flag = false;
					}
					else
					{
						builder.Append(",");
					}
					builder.Append(facet.Name);
					builder.Append("=");
					builder.Append(facet.Value ?? string.Empty);
				}
			}
			builder.Append(")");
		}

		// Token: 0x06003FA8 RID: 16296 RVA: 0x000D2FAC File Offset: 0x000D11AC
		public override string ToString()
		{
			return this.EdmType.ToString();
		}

		// Token: 0x06003FA9 RID: 16297 RVA: 0x000D2FBC File Offset: 0x000D11BC
		internal override bool EdmEquals(MetadataItem item)
		{
			if (this == item)
			{
				return true;
			}
			if (item == null || BuiltInTypeKind.TypeUsage != item.BuiltInTypeKind)
			{
				return false;
			}
			TypeUsage typeUsage = (TypeUsage)item;
			if (!this.EdmType.EdmEquals(typeUsage.EdmType))
			{
				return false;
			}
			if (this._facets == null && typeUsage._facets == null)
			{
				return true;
			}
			if (this.Facets.Count != typeUsage.Facets.Count)
			{
				return false;
			}
			foreach (Facet facet in this.Facets)
			{
				Facet facet2;
				if (!typeUsage.Facets.TryGetValue(facet.Name, false, out facet2))
				{
					return false;
				}
				if (!object.Equals(facet.Value, facet2.Value))
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x06003FAA RID: 16298 RVA: 0x000D309C File Offset: 0x000D129C
		private static void ValidateMaxLength(int maxLength)
		{
			if (maxLength <= 0)
			{
				throw new ArgumentOutOfRangeException("maxLength", Strings.InvalidMaxLengthSize);
			}
		}

		// Token: 0x04001598 RID: 5528
		private TypeUsage _modelTypeUsage;

		// Token: 0x04001599 RID: 5529
		private readonly EdmType _edmType;

		// Token: 0x0400159A RID: 5530
		private ReadOnlyMetadataCollection<Facet> _facets;

		// Token: 0x0400159B RID: 5531
		private string _identity;

		// Token: 0x0400159C RID: 5532
		private static readonly string[] _identityFacets = new string[] { "DefaultValue", "FixedLength", "MaxLength", "Nullable", "Precision", "Scale", "Unicode", "SRID" };

		// Token: 0x0400159D RID: 5533
		internal static readonly EdmConstants.Unbounded DefaultMaxLengthFacetValue = EdmConstants.UnboundedValue;

		// Token: 0x0400159E RID: 5534
		internal static readonly EdmConstants.Unbounded DefaultPrecisionFacetValue = EdmConstants.UnboundedValue;

		// Token: 0x0400159F RID: 5535
		internal static readonly EdmConstants.Unbounded DefaultScaleFacetValue = EdmConstants.UnboundedValue;

		// Token: 0x040015A0 RID: 5536
		internal const bool DefaultUnicodeFacetValue = true;

		// Token: 0x040015A1 RID: 5537
		internal const bool DefaultFixedLengthFacetValue = false;

		// Token: 0x040015A2 RID: 5538
		internal static readonly byte? DefaultDateTimePrecisionFacetValue = null;
	}
}
