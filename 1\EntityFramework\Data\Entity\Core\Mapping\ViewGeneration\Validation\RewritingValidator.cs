﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Common.Utils.Boolean;
using System.Data.Entity.Core.Mapping.ViewGeneration.QueryRewriting;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Globalization;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Validation
{
	// Token: 0x02000576 RID: 1398
	internal class RewritingValidator
	{
		// Token: 0x060043EF RID: 17391 RVA: 0x000EBF2C File Offset: 0x000EA12C
		internal RewritingValidator(ViewgenContext context, CellTreeNode basicView)
		{
			this._viewgenContext = context;
			this._basicView = basicView;
			this._domainMap = this._viewgenContext.MemberMaps.UpdateDomainMap;
			this._keyAttributes = MemberPath.GetKeyMembers(this._viewgenContext.Extent, this._domainMap);
			this._errorLog = new ErrorLog();
		}

		// Token: 0x060043F0 RID: 17392 RVA: 0x000EBF8C File Offset: 0x000EA18C
		internal void Validate()
		{
			Dictionary<RewritingValidator.MemberValueBinding, CellTreeNode> dictionary = this.CreateMemberValueTrees(false);
			Dictionary<RewritingValidator.MemberValueBinding, CellTreeNode> dictionary2 = this.CreateMemberValueTrees(true);
			RewritingValidator.WhereClauseVisitor whereClauseVisitor = new RewritingValidator.WhereClauseVisitor(this._basicView, dictionary);
			RewritingValidator.WhereClauseVisitor whereClauseVisitor2 = new RewritingValidator.WhereClauseVisitor(this._basicView, dictionary2);
			foreach (LeftCellWrapper leftCellWrapper in this._viewgenContext.AllWrappersForExtent)
			{
				Cell onlyInputCell = leftCellWrapper.OnlyInputCell;
				CellTreeNode cellTreeNode = new LeafCellTreeNode(this._viewgenContext, leftCellWrapper);
				CellTreeNode cellTreeNode2 = whereClauseVisitor2.GetCellTreeNode(onlyInputCell.SQuery.WhereClause);
				if (cellTreeNode2 != null)
				{
					CellTreeNode cellTreeNode3;
					if (cellTreeNode2 != this._basicView)
					{
						cellTreeNode3 = new OpCellTreeNode(this._viewgenContext, CellTreeOpType.IJ, new CellTreeNode[] { cellTreeNode2, this._basicView });
					}
					else
					{
						cellTreeNode3 = this._basicView;
					}
					BoolExpression boolExpression = BoolExpression.CreateLiteral(leftCellWrapper.CreateRoleBoolean(), this._viewgenContext.MemberMaps.QueryDomainMap);
					BoolExpression boolExpression2;
					if (!this.CheckEquivalence(cellTreeNode.RightFragmentQuery, cellTreeNode3.RightFragmentQuery, boolExpression, out boolExpression2))
					{
						object obj = StringUtil.FormatInvariant("{0}", new object[] { this._viewgenContext.Extent });
						cellTreeNode.RightFragmentQuery.Condition.ExpensiveSimplify();
						cellTreeNode3.RightFragmentQuery.Condition.ExpensiveSimplify();
						string text = Strings.ViewGen_CQ_PartitionConstraint(obj);
						this.ReportConstraintViolation(text, boolExpression2, ViewGenErrorCode.PartitionConstraintViolation, cellTreeNode.GetLeaves().Concat(cellTreeNode3.GetLeaves()));
					}
					CellTreeNode cellTreeNode4 = whereClauseVisitor.GetCellTreeNode(onlyInputCell.SQuery.WhereClause);
					if (cellTreeNode4 != null)
					{
						RewritingValidator.DomainConstraintVisitor.CheckConstraints(cellTreeNode4, leftCellWrapper, this._viewgenContext, this._errorLog);
						if (this._errorLog.Count > 0)
						{
							continue;
						}
						this.CheckConstraintsOnProjectedConditionMembers(dictionary, leftCellWrapper, cellTreeNode3, boolExpression);
						if (this._errorLog.Count > 0)
						{
							continue;
						}
					}
					this.CheckConstraintsOnNonNullableMembers(leftCellWrapper);
				}
			}
			if (this._errorLog.Count > 0)
			{
				ExceptionHelpers.ThrowMappingException(this._errorLog, this._viewgenContext.Config);
			}
		}

		// Token: 0x060043F1 RID: 17393 RVA: 0x000EC1A8 File Offset: 0x000EA3A8
		private bool CheckEquivalence(FragmentQuery cQuery, FragmentQuery sQuery, BoolExpression inExtentCondition, out BoolExpression unsatisfiedConstraint)
		{
			FragmentQuery fragmentQuery = this._viewgenContext.RightFragmentQP.Difference(cQuery, sQuery);
			FragmentQuery fragmentQuery2 = this._viewgenContext.RightFragmentQP.Difference(sQuery, cQuery);
			FragmentQuery fragmentQuery3 = FragmentQuery.Create(BoolExpression.CreateAnd(new BoolExpression[] { fragmentQuery.Condition, inExtentCondition }));
			FragmentQuery fragmentQuery4 = FragmentQuery.Create(BoolExpression.CreateAnd(new BoolExpression[] { fragmentQuery2.Condition, inExtentCondition }));
			unsatisfiedConstraint = null;
			bool flag = true;
			bool flag2 = true;
			if (this._viewgenContext.RightFragmentQP.IsSatisfiable(fragmentQuery3))
			{
				unsatisfiedConstraint = fragmentQuery3.Condition;
				flag = false;
			}
			if (this._viewgenContext.RightFragmentQP.IsSatisfiable(fragmentQuery4))
			{
				unsatisfiedConstraint = fragmentQuery4.Condition;
				flag2 = false;
			}
			if (flag && flag2)
			{
				return true;
			}
			unsatisfiedConstraint.ExpensiveSimplify();
			return false;
		}

		// Token: 0x060043F2 RID: 17394 RVA: 0x000EC274 File Offset: 0x000EA474
		private void ReportConstraintViolation(string message, BoolExpression extraConstraint, ViewGenErrorCode errorCode, IEnumerable<LeftCellWrapper> relevantWrappers)
		{
			if (ErrorPatternMatcher.FindMappingErrors(this._viewgenContext, this._domainMap, this._errorLog))
			{
				return;
			}
			extraConstraint.ExpensiveSimplify();
			HashSet<LeftCellWrapper> hashSet = new HashSet<LeftCellWrapper>(relevantWrappers);
			new List<LeftCellWrapper>(hashSet).Sort(LeftCellWrapper.OriginalCellIdComparer);
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.AppendLine(message);
			RewritingValidator.EntityConfigurationToUserString(extraConstraint, stringBuilder);
			this._errorLog.AddEntry(new ErrorLog.Record(errorCode, stringBuilder.ToString(), hashSet, ""));
		}

		// Token: 0x060043F3 RID: 17395 RVA: 0x000EC2EC File Offset: 0x000EA4EC
		private Dictionary<RewritingValidator.MemberValueBinding, CellTreeNode> CreateMemberValueTrees(bool complementElse)
		{
			Dictionary<RewritingValidator.MemberValueBinding, CellTreeNode> dictionary = new Dictionary<RewritingValidator.MemberValueBinding, CellTreeNode>();
			foreach (MemberPath memberPath in this._domainMap.ConditionMembers(this._viewgenContext.Extent))
			{
				List<Constant> list = new List<Constant>(this._domainMap.GetDomain(memberPath));
				OpCellTreeNode opCellTreeNode = new OpCellTreeNode(this._viewgenContext, CellTreeOpType.Union);
				for (int i = 0; i < list.Count; i++)
				{
					Constant constant = list[i];
					RewritingValidator.MemberValueBinding memberValueBinding = new RewritingValidator.MemberValueBinding(memberPath, constant);
					FragmentQuery fragmentQuery = QueryRewriter.CreateMemberConditionQuery(memberPath, constant, this._keyAttributes, this._domainMap);
					Tile<FragmentQuery> tile;
					if (this._viewgenContext.TryGetCachedRewriting(fragmentQuery, out tile))
					{
						CellTreeNode cellTreeNode = QueryRewriter.TileToCellTree(tile, this._viewgenContext);
						dictionary[memberValueBinding] = cellTreeNode;
						if (i < list.Count - 1)
						{
							opCellTreeNode.Add(cellTreeNode);
						}
					}
				}
				if (complementElse && list.Count > 1)
				{
					Constant constant2 = list[list.Count - 1];
					RewritingValidator.MemberValueBinding memberValueBinding2 = new RewritingValidator.MemberValueBinding(memberPath, constant2);
					dictionary[memberValueBinding2] = new OpCellTreeNode(this._viewgenContext, CellTreeOpType.LASJ, new CellTreeNode[] { this._basicView, opCellTreeNode });
				}
			}
			return dictionary;
		}

		// Token: 0x060043F4 RID: 17396 RVA: 0x000EC448 File Offset: 0x000EA648
		private void CheckConstraintsOnProjectedConditionMembers(Dictionary<RewritingValidator.MemberValueBinding, CellTreeNode> memberValueTrees, LeftCellWrapper wrapper, CellTreeNode sQueryTree, BoolExpression inExtentCondition)
		{
			foreach (MemberPath memberPath in this._domainMap.ConditionMembers(this._viewgenContext.Extent))
			{
				int num = this._viewgenContext.MemberMaps.ProjectedSlotMap.IndexOf(memberPath);
				MemberProjectedSlot memberProjectedSlot = wrapper.RightCellQuery.ProjectedSlotAt(num) as MemberProjectedSlot;
				if (memberProjectedSlot != null)
				{
					foreach (Constant constant in this._domainMap.GetDomain(memberPath))
					{
						CellTreeNode cellTreeNode;
						if (memberValueTrees.TryGetValue(new RewritingValidator.MemberValueBinding(memberPath, constant), out cellTreeNode))
						{
							FragmentQuery fragmentQuery = FragmentQuery.Create(RewritingValidator.PropagateCellConstantsToWhereClause(wrapper, wrapper.RightCellQuery.WhereClause, constant, memberPath, this._viewgenContext.MemberMaps));
							CellTreeNode cellTreeNode2 = ((sQueryTree == this._basicView) ? cellTreeNode : new OpCellTreeNode(this._viewgenContext, CellTreeOpType.IJ, new CellTreeNode[] { cellTreeNode, sQueryTree }));
							BoolExpression boolExpression;
							if (!this.CheckEquivalence(fragmentQuery, cellTreeNode2.RightFragmentQuery, inExtentCondition, out boolExpression))
							{
								string text = Strings.ViewGen_CQ_DomainConstraint(memberProjectedSlot.ToUserString());
								this.ReportConstraintViolation(text, boolExpression, ViewGenErrorCode.DomainConstraintViolation, cellTreeNode2.GetLeaves().Concat(new LeftCellWrapper[] { wrapper }));
							}
						}
					}
				}
			}
		}

		// Token: 0x060043F5 RID: 17397 RVA: 0x000EC5E0 File Offset: 0x000EA7E0
		internal static BoolExpression PropagateCellConstantsToWhereClause(LeftCellWrapper wrapper, BoolExpression expression, Constant constant, MemberPath member, MemberMaps memberMaps)
		{
			MemberProjectedSlot csideMappedSlotForSMember = wrapper.GetCSideMappedSlotForSMember(member);
			if (csideMappedSlotForSMember == null)
			{
				return expression;
			}
			NegatedConstant negatedConstant = constant as NegatedConstant;
			IEnumerable<Constant> domain = memberMaps.QueryDomainMap.GetDomain(csideMappedSlotForSMember.MemberPath);
			Set<Constant> set = new Set<Constant>(Constant.EqualityComparer);
			if (negatedConstant != null)
			{
				set.Unite(domain);
				set.Difference(negatedConstant.Elements);
			}
			else
			{
				set.Add(constant);
			}
			MemberRestriction memberRestriction = new ScalarRestriction(csideMappedSlotForSMember.MemberPath, set, domain);
			return BoolExpression.CreateAnd(new BoolExpression[]
			{
				expression,
				BoolExpression.CreateLiteral(memberRestriction, memberMaps.QueryDomainMap)
			});
		}

		// Token: 0x060043F6 RID: 17398 RVA: 0x000EC670 File Offset: 0x000EA870
		private static FragmentQuery AddNullConditionOnCSideFragment(LeftCellWrapper wrapper, MemberPath member, MemberMaps memberMaps)
		{
			MemberProjectedSlot csideMappedSlotForSMember = wrapper.GetCSideMappedSlotForSMember(member);
			if (csideMappedSlotForSMember == null || !csideMappedSlotForSMember.MemberPath.IsNullable)
			{
				return null;
			}
			BoolExpression whereClause = wrapper.RightCellQuery.WhereClause;
			IEnumerable<Constant> domain = memberMaps.QueryDomainMap.GetDomain(csideMappedSlotForSMember.MemberPath);
			Set<Constant> set = new Set<Constant>(Constant.EqualityComparer);
			set.Add(Constant.Null);
			MemberRestriction memberRestriction = new ScalarRestriction(csideMappedSlotForSMember.MemberPath, set, domain);
			return FragmentQuery.Create(BoolExpression.CreateAnd(new BoolExpression[]
			{
				whereClause,
				BoolExpression.CreateLiteral(memberRestriction, memberMaps.QueryDomainMap)
			}));
		}

		// Token: 0x060043F7 RID: 17399 RVA: 0x000EC700 File Offset: 0x000EA900
		private void CheckConstraintsOnNonNullableMembers(LeftCellWrapper wrapper)
		{
			foreach (MemberPath memberPath in this._domainMap.NonConditionMembers(this._viewgenContext.Extent))
			{
				bool flag = memberPath.EdmType is SimpleType;
				if (!memberPath.IsNullable && flag)
				{
					FragmentQuery fragmentQuery = RewritingValidator.AddNullConditionOnCSideFragment(wrapper, memberPath, this._viewgenContext.MemberMaps);
					if (fragmentQuery != null && this._viewgenContext.RightFragmentQP.IsSatisfiable(fragmentQuery))
					{
						this._errorLog.AddEntry(new ErrorLog.Record(ViewGenErrorCode.NullableMappingForNonNullableColumn, Strings.Viewgen_NullableMappingForNonNullableColumn(wrapper.LeftExtent.ToString(), memberPath.ToFullString()), wrapper.Cells, ""));
					}
				}
			}
		}

		// Token: 0x060043F8 RID: 17400 RVA: 0x000EC7D8 File Offset: 0x000EA9D8
		internal static void EntityConfigurationToUserString(BoolExpression condition, StringBuilder builder)
		{
			RewritingValidator.EntityConfigurationToUserString(condition, builder, true);
		}

		// Token: 0x060043F9 RID: 17401 RVA: 0x000EC7E2 File Offset: 0x000EA9E2
		internal static void EntityConfigurationToUserString(BoolExpression condition, StringBuilder builder, bool writeRoundTrippingMessage)
		{
			condition.AsUserString(builder, "PK", writeRoundTrippingMessage);
		}

		// Token: 0x04001884 RID: 6276
		private readonly ViewgenContext _viewgenContext;

		// Token: 0x04001885 RID: 6277
		private readonly MemberDomainMap _domainMap;

		// Token: 0x04001886 RID: 6278
		private readonly CellTreeNode _basicView;

		// Token: 0x04001887 RID: 6279
		private readonly IEnumerable<MemberPath> _keyAttributes;

		// Token: 0x04001888 RID: 6280
		private readonly ErrorLog _errorLog;

		// Token: 0x02000B85 RID: 2949
		private class WhereClauseVisitor : Visitor<DomainConstraint<BoolLiteral, Constant>, CellTreeNode>
		{
			// Token: 0x0600669C RID: 26268 RVA: 0x0015F23C File Offset: 0x0015D43C
			internal WhereClauseVisitor(CellTreeNode topLevelTree, Dictionary<RewritingValidator.MemberValueBinding, CellTreeNode> memberValueTrees)
			{
				this._topLevelTree = topLevelTree;
				this._memberValueTrees = memberValueTrees;
				this._viewgenContext = topLevelTree.ViewgenContext;
			}

			// Token: 0x0600669D RID: 26269 RVA: 0x0015F25E File Offset: 0x0015D45E
			internal CellTreeNode GetCellTreeNode(BoolExpression whereClause)
			{
				return whereClause.Tree.Accept<CellTreeNode>(this);
			}

			// Token: 0x0600669E RID: 26270 RVA: 0x0015F26C File Offset: 0x0015D46C
			internal override CellTreeNode VisitAnd(AndExpr<DomainConstraint<BoolLiteral, Constant>> expression)
			{
				IEnumerable<CellTreeNode> enumerable = this.AcceptChildren(expression.Children);
				OpCellTreeNode opCellTreeNode = new OpCellTreeNode(this._viewgenContext, CellTreeOpType.IJ);
				foreach (CellTreeNode cellTreeNode in enumerable)
				{
					if (cellTreeNode == null)
					{
						return null;
					}
					if (cellTreeNode != this._topLevelTree)
					{
						opCellTreeNode.Add(cellTreeNode);
					}
				}
				if (opCellTreeNode.Children.Count != 0)
				{
					return opCellTreeNode;
				}
				return this._topLevelTree;
			}

			// Token: 0x0600669F RID: 26271 RVA: 0x0015F2F4 File Offset: 0x0015D4F4
			internal override CellTreeNode VisitTrue(TrueExpr<DomainConstraint<BoolLiteral, Constant>> expression)
			{
				return this._topLevelTree;
			}

			// Token: 0x060066A0 RID: 26272 RVA: 0x0015F2FC File Offset: 0x0015D4FC
			internal override CellTreeNode VisitTerm(TermExpr<DomainConstraint<BoolLiteral, Constant>> expression)
			{
				MemberRestriction memberRestriction = (MemberRestriction)expression.Identifier.Variable.Identifier;
				Set<Constant> range = expression.Identifier.Range;
				OpCellTreeNode opCellTreeNode = new OpCellTreeNode(this._viewgenContext, CellTreeOpType.Union);
				CellTreeNode cellTreeNode = null;
				foreach (Constant constant in range)
				{
					if (this.TryGetCellTreeNode(memberRestriction.RestrictedMemberSlot.MemberPath, constant, out cellTreeNode))
					{
						opCellTreeNode.Add(cellTreeNode);
					}
				}
				int count = opCellTreeNode.Children.Count;
				if (count == 0)
				{
					return null;
				}
				if (count != 1)
				{
					return opCellTreeNode;
				}
				return cellTreeNode;
			}

			// Token: 0x060066A1 RID: 26273 RVA: 0x0015F3B0 File Offset: 0x0015D5B0
			internal override CellTreeNode VisitFalse(FalseExpr<DomainConstraint<BoolLiteral, Constant>> expression)
			{
				throw new NotImplementedException();
			}

			// Token: 0x060066A2 RID: 26274 RVA: 0x0015F3B7 File Offset: 0x0015D5B7
			internal override CellTreeNode VisitNot(NotExpr<DomainConstraint<BoolLiteral, Constant>> expression)
			{
				throw new NotImplementedException();
			}

			// Token: 0x060066A3 RID: 26275 RVA: 0x0015F3BE File Offset: 0x0015D5BE
			internal override CellTreeNode VisitOr(OrExpr<DomainConstraint<BoolLiteral, Constant>> expression)
			{
				throw new NotImplementedException();
			}

			// Token: 0x060066A4 RID: 26276 RVA: 0x0015F3C5 File Offset: 0x0015D5C5
			private bool TryGetCellTreeNode(MemberPath memberPath, Constant value, out CellTreeNode singleNode)
			{
				return this._memberValueTrees.TryGetValue(new RewritingValidator.MemberValueBinding(memberPath, value), out singleNode);
			}

			// Token: 0x060066A5 RID: 26277 RVA: 0x0015F3DA File Offset: 0x0015D5DA
			private IEnumerable<CellTreeNode> AcceptChildren(IEnumerable<BoolExpr<DomainConstraint<BoolLiteral, Constant>>> children)
			{
				foreach (BoolExpr<DomainConstraint<BoolLiteral, Constant>> boolExpr in children)
				{
					yield return boolExpr.Accept<CellTreeNode>(this);
				}
				IEnumerator<BoolExpr<DomainConstraint<BoolLiteral, Constant>>> enumerator = null;
				yield break;
				yield break;
			}

			// Token: 0x04002E06 RID: 11782
			private readonly ViewgenContext _viewgenContext;

			// Token: 0x04002E07 RID: 11783
			private readonly CellTreeNode _topLevelTree;

			// Token: 0x04002E08 RID: 11784
			private readonly Dictionary<RewritingValidator.MemberValueBinding, CellTreeNode> _memberValueTrees;
		}

		// Token: 0x02000B86 RID: 2950
		internal class DomainConstraintVisitor : CellTreeNode.SimpleCellTreeVisitor<bool, bool>
		{
			// Token: 0x060066A6 RID: 26278 RVA: 0x0015F3F1 File Offset: 0x0015D5F1
			private DomainConstraintVisitor(LeftCellWrapper wrapper, ViewgenContext context, ErrorLog errorLog)
			{
				this.m_wrapper = wrapper;
				this.m_viewgenContext = context;
				this.m_errorLog = errorLog;
			}

			// Token: 0x060066A7 RID: 26279 RVA: 0x0015F410 File Offset: 0x0015D610
			internal static void CheckConstraints(CellTreeNode node, LeftCellWrapper wrapper, ViewgenContext context, ErrorLog errorLog)
			{
				RewritingValidator.DomainConstraintVisitor domainConstraintVisitor = new RewritingValidator.DomainConstraintVisitor(wrapper, context, errorLog);
				node.Accept<bool, bool>(domainConstraintVisitor, true);
			}

			// Token: 0x060066A8 RID: 26280 RVA: 0x0015F430 File Offset: 0x0015D630
			internal override bool VisitLeaf(LeafCellTreeNode node, bool dummy)
			{
				CellQuery rightCellQuery = this.m_wrapper.RightCellQuery;
				CellQuery rightCellQuery2 = node.LeftCellWrapper.RightCellQuery;
				List<MemberPath> list = new List<MemberPath>();
				if (rightCellQuery != rightCellQuery2)
				{
					for (int i = 0; i < rightCellQuery.NumProjectedSlots; i++)
					{
						MemberProjectedSlot memberProjectedSlot = rightCellQuery.ProjectedSlotAt(i) as MemberProjectedSlot;
						if (memberProjectedSlot != null)
						{
							MemberProjectedSlot memberProjectedSlot2 = rightCellQuery2.ProjectedSlotAt(i) as MemberProjectedSlot;
							if (memberProjectedSlot2 != null)
							{
								MemberPath memberPath = this.m_viewgenContext.MemberMaps.ProjectedSlotMap[i];
								if (!memberPath.IsPartOfKey && !MemberPath.EqualityComparer.Equals(memberProjectedSlot.MemberPath, memberProjectedSlot2.MemberPath))
								{
									list.Add(memberPath);
								}
							}
						}
					}
				}
				if (list.Count > 0)
				{
					string text = Strings.ViewGen_NonKeyProjectedWithOverlappingPartitions(MemberPath.PropertiesToUserString(list, false));
					ErrorLog.Record record = new ErrorLog.Record(ViewGenErrorCode.NonKeyProjectedWithOverlappingPartitions, text, new LeftCellWrapper[] { this.m_wrapper, node.LeftCellWrapper }, string.Empty);
					this.m_errorLog.AddEntry(record);
				}
				return true;
			}

			// Token: 0x060066A9 RID: 26281 RVA: 0x0015F528 File Offset: 0x0015D728
			internal override bool VisitOpNode(OpCellTreeNode node, bool dummy)
			{
				if (node.OpType == CellTreeOpType.LASJ)
				{
					node.Children[0].Accept<bool, bool>(this, dummy);
				}
				else
				{
					foreach (CellTreeNode cellTreeNode in node.Children)
					{
						cellTreeNode.Accept<bool, bool>(this, dummy);
					}
				}
				return true;
			}

			// Token: 0x04002E09 RID: 11785
			private readonly LeftCellWrapper m_wrapper;

			// Token: 0x04002E0A RID: 11786
			private readonly ViewgenContext m_viewgenContext;

			// Token: 0x04002E0B RID: 11787
			private readonly ErrorLog m_errorLog;
		}

		// Token: 0x02000B87 RID: 2951
		private struct MemberValueBinding : IEquatable<RewritingValidator.MemberValueBinding>
		{
			// Token: 0x060066AA RID: 26282 RVA: 0x0015F59C File Offset: 0x0015D79C
			public MemberValueBinding(MemberPath member, Constant value)
			{
				this.Member = member;
				this.Value = value;
			}

			// Token: 0x060066AB RID: 26283 RVA: 0x0015F5AC File Offset: 0x0015D7AC
			public override string ToString()
			{
				return string.Format(CultureInfo.InvariantCulture, "{0}={1}", new object[] { this.Member, this.Value });
			}

			// Token: 0x060066AC RID: 26284 RVA: 0x0015F5D5 File Offset: 0x0015D7D5
			public bool Equals(RewritingValidator.MemberValueBinding other)
			{
				return MemberPath.EqualityComparer.Equals(this.Member, other.Member) && Constant.EqualityComparer.Equals(this.Value, other.Value);
			}

			// Token: 0x04002E0C RID: 11788
			internal readonly MemberPath Member;

			// Token: 0x04002E0D RID: 11789
			internal readonly Constant Value;
		}
	}
}
