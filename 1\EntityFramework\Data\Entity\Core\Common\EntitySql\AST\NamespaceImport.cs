﻿using System;
using System.Data.Entity.Resources;

namespace System.Data.Entity.Core.Common.EntitySql.AST
{
	// Token: 0x02000690 RID: 1680
	internal sealed class NamespaceImport : Node
	{
		// Token: 0x06004F87 RID: 20359 RVA: 0x00120009 File Offset: 0x0011E209
		internal NamespaceImport(Identifier identifier)
		{
			this._namespaceName = identifier;
		}

		// Token: 0x06004F88 RID: 20360 RVA: 0x00120018 File Offset: 0x0011E218
		internal NamespaceImport(DotExpr dorExpr)
		{
			this._namespaceName = dorExpr;
		}

		// Token: 0x06004F89 RID: 20361 RVA: 0x00120028 File Offset: 0x0011E228
		internal NamespaceImport(BuiltInExpr bltInExpr)
		{
			this._namespaceAlias = null;
			Identifier identifier = bltInExpr.Arg1 as Identifier;
			if (identifier == null)
			{
				ErrorContext errCtx = bltInExpr.Arg1.ErrCtx;
				string invalidNamespaceAlias = Strings.InvalidNamespaceAlias;
				throw EntitySqlException.Create(errCtx, invalidNamespaceAlias, null);
			}
			this._namespaceAlias = identifier;
			this._namespaceName = bltInExpr.Arg2;
		}

		// Token: 0x17000F6F RID: 3951
		// (get) Token: 0x06004F8A RID: 20362 RVA: 0x0012007D File Offset: 0x0011E27D
		internal Identifier Alias
		{
			get
			{
				return this._namespaceAlias;
			}
		}

		// Token: 0x17000F70 RID: 3952
		// (get) Token: 0x06004F8B RID: 20363 RVA: 0x00120085 File Offset: 0x0011E285
		internal Node NamespaceName
		{
			get
			{
				return this._namespaceName;
			}
		}

		// Token: 0x04001D12 RID: 7442
		private readonly Identifier _namespaceAlias;

		// Token: 0x04001D13 RID: 7443
		private readonly Node _namespaceName;
	}
}
