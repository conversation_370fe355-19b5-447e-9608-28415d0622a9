﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Core.SchemaObjectModel;
using System.Data.Entity.Resources;
using System.Xml;

namespace System.Data.Entity.Core.Common
{
	// Token: 0x020005ED RID: 1517
	public abstract class DbXmlEnabledProviderManifest : DbProviderManifest
	{
		// Token: 0x06004A54 RID: 19028 RVA: 0x001067CC File Offset: 0x001049CC
		protected DbXmlEnabledProviderManifest(XmlReader reader)
		{
			if (reader == null)
			{
				throw new ProviderIncompatibleException(Strings.IncorrectProviderManifest, new ArgumentNullException("reader"));
			}
			this.Load(reader);
		}

		// Token: 0x17000EA8 RID: 3752
		// (get) Token: 0x06004A55 RID: 19029 RVA: 0x0010681F File Offset: 0x00104A1F
		public override string NamespaceName
		{
			get
			{
				return this._namespaceName;
			}
		}

		// Token: 0x17000EA9 RID: 3753
		// (get) Token: 0x06004A56 RID: 19030 RVA: 0x00106827 File Offset: 0x00104A27
		protected Dictionary<string, PrimitiveType> StoreTypeNameToEdmPrimitiveType
		{
			get
			{
				return this._storeTypeNameToEdmPrimitiveType;
			}
		}

		// Token: 0x17000EAA RID: 3754
		// (get) Token: 0x06004A57 RID: 19031 RVA: 0x0010682F File Offset: 0x00104A2F
		protected Dictionary<string, PrimitiveType> StoreTypeNameToStorePrimitiveType
		{
			get
			{
				return this._storeTypeNameToStorePrimitiveType;
			}
		}

		// Token: 0x06004A58 RID: 19032 RVA: 0x00106837 File Offset: 0x00104A37
		public override ReadOnlyCollection<FacetDescription> GetFacetDescriptions(EdmType edmType)
		{
			return DbXmlEnabledProviderManifest.GetReadOnlyCollection<FacetDescription>(edmType as PrimitiveType, this._facetDescriptions, Helper.EmptyFacetDescriptionEnumerable);
		}

		// Token: 0x06004A59 RID: 19033 RVA: 0x0010684F File Offset: 0x00104A4F
		public override ReadOnlyCollection<PrimitiveType> GetStoreTypes()
		{
			return this._primitiveTypes;
		}

		// Token: 0x06004A5A RID: 19034 RVA: 0x00106857 File Offset: 0x00104A57
		public override ReadOnlyCollection<EdmFunction> GetStoreFunctions()
		{
			return this._functions;
		}

		// Token: 0x06004A5B RID: 19035 RVA: 0x00106860 File Offset: 0x00104A60
		private void Load(XmlReader reader)
		{
			Schema schema;
			IList<EdmSchemaError> list = SchemaManager.LoadProviderManifest(reader, (reader.BaseURI.Length > 0) ? reader.BaseURI : null, true, out schema);
			if (list.Count != 0)
			{
				throw new ProviderIncompatibleException(Strings.IncorrectProviderManifest + Helper.CombineErrorMessage(list));
			}
			this._namespaceName = schema.Namespace;
			List<PrimitiveType> list2 = new List<PrimitiveType>();
			foreach (SchemaType schemaType in schema.SchemaTypes)
			{
				TypeElement typeElement = schemaType as TypeElement;
				if (typeElement != null)
				{
					PrimitiveType primitiveType = typeElement.PrimitiveType;
					primitiveType.ProviderManifest = this;
					primitiveType.DataSpace = DataSpace.SSpace;
					primitiveType.SetReadOnly();
					list2.Add(primitiveType);
					this._storeTypeNameToStorePrimitiveType.Add(primitiveType.Name.ToLowerInvariant(), primitiveType);
					this._storeTypeNameToEdmPrimitiveType.Add(primitiveType.Name.ToLowerInvariant(), EdmProviderManifest.Instance.GetPrimitiveType(primitiveType.PrimitiveTypeKind));
					ReadOnlyCollection<FacetDescription> readOnlyCollection;
					if (DbXmlEnabledProviderManifest.EnumerableToReadOnlyCollection<FacetDescription, FacetDescription>(typeElement.FacetDescriptions, out readOnlyCollection))
					{
						this._facetDescriptions.Add(primitiveType, readOnlyCollection);
					}
				}
			}
			this._primitiveTypes = new ReadOnlyCollection<PrimitiveType>(list2.ToArray());
			ItemCollection itemCollection = new DbXmlEnabledProviderManifest.EmptyItemCollection();
			if (!DbXmlEnabledProviderManifest.EnumerableToReadOnlyCollection<EdmFunction, GlobalItem>(Converter.ConvertSchema(schema, this, itemCollection), out this._functions))
			{
				this._functions = Helper.EmptyEdmFunctionReadOnlyCollection;
			}
			foreach (EdmFunction edmFunction in this._functions)
			{
				edmFunction.SetReadOnly();
			}
		}

		// Token: 0x06004A5C RID: 19036 RVA: 0x00106A10 File Offset: 0x00104C10
		private static ReadOnlyCollection<T> GetReadOnlyCollection<T>(PrimitiveType type, Dictionary<PrimitiveType, ReadOnlyCollection<T>> typeDictionary, ReadOnlyCollection<T> useIfEmpty)
		{
			ReadOnlyCollection<T> readOnlyCollection;
			if (typeDictionary.TryGetValue(type, out readOnlyCollection))
			{
				return readOnlyCollection;
			}
			return useIfEmpty;
		}

		// Token: 0x06004A5D RID: 19037 RVA: 0x00106A2C File Offset: 0x00104C2C
		private static bool EnumerableToReadOnlyCollection<Target, BaseType>(IEnumerable<BaseType> enumerable, out ReadOnlyCollection<Target> collection) where Target : BaseType
		{
			List<Target> list = new List<Target>();
			foreach (BaseType baseType in enumerable)
			{
				if (typeof(Target) == typeof(BaseType) || baseType is Target)
				{
					list.Add((Target)((object)baseType));
				}
			}
			if (list.Count != 0)
			{
				collection = new ReadOnlyCollection<Target>(list);
				return true;
			}
			collection = null;
			return false;
		}

		// Token: 0x04001A30 RID: 6704
		private string _namespaceName;

		// Token: 0x04001A31 RID: 6705
		private ReadOnlyCollection<PrimitiveType> _primitiveTypes;

		// Token: 0x04001A32 RID: 6706
		private readonly Dictionary<PrimitiveType, ReadOnlyCollection<FacetDescription>> _facetDescriptions = new Dictionary<PrimitiveType, ReadOnlyCollection<FacetDescription>>();

		// Token: 0x04001A33 RID: 6707
		private ReadOnlyCollection<EdmFunction> _functions;

		// Token: 0x04001A34 RID: 6708
		private readonly Dictionary<string, PrimitiveType> _storeTypeNameToEdmPrimitiveType = new Dictionary<string, PrimitiveType>();

		// Token: 0x04001A35 RID: 6709
		private readonly Dictionary<string, PrimitiveType> _storeTypeNameToStorePrimitiveType = new Dictionary<string, PrimitiveType>();

		// Token: 0x02000C36 RID: 3126
		private class EmptyItemCollection : ItemCollection
		{
			// Token: 0x06006A12 RID: 27154 RVA: 0x0016A3B0 File Offset: 0x001685B0
			public EmptyItemCollection()
				: base(DataSpace.SSpace)
			{
			}
		}
	}
}
