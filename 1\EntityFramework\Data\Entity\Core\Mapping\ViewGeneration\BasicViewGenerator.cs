﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.QueryRewriting;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Validation;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration
{
	// Token: 0x02000563 RID: 1379
	internal class BasicViewGenerator : InternalBase
	{
		// Token: 0x06004357 RID: 17239 RVA: 0x000E6974 File Offset: 0x000E4B74
		internal BasicViewGenerator(MemberProjectionIndex projectedSlotMap, List<LeftCellWrapper> usedCells, FragmentQuery activeDomain, ViewgenContext context, MemberDomainMap domainMap, ErrorLog errorLog, ConfigViewGenerator config)
		{
			this.m_projectedSlotMap = projectedSlotMap;
			this.m_usedCells = usedCells;
			this.m_viewgenContext = context;
			this.m_activeDomain = activeDomain;
			this.m_errorLog = errorLog;
			this.m_config = config;
			this.m_domainMap = domainMap;
		}

		// Token: 0x17000D5F RID: 3423
		// (get) Token: 0x06004358 RID: 17240 RVA: 0x000E69B1 File Offset: 0x000E4BB1
		private FragmentQueryProcessor LeftQP
		{
			get
			{
				return this.m_viewgenContext.LeftFragmentQP;
			}
		}

		// Token: 0x06004359 RID: 17241 RVA: 0x000E69C0 File Offset: 0x000E4BC0
		internal CellTreeNode CreateViewExpression()
		{
			OpCellTreeNode opCellTreeNode = new OpCellTreeNode(this.m_viewgenContext, CellTreeOpType.FOJ);
			foreach (LeftCellWrapper leftCellWrapper in this.m_usedCells)
			{
				LeafCellTreeNode leafCellTreeNode = new LeafCellTreeNode(this.m_viewgenContext, leftCellWrapper);
				opCellTreeNode.Add(leafCellTreeNode);
			}
			CellTreeNode cellTreeNode = this.GroupByRightExtent(opCellTreeNode);
			cellTreeNode = this.IsolateUnions(cellTreeNode);
			cellTreeNode = this.IsolateByOperator(cellTreeNode, CellTreeOpType.Union);
			cellTreeNode = this.IsolateByOperator(cellTreeNode, CellTreeOpType.IJ);
			cellTreeNode = this.IsolateByOperator(cellTreeNode, CellTreeOpType.LOJ);
			if (this.m_viewgenContext.ViewTarget == ViewTarget.QueryView)
			{
				cellTreeNode = this.ConvertUnionsToNormalizedLOJs(cellTreeNode);
			}
			return cellTreeNode;
		}

		// Token: 0x0600435A RID: 17242 RVA: 0x000E6A70 File Offset: 0x000E4C70
		internal CellTreeNode GroupByRightExtent(CellTreeNode rootNode)
		{
			KeyToListMap<EntitySetBase, LeafCellTreeNode> keyToListMap = new KeyToListMap<EntitySetBase, LeafCellTreeNode>(EqualityComparer<EntitySetBase>.Default);
			foreach (CellTreeNode cellTreeNode in rootNode.Children)
			{
				LeafCellTreeNode leafCellTreeNode = (LeafCellTreeNode)cellTreeNode;
				EntitySetBase extent = leafCellTreeNode.LeftCellWrapper.RightCellQuery.Extent;
				keyToListMap.Add(extent, leafCellTreeNode);
			}
			OpCellTreeNode opCellTreeNode = new OpCellTreeNode(this.m_viewgenContext, CellTreeOpType.FOJ);
			foreach (EntitySetBase entitySetBase in keyToListMap.Keys)
			{
				OpCellTreeNode opCellTreeNode2 = new OpCellTreeNode(this.m_viewgenContext, CellTreeOpType.FOJ);
				foreach (LeafCellTreeNode leafCellTreeNode2 in keyToListMap.ListForKey(entitySetBase))
				{
					opCellTreeNode2.Add(leafCellTreeNode2);
				}
				opCellTreeNode.Add(opCellTreeNode2);
			}
			return opCellTreeNode.Flatten();
		}

		// Token: 0x0600435B RID: 17243 RVA: 0x000E6B94 File Offset: 0x000E4D94
		private CellTreeNode IsolateUnions(CellTreeNode rootNode)
		{
			if (rootNode.Children.Count <= 1)
			{
				return rootNode;
			}
			for (int i = 0; i < rootNode.Children.Count; i++)
			{
				rootNode.Children[i] = this.IsolateUnions(rootNode.Children[i]);
			}
			OpCellTreeNode opCellTreeNode = new OpCellTreeNode(this.m_viewgenContext, CellTreeOpType.Union);
			ModifiableIteratorCollection<CellTreeNode> modifiableIteratorCollection = new ModifiableIteratorCollection<CellTreeNode>(rootNode.Children);
			while (!modifiableIteratorCollection.IsEmpty)
			{
				OpCellTreeNode opCellTreeNode2 = new OpCellTreeNode(this.m_viewgenContext, CellTreeOpType.FOJ);
				CellTreeNode cellTreeNode = modifiableIteratorCollection.RemoveOneElement();
				opCellTreeNode2.Add(cellTreeNode);
				foreach (CellTreeNode cellTreeNode2 in modifiableIteratorCollection.Elements())
				{
					if (!this.IsDisjoint(opCellTreeNode2, cellTreeNode2))
					{
						opCellTreeNode2.Add(cellTreeNode2);
						modifiableIteratorCollection.RemoveCurrentOfIterator();
						modifiableIteratorCollection.ResetIterator();
					}
				}
				opCellTreeNode.Add(opCellTreeNode2);
			}
			return opCellTreeNode.Flatten();
		}

		// Token: 0x0600435C RID: 17244 RVA: 0x000E6C90 File Offset: 0x000E4E90
		private CellTreeNode ConvertUnionsToNormalizedLOJs(CellTreeNode rootNode)
		{
			for (int i = 0; i < rootNode.Children.Count; i++)
			{
				rootNode.Children[i] = this.ConvertUnionsToNormalizedLOJs(rootNode.Children[i]);
			}
			if (rootNode.OpType != CellTreeOpType.LOJ || rootNode.Children.Count < 2)
			{
				return rootNode;
			}
			OpCellTreeNode opCellTreeNode = new OpCellTreeNode(this.m_viewgenContext, rootNode.OpType);
			List<CellTreeNode> list = new List<CellTreeNode>();
			OpCellTreeNode opCellTreeNode2 = null;
			HashSet<CellTreeNode> hashSet = null;
			if (rootNode.Children[0].OpType == CellTreeOpType.IJ)
			{
				opCellTreeNode2 = new OpCellTreeNode(this.m_viewgenContext, rootNode.Children[0].OpType);
				opCellTreeNode.Add(opCellTreeNode2);
				list.AddRange(rootNode.Children[0].Children);
				hashSet = new HashSet<CellTreeNode>(rootNode.Children[0].Children);
			}
			else
			{
				opCellTreeNode.Add(rootNode.Children[0]);
			}
			foreach (CellTreeNode cellTreeNode in rootNode.Children.Skip(1))
			{
				OpCellTreeNode opCellTreeNode3 = cellTreeNode as OpCellTreeNode;
				if (opCellTreeNode3 != null && opCellTreeNode3.OpType == CellTreeOpType.Union)
				{
					list.AddRange(opCellTreeNode3.Children);
				}
				else
				{
					list.Add(cellTreeNode);
				}
			}
			KeyToListMap<EntitySet, LeafCellTreeNode> keyToListMap = new KeyToListMap<EntitySet, LeafCellTreeNode>(EqualityComparer<EntitySet>.Default);
			foreach (CellTreeNode cellTreeNode2 in list)
			{
				LeafCellTreeNode leafCellTreeNode = cellTreeNode2 as LeafCellTreeNode;
				if (leafCellTreeNode != null)
				{
					EntitySetBase leafNodeTable = BasicViewGenerator.GetLeafNodeTable(leafCellTreeNode);
					if (leafNodeTable != null)
					{
						keyToListMap.Add((EntitySet)leafNodeTable, leafCellTreeNode);
					}
				}
				else if (hashSet != null && hashSet.Contains(cellTreeNode2))
				{
					opCellTreeNode2.Add(cellTreeNode2);
				}
				else
				{
					opCellTreeNode.Add(cellTreeNode2);
				}
			}
			foreach (KeyValuePair<EntitySet, List<LeafCellTreeNode>> keyValuePair in keyToListMap.KeyValuePairs.Where((KeyValuePair<EntitySet, List<LeafCellTreeNode>> m) => m.Value.Count > 1).ToArray<KeyValuePair<EntitySet, List<LeafCellTreeNode>>>())
			{
				keyToListMap.RemoveKey(keyValuePair.Key);
				foreach (LeafCellTreeNode leafCellTreeNode2 in keyValuePair.Value)
				{
					if (hashSet != null && hashSet.Contains(leafCellTreeNode2))
					{
						opCellTreeNode2.Add(leafCellTreeNode2);
					}
					else
					{
						opCellTreeNode.Add(leafCellTreeNode2);
					}
				}
			}
			KeyToListMap<EntitySet, EntitySet> keyToListMap2 = new KeyToListMap<EntitySet, EntitySet>(EqualityComparer<EntitySet>.Default);
			Dictionary<EntitySet, OpCellTreeNode> dictionary = new Dictionary<EntitySet, OpCellTreeNode>(EqualityComparer<EntitySet>.Default);
			foreach (KeyValuePair<EntitySet, List<LeafCellTreeNode>> keyValuePair2 in keyToListMap.KeyValuePairs)
			{
				EntitySet key = keyValuePair2.Key;
				foreach (EntitySet entitySet in BasicViewGenerator.GetFKOverPKDependents(key))
				{
					ReadOnlyCollection<LeafCellTreeNode> readOnlyCollection;
					if (keyToListMap.TryGetListForKey(entitySet, out readOnlyCollection) && (hashSet == null || !hashSet.Contains(readOnlyCollection.Single<LeafCellTreeNode>())))
					{
						keyToListMap2.Add(key, entitySet);
					}
				}
				OpCellTreeNode opCellTreeNode4 = new OpCellTreeNode(this.m_viewgenContext, CellTreeOpType.LOJ);
				opCellTreeNode4.Add(keyValuePair2.Value.Single<LeafCellTreeNode>());
				dictionary.Add(key, opCellTreeNode4);
			}
			Dictionary<EntitySet, EntitySet> dictionary2 = new Dictionary<EntitySet, EntitySet>(EqualityComparer<EntitySet>.Default);
			foreach (KeyValuePair<EntitySet, List<EntitySet>> keyValuePair3 in keyToListMap2.KeyValuePairs)
			{
				EntitySet key2 = keyValuePair3.Key;
				foreach (EntitySet entitySet2 in keyValuePair3.Value)
				{
					OpCellTreeNode opCellTreeNode5;
					if (dictionary.TryGetValue(entitySet2, out opCellTreeNode5) && !dictionary2.ContainsKey(entitySet2) && !BasicViewGenerator.CheckLOJCycle(entitySet2, key2, dictionary2))
					{
						dictionary[keyValuePair3.Key].Add(opCellTreeNode5);
						dictionary2.Add(entitySet2, key2);
					}
				}
			}
			foreach (KeyValuePair<EntitySet, OpCellTreeNode> keyValuePair4 in dictionary)
			{
				if (!dictionary2.ContainsKey(keyValuePair4.Key))
				{
					OpCellTreeNode value = keyValuePair4.Value;
					if (hashSet != null && hashSet.Contains(value.Children[0]))
					{
						opCellTreeNode2.Add(value);
					}
					else
					{
						opCellTreeNode.Add(value);
					}
				}
			}
			return opCellTreeNode.Flatten();
		}

		// Token: 0x0600435D RID: 17245 RVA: 0x000E7198 File Offset: 0x000E5398
		private static IEnumerable<EntitySet> GetFKOverPKDependents(EntitySet principal)
		{
			using (IEnumerator<Tuple<AssociationSet, ReferentialConstraint>> enumerator = principal.ForeignKeyPrincipals.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					Tuple<AssociationSet, ReferentialConstraint> pkFkInfo = enumerator.Current;
					ReadOnlyMetadataCollection<EdmMember> keyMembers = pkFkInfo.Item2.ToRole.GetEntityType().KeyMembers;
					ReadOnlyMetadataCollection<EdmProperty> toProperties = pkFkInfo.Item2.ToProperties;
					if (keyMembers.Count == toProperties.Count)
					{
						int num = 0;
						while (num < keyMembers.Count && keyMembers[num].EdmEquals(toProperties[num]))
						{
							num++;
						}
						if (num == keyMembers.Count)
						{
							yield return pkFkInfo.Item1.AssociationSetEnds.Where((AssociationSetEnd ase) => ase.Name == pkFkInfo.Item2.ToRole.Name).Single<AssociationSetEnd>().EntitySet;
						}
					}
				}
			}
			IEnumerator<Tuple<AssociationSet, ReferentialConstraint>> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x0600435E RID: 17246 RVA: 0x000E71A8 File Offset: 0x000E53A8
		private static EntitySet GetLeafNodeTable(LeafCellTreeNode leaf)
		{
			return leaf.LeftCellWrapper.RightCellQuery.Extent as EntitySet;
		}

		// Token: 0x0600435F RID: 17247 RVA: 0x000E71BF File Offset: 0x000E53BF
		private static bool CheckLOJCycle(EntitySet child, EntitySet parent, Dictionary<EntitySet, EntitySet> nestedExtents)
		{
			while (!EqualityComparer<EntitySet>.Default.Equals(parent, child))
			{
				if (!nestedExtents.TryGetValue(parent, out parent))
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x06004360 RID: 17248 RVA: 0x000E71E0 File Offset: 0x000E53E0
		internal CellTreeNode IsolateByOperator(CellTreeNode rootNode, CellTreeOpType opTypeToIsolate)
		{
			List<CellTreeNode> children = rootNode.Children;
			if (children.Count <= 1)
			{
				return rootNode;
			}
			for (int i = 0; i < children.Count; i++)
			{
				children[i] = this.IsolateByOperator(children[i], opTypeToIsolate);
			}
			if ((rootNode.OpType != CellTreeOpType.FOJ && rootNode.OpType != CellTreeOpType.LOJ) || rootNode.OpType == opTypeToIsolate)
			{
				return rootNode;
			}
			OpCellTreeNode opCellTreeNode = new OpCellTreeNode(this.m_viewgenContext, rootNode.OpType);
			ModifiableIteratorCollection<CellTreeNode> modifiableIteratorCollection = new ModifiableIteratorCollection<CellTreeNode>(children);
			while (!modifiableIteratorCollection.IsEmpty)
			{
				OpCellTreeNode opCellTreeNode2 = new OpCellTreeNode(this.m_viewgenContext, opTypeToIsolate);
				CellTreeNode cellTreeNode = modifiableIteratorCollection.RemoveOneElement();
				opCellTreeNode2.Add(cellTreeNode);
				foreach (CellTreeNode cellTreeNode2 in modifiableIteratorCollection.Elements())
				{
					if (this.TryAddChildToGroup(opTypeToIsolate, cellTreeNode2, opCellTreeNode2))
					{
						modifiableIteratorCollection.RemoveCurrentOfIterator();
						if (opTypeToIsolate == CellTreeOpType.LOJ)
						{
							modifiableIteratorCollection.ResetIterator();
						}
					}
				}
				opCellTreeNode.Add(opCellTreeNode2);
			}
			return opCellTreeNode.Flatten();
		}

		// Token: 0x06004361 RID: 17249 RVA: 0x000E72F0 File Offset: 0x000E54F0
		private bool TryAddChildToGroup(CellTreeOpType opTypeToIsolate, CellTreeNode childNode, OpCellTreeNode groupNode)
		{
			switch (opTypeToIsolate)
			{
			case CellTreeOpType.Union:
				if (this.IsDisjoint(childNode, groupNode))
				{
					groupNode.Add(childNode);
					return true;
				}
				break;
			case CellTreeOpType.LOJ:
				if (this.IsContainedIn(childNode, groupNode))
				{
					groupNode.Add(childNode);
					return true;
				}
				if (this.IsContainedIn(groupNode, childNode))
				{
					groupNode.AddFirst(childNode);
					return true;
				}
				break;
			case CellTreeOpType.IJ:
				if (this.IsEquivalentTo(childNode, groupNode))
				{
					groupNode.Add(childNode);
					return true;
				}
				break;
			}
			return false;
		}

		// Token: 0x06004362 RID: 17250 RVA: 0x000E7364 File Offset: 0x000E5564
		private bool IsDisjoint(CellTreeNode n1, CellTreeNode n2)
		{
			bool flag = this.LeftQP.IsDisjointFrom(n1.LeftFragmentQuery, n2.LeftFragmentQuery);
			if (flag && this.m_viewgenContext.ViewTarget == ViewTarget.QueryView)
			{
				return true;
			}
			bool isEmptyRightFragmentQuery = new OpCellTreeNode(this.m_viewgenContext, CellTreeOpType.IJ, new CellTreeNode[] { n1, n2 }).IsEmptyRightFragmentQuery;
			if (this.m_viewgenContext.ViewTarget != ViewTarget.UpdateView || !flag || isEmptyRightFragmentQuery)
			{
				return flag || isEmptyRightFragmentQuery;
			}
			if (ErrorPatternMatcher.FindMappingErrors(this.m_viewgenContext, this.m_domainMap, this.m_errorLog))
			{
				return false;
			}
			StringBuilder stringBuilder = new StringBuilder(Strings.Viewgen_RightSideNotDisjoint(this.m_viewgenContext.Extent.ToString()));
			stringBuilder.AppendLine();
			FragmentQuery fragmentQuery = this.LeftQP.Intersect(n1.RightFragmentQuery, n2.RightFragmentQuery);
			if (this.LeftQP.IsSatisfiable(fragmentQuery))
			{
				fragmentQuery.Condition.ExpensiveSimplify();
				RewritingValidator.EntityConfigurationToUserString(fragmentQuery.Condition, stringBuilder);
			}
			this.m_errorLog.AddEntry(new ErrorLog.Record(ViewGenErrorCode.DisjointConstraintViolation, stringBuilder.ToString(), this.m_viewgenContext.AllWrappersForExtent, string.Empty));
			ExceptionHelpers.ThrowMappingException(this.m_errorLog, this.m_config);
			return false;
		}

		// Token: 0x06004363 RID: 17251 RVA: 0x000E7494 File Offset: 0x000E5694
		private bool IsContainedIn(CellTreeNode n1, CellTreeNode n2)
		{
			FragmentQuery fragmentQuery = this.LeftQP.Intersect(n1.LeftFragmentQuery, this.m_activeDomain);
			FragmentQuery fragmentQuery2 = this.LeftQP.Intersect(n2.LeftFragmentQuery, this.m_activeDomain);
			return this.LeftQP.IsContainedIn(fragmentQuery, fragmentQuery2) || new OpCellTreeNode(this.m_viewgenContext, CellTreeOpType.LASJ, new CellTreeNode[] { n1, n2 }).IsEmptyRightFragmentQuery;
		}

		// Token: 0x06004364 RID: 17252 RVA: 0x000E7501 File Offset: 0x000E5701
		private bool IsEquivalentTo(CellTreeNode n1, CellTreeNode n2)
		{
			return this.IsContainedIn(n1, n2) && this.IsContainedIn(n2, n1);
		}

		// Token: 0x06004365 RID: 17253 RVA: 0x000E7517 File Offset: 0x000E5717
		internal override void ToCompactString(StringBuilder builder)
		{
			this.m_projectedSlotMap.ToCompactString(builder);
		}

		// Token: 0x04001807 RID: 6151
		private readonly MemberProjectionIndex m_projectedSlotMap;

		// Token: 0x04001808 RID: 6152
		private readonly List<LeftCellWrapper> m_usedCells;

		// Token: 0x04001809 RID: 6153
		private readonly FragmentQuery m_activeDomain;

		// Token: 0x0400180A RID: 6154
		private readonly ViewgenContext m_viewgenContext;

		// Token: 0x0400180B RID: 6155
		private readonly ErrorLog m_errorLog;

		// Token: 0x0400180C RID: 6156
		private readonly ConfigViewGenerator m_config;

		// Token: 0x0400180D RID: 6157
		private readonly MemberDomainMap m_domainMap;
	}
}
