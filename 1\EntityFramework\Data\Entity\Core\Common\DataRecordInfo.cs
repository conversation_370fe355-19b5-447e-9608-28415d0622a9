﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common
{
	// Token: 0x020005E9 RID: 1513
	public class DataRecordInfo
	{
		// Token: 0x06004A02 RID: 18946 RVA: 0x00105A12 File Offset: 0x00103C12
		internal DataRecordInfo()
		{
		}

		// Token: 0x06004A03 RID: 18947 RVA: 0x00105A1C File Offset: 0x00103C1C
		public DataRecordInfo(TypeUsage metadata, IEnumerable<EdmMember> memberInfo)
		{
			Check.NotNull<TypeUsage>(metadata, "metadata");
			IBaseList<EdmMember> allStructuralMembers = TypeHelpers.GetAllStructuralMembers(metadata.EdmType);
			List<FieldMetadata> list = new List<FieldMetadata>(allStructuralMembers.Count);
			if (memberInfo != null)
			{
				foreach (EdmMember edmMember in memberInfo)
				{
					if (edmMember == null || 0 > allStructuralMembers.IndexOf(edmMember) || (BuiltInTypeKind.EdmProperty != edmMember.BuiltInTypeKind && edmMember.BuiltInTypeKind != BuiltInTypeKind.AssociationEndMember))
					{
						throw Error.InvalidEdmMemberInstance();
					}
					if (edmMember.DeclaringType != metadata.EdmType && !edmMember.DeclaringType.IsBaseTypeOf(metadata.EdmType))
					{
						throw new ArgumentException(Strings.EdmMembersDefiningTypeDoNotAgreeWithMetadataType);
					}
					list.Add(new FieldMetadata(list.Count, edmMember));
				}
			}
			if (Helper.IsStructuralType(metadata.EdmType) == 0 < list.Count)
			{
				this._fieldMetadata = new ReadOnlyCollection<FieldMetadata>(list);
				this._metadata = metadata;
				return;
			}
			throw Error.InvalidEdmMemberInstance();
		}

		// Token: 0x06004A04 RID: 18948 RVA: 0x00105B24 File Offset: 0x00103D24
		internal DataRecordInfo(TypeUsage metadata)
		{
			IBaseList<EdmMember> allStructuralMembers = TypeHelpers.GetAllStructuralMembers(metadata);
			FieldMetadata[] array = new FieldMetadata[allStructuralMembers.Count];
			for (int i = 0; i < array.Length; i++)
			{
				EdmMember edmMember = allStructuralMembers[i];
				array[i] = new FieldMetadata(i, edmMember);
			}
			this._fieldMetadata = new ReadOnlyCollection<FieldMetadata>(array);
			this._metadata = metadata;
		}

		// Token: 0x06004A05 RID: 18949 RVA: 0x00105B81 File Offset: 0x00103D81
		internal DataRecordInfo(DataRecordInfo recordInfo)
		{
			this._fieldMetadata = recordInfo._fieldMetadata;
			this._metadata = recordInfo._metadata;
		}

		// Token: 0x17000EA5 RID: 3749
		// (get) Token: 0x06004A06 RID: 18950 RVA: 0x00105BA1 File Offset: 0x00103DA1
		public ReadOnlyCollection<FieldMetadata> FieldMetadata
		{
			get
			{
				return this._fieldMetadata;
			}
		}

		// Token: 0x17000EA6 RID: 3750
		// (get) Token: 0x06004A07 RID: 18951 RVA: 0x00105BA9 File Offset: 0x00103DA9
		public virtual TypeUsage RecordType
		{
			get
			{
				return this._metadata;
			}
		}

		// Token: 0x04001A17 RID: 6679
		private readonly ReadOnlyCollection<FieldMetadata> _fieldMetadata;

		// Token: 0x04001A18 RID: 6680
		private readonly TypeUsage _metadata;
	}
}
