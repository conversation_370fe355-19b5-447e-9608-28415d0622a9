﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000019 RID: 25
	public class TS_UltimateLuxuryRecharge
	{
		// Token: 0x170000AA RID: 170
		// (get) Token: 0x0600016B RID: 363 RVA: 0x00002C7C File Offset: 0x00000E7C
		// (set) Token: 0x0600016C RID: 364 RVA: 0x00002C84 File Offset: 0x00000E84
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int ID { get; set; }

		// Token: 0x170000AB RID: 171
		// (get) Token: 0x0600016D RID: 365 RVA: 0x00002C8D File Offset: 0x00000E8D
		// (set) Token: 0x0600016E RID: 366 RVA: 0x00002C95 File Offset: 0x00000E95
		public int ItemID { get; set; }

		// Token: 0x170000AC RID: 172
		// (get) Token: 0x0600016F RID: 367 RVA: 0x00002C9E File Offset: 0x00000E9E
		// (set) Token: 0x06000170 RID: 368 RVA: 0x00002CA6 File Offset: 0x00000EA6
		public int Count { get; set; }

		// Token: 0x170000AD RID: 173
		// (get) Token: 0x06000171 RID: 369 RVA: 0x00002CAF File Offset: 0x00000EAF
		// (set) Token: 0x06000172 RID: 370 RVA: 0x00002CB7 File Offset: 0x00000EB7
		public bool IsBind { get; set; }

		// Token: 0x170000AE RID: 174
		// (get) Token: 0x06000173 RID: 371 RVA: 0x00002CC0 File Offset: 0x00000EC0
		// (set) Token: 0x06000174 RID: 372 RVA: 0x00002CC8 File Offset: 0x00000EC8
		public int Grade { get; set; }
	}
}
