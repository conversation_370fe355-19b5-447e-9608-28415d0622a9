﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000032 RID: 50
	public class Sys_User_Info
	{
		// Token: 0x17000189 RID: 393
		// (get) Token: 0x06000343 RID: 835 RVA: 0x00003C35 File Offset: 0x00001E35
		// (set) Token: 0x06000344 RID: 836 RVA: 0x00003C3D File Offset: 0x00001E3D
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x1700018A RID: 394
		// (get) Token: 0x06000345 RID: 837 RVA: 0x00003C46 File Offset: 0x00001E46
		// (set) Token: 0x06000346 RID: 838 RVA: 0x00003C4E File Offset: 0x00001E4E
		public int UserID { get; set; }

		// Token: 0x1700018B RID: 395
		// (get) Token: 0x06000347 RID: 839 RVA: 0x00003C57 File Offset: 0x00001E57
		// (set) Token: 0x06000348 RID: 840 RVA: 0x00003C5F File Offset: 0x00001E5F
		public int ActivityTanabataNum { get; set; }

		// Token: 0x1700018C RID: 396
		// (get) Token: 0x06000349 RID: 841 RVA: 0x00003C68 File Offset: 0x00001E68
		// (set) Token: 0x0600034A RID: 842 RVA: 0x00003C70 File Offset: 0x00001E70
		public DateTime Activity_LastDate { get; set; }

		// Token: 0x1700018D RID: 397
		// (get) Token: 0x0600034B RID: 843 RVA: 0x00003C79 File Offset: 0x00001E79
		// (set) Token: 0x0600034C RID: 844 RVA: 0x00003C81 File Offset: 0x00001E81
		public int DDPlayPoint { get; set; }
	}
}
