﻿using System;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects.DataClasses;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Reflection;

namespace System.Data.Entity.Core.Objects.Internal
{
	// Token: 0x02000442 RID: 1090
	internal class EntityWrapperFactory
	{
		// Token: 0x06003547 RID: 13639 RVA: 0x000AA7B0 File Offset: 0x000A89B0
		internal static IEntityWrapper CreateNewWrapper(object entity, EntityKey key)
		{
			if (entity == null)
			{
				return NullEntityWrapper.NullWrapper;
			}
			IEntityWrapper entityWrapper = EntityWrapperFactory._delegateCache.Evaluate(entity.GetType())(entity);
			entityWrapper.RelationshipManager.SetWrappedOwner(entityWrapper, entity);
			if (key != null && entityWrapper.EntityKey == null)
			{
				entityWrapper.EntityKey = key;
			}
			EntityProxyTypeInfo entityProxyTypeInfo;
			if (EntityProxyFactory.TryGetProxyType(entity.GetType(), out entityProxyTypeInfo))
			{
				entityProxyTypeInfo.SetEntityWrapper(entityWrapper);
			}
			return entityWrapper;
		}

		// Token: 0x06003548 RID: 13640 RVA: 0x000AA814 File Offset: 0x000A8A14
		private static Func<object, IEntityWrapper> CreateWrapperDelegate(Type entityType)
		{
			bool flag = typeof(IEntityWithRelationships).IsAssignableFrom(entityType);
			bool flag2 = typeof(IEntityWithChangeTracker).IsAssignableFrom(entityType);
			bool flag3 = typeof(IEntityWithKey).IsAssignableFrom(entityType);
			bool flag4 = EntityProxyFactory.IsProxyType(entityType);
			MethodInfo methodInfo;
			if (flag && flag2 && flag3 && !flag4)
			{
				methodInfo = EntityWrapperFactory.CreateWrapperDelegateTypedLightweightMethod;
			}
			else if (flag)
			{
				methodInfo = EntityWrapperFactory.CreateWrapperDelegateTypedWithRelationshipsMethod;
			}
			else
			{
				methodInfo = EntityWrapperFactory.CreateWrapperDelegateTypedWithoutRelationshipsMethod;
			}
			methodInfo = methodInfo.MakeGenericMethod(new Type[] { entityType });
			return (Func<object, IEntityWrapper>)methodInfo.Invoke(null, new object[0]);
		}

		// Token: 0x06003549 RID: 13641 RVA: 0x000AA8A7 File Offset: 0x000A8AA7
		private static Func<object, IEntityWrapper> CreateWrapperDelegateTypedLightweight<TEntity>() where TEntity : class, IEntityWithRelationships, IEntityWithKey, IEntityWithChangeTracker
		{
			bool overridesEquals = typeof(TEntity).OverridesEqualsOrGetHashCode();
			return (object entity) => new LightweightEntityWrapper<TEntity>((TEntity)((object)entity), overridesEquals);
		}

		// Token: 0x0600354A RID: 13642 RVA: 0x000AA8D0 File Offset: 0x000A8AD0
		private static Func<object, IEntityWrapper> CreateWrapperDelegateTypedWithRelationships<TEntity>() where TEntity : class, IEntityWithRelationships
		{
			bool overridesEquals = typeof(TEntity).OverridesEqualsOrGetHashCode();
			Func<object, IPropertyAccessorStrategy> propertyAccessorStrategy;
			Func<object, IChangeTrackingStrategy> changeTrackingStrategy;
			Func<object, IEntityKeyStrategy> keyStrategy;
			EntityWrapperFactory.CreateStrategies<TEntity>(out propertyAccessorStrategy, out changeTrackingStrategy, out keyStrategy);
			return (object entity) => new EntityWrapperWithRelationships<TEntity>((TEntity)((object)entity), propertyAccessorStrategy, changeTrackingStrategy, keyStrategy, overridesEquals);
		}

		// Token: 0x0600354B RID: 13643 RVA: 0x000AA91C File Offset: 0x000A8B1C
		private static Func<object, IEntityWrapper> CreateWrapperDelegateTypedWithoutRelationships<TEntity>() where TEntity : class
		{
			bool overridesEquals = typeof(TEntity).OverridesEqualsOrGetHashCode();
			Func<object, IPropertyAccessorStrategy> propertyAccessorStrategy;
			Func<object, IChangeTrackingStrategy> changeTrackingStrategy;
			Func<object, IEntityKeyStrategy> keyStrategy;
			EntityWrapperFactory.CreateStrategies<TEntity>(out propertyAccessorStrategy, out changeTrackingStrategy, out keyStrategy);
			return (object entity) => new EntityWrapperWithoutRelationships<TEntity>((TEntity)((object)entity), propertyAccessorStrategy, changeTrackingStrategy, keyStrategy, overridesEquals);
		}

		// Token: 0x0600354C RID: 13644 RVA: 0x000AA968 File Offset: 0x000A8B68
		private static void CreateStrategies<TEntity>(out Func<object, IPropertyAccessorStrategy> createPropertyAccessorStrategy, out Func<object, IChangeTrackingStrategy> createChangeTrackingStrategy, out Func<object, IEntityKeyStrategy> createKeyStrategy)
		{
			Type typeFromHandle = typeof(TEntity);
			int num = (typeof(IEntityWithRelationships).IsAssignableFrom(typeFromHandle) ? 1 : 0);
			bool flag = typeof(IEntityWithChangeTracker).IsAssignableFrom(typeFromHandle);
			bool flag2 = typeof(IEntityWithKey).IsAssignableFrom(typeFromHandle);
			bool flag3 = EntityProxyFactory.IsProxyType(typeFromHandle);
			if (num == 0 || flag3)
			{
				createPropertyAccessorStrategy = EntityWrapperFactory.GetPocoPropertyAccessorStrategyFunc();
			}
			else
			{
				createPropertyAccessorStrategy = EntityWrapperFactory.GetNullPropertyAccessorStrategyFunc();
			}
			if (flag)
			{
				createChangeTrackingStrategy = EntityWrapperFactory.GetEntityWithChangeTrackerStrategyFunc();
			}
			else
			{
				createChangeTrackingStrategy = EntityWrapperFactory.GetSnapshotChangeTrackingStrategyFunc();
			}
			if (flag2)
			{
				createKeyStrategy = EntityWrapperFactory.GetEntityWithKeyStrategyStrategyFunc();
				return;
			}
			createKeyStrategy = EntityWrapperFactory.GetPocoEntityKeyStrategyFunc();
		}

		// Token: 0x0600354D RID: 13645 RVA: 0x000AA9F8 File Offset: 0x000A8BF8
		internal IEntityWrapper WrapEntityUsingContext(object entity, ObjectContext context)
		{
			EntityEntry entityEntry;
			return this.WrapEntityUsingStateManagerGettingEntry(entity, (context == null) ? null : context.ObjectStateManager, out entityEntry);
		}

		// Token: 0x0600354E RID: 13646 RVA: 0x000AAA1A File Offset: 0x000A8C1A
		internal IEntityWrapper WrapEntityUsingContextGettingEntry(object entity, ObjectContext context, out EntityEntry existingEntry)
		{
			return this.WrapEntityUsingStateManagerGettingEntry(entity, (context == null) ? null : context.ObjectStateManager, out existingEntry);
		}

		// Token: 0x0600354F RID: 13647 RVA: 0x000AAA30 File Offset: 0x000A8C30
		internal IEntityWrapper WrapEntityUsingStateManager(object entity, ObjectStateManager stateManager)
		{
			EntityEntry entityEntry;
			return this.WrapEntityUsingStateManagerGettingEntry(entity, stateManager, out entityEntry);
		}

		// Token: 0x06003550 RID: 13648 RVA: 0x000AAA48 File Offset: 0x000A8C48
		internal virtual IEntityWrapper WrapEntityUsingStateManagerGettingEntry(object entity, ObjectStateManager stateManager, out EntityEntry existingEntry)
		{
			IEntityWrapper entityWrapper = null;
			existingEntry = null;
			if (entity == null)
			{
				return NullEntityWrapper.NullWrapper;
			}
			if (stateManager != null)
			{
				existingEntry = stateManager.FindEntityEntry(entity);
				if (existingEntry != null)
				{
					return existingEntry.WrappedEntity;
				}
				if (stateManager.TransactionManager.TrackProcessedEntities && stateManager.TransactionManager.WrappedEntities.TryGetValue(entity, out entityWrapper))
				{
					return entityWrapper;
				}
			}
			IEntityWithRelationships entityWithRelationships = entity as IEntityWithRelationships;
			if (entityWithRelationships == null)
			{
				EntityProxyFactory.TryGetProxyWrapper(entity, out entityWrapper);
				if (entityWrapper == null)
				{
					IEntityWithKey entityWithKey = entity as IEntityWithKey;
					entityWrapper = EntityWrapperFactory.CreateNewWrapper(entity, (entityWithKey == null) ? null : entityWithKey.EntityKey);
				}
				if (stateManager != null && stateManager.TransactionManager.TrackProcessedEntities)
				{
					stateManager.TransactionManager.WrappedEntities.Add(entity, entityWrapper);
				}
				return entityWrapper;
			}
			RelationshipManager relationshipManager = entityWithRelationships.RelationshipManager;
			if (relationshipManager == null)
			{
				throw new InvalidOperationException(Strings.RelationshipManager_UnexpectedNull);
			}
			IEntityWrapper wrappedOwner = relationshipManager.WrappedOwner;
			if (wrappedOwner.Entity != entity)
			{
				throw new InvalidOperationException(Strings.RelationshipManager_InvalidRelationshipManagerOwner);
			}
			return wrappedOwner;
		}

		// Token: 0x06003551 RID: 13649 RVA: 0x000AAB20 File Offset: 0x000A8D20
		internal virtual void UpdateNoTrackingWrapper(IEntityWrapper wrapper, ObjectContext context, EntitySet entitySet)
		{
			if (wrapper.EntityKey == null)
			{
				wrapper.EntityKey = context.ObjectStateManager.CreateEntityKey(entitySet, wrapper.Entity);
			}
			if (wrapper.Context == null)
			{
				wrapper.AttachContext(context, entitySet, MergeOption.NoTracking);
			}
		}

		// Token: 0x06003552 RID: 13650 RVA: 0x000AAB59 File Offset: 0x000A8D59
		internal static Func<object, IPropertyAccessorStrategy> GetPocoPropertyAccessorStrategyFunc()
		{
			return (object entity) => new PocoPropertyAccessorStrategy(entity);
		}

		// Token: 0x06003553 RID: 13651 RVA: 0x000AAB7A File Offset: 0x000A8D7A
		internal static Func<object, IPropertyAccessorStrategy> GetNullPropertyAccessorStrategyFunc()
		{
			return (object entity) => null;
		}

		// Token: 0x06003554 RID: 13652 RVA: 0x000AAB9B File Offset: 0x000A8D9B
		internal static Func<object, IChangeTrackingStrategy> GetEntityWithChangeTrackerStrategyFunc()
		{
			return (object entity) => new EntityWithChangeTrackerStrategy((IEntityWithChangeTracker)entity);
		}

		// Token: 0x06003555 RID: 13653 RVA: 0x000AABBC File Offset: 0x000A8DBC
		internal static Func<object, IChangeTrackingStrategy> GetSnapshotChangeTrackingStrategyFunc()
		{
			return (object entity) => SnapshotChangeTrackingStrategy.Instance;
		}

		// Token: 0x06003556 RID: 13654 RVA: 0x000AABDD File Offset: 0x000A8DDD
		internal static Func<object, IEntityKeyStrategy> GetEntityWithKeyStrategyStrategyFunc()
		{
			return (object entity) => new EntityWithKeyStrategy((IEntityWithKey)entity);
		}

		// Token: 0x06003557 RID: 13655 RVA: 0x000AABFE File Offset: 0x000A8DFE
		internal static Func<object, IEntityKeyStrategy> GetPocoEntityKeyStrategyFunc()
		{
			return (object entity) => new PocoEntityKeyStrategy();
		}

		// Token: 0x0400113F RID: 4415
		private static readonly Memoizer<Type, Func<object, IEntityWrapper>> _delegateCache = new Memoizer<Type, Func<object, IEntityWrapper>>(new Func<Type, Func<object, IEntityWrapper>>(EntityWrapperFactory.CreateWrapperDelegate), null);

		// Token: 0x04001140 RID: 4416
		internal static readonly MethodInfo CreateWrapperDelegateTypedLightweightMethod = typeof(EntityWrapperFactory).GetOnlyDeclaredMethod("CreateWrapperDelegateTypedLightweight");

		// Token: 0x04001141 RID: 4417
		internal static readonly MethodInfo CreateWrapperDelegateTypedWithRelationshipsMethod = typeof(EntityWrapperFactory).GetOnlyDeclaredMethod("CreateWrapperDelegateTypedWithRelationships");

		// Token: 0x04001142 RID: 4418
		internal static readonly MethodInfo CreateWrapperDelegateTypedWithoutRelationshipsMethod = typeof(EntityWrapperFactory).GetOnlyDeclaredMethod("CreateWrapperDelegateTypedWithoutRelationships");
	}
}
