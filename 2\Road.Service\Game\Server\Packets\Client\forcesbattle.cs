using System;
using System.Collections.Generic;
using System.Linq;
using Bussiness;
using Bussiness.Managers;
using EntityDatabase.PlayerModels;
using EntityDatabase.ServerModels;
using Game.Base.Packets;
using Game.Server.GameObjects;
using SqlDataProvider.Data;

namespace Game.Server.Packets.Client
{
	// Token: 0x020009C8 RID: 2504
	[PacketHandler(768, "圣物")]
	public class forcesbattle : IPacketHandler
	{
		// Token: 0x060061CE RID: 25038 RVA: 0x001EAC18 File Offset: 0x001E8E18
		public int HandlePacket(GameClient client, GSPacketIn packet)
		{
			switch (packet.ReadByte())
			{
			case 1:
				this.SendForcesBaseInfo(client.Player);
				break;
			case 2:
				this.SendUserRelicItem(client.Player, 2);
				break;
			case 3:
				this.SendUpgradeRelic(client.Player, packet);
				break;
			case 4:
				this.SendAdvanceRelic(client.Player, packet);
				break;
			case 5:
				this.SendUseManualInfo(client.Player);
				break;
			case 6:
				this.SendRelicZF(client.Player, packet);
				break;
			case 7:
				this.SendUpgradeManual(client.Player, packet);
				break;
			case 8:
				this.SendEquipRelic(client.Player, packet);
				break;
			}
			return 0;
		}

		// Token: 0x060061CF RID: 25039 RVA: 0x001EACCC File Offset: 0x001E8ECC
		public void SendForcesBaseInfo(GamePlayer Player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(768);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteInt(Player.PlayerCharacter.RelicItemInfo.shopScore);
			string[] array = Player.PlayerCharacter.RelicItemInfo.RelicInfo.Split(new char[] { ',' });
			gspacketIn.WriteInt(Player.PlayerCharacter.RelicItemInfo.OpenCount);
			gspacketIn.WriteInt(Player.PlayerCharacter.RelicItemInfo.OpenCount);
			for (int i = 0; i < Player.PlayerCharacter.RelicItemInfo.OpenCount; i++)
			{
				gspacketIn.WriteInt(int.Parse(array[i]));
			}
			gspacketIn.WriteInt(1);
			for (int j = 0; j < 1; j++)
			{
				gspacketIn.WriteInt(j);
				gspacketIn.WriteInt(j);
			}
			Player.Out.SendTCP(gspacketIn);
		}

		// Token: 0x060061D0 RID: 25040 RVA: 0x001EADA4 File Offset: 0x001E8FA4
		public void SendUserRelicItem(GamePlayer Player, int Type)
		{
			try
			{
				if (Player == null)
				{
					Console.WriteLine("SendUserRelicItem Error: Player is null");
				}
				else if (Player.PlayerCharacter == null)
				{
					Console.WriteLine("SendUserRelicItem Error: Player.PlayerCharacter is null");
				}
				else if (Player.PlayerCharacter.RelicItem == null)
				{
					Console.WriteLine("SendUserRelicItem Error: Player.PlayerCharacter.RelicItem is null");
				}
				else if (Player.Out == null)
				{
					Console.WriteLine("SendUserRelicItem Error: Player.Out is null");
				}
				else
				{
					GSPacketIn gspacketIn = new GSPacketIn(768);
					gspacketIn.WriteByte((byte)Type);
					gspacketIn.WriteBoolean(true);
					gspacketIn.WriteInt(3);
					gspacketIn.WriteInt(3);
					gspacketIn.WriteInt(Player.PlayerCharacter.RelicItem.Count);
					foreach (Sys_User_RelicItemTemplate sys_User_RelicItemTemplate in Player.PlayerCharacter.RelicItem)
					{
						if (sys_User_RelicItemTemplate == null)
						{
							Console.WriteLine("SendUserRelicItem Error: sys_User_RelicItemTemplate is null");
						}
						else
						{
							gspacketIn.WriteInt(sys_User_RelicItemTemplate.itemID);
							gspacketIn.WriteInt(sys_User_RelicItemTemplate.level);
							gspacketIn.WriteInt(sys_User_RelicItemTemplate.stage);
							gspacketIn.WriteInt(sys_User_RelicItemTemplate.curExp);
							gspacketIn.WriteInt(sys_User_RelicItemTemplate.ShardNum);
							if (string.IsNullOrEmpty(sys_User_RelicItemTemplate.ProArr))
							{
								Console.WriteLine("SendUserRelicItem Warning: ProArr is null or empty for itemID " + sys_User_RelicItemTemplate.itemID.ToString() + ", initializing empty list");
								sys_User_RelicItemTemplate.ProArr = RelicItemMgr.ConvertToJsonString(new List<RelicProDataInfo>());
								gspacketIn.WriteInt(0);
							}
							else
							{
								List<RelicProDataInfo> list = RelicItemMgr.ParseToList(sys_User_RelicItemTemplate.ProArr);
								if (list == null)
								{
									Console.WriteLine("SendUserRelicItem Error: ParseToList returned null for itemID " + sys_User_RelicItemTemplate.itemID.ToString());
									gspacketIn.WriteInt(0);
								}
								else
								{
									gspacketIn.WriteInt(list.Count);
									foreach (RelicProDataInfo relicProDataInfo in list)
									{
										if (relicProDataInfo == null)
										{
											Console.WriteLine("SendUserRelicItem Error: relicProDataInfo is null");
										}
										else
										{
											gspacketIn.WriteInt(0);
											gspacketIn.WriteInt(relicProDataInfo.type);
											gspacketIn.WriteInt(relicProDataInfo.value);
											gspacketIn.WriteInt(relicProDataInfo.attachValue);
										}
									}
								}
							}
						}
					}
					Player.Out.SendTCP(gspacketIn);
				}
			}
			catch (Exception ex)
			{
				Console.WriteLine("SendUserRelicItem Error: " + ex.Message);
				Console.WriteLine("SendUserRelicItem StackTrace: " + ex.StackTrace);
			}
		}

		// Token: 0x060061D1 RID: 25041 RVA: 0x001EB058 File Offset: 0x001E9258
		public void SendUpgradeRelic(GamePlayer Player, GSPacketIn packet)
		{
			try
			{
				if (Player == null)
				{
					Console.WriteLine("SendUpgradeRelic Error: Player is null");
				}
				else if (packet == null)
				{
					Console.WriteLine("SendUpgradeRelic Error: packet is null");
				}
				else
				{
					int _curDetailID = packet.ReadInt();
					int num = packet.ReadInt();
					int num2 = packet.ReadInt();
					int relicUpgradeItem = GameProperties.RelicUpgradeItem;
					Console.WriteLine(string.Concat(new string[]
					{
						"SendUpgradeRelic: _curDetailID=",
						_curDetailID.ToString(),
						", templateID=",
						num.ToString(),
						", num=",
						num2.ToString(),
						", relicUpgradeItem=",
						relicUpgradeItem.ToString()
					}));
					if (num2 <= 0)
					{
						Player.SendMessage("数量异常");
					}
					else if (Player.PropBag == null)
					{
						Console.WriteLine("SendUpgradeRelic Error: Player.PropBag is null");
						Player.SendMessage("背包信息异常！");
					}
					else
					{
						ItemInfo itemByTemplateID = Player.PropBag.GetItemByTemplateID(num);
						if (itemByTemplateID == null || itemByTemplateID.Count < num2)
						{
							Player.SendMessage("圣物升级石数量不足！");
						}
						else if (!Player.PropBag.RemoveCountFromStack(itemByTemplateID, num2))
						{
							Player.SendMessage("扣除道具失败，请稍后重试！");
						}
						else if (Player.PlayerCharacter == null)
						{
							Console.WriteLine("SendUpgradeRelic Error: Player.PlayerCharacter is null");
							Player.SendMessage("角色圣物信息异常！");
						}
						else if (Player.PlayerCharacter.RelicItem == null)
						{
							Console.WriteLine("SendUpgradeRelic Error: Player.PlayerCharacter.RelicItem is null");
							Player.SendMessage("角色圣物信息异常！");
						}
						else
						{
							Sys_User_RelicItemTemplate sys_User_RelicItemTemplate = Player.PlayerCharacter.RelicItem.FirstOrDefault((Sys_User_RelicItemTemplate i) => i.itemID == _curDetailID);
							if (sys_User_RelicItemTemplate == null)
							{
								Player.SendMessage("未找到对应的圣物信息！");
							}
							else
							{
								sys_User_RelicItemTemplate.curExp += num2 * relicUpgradeItem;
								try
								{
									if (RelicItemMgr.GetRelicLevel(sys_User_RelicItemTemplate.itemID, sys_User_RelicItemTemplate.curExp, sys_User_RelicItemTemplate.level) != sys_User_RelicItemTemplate.level)
									{
										Sys_User_RelicItemTemplate sys_User_RelicItemTemplate2 = sys_User_RelicItemTemplate;
										int level = sys_User_RelicItemTemplate2.level;
										sys_User_RelicItemTemplate2.level = level + 1;
										sys_User_RelicItemTemplate.curExp = 0;
									}
								}
								catch (Exception ex)
								{
									Console.WriteLine("SendUpgradeRelic GetRelicLevel Error: " + ex.Message);
								}
								this.UpdatePlayerProperties(Player);
								try
								{
									this.SendUserRelicItem(Player, 3);
								}
								catch (Exception ex2)
								{
									Console.WriteLine("SendUpgradeRelic SendUserRelicItem Error: " + ex2.Message);
								}
								Player.SendMessage("升级成功！");
							}
						}
					}
				}
			}
			catch (Exception ex3)
			{
				Console.WriteLine("SendUpgradeRelic Error: " + ex3.Message);
				Console.WriteLine("SendUpgradeRelic StackTrace: " + ex3.StackTrace);
				if (Player != null)
				{
					Player.SendMessage("升级操作异常，请稍后重试！");
				}
			}
		}

		// Token: 0x060061D2 RID: 25042 RVA: 0x001EB34C File Offset: 0x001E954C
		public void SendAdvanceRelic(GamePlayer Player, GSPacketIn packet)
		{
			try
			{
				if (Player != null && packet != null)
				{
					int _curDetailID = packet.ReadInt();
					int num = packet.ReadInt();
					int num2 = packet.ReadInt();
					if (num2 != 1 && num2 != 2)
					{
						Player.SendMessage("类型错误！");
					}
					else if (Player.PropBag == null)
					{
						Player.SendMessage("背包信息异常！");
					}
					else
					{
						int num3 = 11068;
						int num4 = 386298;
						int num5 = num * 500;
						int num6 = num * 100;
						ItemInfo itemByTemplateID = Player.PropBag.GetItemByTemplateID(num3);
						ItemInfo itemByTemplateID2 = Player.PropBag.GetItemByTemplateID(num4);
						if (itemByTemplateID == null || itemByTemplateID.Count < num5 || itemByTemplateID2 == null || itemByTemplateID2.Count < num6)
						{
							Player.SendMessage("材料不足！");
						}
						else if (!Player.PropBag.RemoveCountFromStack(itemByTemplateID, num5) || !Player.PropBag.RemoveCountFromStack(itemByTemplateID2, num6))
						{
							Player.SendMessage("扣除材料失败！");
						}
						else if (Player.PlayerCharacter == null || Player.PlayerCharacter.RelicItem == null)
						{
							Player.SendMessage("角色圣物信息异常！");
						}
						else
						{
							Sys_User_RelicItemTemplate sys_User_RelicItemTemplate = Player.PlayerCharacter.RelicItem.Where((Sys_User_RelicItemTemplate i) => i.itemID == _curDetailID).FirstOrDefault<Sys_User_RelicItemTemplate>();
							if (sys_User_RelicItemTemplate == null)
							{
								Player.SendMessage("该圣物不存在！");
							}
							else if (num <= sys_User_RelicItemTemplate.stage || num != sys_User_RelicItemTemplate.stage + 1)
							{
								Player.SendMessage("进阶等级错误！");
							}
							else
							{
								this.UpgradeRelicItem(sys_User_RelicItemTemplate);
								Player.EquipBag.UpdatePlayerProperties();
								Player.SendMessage("进阶成功！");
								this.SendUserRelicItem(Player, 4);
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				if (Player != null)
				{
					Player.SendMessage("进阶操作异常，请稍后重试！");
				}
				Console.WriteLine("SendAdvanceRelic Error: " + ex.Message);
			}
		}

		// Token: 0x060061D3 RID: 25043 RVA: 0x001EB558 File Offset: 0x001E9758
		private bool CheckAndDeductAdvanceNum(GamePlayer Player, int NeedType, int ItemCount)
		{
			if (NeedType == 2)
			{
				if (Player.PlayerCharacter.RelicItemInfo.GJZFNum < ItemCount)
				{
					Player.SendMessage("高级圣物增幅石不足！！");
					return false;
				}
				Player.PlayerCharacter.RelicItemInfo.GJZFNum -= ItemCount;
			}
			else
			{
				if (Player.PlayerCharacter.RelicItemInfo.ZFNum < ItemCount)
				{
					Player.SendMessage("圣物增幅石不足！！");
					return false;
				}
				Player.PlayerCharacter.RelicItemInfo.ZFNum -= ItemCount;
			}
			return true;
		}

		// Token: 0x060061D4 RID: 25044 RVA: 0x001EB558 File Offset: 0x001E9758
		private bool CheckAndDeductZfNum(GamePlayer Player, int NeedType, int ItemCount)
		{
			if (NeedType == 2)
			{
				if (Player.PlayerCharacter.RelicItemInfo.GJZFNum < ItemCount)
				{
					Player.SendMessage("高级圣物增幅石不足！！");
					return false;
				}
				Player.PlayerCharacter.RelicItemInfo.GJZFNum -= ItemCount;
			}
			else
			{
				if (Player.PlayerCharacter.RelicItemInfo.ZFNum < ItemCount)
				{
					Player.SendMessage("圣物增幅石不足！！");
					return false;
				}
				Player.PlayerCharacter.RelicItemInfo.ZFNum -= ItemCount;
			}
			return true;
		}

		// Token: 0x060061D5 RID: 25045 RVA: 0x001EB5E4 File Offset: 0x001E97E4
		public void SendUseManualInfo(GamePlayer Player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(768);
			gspacketIn.WriteByte(5);
			List<ManualDataInfo> list = RelicItemMgr.ManualToList(Player.PlayerCharacter.RelicItemInfo.ManualInfo);
			gspacketIn.WriteInt(list.Count);
			foreach (ManualDataInfo manualDataInfo in list)
			{
				gspacketIn.WriteInt(manualDataInfo.type);
				gspacketIn.WriteInt(manualDataInfo.exp);
				gspacketIn.WriteInt(manualDataInfo.level);
			}
			Player.Out.SendTCP(gspacketIn);
		}

		// Token: 0x060061D6 RID: 25046 RVA: 0x001EB690 File Offset: 0x001E9890
		public void SendUpgradeManual(GamePlayer Player, GSPacketIn packet)
		{
			try
			{
				if (Player != null && packet != null)
				{
					int ManualType = packet.ReadInt();
					int Manuallevel = packet.ReadInt();
					if (Manuallevel > 10)
					{
						Player.SendMessage("等级超过最大限制！");
					}
					else if (Player.PlayerCharacter == null || Player.PlayerCharacter.RelicItemInfo == null)
					{
						Player.SendMessage("角色圣物信息异常！");
					}
					else
					{
						List<ManualDataInfo> list = RelicItemMgr.ManualToList(Player.PlayerCharacter.RelicItemInfo.ManualInfo);
						if (ManualType > 5 || ManualType < 0)
						{
							Player.SendMessage("类型异常！");
						}
						else
						{
							ManualDataInfo manualDataInfo = list.Where((ManualDataInfo i) => i.type == ManualType).FirstOrDefault<ManualDataInfo>();
							if (manualDataInfo == null)
							{
								Player.SendMessage("该宝典不存在！");
							}
							else if (manualDataInfo.level >= 10)
							{
								Player.SendMessage("该宝典已达到最大等级！");
							}
							else
							{
								TS_Relic_DegreeTemplate ts_Relic_DegreeTemplate = RelicItemMgr.RelicDegreeList.FirstOrDefault((TS_Relic_DegreeTemplate item) => item.Level == Manuallevel && item.Quality == ManualType);
								if (ts_Relic_DegreeTemplate == null)
								{
									Player.SendMessage("配置异常！");
								}
								else if (manualDataInfo.exp < ts_Relic_DegreeTemplate.Exp)
								{
									Player.SendMessage("经验不足！");
								}
								else
								{
									ManualDataInfo manualDataInfo2 = manualDataInfo;
									int level = manualDataInfo2.level;
									manualDataInfo2.level = level + 1;
									manualDataInfo.exp -= ts_Relic_DegreeTemplate.Exp;
									Player.PlayerCharacter.RelicItemInfo.ManualInfo = RelicItemMgr.ManualToJsonString(list);
									Player.SendMessage("宝典升级成功！");
									this.SendUseManualInfo(Player);
									Player.EquipBag.UpdatePlayerProperties();
								}
							}
						}
					}
				}
			}
			catch (Exception ex)
			{
				if (Player != null)
				{
					Player.SendMessage("宝典升级操作异常，请稍后重试！");
				}
				Console.WriteLine("SendUpgradeManual Error: " + ex.Message);
			}
		}

		// Token: 0x060061D7 RID: 25047 RVA: 0x001EB878 File Offset: 0x001E9A78
		private void UpgradeRelicItem(Sys_User_RelicItemTemplate RelicItem)
		{
			List<RelicProDataInfo> list = RelicItemMgr.ParseToList(RelicItem.ProArr);
			list.Add(RelicItemMgr.NewRelicProDataInfo(RelicItem.Quality));
			RelicItem.ProArr = RelicItemMgr.ConvertToJsonString(list);
			int stage = RelicItem.stage;
			RelicItem.stage = stage + 1;
		}

		// Token: 0x060061D8 RID: 25048 RVA: 0x001EB8C0 File Offset: 0x001E9AC0
		public void SendRelicZF(GamePlayer Player, GSPacketIn packet)
		{
			int RelicID = packet.ReadInt();
			int num = packet.ReadInt();
			int num2 = packet.ReadInt();
			Sys_User_RelicItemTemplate sys_User_RelicItemTemplate = Player.PlayerCharacter.RelicItem.Where((Sys_User_RelicItemTemplate i) => i.itemID == RelicID).FirstOrDefault<Sys_User_RelicItemTemplate>();
			if (sys_User_RelicItemTemplate == null)
			{
				Player.SendMessage("该圣物不存在！");
				return;
			}
			int zfcount = RelicItemMgr.GetZFCount(sys_User_RelicItemTemplate.Quality, num, num2);
			if (zfcount == 0)
			{
				Player.SendMessage("该词条不能增幅");
				return;
			}
			if (this.CheckAndDeductZfNum(Player, num2, zfcount))
			{
				List<RelicProDataInfo> list = RelicItemMgr.ParseToList(sys_User_RelicItemTemplate.ProArr);
				list[num - 1] = RelicItemMgr.NewRelicProDataInfo(sys_User_RelicItemTemplate.Quality);
				sys_User_RelicItemTemplate.ProArr = RelicItemMgr.ConvertToJsonString(list);
				this.SendUserRelicItem(Player, 2);
				Player.EquipBag.UpdatePlayerProperties();
			}
		}

		// Token: 0x060061D9 RID: 25049 RVA: 0x001EB994 File Offset: 0x001E9B94
		public void SendEquipRelic(GamePlayer Player, GSPacketIn packet)
		{
			int num = packet.ReadInt();
			int num2 = packet.ReadInt();
			int num3 = packet.ReadInt();
			if (num2 == 1)
			{
				Player.PlayerCharacter.RelicItemInfo.RelicInfo = RelicItemMgr.GetRelicInfo(Player.PlayerCharacter.RelicItemInfo.RelicInfo, 0, num3 - 1, num2);
				this.SendForcesBaseInfo(Player);
				Player.MainBag.UpdatePlayerProperties();
				return;
			}

			// 检查圣物是否存在
			Sys_User_RelicItemTemplate sys_User_RelicItemTemplate = Player.PlayerCharacter.RelicItem.Where((Sys_User_RelicItemTemplate i) => i.itemID == num).FirstOrDefault<Sys_User_RelicItemTemplate>();
			if (sys_User_RelicItemTemplate == null)
			{
				Player.SendMessage("该圣物不存在！");
				return;
			}

			if (num3 > Player.PlayerCharacter.RelicItemInfo.OpenCount)
			{
				Player.SendMessage("装备位置不存在！");
				return;
			}
			Player.PlayerCharacter.RelicItemInfo.RelicInfo = RelicItemMgr.GetRelicInfo(Player.PlayerCharacter.RelicItemInfo.RelicInfo, num, num3 - 1, num2);
			this.SendForcesBaseInfo(Player);
			Player.MainBag.UpdatePlayerProperties();
		}

		// Token: 0x060061DB RID: 25051 RVA: 0x001EBA58 File Offset: 0x001E9C58
		private void UpdatePlayerProperties(GamePlayer player)
		{
			try
			{
				if (player != null)
				{
					if (player.EquipBag != null)
					{
						player.EquipBag.UpdatePlayerProperties();
					}
					else if (player.MainBag != null)
					{
						player.MainBag.UpdatePlayerProperties();
					}
					else
					{
						Console.WriteLine("UpdatePlayerProperties Warning: Both EquipBag and MainBag are null");
					}
				}
			}
			catch (Exception ex)
			{
				Console.WriteLine("UpdatePlayerProperties Error: " + ex.Message);
			}
		}
	}
}
