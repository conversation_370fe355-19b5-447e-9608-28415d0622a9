﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Common.Utils.Boolean;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x02000597 RID: 1431
	internal abstract class BoolLiteral : InternalBase
	{
		// Token: 0x06004550 RID: 17744 RVA: 0x000F3CA4 File Offset: 0x000F1EA4
		internal static TermExpr<DomainConstraint<BoolLiteral, Constant>> MakeTermExpression(BoolLiteral literal, IEnumerable<Constant> domain, IEnumerable<Constant> range)
		{
			Set<Constant> set = new Set<Constant>(domain, Constant.EqualityComparer);
			Set<Constant> set2 = new Set<Constant>(range, Constant.EqualityComparer);
			return BoolLiteral.MakeTermExpression(literal, set, set2);
		}

		// Token: 0x06004551 RID: 17745 RVA: 0x000F3CD4 File Offset: 0x000F1ED4
		internal static TermExpr<DomainConstraint<BoolLiteral, Constant>> MakeTermExpression(BoolLiteral literal, Set<Constant> domain, Set<Constant> range)
		{
			domain.MakeReadOnly();
			range.MakeReadOnly();
			DomainConstraint<BoolLiteral, Constant> domainConstraint = new DomainConstraint<BoolLiteral, Constant>(new DomainVariable<BoolLiteral, Constant>(literal, domain, BoolLiteral.EqualityIdentifierComparer), range);
			return new TermExpr<DomainConstraint<BoolLiteral, Constant>>(EqualityComparer<DomainConstraint<BoolLiteral, Constant>>.Default, domainConstraint);
		}

		// Token: 0x06004552 RID: 17746
		internal abstract BoolExpr<DomainConstraint<BoolLiteral, Constant>> FixRange(Set<Constant> range, MemberDomainMap memberDomainMap);

		// Token: 0x06004553 RID: 17747
		internal abstract BoolExpr<DomainConstraint<BoolLiteral, Constant>> GetDomainBoolExpression(MemberDomainMap domainMap);

		// Token: 0x06004554 RID: 17748
		internal abstract BoolLiteral RemapBool(Dictionary<MemberPath, MemberPath> remap);

		// Token: 0x06004555 RID: 17749
		internal abstract void GetRequiredSlots(MemberProjectionIndex projectedSlotMap, bool[] requiredSlots);

		// Token: 0x06004556 RID: 17750
		internal abstract StringBuilder AsEsql(StringBuilder builder, string blockAlias, bool skipIsNotNull);

		// Token: 0x06004557 RID: 17751
		internal abstract DbExpression AsCqt(DbExpression row, bool skipIsNotNull);

		// Token: 0x06004558 RID: 17752
		internal abstract StringBuilder AsUserString(StringBuilder builder, string blockAlias, bool skipIsNotNull);

		// Token: 0x06004559 RID: 17753
		internal abstract StringBuilder AsNegatedUserString(StringBuilder builder, string blockAlias, bool skipIsNotNull);

		// Token: 0x0600455A RID: 17754 RVA: 0x000F3D0D File Offset: 0x000F1F0D
		protected virtual bool IsIdentifierEqualTo(BoolLiteral right)
		{
			return this.IsEqualTo(right);
		}

		// Token: 0x0600455B RID: 17755
		protected abstract bool IsEqualTo(BoolLiteral right);

		// Token: 0x0600455C RID: 17756 RVA: 0x000F3D16 File Offset: 0x000F1F16
		protected virtual int GetIdentifierHash()
		{
			return this.GetHashCode();
		}

		// Token: 0x040018E3 RID: 6371
		internal static readonly IEqualityComparer<BoolLiteral> EqualityComparer = new BoolLiteral.BoolLiteralComparer();

		// Token: 0x040018E4 RID: 6372
		internal static readonly IEqualityComparer<BoolLiteral> EqualityIdentifierComparer = new BoolLiteral.IdentifierComparer();

		// Token: 0x02000BB1 RID: 2993
		private sealed class BoolLiteralComparer : IEqualityComparer<BoolLiteral>
		{
			// Token: 0x06006783 RID: 26499 RVA: 0x0016138D File Offset: 0x0015F58D
			public bool Equals(BoolLiteral left, BoolLiteral right)
			{
				return left == right || (left != null && right != null && left.IsEqualTo(right));
			}

			// Token: 0x06006784 RID: 26500 RVA: 0x001613A4 File Offset: 0x0015F5A4
			public int GetHashCode(BoolLiteral literal)
			{
				return literal.GetHashCode();
			}
		}

		// Token: 0x02000BB2 RID: 2994
		private sealed class IdentifierComparer : IEqualityComparer<BoolLiteral>
		{
			// Token: 0x06006786 RID: 26502 RVA: 0x001613B4 File Offset: 0x0015F5B4
			public bool Equals(BoolLiteral left, BoolLiteral right)
			{
				return left == right || (left != null && right != null && left.IsIdentifierEqualTo(right));
			}

			// Token: 0x06006787 RID: 26503 RVA: 0x001613CB File Offset: 0x0015F5CB
			public int GetHashCode(BoolLiteral literal)
			{
				return literal.GetIdentifierHash();
			}
		}
	}
}
