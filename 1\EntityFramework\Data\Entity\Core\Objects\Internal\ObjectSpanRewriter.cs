﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;
using System.Globalization;

namespace System.Data.Entity.Core.Objects.Internal
{
	// Token: 0x02000453 RID: 1107
	internal class ObjectSpanRewriter
	{
		// Token: 0x0600360F RID: 13839 RVA: 0x000ACF11 File Offset: 0x000AB111
		internal static bool EntityTypeEquals(EntityTypeBase entityType1, EntityTypeBase entityType2)
		{
			return entityType1 == entityType2;
		}

		// Token: 0x06003610 RID: 13840 RVA: 0x000ACF18 File Offset: 0x000AB118
		internal static bool TryRewrite(DbQueryCommandTree tree, Span span, MergeOption mergeOption, AliasGenerator aliasGenerator, out DbExpression newQuery, out SpanIndex spanInfo)
		{
			newQuery = null;
			spanInfo = null;
			ObjectSpanRewriter objectSpanRewriter = null;
			bool flag = Span.RequiresRelationshipSpan(mergeOption);
			if (span != null && span.SpanList.Count > 0)
			{
				objectSpanRewriter = new ObjectFullSpanRewriter(tree, tree.Query, span, aliasGenerator);
			}
			else if (flag)
			{
				objectSpanRewriter = new ObjectSpanRewriter(tree, tree.Query, aliasGenerator);
			}
			if (objectSpanRewriter != null)
			{
				objectSpanRewriter.RelationshipSpan = flag;
				newQuery = objectSpanRewriter.RewriteQuery();
				if (newQuery != null)
				{
					spanInfo = objectSpanRewriter.SpanIndex;
				}
			}
			return spanInfo != null;
		}

		// Token: 0x06003611 RID: 13841 RVA: 0x000ACF90 File Offset: 0x000AB190
		internal ObjectSpanRewriter(DbCommandTree tree, DbExpression toRewrite, AliasGenerator aliasGenerator)
		{
			this._toRewrite = toRewrite;
			this._tree = tree;
			this._aliasGenerator = aliasGenerator;
		}

		// Token: 0x17000A74 RID: 2676
		// (get) Token: 0x06003612 RID: 13842 RVA: 0x000ACFB8 File Offset: 0x000AB1B8
		internal MetadataWorkspace Metadata
		{
			get
			{
				return this._tree.MetadataWorkspace;
			}
		}

		// Token: 0x17000A75 RID: 2677
		// (get) Token: 0x06003613 RID: 13843 RVA: 0x000ACFC5 File Offset: 0x000AB1C5
		internal DbExpression Query
		{
			get
			{
				return this._toRewrite;
			}
		}

		// Token: 0x17000A76 RID: 2678
		// (get) Token: 0x06003614 RID: 13844 RVA: 0x000ACFCD File Offset: 0x000AB1CD
		// (set) Token: 0x06003615 RID: 13845 RVA: 0x000ACFD5 File Offset: 0x000AB1D5
		internal bool RelationshipSpan
		{
			get
			{
				return this._relationshipSpan;
			}
			set
			{
				this._relationshipSpan = value;
			}
		}

		// Token: 0x17000A77 RID: 2679
		// (get) Token: 0x06003616 RID: 13846 RVA: 0x000ACFDE File Offset: 0x000AB1DE
		internal SpanIndex SpanIndex
		{
			get
			{
				return this._spanIndex;
			}
		}

		// Token: 0x06003617 RID: 13847 RVA: 0x000ACFE8 File Offset: 0x000AB1E8
		internal DbExpression RewriteQuery()
		{
			DbExpression dbExpression = this.Rewrite(this._toRewrite);
			if (this._toRewrite == dbExpression)
			{
				return null;
			}
			return dbExpression;
		}

		// Token: 0x06003618 RID: 13848 RVA: 0x000AD010 File Offset: 0x000AB210
		internal ObjectSpanRewriter.SpanTrackingInfo InitializeTrackingInfo(bool createAssociationEndTrackingInfo)
		{
			ObjectSpanRewriter.SpanTrackingInfo spanTrackingInfo = default(ObjectSpanRewriter.SpanTrackingInfo);
			spanTrackingInfo.ColumnDefinitions = new List<KeyValuePair<string, DbExpression>>();
			spanTrackingInfo.ColumnNames = new AliasGenerator(string.Format(CultureInfo.InvariantCulture, "Span{0}_Column", new object[] { this._spanCount }));
			spanTrackingInfo.SpannedColumns = new Dictionary<int, AssociationEndMember>();
			if (createAssociationEndTrackingInfo)
			{
				spanTrackingInfo.FullSpannedEnds = new Dictionary<AssociationEndMember, bool>();
			}
			return spanTrackingInfo;
		}

		// Token: 0x06003619 RID: 13849 RVA: 0x000AD07C File Offset: 0x000AB27C
		internal virtual ObjectSpanRewriter.SpanTrackingInfo CreateEntitySpanTrackingInfo(DbExpression expression, EntityType entityType)
		{
			return default(ObjectSpanRewriter.SpanTrackingInfo);
		}

		// Token: 0x0600361A RID: 13850 RVA: 0x000AD094 File Offset: 0x000AB294
		protected DbExpression Rewrite(DbExpression expression)
		{
			DbExpressionKind expressionKind = expression.ExpressionKind;
			if (expressionKind == DbExpressionKind.Element)
			{
				return this.RewriteElementExpression((DbElementExpression)expression);
			}
			if (expressionKind == DbExpressionKind.Limit)
			{
				return this.RewriteLimitExpression((DbLimitExpression)expression);
			}
			BuiltInTypeKind builtInTypeKind = expression.ResultType.EdmType.BuiltInTypeKind;
			if (builtInTypeKind == BuiltInTypeKind.CollectionType)
			{
				return this.RewriteCollection(expression);
			}
			if (builtInTypeKind == BuiltInTypeKind.EntityType)
			{
				return this.RewriteEntity(expression, (EntityType)expression.ResultType.EdmType);
			}
			if (builtInTypeKind != BuiltInTypeKind.RowType)
			{
				return expression;
			}
			return this.RewriteRow(expression, (RowType)expression.ResultType.EdmType);
		}

		// Token: 0x0600361B RID: 13851 RVA: 0x000AD128 File Offset: 0x000AB328
		private void AddSpannedRowType(RowType spannedType, TypeUsage originalType)
		{
			if (this._spanIndex == null)
			{
				this._spanIndex = new SpanIndex();
			}
			this._spanIndex.AddSpannedRowType(spannedType, originalType);
		}

		// Token: 0x0600361C RID: 13852 RVA: 0x000AD14A File Offset: 0x000AB34A
		private void AddSpanMap(RowType rowType, Dictionary<int, AssociationEndMember> columnMap)
		{
			if (this._spanIndex == null)
			{
				this._spanIndex = new SpanIndex();
			}
			this._spanIndex.AddSpanMap(rowType, columnMap);
		}

		// Token: 0x0600361D RID: 13853 RVA: 0x000AD16C File Offset: 0x000AB36C
		private DbExpression RewriteEntity(DbExpression expression, EntityType entityType)
		{
			if (DbExpressionKind.NewInstance == expression.ExpressionKind)
			{
				return expression;
			}
			this._spanCount++;
			int spanCount = this._spanCount;
			ObjectSpanRewriter.SpanTrackingInfo spanTrackingInfo = this.CreateEntitySpanTrackingInfo(expression, entityType);
			List<KeyValuePair<AssociationEndMember, AssociationEndMember>> relationshipSpanEnds = this.GetRelationshipSpanEnds(entityType);
			if (relationshipSpanEnds != null)
			{
				if (spanTrackingInfo.ColumnDefinitions == null)
				{
					spanTrackingInfo = this.InitializeTrackingInfo(false);
				}
				int num = spanTrackingInfo.ColumnDefinitions.Count + 1;
				foreach (KeyValuePair<AssociationEndMember, AssociationEndMember> keyValuePair in relationshipSpanEnds)
				{
					if (spanTrackingInfo.FullSpannedEnds == null || !spanTrackingInfo.FullSpannedEnds.ContainsKey(keyValuePair.Value))
					{
						DbExpression dbExpression = null;
						if (!this.TryGetNavigationSource(keyValuePair.Value, out dbExpression))
						{
							dbExpression = expression.GetEntityRef().NavigateAllowingAllRelationshipsInSameTypeHierarchy(keyValuePair.Key, keyValuePair.Value);
						}
						spanTrackingInfo.ColumnDefinitions.Add(new KeyValuePair<string, DbExpression>(spanTrackingInfo.ColumnNames.Next(), dbExpression));
						spanTrackingInfo.SpannedColumns[num] = keyValuePair.Value;
						num++;
					}
				}
			}
			if (spanTrackingInfo.ColumnDefinitions == null)
			{
				this._spanCount--;
				return expression;
			}
			spanTrackingInfo.ColumnDefinitions.Insert(0, new KeyValuePair<string, DbExpression>(string.Format(CultureInfo.InvariantCulture, "Span{0}_SpanRoot", new object[] { spanCount }), expression));
			DbNewInstanceExpression dbNewInstanceExpression = DbExpressionBuilder.NewRow(spanTrackingInfo.ColumnDefinitions);
			RowType rowType = (RowType)dbNewInstanceExpression.ResultType.EdmType;
			this.AddSpanMap(rowType, spanTrackingInfo.SpannedColumns);
			return dbNewInstanceExpression;
		}

		// Token: 0x0600361E RID: 13854 RVA: 0x000AD308 File Offset: 0x000AB508
		private DbExpression RewriteElementExpression(DbElementExpression expression)
		{
			DbExpression dbExpression = this.Rewrite(expression.Argument);
			if (expression.Argument != dbExpression)
			{
				expression = dbExpression.Element();
			}
			return expression;
		}

		// Token: 0x0600361F RID: 13855 RVA: 0x000AD334 File Offset: 0x000AB534
		private DbExpression RewriteLimitExpression(DbLimitExpression expression)
		{
			DbExpression dbExpression = this.Rewrite(expression.Argument);
			if (expression.Argument != dbExpression)
			{
				expression = dbExpression.Limit(expression.Limit);
			}
			return expression;
		}

		// Token: 0x06003620 RID: 13856 RVA: 0x000AD368 File Offset: 0x000AB568
		private DbExpression RewriteRow(DbExpression expression, RowType rowType)
		{
			DbLambdaExpression dbLambdaExpression = expression as DbLambdaExpression;
			DbNewInstanceExpression dbNewInstanceExpression;
			if (dbLambdaExpression != null)
			{
				dbNewInstanceExpression = dbLambdaExpression.Lambda.Body as DbNewInstanceExpression;
			}
			else
			{
				dbNewInstanceExpression = expression as DbNewInstanceExpression;
			}
			Dictionary<int, DbExpression> dictionary = null;
			Dictionary<int, DbExpression> dictionary2 = null;
			for (int i = 0; i < rowType.Properties.Count; i++)
			{
				EdmProperty edmProperty = rowType.Properties[i];
				DbExpression dbExpression;
				if (dbNewInstanceExpression != null)
				{
					dbExpression = dbNewInstanceExpression.Arguments[i];
				}
				else
				{
					dbExpression = expression.Property(edmProperty.Name);
				}
				DbExpression dbExpression2 = this.Rewrite(dbExpression);
				if (dbExpression2 != dbExpression)
				{
					if (dictionary2 == null)
					{
						dictionary2 = new Dictionary<int, DbExpression>();
					}
					dictionary2[i] = dbExpression2;
				}
				else
				{
					if (dictionary == null)
					{
						dictionary = new Dictionary<int, DbExpression>();
					}
					dictionary[i] = dbExpression;
				}
			}
			if (dictionary2 == null)
			{
				return expression;
			}
			List<DbExpression> list = new List<DbExpression>(rowType.Properties.Count);
			List<EdmProperty> list2 = new List<EdmProperty>(rowType.Properties.Count);
			for (int j = 0; j < rowType.Properties.Count; j++)
			{
				EdmProperty edmProperty2 = rowType.Properties[j];
				DbExpression dbExpression3 = null;
				if (!dictionary2.TryGetValue(j, out dbExpression3))
				{
					dbExpression3 = dictionary[j];
				}
				list.Add(dbExpression3);
				list2.Add(new EdmProperty(edmProperty2.Name, dbExpression3.ResultType));
			}
			RowType rowType2 = new RowType(list2, rowType.InitializerMetadata);
			TypeUsage typeUsage = TypeUsage.Create(rowType2);
			DbExpression dbExpression4 = typeUsage.New(list);
			if (dbNewInstanceExpression == null)
			{
				DbExpression dbExpression5 = expression.IsNull();
				DbExpression dbExpression6 = typeUsage.Null();
				dbExpression4 = DbExpressionBuilder.Case(new List<DbExpression>(new DbExpression[] { dbExpression5 }), new List<DbExpression>(new DbExpression[] { dbExpression6 }), dbExpression4);
			}
			this.AddSpannedRowType(rowType2, expression.ResultType);
			if (dbLambdaExpression != null && dbNewInstanceExpression != null)
			{
				dbExpression4 = DbLambda.Create(dbExpression4, dbLambdaExpression.Lambda.Variables).Invoke(dbLambdaExpression.Arguments);
			}
			return dbExpression4;
		}

		// Token: 0x06003621 RID: 13857 RVA: 0x000AD54C File Offset: 0x000AB74C
		private DbExpression RewriteCollection(DbExpression expression)
		{
			DbExpression dbExpression = expression;
			DbProjectExpression dbProjectExpression = null;
			if (DbExpressionKind.Project == expression.ExpressionKind)
			{
				dbProjectExpression = (DbProjectExpression)expression;
				dbExpression = dbProjectExpression.Input.Expression;
			}
			ObjectSpanRewriter.NavigationInfo navigationInfo = null;
			if (this.RelationshipSpan)
			{
				dbExpression = ObjectSpanRewriter.RelationshipNavigationVisitor.FindNavigationExpression(dbExpression, this._aliasGenerator, out navigationInfo);
			}
			if (navigationInfo != null)
			{
				this.EnterNavigationCollection(navigationInfo);
			}
			else
			{
				this.EnterCollection();
			}
			DbExpression dbExpression2 = expression;
			if (dbProjectExpression != null)
			{
				DbExpression dbExpression3 = this.Rewrite(dbProjectExpression.Projection);
				if (dbProjectExpression.Projection != dbExpression3)
				{
					dbExpression2 = dbExpression.BindAs(dbProjectExpression.Input.VariableName).Project(dbExpression3);
				}
			}
			else
			{
				DbExpressionBinding dbExpressionBinding = dbExpression.BindAs(this._aliasGenerator.Next());
				DbExpression variable = dbExpressionBinding.Variable;
				DbExpression dbExpression4 = this.Rewrite(variable);
				if (variable != dbExpression4)
				{
					dbExpression2 = dbExpressionBinding.Project(dbExpression4);
				}
			}
			this.ExitCollection();
			if (navigationInfo != null && navigationInfo.InUse)
			{
				List<DbVariableReferenceExpression> list = new List<DbVariableReferenceExpression>(1);
				list.Add(navigationInfo.SourceVariable);
				List<DbExpression> list2 = new List<DbExpression>(1);
				list2.Add(navigationInfo.Source);
				dbExpression2 = DbExpressionBuilder.Lambda(dbExpression2, list).Invoke(list2);
			}
			return dbExpression2;
		}

		// Token: 0x06003622 RID: 13858 RVA: 0x000AD65F File Offset: 0x000AB85F
		private void EnterCollection()
		{
			this._navSources.Push(null);
		}

		// Token: 0x06003623 RID: 13859 RVA: 0x000AD66D File Offset: 0x000AB86D
		private void EnterNavigationCollection(ObjectSpanRewriter.NavigationInfo info)
		{
			this._navSources.Push(info);
		}

		// Token: 0x06003624 RID: 13860 RVA: 0x000AD67B File Offset: 0x000AB87B
		private void ExitCollection()
		{
			this._navSources.Pop();
		}

		// Token: 0x06003625 RID: 13861 RVA: 0x000AD68C File Offset: 0x000AB88C
		private bool TryGetNavigationSource(AssociationEndMember wasSourceNowTargetEnd, out DbExpression source)
		{
			source = null;
			ObjectSpanRewriter.NavigationInfo navigationInfo = null;
			if (this._navSources.Count > 0)
			{
				navigationInfo = this._navSources.Peek();
				if (navigationInfo != null && wasSourceNowTargetEnd != navigationInfo.SourceEnd)
				{
					navigationInfo = null;
				}
			}
			if (navigationInfo != null)
			{
				source = navigationInfo.SourceVariable;
				navigationInfo.InUse = true;
				return true;
			}
			return false;
		}

		// Token: 0x06003626 RID: 13862 RVA: 0x000AD6DC File Offset: 0x000AB8DC
		private List<KeyValuePair<AssociationEndMember, AssociationEndMember>> GetRelationshipSpanEnds(EntityType entityType)
		{
			List<KeyValuePair<AssociationEndMember, AssociationEndMember>> list = null;
			if (this._relationshipSpan)
			{
				foreach (AssociationType associationType in this._tree.MetadataWorkspace.GetItems<AssociationType>(DataSpace.CSpace))
				{
					if (2 == associationType.AssociationEndMembers.Count)
					{
						AssociationEndMember associationEndMember = associationType.AssociationEndMembers[0];
						AssociationEndMember associationEndMember2 = associationType.AssociationEndMembers[1];
						if (ObjectSpanRewriter.IsValidRelationshipSpan(entityType, associationType, associationEndMember, associationEndMember2))
						{
							if (list == null)
							{
								list = new List<KeyValuePair<AssociationEndMember, AssociationEndMember>>();
							}
							list.Add(new KeyValuePair<AssociationEndMember, AssociationEndMember>(associationEndMember, associationEndMember2));
						}
						if (ObjectSpanRewriter.IsValidRelationshipSpan(entityType, associationType, associationEndMember2, associationEndMember))
						{
							if (list == null)
							{
								list = new List<KeyValuePair<AssociationEndMember, AssociationEndMember>>();
							}
							list.Add(new KeyValuePair<AssociationEndMember, AssociationEndMember>(associationEndMember2, associationEndMember));
						}
					}
				}
			}
			return list;
		}

		// Token: 0x06003627 RID: 13863 RVA: 0x000AD7AC File Offset: 0x000AB9AC
		private static bool IsValidRelationshipSpan(EntityType compareType, AssociationType associationType, AssociationEndMember fromEnd, AssociationEndMember toEnd)
		{
			if (!associationType.IsForeignKey && (RelationshipMultiplicity.One == toEnd.RelationshipMultiplicity || toEnd.RelationshipMultiplicity == RelationshipMultiplicity.ZeroOrOne))
			{
				EntityType entityType = (EntityType)((RefType)fromEnd.TypeUsage.EdmType).ElementType;
				return ObjectSpanRewriter.EntityTypeEquals(compareType, entityType) || TypeSemantics.IsSubTypeOf(compareType, entityType) || TypeSemantics.IsSubTypeOf(entityType, compareType);
			}
			return false;
		}

		// Token: 0x04001177 RID: 4471
		private int _spanCount;

		// Token: 0x04001178 RID: 4472
		private SpanIndex _spanIndex;

		// Token: 0x04001179 RID: 4473
		private readonly DbExpression _toRewrite;

		// Token: 0x0400117A RID: 4474
		private bool _relationshipSpan;

		// Token: 0x0400117B RID: 4475
		private readonly DbCommandTree _tree;

		// Token: 0x0400117C RID: 4476
		private readonly Stack<ObjectSpanRewriter.NavigationInfo> _navSources = new Stack<ObjectSpanRewriter.NavigationInfo>();

		// Token: 0x0400117D RID: 4477
		private readonly AliasGenerator _aliasGenerator;

		// Token: 0x02000A56 RID: 2646
		internal struct SpanTrackingInfo
		{
			// Token: 0x04002A81 RID: 10881
			public List<KeyValuePair<string, DbExpression>> ColumnDefinitions;

			// Token: 0x04002A82 RID: 10882
			public AliasGenerator ColumnNames;

			// Token: 0x04002A83 RID: 10883
			public Dictionary<int, AssociationEndMember> SpannedColumns;

			// Token: 0x04002A84 RID: 10884
			public Dictionary<AssociationEndMember, bool> FullSpannedEnds;
		}

		// Token: 0x02000A57 RID: 2647
		private class NavigationInfo
		{
			// Token: 0x060061C1 RID: 25025 RVA: 0x0014FC42 File Offset: 0x0014DE42
			public NavigationInfo(DbRelationshipNavigationExpression originalNavigation, DbRelationshipNavigationExpression rewrittenNavigation)
			{
				this._sourceEnd = (AssociationEndMember)originalNavigation.NavigateFrom;
				this._sourceRef = (DbVariableReferenceExpression)rewrittenNavigation.NavigationSource;
				this._source = originalNavigation.NavigationSource;
			}

			// Token: 0x170010BF RID: 4287
			// (get) Token: 0x060061C2 RID: 25026 RVA: 0x0014FC78 File Offset: 0x0014DE78
			public AssociationEndMember SourceEnd
			{
				get
				{
					return this._sourceEnd;
				}
			}

			// Token: 0x170010C0 RID: 4288
			// (get) Token: 0x060061C3 RID: 25027 RVA: 0x0014FC80 File Offset: 0x0014DE80
			public DbExpression Source
			{
				get
				{
					return this._source;
				}
			}

			// Token: 0x170010C1 RID: 4289
			// (get) Token: 0x060061C4 RID: 25028 RVA: 0x0014FC88 File Offset: 0x0014DE88
			public DbVariableReferenceExpression SourceVariable
			{
				get
				{
					return this._sourceRef;
				}
			}

			// Token: 0x04002A85 RID: 10885
			private readonly DbVariableReferenceExpression _sourceRef;

			// Token: 0x04002A86 RID: 10886
			private readonly AssociationEndMember _sourceEnd;

			// Token: 0x04002A87 RID: 10887
			private readonly DbExpression _source;

			// Token: 0x04002A88 RID: 10888
			public bool InUse;
		}

		// Token: 0x02000A58 RID: 2648
		private class RelationshipNavigationVisitor : DefaultExpressionVisitor
		{
			// Token: 0x060061C5 RID: 25029 RVA: 0x0014FC90 File Offset: 0x0014DE90
			internal static DbExpression FindNavigationExpression(DbExpression expression, AliasGenerator aliasGenerator, out ObjectSpanRewriter.NavigationInfo navInfo)
			{
				navInfo = null;
				TypeUsage typeUsage = ((CollectionType)expression.ResultType.EdmType).TypeUsage;
				if (!TypeSemantics.IsEntityType(typeUsage) && !TypeSemantics.IsReferenceType(typeUsage))
				{
					return expression;
				}
				ObjectSpanRewriter.RelationshipNavigationVisitor relationshipNavigationVisitor = new ObjectSpanRewriter.RelationshipNavigationVisitor(aliasGenerator);
				DbExpression dbExpression = relationshipNavigationVisitor.Find(expression);
				if (expression != dbExpression)
				{
					navInfo = new ObjectSpanRewriter.NavigationInfo(relationshipNavigationVisitor._original, relationshipNavigationVisitor._rewritten);
					return dbExpression;
				}
				return expression;
			}

			// Token: 0x060061C6 RID: 25030 RVA: 0x0014FCF1 File Offset: 0x0014DEF1
			private RelationshipNavigationVisitor(AliasGenerator aliasGenerator)
			{
				this._aliasGenerator = aliasGenerator;
			}

			// Token: 0x060061C7 RID: 25031 RVA: 0x0014FD00 File Offset: 0x0014DF00
			private DbExpression Find(DbExpression expression)
			{
				return this.VisitExpression(expression);
			}

			// Token: 0x060061C8 RID: 25032 RVA: 0x0014FD0C File Offset: 0x0014DF0C
			protected override DbExpression VisitExpression(DbExpression expression)
			{
				DbExpressionKind expressionKind = expression.ExpressionKind;
				if (expressionKind <= DbExpressionKind.Limit)
				{
					if (expressionKind != DbExpressionKind.Distinct && expressionKind != DbExpressionKind.Filter && expressionKind != DbExpressionKind.Limit)
					{
						return expression;
					}
				}
				else if (expressionKind <= DbExpressionKind.Project)
				{
					if (expressionKind != DbExpressionKind.OfType && expressionKind != DbExpressionKind.Project)
					{
						return expression;
					}
				}
				else if (expressionKind != DbExpressionKind.RelationshipNavigation && expressionKind - DbExpressionKind.Skip > 1)
				{
					return expression;
				}
				return base.VisitExpression(expression);
			}

			// Token: 0x060061C9 RID: 25033 RVA: 0x0014FD5C File Offset: 0x0014DF5C
			public override DbExpression Visit(DbRelationshipNavigationExpression expression)
			{
				Check.NotNull<DbRelationshipNavigationExpression>(expression, "expression");
				this._original = expression;
				string text = this._aliasGenerator.Next();
				DbVariableReferenceExpression dbVariableReferenceExpression = new DbVariableReferenceExpression(expression.NavigationSource.ResultType, text);
				this._rewritten = dbVariableReferenceExpression.Navigate(expression.NavigateFrom, expression.NavigateTo);
				return this._rewritten;
			}

			// Token: 0x060061CA RID: 25034 RVA: 0x0014FDB8 File Offset: 0x0014DFB8
			public override DbExpression Visit(DbFilterExpression expression)
			{
				Check.NotNull<DbFilterExpression>(expression, "expression");
				DbExpression dbExpression = this.Find(expression.Input.Expression);
				if (dbExpression != expression.Input.Expression)
				{
					return dbExpression.BindAs(expression.Input.VariableName).Filter(expression.Predicate);
				}
				return expression;
			}

			// Token: 0x060061CB RID: 25035 RVA: 0x0014FE10 File Offset: 0x0014E010
			public override DbExpression Visit(DbProjectExpression expression)
			{
				Check.NotNull<DbProjectExpression>(expression, "expression");
				DbExpression dbExpression = expression.Projection;
				if (DbExpressionKind.Deref == dbExpression.ExpressionKind)
				{
					dbExpression = ((DbDerefExpression)dbExpression).Argument;
				}
				if (DbExpressionKind.VariableReference == dbExpression.ExpressionKind && ((DbVariableReferenceExpression)dbExpression).VariableName.Equals(expression.Input.VariableName, StringComparison.Ordinal))
				{
					DbExpression dbExpression2 = this.Find(expression.Input.Expression);
					if (dbExpression2 != expression.Input.Expression)
					{
						return dbExpression2.BindAs(expression.Input.VariableName).Project(expression.Projection);
					}
				}
				return expression;
			}

			// Token: 0x060061CC RID: 25036 RVA: 0x0014FEAC File Offset: 0x0014E0AC
			public override DbExpression Visit(DbSortExpression expression)
			{
				Check.NotNull<DbSortExpression>(expression, "expression");
				DbExpression dbExpression = this.Find(expression.Input.Expression);
				if (dbExpression != expression.Input.Expression)
				{
					return dbExpression.BindAs(expression.Input.VariableName).Sort(expression.SortOrder);
				}
				return expression;
			}

			// Token: 0x060061CD RID: 25037 RVA: 0x0014FF04 File Offset: 0x0014E104
			public override DbExpression Visit(DbSkipExpression expression)
			{
				Check.NotNull<DbSkipExpression>(expression, "expression");
				DbExpression dbExpression = this.Find(expression.Input.Expression);
				if (dbExpression != expression.Input.Expression)
				{
					return dbExpression.BindAs(expression.Input.VariableName).Skip(expression.SortOrder, expression.Count);
				}
				return expression;
			}

			// Token: 0x04002A89 RID: 10889
			private readonly AliasGenerator _aliasGenerator;

			// Token: 0x04002A8A RID: 10890
			private DbRelationshipNavigationExpression _original;

			// Token: 0x04002A8B RID: 10891
			private DbRelationshipNavigationExpression _rewritten;
		}
	}
}
