﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x02000548 RID: 1352
	public class MappingFragment : StructuralTypeMapping
	{
		// Token: 0x0600422E RID: 16942 RVA: 0x000DFC08 File Offset: 0x000DDE08
		public MappingFragment(EntitySet storeEntitySet, TypeMapping typeMapping, bool makeColumnsDistinct)
		{
			Check.NotNull<EntitySet>(storeEntitySet, "storeEntitySet");
			Check.NotNull<TypeMapping>(typeMapping, "typeMapping");
			this.m_tableExtent = storeEntitySet;
			this.m_typeMapping = typeMapping;
			this.m_isSQueryDistinct = makeColumnsDistinct;
		}

		// Token: 0x17000D1D RID: 3357
		// (get) Token: 0x0600422F RID: 16943 RVA: 0x000DFC6E File Offset: 0x000DDE6E
		internal IEnumerable<ColumnMappingBuilder> ColumnMappings
		{
			get
			{
				return this._columnMappings;
			}
		}

		// Token: 0x06004230 RID: 16944 RVA: 0x000DFC78 File Offset: 0x000DDE78
		internal void AddColumnMapping(ColumnMappingBuilder columnMappingBuilder)
		{
			Check.NotNull<ColumnMappingBuilder>(columnMappingBuilder, "columnMappingBuilder");
			if (!columnMappingBuilder.PropertyPath.Any<EdmProperty>() || this._columnMappings.Contains(columnMappingBuilder))
			{
				throw new ArgumentException(Strings.InvalidColumnBuilderArgument("columnBuilderMapping"));
			}
			this._columnMappings.Add(columnMappingBuilder);
			StructuralTypeMapping structuralTypeMapping = this;
			EdmProperty property;
			Func<ComplexPropertyMapping, bool> <>9__1;
			int i;
			for (i = 0; i < columnMappingBuilder.PropertyPath.Count - 1; i++)
			{
				property = columnMappingBuilder.PropertyPath[i];
				IEnumerable<ComplexPropertyMapping> enumerable = structuralTypeMapping.PropertyMappings.OfType<ComplexPropertyMapping>();
				Func<ComplexPropertyMapping, bool> func;
				if ((func = <>9__1) == null)
				{
					func = (<>9__1 = (ComplexPropertyMapping pm) => pm.Property == property);
				}
				ComplexPropertyMapping complexPropertyMapping = enumerable.SingleOrDefault(func);
				ComplexTypeMapping complexTypeMapping = null;
				if (complexPropertyMapping == null)
				{
					complexTypeMapping = new ComplexTypeMapping(false);
					complexTypeMapping.AddType(property.ComplexType);
					complexPropertyMapping = new ComplexPropertyMapping(property);
					complexPropertyMapping.AddTypeMapping(complexTypeMapping);
					structuralTypeMapping.AddPropertyMapping(complexPropertyMapping);
				}
				structuralTypeMapping = complexTypeMapping ?? complexPropertyMapping.TypeMappings.Single<ComplexTypeMapping>();
			}
			property = columnMappingBuilder.PropertyPath[i];
			ScalarPropertyMapping scalarPropertyMapping = structuralTypeMapping.PropertyMappings.OfType<ScalarPropertyMapping>().SingleOrDefault((ScalarPropertyMapping pm) => pm.Property == property);
			if (scalarPropertyMapping == null)
			{
				scalarPropertyMapping = new ScalarPropertyMapping(property, columnMappingBuilder.ColumnProperty);
				structuralTypeMapping.AddPropertyMapping(scalarPropertyMapping);
				columnMappingBuilder.SetTarget(scalarPropertyMapping);
				return;
			}
			scalarPropertyMapping.Column = columnMappingBuilder.ColumnProperty;
		}

		// Token: 0x06004231 RID: 16945 RVA: 0x000DFDE4 File Offset: 0x000DDFE4
		internal void RemoveColumnMapping(ColumnMappingBuilder columnMappingBuilder)
		{
			this._columnMappings.Remove(columnMappingBuilder);
			MappingFragment.RemoveColumnMapping(this, columnMappingBuilder.PropertyPath);
		}

		// Token: 0x06004232 RID: 16946 RVA: 0x000DFE00 File Offset: 0x000DE000
		private static void RemoveColumnMapping(StructuralTypeMapping structuralTypeMapping, IEnumerable<EdmProperty> propertyPath)
		{
			PropertyMapping propertyMapping = structuralTypeMapping.PropertyMappings.Single((PropertyMapping pm) => pm.Property == propertyPath.First<EdmProperty>());
			if (propertyMapping is ScalarPropertyMapping)
			{
				structuralTypeMapping.RemovePropertyMapping(propertyMapping);
				return;
			}
			ComplexPropertyMapping complexPropertyMapping = (ComplexPropertyMapping)propertyMapping;
			ComplexTypeMapping complexTypeMapping = complexPropertyMapping.TypeMappings.Single<ComplexTypeMapping>();
			MappingFragment.RemoveColumnMapping(complexTypeMapping, propertyPath.Skip(1));
			if (!complexTypeMapping.PropertyMappings.Any<PropertyMapping>())
			{
				structuralTypeMapping.RemovePropertyMapping(complexPropertyMapping);
			}
		}

		// Token: 0x17000D1E RID: 3358
		// (get) Token: 0x06004233 RID: 16947 RVA: 0x000DFE79 File Offset: 0x000DE079
		// (set) Token: 0x06004234 RID: 16948 RVA: 0x000DFE81 File Offset: 0x000DE081
		public EntitySet StoreEntitySet
		{
			get
			{
				return this.m_tableExtent;
			}
			internal set
			{
				this.m_tableExtent = value;
			}
		}

		// Token: 0x17000D1F RID: 3359
		// (get) Token: 0x06004235 RID: 16949 RVA: 0x000DFE8A File Offset: 0x000DE08A
		// (set) Token: 0x06004236 RID: 16950 RVA: 0x000DFE92 File Offset: 0x000DE092
		internal EntitySet TableSet
		{
			get
			{
				return this.StoreEntitySet;
			}
			set
			{
				this.StoreEntitySet = value;
			}
		}

		// Token: 0x17000D20 RID: 3360
		// (get) Token: 0x06004237 RID: 16951 RVA: 0x000DFE9B File Offset: 0x000DE09B
		internal EntityType Table
		{
			get
			{
				return this.m_tableExtent.ElementType;
			}
		}

		// Token: 0x17000D21 RID: 3361
		// (get) Token: 0x06004238 RID: 16952 RVA: 0x000DFEA8 File Offset: 0x000DE0A8
		public TypeMapping TypeMapping
		{
			get
			{
				return this.m_typeMapping;
			}
		}

		// Token: 0x17000D22 RID: 3362
		// (get) Token: 0x06004239 RID: 16953 RVA: 0x000DFEB0 File Offset: 0x000DE0B0
		public bool MakeColumnsDistinct
		{
			get
			{
				return this.m_isSQueryDistinct;
			}
		}

		// Token: 0x17000D23 RID: 3363
		// (get) Token: 0x0600423A RID: 16954 RVA: 0x000DFEB8 File Offset: 0x000DE0B8
		internal bool IsSQueryDistinct
		{
			get
			{
				return this.MakeColumnsDistinct;
			}
		}

		// Token: 0x17000D24 RID: 3364
		// (get) Token: 0x0600423B RID: 16955 RVA: 0x000DFEC0 File Offset: 0x000DE0C0
		internal ReadOnlyCollection<PropertyMapping> AllProperties
		{
			get
			{
				List<PropertyMapping> list = new List<PropertyMapping>();
				list.AddRange(this.m_properties);
				list.AddRange(this.m_conditionProperties.Values);
				return new ReadOnlyCollection<PropertyMapping>(list);
			}
		}

		// Token: 0x17000D25 RID: 3365
		// (get) Token: 0x0600423C RID: 16956 RVA: 0x000DFEE9 File Offset: 0x000DE0E9
		public override ReadOnlyCollection<PropertyMapping> PropertyMappings
		{
			get
			{
				return new ReadOnlyCollection<PropertyMapping>(this.m_properties);
			}
		}

		// Token: 0x17000D26 RID: 3366
		// (get) Token: 0x0600423D RID: 16957 RVA: 0x000DFEF6 File Offset: 0x000DE0F6
		public override ReadOnlyCollection<ConditionPropertyMapping> Conditions
		{
			get
			{
				return new ReadOnlyCollection<ConditionPropertyMapping>(new List<ConditionPropertyMapping>(this.m_conditionProperties.Values));
			}
		}

		// Token: 0x17000D27 RID: 3367
		// (get) Token: 0x0600423E RID: 16958 RVA: 0x000DFF0D File Offset: 0x000DE10D
		internal IEnumerable<ColumnMappingBuilder> FlattenedProperties
		{
			get
			{
				return MappingFragment.GetFlattenedProperties(this.m_properties, new List<EdmProperty>());
			}
		}

		// Token: 0x0600423F RID: 16959 RVA: 0x000DFF1F File Offset: 0x000DE11F
		private static IEnumerable<ColumnMappingBuilder> GetFlattenedProperties(IEnumerable<PropertyMapping> propertyMappings, List<EdmProperty> propertyPath)
		{
			foreach (PropertyMapping propertyMapping in propertyMappings)
			{
				propertyPath.Add(propertyMapping.Property);
				ComplexPropertyMapping complexPropertyMapping = propertyMapping as ComplexPropertyMapping;
				if (complexPropertyMapping != null)
				{
					foreach (ColumnMappingBuilder columnMappingBuilder in MappingFragment.GetFlattenedProperties(complexPropertyMapping.TypeMappings.Single<ComplexTypeMapping>().PropertyMappings, propertyPath))
					{
						yield return columnMappingBuilder;
					}
					IEnumerator<ColumnMappingBuilder> enumerator2 = null;
				}
				else
				{
					ScalarPropertyMapping scalarPropertyMapping = propertyMapping as ScalarPropertyMapping;
					if (scalarPropertyMapping != null)
					{
						yield return new ColumnMappingBuilder(scalarPropertyMapping.Column, propertyPath.ToList<EdmProperty>());
					}
				}
				propertyPath.Remove(propertyMapping.Property);
				propertyMapping = null;
			}
			IEnumerator<PropertyMapping> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x17000D28 RID: 3368
		// (get) Token: 0x06004240 RID: 16960 RVA: 0x000DFF36 File Offset: 0x000DE136
		internal IEnumerable<ConditionPropertyMapping> ColumnConditions
		{
			get
			{
				return this.m_conditionProperties.Values;
			}
		}

		// Token: 0x17000D29 RID: 3369
		// (get) Token: 0x06004241 RID: 16961 RVA: 0x000DFF43 File Offset: 0x000DE143
		// (set) Token: 0x06004242 RID: 16962 RVA: 0x000DFF4B File Offset: 0x000DE14B
		internal int StartLineNumber { get; set; }

		// Token: 0x17000D2A RID: 3370
		// (get) Token: 0x06004243 RID: 16963 RVA: 0x000DFF54 File Offset: 0x000DE154
		// (set) Token: 0x06004244 RID: 16964 RVA: 0x000DFF5C File Offset: 0x000DE15C
		internal int StartLinePosition { get; set; }

		// Token: 0x17000D2B RID: 3371
		// (get) Token: 0x06004245 RID: 16965 RVA: 0x000DFF65 File Offset: 0x000DE165
		internal string SourceLocation
		{
			get
			{
				return this.m_typeMapping.SetMapping.EntityContainerMapping.SourceLocation;
			}
		}

		// Token: 0x06004246 RID: 16966 RVA: 0x000DFF7C File Offset: 0x000DE17C
		public override void AddPropertyMapping(PropertyMapping propertyMapping)
		{
			Check.NotNull<PropertyMapping>(propertyMapping, "propertyMapping");
			base.ThrowIfReadOnly();
			this.m_properties.Add(propertyMapping);
		}

		// Token: 0x06004247 RID: 16967 RVA: 0x000DFF9C File Offset: 0x000DE19C
		public override void RemovePropertyMapping(PropertyMapping propertyMapping)
		{
			Check.NotNull<PropertyMapping>(propertyMapping, "propertyMapping");
			base.ThrowIfReadOnly();
			this.m_properties.Remove(propertyMapping);
		}

		// Token: 0x06004248 RID: 16968 RVA: 0x000DFFBD File Offset: 0x000DE1BD
		public override void AddCondition(ConditionPropertyMapping condition)
		{
			Check.NotNull<ConditionPropertyMapping>(condition, "condition");
			base.ThrowIfReadOnly();
			this.AddConditionProperty(condition);
		}

		// Token: 0x06004249 RID: 16969 RVA: 0x000DFFD8 File Offset: 0x000DE1D8
		public override void RemoveCondition(ConditionPropertyMapping condition)
		{
			Check.NotNull<ConditionPropertyMapping>(condition, "condition");
			base.ThrowIfReadOnly();
			this.RemoveConditionProperty(condition);
		}

		// Token: 0x0600424A RID: 16970 RVA: 0x000DFFF3 File Offset: 0x000DE1F3
		internal void ClearConditions()
		{
			this.m_conditionProperties.Clear();
		}

		// Token: 0x0600424B RID: 16971 RVA: 0x000E0000 File Offset: 0x000DE200
		internal override void SetReadOnly()
		{
			this.m_properties.TrimExcess();
			MappingItem.SetReadOnly(this.m_properties);
			MappingItem.SetReadOnly(this.m_conditionProperties.Values);
			base.SetReadOnly();
		}

		// Token: 0x0600424C RID: 16972 RVA: 0x000E0030 File Offset: 0x000DE230
		internal void RemoveConditionProperty(ConditionPropertyMapping condition)
		{
			EdmProperty edmProperty = condition.Property ?? condition.Column;
			this.m_conditionProperties.Remove(edmProperty);
		}

		// Token: 0x0600424D RID: 16973 RVA: 0x000E005B File Offset: 0x000DE25B
		internal void AddConditionProperty(ConditionPropertyMapping conditionPropertyMap)
		{
			this.AddConditionProperty(conditionPropertyMap, delegate(EdmMember _)
			{
			});
		}

		// Token: 0x0600424E RID: 16974 RVA: 0x000E0084 File Offset: 0x000DE284
		internal void AddConditionProperty(ConditionPropertyMapping conditionPropertyMap, Action<EdmMember> duplicateMemberConditionError)
		{
			EdmProperty edmProperty = conditionPropertyMap.Property ?? conditionPropertyMap.Column;
			if (!this.m_conditionProperties.ContainsKey(edmProperty))
			{
				this.m_conditionProperties.Add(edmProperty, conditionPropertyMap);
				return;
			}
			duplicateMemberConditionError(edmProperty);
		}

		// Token: 0x04001764 RID: 5988
		private readonly List<ColumnMappingBuilder> _columnMappings = new List<ColumnMappingBuilder>();

		// Token: 0x04001765 RID: 5989
		private EntitySet m_tableExtent;

		// Token: 0x04001766 RID: 5990
		private readonly TypeMapping m_typeMapping;

		// Token: 0x04001767 RID: 5991
		private readonly Dictionary<EdmProperty, ConditionPropertyMapping> m_conditionProperties = new Dictionary<EdmProperty, ConditionPropertyMapping>(EqualityComparer<EdmProperty>.Default);

		// Token: 0x04001768 RID: 5992
		private readonly List<PropertyMapping> m_properties = new List<PropertyMapping>();

		// Token: 0x04001769 RID: 5993
		private readonly bool m_isSQueryDistinct;
	}
}
