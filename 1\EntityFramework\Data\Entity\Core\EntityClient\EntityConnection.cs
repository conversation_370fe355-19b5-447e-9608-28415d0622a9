﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Common;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.EntityClient.Internal;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Infrastructure.DependencyResolution;
using System.Data.Entity.Infrastructure.Interception;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Transactions;

namespace System.Data.Entity.Core.EntityClient
{
	// Token: 0x020005DD RID: 1501
	public class EntityConnection : DbConnection
	{
		// Token: 0x06004906 RID: 18694 RVA: 0x0010298D File Offset: 0x00100B8D
		public EntityConnection()
			: this(string.Empty)
		{
		}

		// Token: 0x06004907 RID: 18695 RVA: 0x0010299A File Offset: 0x00100B9A
		public EntityConnection(string connectionString)
		{
			this._connectionStringLock = new object();
			this._entityConnectionOwnsStoreConnection = true;
			this._associatedContexts = new List<ObjectContext>();
			base..ctor();
			this.ChangeConnectionString(connectionString);
		}

		// Token: 0x06004908 RID: 18696 RVA: 0x001029C6 File Offset: 0x00100BC6
		public EntityConnection(MetadataWorkspace workspace, DbConnection connection)
			: this(Check.NotNull<MetadataWorkspace>(workspace, "workspace"), Check.NotNull<DbConnection>(connection, "connection"), false, false)
		{
		}

		// Token: 0x06004909 RID: 18697 RVA: 0x001029E6 File Offset: 0x00100BE6
		public EntityConnection(MetadataWorkspace workspace, DbConnection connection, bool entityConnectionOwnsStoreConnection)
			: this(Check.NotNull<MetadataWorkspace>(workspace, "workspace"), Check.NotNull<DbConnection>(connection, "connection"), false, entityConnectionOwnsStoreConnection)
		{
		}

		// Token: 0x0600490A RID: 18698 RVA: 0x00102A08 File Offset: 0x00100C08
		internal EntityConnection(MetadataWorkspace workspace, DbConnection connection, bool skipInitialization, bool entityConnectionOwnsStoreConnection)
		{
			this._connectionStringLock = new object();
			this._entityConnectionOwnsStoreConnection = true;
			this._associatedContexts = new List<ObjectContext>();
			base..ctor();
			if (!skipInitialization)
			{
				if (!workspace.IsItemCollectionAlreadyRegistered(DataSpace.CSpace))
				{
					throw new ArgumentException(Strings.EntityClient_ItemCollectionsNotRegisteredInWorkspace("EdmItemCollection"));
				}
				if (!workspace.IsItemCollectionAlreadyRegistered(DataSpace.SSpace))
				{
					throw new ArgumentException(Strings.EntityClient_ItemCollectionsNotRegisteredInWorkspace("StoreItemCollection"));
				}
				if (!workspace.IsItemCollectionAlreadyRegistered(DataSpace.CSSpace))
				{
					throw new ArgumentException(Strings.EntityClient_ItemCollectionsNotRegisteredInWorkspace("StorageMappingItemCollection"));
				}
				if (connection.GetProviderFactory() == null)
				{
					throw new ProviderIncompatibleException(Strings.EntityClient_DbConnectionHasNoProvider(connection));
				}
				StoreItemCollection storeItemCollection = (StoreItemCollection)workspace.GetItemCollection(DataSpace.SSpace);
				this._providerFactory = storeItemCollection.ProviderFactory;
				this._initialized = true;
			}
			this._metadataWorkspace = workspace;
			this._storeConnection = connection;
			this._entityConnectionOwnsStoreConnection = entityConnectionOwnsStoreConnection;
			if (this._storeConnection != null)
			{
				this._entityClientConnectionState = DbInterception.Dispatch.Connection.GetState(this._storeConnection, this.InterceptionContext);
			}
			this.SubscribeToStoreConnectionStateChangeEvents();
		}

		// Token: 0x0600490B RID: 18699 RVA: 0x00102AFF File Offset: 0x00100CFF
		private void SubscribeToStoreConnectionStateChangeEvents()
		{
			if (this._storeConnection != null)
			{
				this._storeConnection.StateChange += this.StoreConnectionStateChangeHandler;
			}
		}

		// Token: 0x0600490C RID: 18700 RVA: 0x00102B21 File Offset: 0x00100D21
		private void UnsubscribeFromStoreConnectionStateChangeEvents()
		{
			if (this._storeConnection != null)
			{
				this._storeConnection.StateChange -= this.StoreConnectionStateChangeHandler;
			}
		}

		// Token: 0x0600490D RID: 18701 RVA: 0x00102B44 File Offset: 0x00100D44
		internal virtual void StoreConnectionStateChangeHandler(object sender, StateChangeEventArgs stateChange)
		{
			ConnectionState currentState = stateChange.CurrentState;
			if (this._entityClientConnectionState != currentState)
			{
				ConnectionState entityClientConnectionState = this._entityClientConnectionState;
				this._entityClientConnectionState = stateChange.CurrentState;
				this.OnStateChange(new StateChangeEventArgs(entityClientConnectionState, currentState));
			}
		}

		// Token: 0x17000E60 RID: 3680
		// (get) Token: 0x0600490E RID: 18702 RVA: 0x00102B84 File Offset: 0x00100D84
		// (set) Token: 0x0600490F RID: 18703 RVA: 0x00102CC0 File Offset: 0x00100EC0
		public override string ConnectionString
		{
			get
			{
				if (this._userConnectionOptions == null)
				{
					return string.Format(CultureInfo.InvariantCulture, "{0}={3}{4};{1}={5};{2}=\"{6}\";", new object[]
					{
						"metadata",
						"provider",
						"provider connection string",
						"reader://",
						this._metadataWorkspace.MetadataWorkspaceId,
						this._storeConnection.GetProviderInvariantName(),
						DbInterception.Dispatch.Connection.GetConnectionString(this._storeConnection, this.InterceptionContext)
					});
				}
				string usersConnectionString = this._userConnectionOptions.UsersConnectionString;
				if (this._userConnectionOptions == this._effectiveConnectionOptions && this._storeConnection != null)
				{
					string text = null;
					try
					{
						text = DbInterception.Dispatch.Connection.GetConnectionString(this._storeConnection, this.InterceptionContext);
					}
					catch (Exception ex)
					{
						if (ex.IsCatchableExceptionType())
						{
							throw new EntityException(Strings.EntityClient_ProviderSpecificError("ConnectionString"), ex);
						}
						throw;
					}
					string text2 = this._userConnectionOptions["provider connection string"];
					if (text != text2 && (!string.IsNullOrEmpty(text) || !string.IsNullOrEmpty(text2)))
					{
						return new EntityConnectionStringBuilder(usersConnectionString)
						{
							ProviderConnectionString = text
						}.ConnectionString;
					}
				}
				return usersConnectionString;
			}
			set
			{
				if (this._initialized)
				{
					throw new InvalidOperationException(Strings.EntityClient_SettingsCannotBeChangedOnOpenConnection);
				}
				this.ChangeConnectionString(value);
			}
		}

		// Token: 0x17000E61 RID: 3681
		// (get) Token: 0x06004910 RID: 18704 RVA: 0x00102CDC File Offset: 0x00100EDC
		internal IEnumerable<ObjectContext> AssociatedContexts
		{
			get
			{
				return this._associatedContexts;
			}
		}

		// Token: 0x06004911 RID: 18705 RVA: 0x00102CE4 File Offset: 0x00100EE4
		internal virtual void AssociateContext(ObjectContext context)
		{
			if (this._associatedContexts.Count != 0)
			{
				foreach (ObjectContext objectContext in this._associatedContexts.ToArray())
				{
					if (context == objectContext || objectContext.IsDisposed)
					{
						this._associatedContexts.Remove(objectContext);
					}
				}
			}
			this._associatedContexts.Add(context);
		}

		// Token: 0x17000E62 RID: 3682
		// (get) Token: 0x06004912 RID: 18706 RVA: 0x00102D41 File Offset: 0x00100F41
		internal DbInterceptionContext InterceptionContext
		{
			get
			{
				return DbInterceptionContext.Combine(this.AssociatedContexts.Select((ObjectContext c) => c.InterceptionContext));
			}
		}

		// Token: 0x17000E63 RID: 3683
		// (get) Token: 0x06004913 RID: 18707 RVA: 0x00102D74 File Offset: 0x00100F74
		public override int ConnectionTimeout
		{
			get
			{
				if (this._storeConnection == null)
				{
					return 0;
				}
				int connectionTimeout;
				try
				{
					connectionTimeout = DbInterception.Dispatch.Connection.GetConnectionTimeout(this._storeConnection, this.InterceptionContext);
				}
				catch (Exception ex)
				{
					if (ex.IsCatchableExceptionType())
					{
						throw new EntityException(Strings.EntityClient_ProviderSpecificError("ConnectionTimeout"), ex);
					}
					throw;
				}
				return connectionTimeout;
			}
		}

		// Token: 0x17000E64 RID: 3684
		// (get) Token: 0x06004914 RID: 18708 RVA: 0x00102DD8 File Offset: 0x00100FD8
		public override string Database
		{
			get
			{
				return string.Empty;
			}
		}

		// Token: 0x17000E65 RID: 3685
		// (get) Token: 0x06004915 RID: 18709 RVA: 0x00102DE0 File Offset: 0x00100FE0
		public override ConnectionState State
		{
			get
			{
				ConnectionState? fakeConnectionState = this._fakeConnectionState;
				if (fakeConnectionState == null)
				{
					return this._entityClientConnectionState;
				}
				return fakeConnectionState.GetValueOrDefault();
			}
		}

		// Token: 0x17000E66 RID: 3686
		// (get) Token: 0x06004916 RID: 18710 RVA: 0x00102E0C File Offset: 0x0010100C
		public override string DataSource
		{
			get
			{
				if (this._storeConnection == null)
				{
					return string.Empty;
				}
				string dataSource;
				try
				{
					dataSource = DbInterception.Dispatch.Connection.GetDataSource(this._storeConnection, this.InterceptionContext);
				}
				catch (Exception ex)
				{
					if (ex.IsCatchableExceptionType())
					{
						throw new EntityException(Strings.EntityClient_ProviderSpecificError("DataSource"), ex);
					}
					throw;
				}
				return dataSource;
			}
		}

		// Token: 0x17000E67 RID: 3687
		// (get) Token: 0x06004917 RID: 18711 RVA: 0x00102E74 File Offset: 0x00101074
		public override string ServerVersion
		{
			get
			{
				if (this._storeConnection == null)
				{
					throw Error.EntityClient_ConnectionStringNeededBeforeOperation();
				}
				if (this.State != ConnectionState.Open)
				{
					throw Error.EntityClient_ConnectionNotOpen();
				}
				string serverVersion;
				try
				{
					serverVersion = DbInterception.Dispatch.Connection.GetServerVersion(this._storeConnection, this.InterceptionContext);
				}
				catch (Exception ex)
				{
					if (ex.IsCatchableExceptionType())
					{
						throw new EntityException(Strings.EntityClient_ProviderSpecificError("ServerVersion"), ex);
					}
					throw;
				}
				return serverVersion;
			}
		}

		// Token: 0x17000E68 RID: 3688
		// (get) Token: 0x06004918 RID: 18712 RVA: 0x00102EEC File Offset: 0x001010EC
		protected override DbProviderFactory DbProviderFactory
		{
			get
			{
				return EntityProviderFactory.Instance;
			}
		}

		// Token: 0x17000E69 RID: 3689
		// (get) Token: 0x06004919 RID: 18713 RVA: 0x00102EF3 File Offset: 0x001010F3
		internal virtual DbProviderFactory StoreProviderFactory
		{
			get
			{
				return this._providerFactory;
			}
		}

		// Token: 0x17000E6A RID: 3690
		// (get) Token: 0x0600491A RID: 18714 RVA: 0x00102EFB File Offset: 0x001010FB
		public virtual DbConnection StoreConnection
		{
			get
			{
				return this._storeConnection;
			}
		}

		// Token: 0x0600491B RID: 18715 RVA: 0x00102F03 File Offset: 0x00101103
		public virtual MetadataWorkspace GetMetadataWorkspace()
		{
			if (this._metadataWorkspace != null)
			{
				return this._metadataWorkspace;
			}
			this._metadataWorkspace = MetadataCache.Instance.GetMetadataWorkspace(this._effectiveConnectionOptions);
			this._initialized = true;
			return this._metadataWorkspace;
		}

		// Token: 0x17000E6B RID: 3691
		// (get) Token: 0x0600491C RID: 18716 RVA: 0x00102F37 File Offset: 0x00101137
		public virtual EntityTransaction CurrentTransaction
		{
			get
			{
				if (this._currentTransaction != null && (DbInterception.Dispatch.Transaction.GetConnection(this._currentTransaction.StoreTransaction, this.InterceptionContext) == null || this.State == ConnectionState.Closed))
				{
					this.ClearCurrentTransaction();
				}
				return this._currentTransaction;
			}
		}

		// Token: 0x17000E6C RID: 3692
		// (get) Token: 0x0600491D RID: 18717 RVA: 0x00102F78 File Offset: 0x00101178
		internal virtual bool EnlistedInUserTransaction
		{
			get
			{
				bool flag;
				try
				{
					flag = this._enlistedTransaction != null && this._enlistedTransaction.TransactionInformation.Status == TransactionStatus.Active;
				}
				catch (ObjectDisposedException)
				{
					this._enlistedTransaction = null;
					flag = false;
				}
				return flag;
			}
		}

		// Token: 0x0600491E RID: 18718 RVA: 0x00102FCC File Offset: 0x001011CC
		public override void Open()
		{
			this._fakeConnectionState = null;
			if (!DbInterception.Dispatch.CancelableEntityConnection.Opening(this, this.InterceptionContext))
			{
				this._fakeConnectionState = new ConnectionState?(ConnectionState.Open);
				return;
			}
			if (this._storeConnection == null)
			{
				throw Error.EntityClient_ConnectionStringNeededBeforeOperation();
			}
			if (this.State == ConnectionState.Broken)
			{
				throw Error.EntityClient_CannotOpenBrokenConnection();
			}
			if (DbInterception.Dispatch.Connection.GetState(this._storeConnection, this.InterceptionContext) != ConnectionState.Open)
			{
				MetadataWorkspace metadataWorkspace = this.GetMetadataWorkspace();
				try
				{
					DbProviderServices.GetExecutionStrategy(this._storeConnection, metadataWorkspace).Execute(delegate
					{
						DbInterception.Dispatch.Connection.Open(this._storeConnection, this.InterceptionContext);
					});
				}
				catch (Exception ex)
				{
					if (ex.IsCatchableExceptionType())
					{
						throw new EntityException(Strings.EntityClient_ProviderSpecificError("Open"), ex);
					}
					throw;
				}
				this.ClearTransactions();
			}
			if (this._storeConnection == null || DbInterception.Dispatch.Connection.GetState(this._storeConnection, this.InterceptionContext) != ConnectionState.Open)
			{
				throw Error.EntityClient_ConnectionNotOpen();
			}
		}

		// Token: 0x0600491F RID: 18719 RVA: 0x001030CC File Offset: 0x001012CC
		public override async Task OpenAsync(CancellationToken cancellationToken)
		{
			if (this._storeConnection == null)
			{
				throw Error.EntityClient_ConnectionStringNeededBeforeOperation();
			}
			if (this.State == ConnectionState.Broken)
			{
				throw Error.EntityClient_CannotOpenBrokenConnection();
			}
			cancellationToken.ThrowIfCancellationRequested();
			if (DbInterception.Dispatch.Connection.GetState(this._storeConnection, this.InterceptionContext) != ConnectionState.Open)
			{
				MetadataWorkspace metadataWorkspace = this.GetMetadataWorkspace();
				try
				{
					await DbProviderServices.GetExecutionStrategy(this._storeConnection, metadataWorkspace).ExecuteAsync(() => DbInterception.Dispatch.Connection.OpenAsync(this._storeConnection, this.InterceptionContext, cancellationToken), cancellationToken).WithCurrentCulture();
				}
				catch (Exception ex)
				{
					if (ex.IsCatchableExceptionType())
					{
						throw new EntityException(Strings.EntityClient_ProviderSpecificError("Open"), ex);
					}
					throw;
				}
				this.ClearTransactions();
			}
			if (this._storeConnection == null || DbInterception.Dispatch.Connection.GetState(this._storeConnection, this.InterceptionContext) != ConnectionState.Open)
			{
				throw Error.EntityClient_ConnectionNotOpen();
			}
		}

		// Token: 0x06004920 RID: 18720 RVA: 0x00103119 File Offset: 0x00101319
		public new virtual EntityCommand CreateCommand()
		{
			return new EntityCommand(null, this);
		}

		// Token: 0x06004921 RID: 18721 RVA: 0x00103122 File Offset: 0x00101322
		protected override DbCommand CreateDbCommand()
		{
			return this.CreateCommand();
		}

		// Token: 0x06004922 RID: 18722 RVA: 0x0010312A File Offset: 0x0010132A
		public override void Close()
		{
			this._fakeConnectionState = null;
			if (this._storeConnection == null)
			{
				return;
			}
			this.StoreCloseHelper();
		}

		// Token: 0x06004923 RID: 18723 RVA: 0x00103147 File Offset: 0x00101347
		public override void ChangeDatabase(string databaseName)
		{
			throw new NotSupportedException();
		}

		// Token: 0x06004924 RID: 18724 RVA: 0x0010314E File Offset: 0x0010134E
		public new virtual EntityTransaction BeginTransaction()
		{
			return base.BeginTransaction() as EntityTransaction;
		}

		// Token: 0x06004925 RID: 18725 RVA: 0x0010315B File Offset: 0x0010135B
		public new virtual EntityTransaction BeginTransaction(IsolationLevel isolationLevel)
		{
			return base.BeginTransaction(isolationLevel) as EntityTransaction;
		}

		// Token: 0x06004926 RID: 18726 RVA: 0x0010316C File Offset: 0x0010136C
		protected override DbTransaction BeginDbTransaction(IsolationLevel isolationLevel)
		{
			if (this._fakeConnectionState != null)
			{
				return new EntityTransaction();
			}
			if (this.CurrentTransaction != null)
			{
				throw new InvalidOperationException(Strings.EntityClient_TransactionAlreadyStarted);
			}
			if (this._storeConnection == null)
			{
				throw Error.EntityClient_ConnectionStringNeededBeforeOperation();
			}
			if (this.State != ConnectionState.Open)
			{
				throw Error.EntityClient_ConnectionNotOpen();
			}
			BeginTransactionInterceptionContext interceptionContext = new BeginTransactionInterceptionContext(this.InterceptionContext);
			if (isolationLevel != IsolationLevel.Unspecified)
			{
				interceptionContext = interceptionContext.WithIsolationLevel(isolationLevel);
			}
			DbTransaction dbTransaction = null;
			try
			{
				dbTransaction = DbProviderServices.GetExecutionStrategy(this._storeConnection, this.GetMetadataWorkspace()).Execute<DbTransaction>(delegate
				{
					if (DbInterception.Dispatch.Connection.GetState(this._storeConnection, this.InterceptionContext) == ConnectionState.Broken)
					{
						DbInterception.Dispatch.Connection.Close(this._storeConnection, interceptionContext);
					}
					if (DbInterception.Dispatch.Connection.GetState(this._storeConnection, this.InterceptionContext) == ConnectionState.Closed)
					{
						DbInterception.Dispatch.Connection.Open(this._storeConnection, interceptionContext);
					}
					return DbInterception.Dispatch.Connection.BeginTransaction(this._storeConnection, interceptionContext);
				});
			}
			catch (Exception ex)
			{
				if (ex.IsCatchableExceptionType())
				{
					throw new EntityException(Strings.EntityClient_ErrorInBeginningTransaction, ex);
				}
				throw;
			}
			if (dbTransaction == null)
			{
				throw new ProviderIncompatibleException(Strings.EntityClient_ReturnedNullOnProviderMethod("BeginTransaction", this._storeConnection.GetType().Name));
			}
			this._currentTransaction = new EntityTransaction(this, dbTransaction);
			return this._currentTransaction;
		}

		// Token: 0x06004927 RID: 18727 RVA: 0x00103274 File Offset: 0x00101474
		internal virtual EntityTransaction UseStoreTransaction(DbTransaction storeTransaction)
		{
			if (storeTransaction == null)
			{
				this.ClearCurrentTransaction();
			}
			else
			{
				if (this.CurrentTransaction != null)
				{
					throw new InvalidOperationException(Strings.DbContext_TransactionAlreadyStarted);
				}
				if (this.EnlistedInUserTransaction)
				{
					throw new InvalidOperationException(Strings.DbContext_TransactionAlreadyEnlistedInUserTransaction);
				}
				DbConnection connection = DbInterception.Dispatch.Transaction.GetConnection(storeTransaction, this.InterceptionContext);
				if (connection == null)
				{
					throw new InvalidOperationException(Strings.DbContext_InvalidTransactionNoConnection);
				}
				if (connection != this.StoreConnection)
				{
					throw new InvalidOperationException(Strings.DbContext_InvalidTransactionForConnection);
				}
				this._currentTransaction = new EntityTransaction(this, storeTransaction);
			}
			return this._currentTransaction;
		}

		// Token: 0x06004928 RID: 18728 RVA: 0x001032FC File Offset: 0x001014FC
		public override void EnlistTransaction(Transaction transaction)
		{
			if (this._storeConnection == null)
			{
				throw Error.EntityClient_ConnectionStringNeededBeforeOperation();
			}
			if (this.State != ConnectionState.Open)
			{
				throw Error.EntityClient_ConnectionNotOpen();
			}
			try
			{
				EnlistTransactionInterceptionContext enlistTransactionInterceptionContext = new EnlistTransactionInterceptionContext(this.InterceptionContext);
				enlistTransactionInterceptionContext = enlistTransactionInterceptionContext.WithTransaction(transaction);
				DbInterception.Dispatch.Connection.EnlistTransaction(this._storeConnection, enlistTransactionInterceptionContext);
				if (transaction != null && !this.EnlistedInUserTransaction)
				{
					transaction.TransactionCompleted += this.EnlistedTransactionCompleted;
				}
				this._enlistedTransaction = transaction;
			}
			catch (Exception ex)
			{
				if (ex.IsCatchableExceptionType())
				{
					throw new EntityException(Strings.EntityClient_ProviderSpecificError("EnlistTransaction"), ex);
				}
				throw;
			}
		}

		// Token: 0x06004929 RID: 18729 RVA: 0x001033AC File Offset: 0x001015AC
		protected override void Dispose(bool disposing)
		{
			if (disposing)
			{
				this.ClearTransactions();
				if (this._storeConnection != null)
				{
					if (this._entityConnectionOwnsStoreConnection)
					{
						this.StoreCloseHelper();
					}
					this.UnsubscribeFromStoreConnectionStateChangeEvents();
					if (this._entityConnectionOwnsStoreConnection)
					{
						DbInterception.Dispatch.Connection.Dispose(this._storeConnection, this.InterceptionContext);
					}
					this._storeConnection = null;
				}
				this._entityClientConnectionState = ConnectionState.Closed;
				this.ChangeConnectionString(string.Empty);
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600492A RID: 18730 RVA: 0x00103421 File Offset: 0x00101621
		internal virtual void ClearCurrentTransaction()
		{
			this._currentTransaction = null;
		}

		// Token: 0x0600492B RID: 18731 RVA: 0x0010342C File Offset: 0x0010162C
		private void ChangeConnectionString(string newConnectionString)
		{
			DbConnectionOptions dbConnectionOptions = EntityConnection._emptyConnectionOptions;
			if (!string.IsNullOrEmpty(newConnectionString))
			{
				dbConnectionOptions = new DbConnectionOptions(newConnectionString, EntityConnectionStringBuilder.ValidKeywords);
			}
			DbProviderFactory dbProviderFactory = null;
			DbConnection dbConnection = null;
			DbConnectionOptions dbConnectionOptions2 = dbConnectionOptions;
			if (!dbConnectionOptions.IsEmpty)
			{
				string text = dbConnectionOptions["name"];
				if (!string.IsNullOrEmpty(text))
				{
					if (1 < dbConnectionOptions.Parsetable.Count)
					{
						throw new ArgumentException(Strings.EntityClient_ExtraParametersWithNamedConnection);
					}
					ConnectionStringSettings connectionStringSettings = ConfigurationManager.ConnectionStrings[text];
					if (connectionStringSettings == null || connectionStringSettings.ProviderName != "System.Data.EntityClient")
					{
						throw new ArgumentException(Strings.EntityClient_InvalidNamedConnection);
					}
					dbConnectionOptions2 = new DbConnectionOptions(connectionStringSettings.ConnectionString, EntityConnectionStringBuilder.ValidKeywords);
					if (!string.IsNullOrEmpty(dbConnectionOptions2["name"]))
					{
						throw new ArgumentException(Strings.EntityClient_NestedNamedConnection(text));
					}
				}
				EntityConnection.ValidateValueForTheKeyword(dbConnectionOptions2, "metadata");
				string text2 = EntityConnection.ValidateValueForTheKeyword(dbConnectionOptions2, "provider");
				dbProviderFactory = DbConfiguration.DependencyResolver.GetService(text2);
				dbConnection = EntityConnection.GetStoreConnection(dbProviderFactory);
				try
				{
					string text3 = dbConnectionOptions2["provider connection string"];
					if (text3 != null)
					{
						DbInterception.Dispatch.Connection.SetConnectionString(dbConnection, new DbConnectionPropertyInterceptionContext<string>(this.InterceptionContext).WithValue(text3));
					}
				}
				catch (Exception ex)
				{
					if (ex.IsCatchableExceptionType())
					{
						throw new EntityException(Strings.EntityClient_ProviderSpecificError("ConnectionString"), ex);
					}
					throw;
				}
			}
			object connectionStringLock = this._connectionStringLock;
			lock (connectionStringLock)
			{
				this._providerFactory = dbProviderFactory;
				this._metadataWorkspace = null;
				this.ClearTransactions();
				this.UnsubscribeFromStoreConnectionStateChangeEvents();
				this._storeConnection = dbConnection;
				this.SubscribeToStoreConnectionStateChangeEvents();
				this._userConnectionOptions = dbConnectionOptions;
				this._effectiveConnectionOptions = dbConnectionOptions2;
			}
		}

		// Token: 0x0600492C RID: 18732 RVA: 0x001035E8 File Offset: 0x001017E8
		private static string ValidateValueForTheKeyword(DbConnectionOptions effectiveConnectionOptions, string keywordName)
		{
			string text = effectiveConnectionOptions[keywordName];
			if (!string.IsNullOrEmpty(text))
			{
				text = text.Trim();
			}
			if (string.IsNullOrEmpty(text))
			{
				throw new ArgumentException(Strings.EntityClient_ConnectionStringMissingInfo(keywordName));
			}
			return text;
		}

		// Token: 0x0600492D RID: 18733 RVA: 0x00103621 File Offset: 0x00101821
		private void ClearTransactions()
		{
			this.ClearCurrentTransaction();
			this.ClearEnlistedTransaction();
		}

		// Token: 0x0600492E RID: 18734 RVA: 0x0010362F File Offset: 0x0010182F
		private void ClearEnlistedTransaction()
		{
			if (this.EnlistedInUserTransaction)
			{
				this._enlistedTransaction.TransactionCompleted -= this.EnlistedTransactionCompleted;
			}
			this._enlistedTransaction = null;
		}

		// Token: 0x0600492F RID: 18735 RVA: 0x00103657 File Offset: 0x00101857
		private void EnlistedTransactionCompleted(object sender, TransactionEventArgs e)
		{
			e.Transaction.TransactionCompleted -= this.EnlistedTransactionCompleted;
		}

		// Token: 0x06004930 RID: 18736 RVA: 0x00103670 File Offset: 0x00101870
		private void StoreCloseHelper()
		{
			try
			{
				if (this._storeConnection != null && DbInterception.Dispatch.Connection.GetState(this._storeConnection, this.InterceptionContext) != ConnectionState.Closed)
				{
					DbInterception.Dispatch.Connection.Close(this._storeConnection, this.InterceptionContext);
				}
				this.ClearTransactions();
			}
			catch (Exception ex)
			{
				if (ex.IsCatchableExceptionType())
				{
					throw new EntityException(Strings.EntityClient_ErrorInClosingConnection, ex);
				}
				throw;
			}
		}

		// Token: 0x06004931 RID: 18737 RVA: 0x001036EC File Offset: 0x001018EC
		private static DbConnection GetStoreConnection(DbProviderFactory factory)
		{
			DbConnection dbConnection = factory.CreateConnection();
			if (dbConnection == null)
			{
				throw new ProviderIncompatibleException(Strings.EntityClient_ReturnedNullOnProviderMethod("CreateConnection", factory.GetType().Name));
			}
			return dbConnection;
		}

		// Token: 0x040019D4 RID: 6612
		private const string EntityClientProviderName = "System.Data.EntityClient";

		// Token: 0x040019D5 RID: 6613
		private const string ProviderInvariantName = "provider";

		// Token: 0x040019D6 RID: 6614
		private const string ProviderConnectionString = "provider connection string";

		// Token: 0x040019D7 RID: 6615
		private const string ReaderPrefix = "reader://";

		// Token: 0x040019D8 RID: 6616
		private readonly object _connectionStringLock;

		// Token: 0x040019D9 RID: 6617
		private static readonly DbConnectionOptions _emptyConnectionOptions = new DbConnectionOptions(string.Empty, new string[0]);

		// Token: 0x040019DA RID: 6618
		private DbConnectionOptions _userConnectionOptions;

		// Token: 0x040019DB RID: 6619
		private DbConnectionOptions _effectiveConnectionOptions;

		// Token: 0x040019DC RID: 6620
		private ConnectionState _entityClientConnectionState;

		// Token: 0x040019DD RID: 6621
		private DbProviderFactory _providerFactory;

		// Token: 0x040019DE RID: 6622
		private DbConnection _storeConnection;

		// Token: 0x040019DF RID: 6623
		private readonly bool _entityConnectionOwnsStoreConnection;

		// Token: 0x040019E0 RID: 6624
		private MetadataWorkspace _metadataWorkspace;

		// Token: 0x040019E1 RID: 6625
		private EntityTransaction _currentTransaction;

		// Token: 0x040019E2 RID: 6626
		private Transaction _enlistedTransaction;

		// Token: 0x040019E3 RID: 6627
		private bool _initialized;

		// Token: 0x040019E4 RID: 6628
		private ConnectionState? _fakeConnectionState;

		// Token: 0x040019E5 RID: 6629
		private readonly List<ObjectContext> _associatedContexts;
	}
}
