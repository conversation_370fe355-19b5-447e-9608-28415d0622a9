﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Globalization;
using System.Linq;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x0200054D RID: 1357
	internal class MetadataMappingHasherVisitor : BaseMetadataMappingVisitor
	{
		// Token: 0x060042A0 RID: 17056 RVA: 0x000E44C4 File Offset: 0x000E26C4
		private MetadataMappingHasherVisitor(double mappingVersion, bool sortSequence)
			: base(sortSequence)
		{
			this.m_MappingVersion = mappingVersion;
			this.m_hashSourceBuilder = new CompressingHashBuilder(MetadataHelper.CreateMetadataHashAlgorithm(this.m_MappingVersion));
		}

		// Token: 0x060042A1 RID: 17057 RVA: 0x000E44F8 File Offset: 0x000E26F8
		protected override void Visit(EntityContainerMapping entityContainerMapping)
		{
			this.m_MappingVersion = entityContainerMapping.StorageMappingItemCollection.MappingVersion;
			this.m_EdmItemCollection = entityContainerMapping.StorageMappingItemCollection.EdmItemCollection;
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(entityContainerMapping, out num))
			{
				return;
			}
			if (this.m_itemsAlreadySeen.Count > 1)
			{
				this.Clean();
				this.Visit(entityContainerMapping);
				return;
			}
			this.AddObjectStartDumpToHashBuilder(entityContainerMapping, num);
			this.AddObjectContentToHashBuilder(entityContainerMapping.Identity);
			this.AddV2ObjectContentToHashBuilder(entityContainerMapping.GenerateUpdateViews, this.m_MappingVersion);
			base.Visit(entityContainerMapping);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042A2 RID: 17058 RVA: 0x000E4588 File Offset: 0x000E2788
		protected override void Visit(EntityContainer entityContainer)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(entityContainer, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(entityContainer, num);
			this.AddObjectContentToHashBuilder(entityContainer.Identity);
			base.Visit(entityContainer);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042A3 RID: 17059 RVA: 0x000E45C4 File Offset: 0x000E27C4
		protected override void Visit(EntitySetBaseMapping setMapping)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(setMapping, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(setMapping, num);
			base.Visit(setMapping);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042A4 RID: 17060 RVA: 0x000E45F4 File Offset: 0x000E27F4
		protected override void Visit(TypeMapping typeMapping)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(typeMapping, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(typeMapping, num);
			base.Visit(typeMapping);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042A5 RID: 17061 RVA: 0x000E4624 File Offset: 0x000E2824
		protected override void Visit(MappingFragment mappingFragment)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(mappingFragment, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(mappingFragment, num);
			this.AddV2ObjectContentToHashBuilder(mappingFragment.IsSQueryDistinct, this.m_MappingVersion);
			base.Visit(mappingFragment);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042A6 RID: 17062 RVA: 0x000E4669 File Offset: 0x000E2869
		protected override void Visit(PropertyMapping propertyMapping)
		{
			base.Visit(propertyMapping);
		}

		// Token: 0x060042A7 RID: 17063 RVA: 0x000E4674 File Offset: 0x000E2874
		protected override void Visit(ComplexPropertyMapping complexPropertyMapping)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(complexPropertyMapping, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(complexPropertyMapping, num);
			base.Visit(complexPropertyMapping);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042A8 RID: 17064 RVA: 0x000E46A4 File Offset: 0x000E28A4
		protected override void Visit(ComplexTypeMapping complexTypeMapping)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(complexTypeMapping, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(complexTypeMapping, num);
			base.Visit(complexTypeMapping);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042A9 RID: 17065 RVA: 0x000E46D4 File Offset: 0x000E28D4
		protected override void Visit(ConditionPropertyMapping conditionPropertyMapping)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(conditionPropertyMapping, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(conditionPropertyMapping, num);
			this.AddObjectContentToHashBuilder(conditionPropertyMapping.IsNull);
			this.AddObjectContentToHashBuilder(conditionPropertyMapping.Value);
			base.Visit(conditionPropertyMapping);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042AA RID: 17066 RVA: 0x000E4720 File Offset: 0x000E2920
		protected override void Visit(ScalarPropertyMapping scalarPropertyMapping)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(scalarPropertyMapping, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(scalarPropertyMapping, num);
			base.Visit(scalarPropertyMapping);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042AB RID: 17067 RVA: 0x000E474E File Offset: 0x000E294E
		protected override void Visit(EntitySetBase entitySetBase)
		{
			base.Visit(entitySetBase);
		}

		// Token: 0x060042AC RID: 17068 RVA: 0x000E4758 File Offset: 0x000E2958
		protected override void Visit(EntitySet entitySet)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(entitySet, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(entitySet, num);
			this.AddObjectContentToHashBuilder(entitySet.Name);
			this.AddObjectContentToHashBuilder(entitySet.Schema);
			this.AddObjectContentToHashBuilder(entitySet.Table);
			base.Visit(entitySet);
			IEnumerable<EdmType> enumerable = from type in MetadataHelper.GetTypeAndSubtypesOf(entitySet.ElementType, this.m_EdmItemCollection, false)
				where type != entitySet.ElementType
				select type;
			foreach (EdmType edmType in base.GetSequence<EdmType>(enumerable, (EdmType it) => it.Identity))
			{
				this.Visit(edmType);
			}
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042AD RID: 17069 RVA: 0x000E4860 File Offset: 0x000E2A60
		protected override void Visit(AssociationSet associationSet)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(associationSet, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(associationSet, num);
			this.AddObjectContentToHashBuilder(associationSet.Identity);
			this.AddObjectContentToHashBuilder(associationSet.Schema);
			this.AddObjectContentToHashBuilder(associationSet.Table);
			base.Visit(associationSet);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042AE RID: 17070 RVA: 0x000E48B4 File Offset: 0x000E2AB4
		protected override void Visit(EntityType entityType)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(entityType, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(entityType, num);
			this.AddObjectContentToHashBuilder(entityType.Abstract);
			this.AddObjectContentToHashBuilder(entityType.Identity);
			base.Visit(entityType);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042AF RID: 17071 RVA: 0x000E4900 File Offset: 0x000E2B00
		protected override void Visit(AssociationSetEnd associationSetEnd)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(associationSetEnd, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(associationSetEnd, num);
			this.AddObjectContentToHashBuilder(associationSetEnd.Identity);
			base.Visit(associationSetEnd);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042B0 RID: 17072 RVA: 0x000E493C File Offset: 0x000E2B3C
		protected override void Visit(AssociationType associationType)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(associationType, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(associationType, num);
			this.AddObjectContentToHashBuilder(associationType.Abstract);
			this.AddObjectContentToHashBuilder(associationType.Identity);
			base.Visit(associationType);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042B1 RID: 17073 RVA: 0x000E4988 File Offset: 0x000E2B88
		protected override void Visit(EdmProperty edmProperty)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(edmProperty, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(edmProperty, num);
			this.AddObjectContentToHashBuilder(edmProperty.DefaultValue);
			this.AddObjectContentToHashBuilder(edmProperty.Identity);
			this.AddObjectContentToHashBuilder(edmProperty.IsStoreGeneratedComputed);
			this.AddObjectContentToHashBuilder(edmProperty.IsStoreGeneratedIdentity);
			this.AddObjectContentToHashBuilder(edmProperty.Nullable);
			base.Visit(edmProperty);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042B2 RID: 17074 RVA: 0x000E4A01 File Offset: 0x000E2C01
		protected override void Visit(NavigationProperty navigationProperty)
		{
		}

		// Token: 0x060042B3 RID: 17075 RVA: 0x000E4A04 File Offset: 0x000E2C04
		protected override void Visit(EdmMember edmMember)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(edmMember, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(edmMember, num);
			this.AddObjectContentToHashBuilder(edmMember.Identity);
			this.AddObjectContentToHashBuilder(edmMember.IsStoreGeneratedComputed);
			this.AddObjectContentToHashBuilder(edmMember.IsStoreGeneratedIdentity);
			base.Visit(edmMember);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042B4 RID: 17076 RVA: 0x000E4A60 File Offset: 0x000E2C60
		protected override void Visit(AssociationEndMember associationEndMember)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(associationEndMember, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(associationEndMember, num);
			this.AddObjectContentToHashBuilder(associationEndMember.DeleteBehavior);
			this.AddObjectContentToHashBuilder(associationEndMember.Identity);
			this.AddObjectContentToHashBuilder(associationEndMember.IsStoreGeneratedComputed);
			this.AddObjectContentToHashBuilder(associationEndMember.IsStoreGeneratedIdentity);
			this.AddObjectContentToHashBuilder(associationEndMember.RelationshipMultiplicity);
			base.Visit(associationEndMember);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042B5 RID: 17077 RVA: 0x000E4AE0 File Offset: 0x000E2CE0
		protected override void Visit(ReferentialConstraint referentialConstraint)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(referentialConstraint, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(referentialConstraint, num);
			this.AddObjectContentToHashBuilder(referentialConstraint.Identity);
			base.Visit(referentialConstraint);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042B6 RID: 17078 RVA: 0x000E4B1C File Offset: 0x000E2D1C
		protected override void Visit(RelationshipEndMember relationshipEndMember)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(relationshipEndMember, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(relationshipEndMember, num);
			this.AddObjectContentToHashBuilder(relationshipEndMember.DeleteBehavior);
			this.AddObjectContentToHashBuilder(relationshipEndMember.Identity);
			this.AddObjectContentToHashBuilder(relationshipEndMember.IsStoreGeneratedComputed);
			this.AddObjectContentToHashBuilder(relationshipEndMember.IsStoreGeneratedIdentity);
			this.AddObjectContentToHashBuilder(relationshipEndMember.RelationshipMultiplicity);
			base.Visit(relationshipEndMember);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042B7 RID: 17079 RVA: 0x000E4B9C File Offset: 0x000E2D9C
		protected override void Visit(TypeUsage typeUsage)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(typeUsage, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(typeUsage, num);
			base.Visit(typeUsage);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042B8 RID: 17080 RVA: 0x000E4BCA File Offset: 0x000E2DCA
		protected override void Visit(RelationshipType relationshipType)
		{
			base.Visit(relationshipType);
		}

		// Token: 0x060042B9 RID: 17081 RVA: 0x000E4BD3 File Offset: 0x000E2DD3
		protected override void Visit(EdmType edmType)
		{
			base.Visit(edmType);
		}

		// Token: 0x060042BA RID: 17082 RVA: 0x000E4BDC File Offset: 0x000E2DDC
		protected override void Visit(EnumType enumType)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(enumType, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(enumType, num);
			this.AddObjectContentToHashBuilder(enumType.Identity);
			this.Visit(enumType.UnderlyingType);
			base.Visit(enumType);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042BB RID: 17083 RVA: 0x000E4C24 File Offset: 0x000E2E24
		protected override void Visit(EnumMember enumMember)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(enumMember, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(enumMember, num);
			this.AddObjectContentToHashBuilder(enumMember.Name);
			this.AddObjectContentToHashBuilder(enumMember.Value);
			base.Visit(enumMember);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042BC RID: 17084 RVA: 0x000E4C6C File Offset: 0x000E2E6C
		protected override void Visit(CollectionType collectionType)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(collectionType, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(collectionType, num);
			this.AddObjectContentToHashBuilder(collectionType.Identity);
			base.Visit(collectionType);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042BD RID: 17085 RVA: 0x000E4CA8 File Offset: 0x000E2EA8
		protected override void Visit(RefType refType)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(refType, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(refType, num);
			this.AddObjectContentToHashBuilder(refType.Identity);
			base.Visit(refType);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042BE RID: 17086 RVA: 0x000E4CE2 File Offset: 0x000E2EE2
		protected override void Visit(EntityTypeBase entityTypeBase)
		{
			base.Visit(entityTypeBase);
		}

		// Token: 0x060042BF RID: 17087 RVA: 0x000E4CEC File Offset: 0x000E2EEC
		protected override void Visit(Facet facet)
		{
			if (facet.Name != "Nullable")
			{
				return;
			}
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(facet, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(facet, num);
			this.AddObjectContentToHashBuilder(facet.Identity);
			this.AddObjectContentToHashBuilder(facet.Value);
			base.Visit(facet);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042C0 RID: 17088 RVA: 0x000E4D45 File Offset: 0x000E2F45
		protected override void Visit(EdmFunction edmFunction)
		{
		}

		// Token: 0x060042C1 RID: 17089 RVA: 0x000E4D48 File Offset: 0x000E2F48
		protected override void Visit(ComplexType complexType)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(complexType, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(complexType, num);
			this.AddObjectContentToHashBuilder(complexType.Abstract);
			this.AddObjectContentToHashBuilder(complexType.Identity);
			base.Visit(complexType);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042C2 RID: 17090 RVA: 0x000E4D94 File Offset: 0x000E2F94
		protected override void Visit(PrimitiveType primitiveType)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(primitiveType, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(primitiveType, num);
			this.AddObjectContentToHashBuilder(primitiveType.Name);
			this.AddObjectContentToHashBuilder(primitiveType.NamespaceName);
			base.Visit(primitiveType);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042C3 RID: 17091 RVA: 0x000E4DDC File Offset: 0x000E2FDC
		protected override void Visit(FunctionParameter functionParameter)
		{
			int num;
			if (!this.AddObjectToSeenListAndHashBuilder(functionParameter, out num))
			{
				return;
			}
			this.AddObjectStartDumpToHashBuilder(functionParameter, num);
			this.AddObjectContentToHashBuilder(functionParameter.Identity);
			this.AddObjectContentToHashBuilder(functionParameter.Mode);
			base.Visit(functionParameter);
			this.AddObjectEndDumpToHashBuilder();
		}

		// Token: 0x060042C4 RID: 17092 RVA: 0x000E4E27 File Offset: 0x000E3027
		protected override void Visit(DbProviderManifest providerManifest)
		{
		}

		// Token: 0x17000D34 RID: 3380
		// (get) Token: 0x060042C5 RID: 17093 RVA: 0x000E4E29 File Offset: 0x000E3029
		internal string HashValue
		{
			get
			{
				return this.m_hashSourceBuilder.ComputeHash();
			}
		}

		// Token: 0x060042C6 RID: 17094 RVA: 0x000E4E36 File Offset: 0x000E3036
		private void Clean()
		{
			this.m_hashSourceBuilder = new CompressingHashBuilder(MetadataHelper.CreateMetadataHashAlgorithm(this.m_MappingVersion));
			this.m_instanceNumber = 0;
			this.m_itemsAlreadySeen = new Dictionary<object, int>();
		}

		// Token: 0x060042C7 RID: 17095 RVA: 0x000E4E60 File Offset: 0x000E3060
		private bool TryAddSeenItem(object o, out int indexSeen)
		{
			if (!this.m_itemsAlreadySeen.TryGetValue(o, out indexSeen))
			{
				this.m_itemsAlreadySeen.Add(o, this.m_instanceNumber);
				indexSeen = this.m_instanceNumber;
				this.m_instanceNumber++;
				return true;
			}
			return false;
		}

		// Token: 0x060042C8 RID: 17096 RVA: 0x000E4E9C File Offset: 0x000E309C
		private bool AddObjectToSeenListAndHashBuilder(object o, out int instanceIndex)
		{
			if (o == null)
			{
				instanceIndex = -1;
				return false;
			}
			if (!this.TryAddSeenItem(o, out instanceIndex))
			{
				this.AddObjectStartDumpToHashBuilder(o, instanceIndex);
				this.AddSeenObjectToHashBuilder(instanceIndex);
				this.AddObjectEndDumpToHashBuilder();
				return false;
			}
			return true;
		}

		// Token: 0x060042C9 RID: 17097 RVA: 0x000E4ECA File Offset: 0x000E30CA
		private void AddSeenObjectToHashBuilder(int instanceIndex)
		{
			this.m_hashSourceBuilder.AppendLine("Instance Reference: " + instanceIndex.ToString());
		}

		// Token: 0x060042CA RID: 17098 RVA: 0x000E4EE8 File Offset: 0x000E30E8
		private void AddObjectStartDumpToHashBuilder(object o, int objectIndex)
		{
			this.m_hashSourceBuilder.AppendObjectStartDump(o, objectIndex);
		}

		// Token: 0x060042CB RID: 17099 RVA: 0x000E4EF7 File Offset: 0x000E30F7
		private void AddObjectEndDumpToHashBuilder()
		{
			this.m_hashSourceBuilder.AppendObjectEndDump();
		}

		// Token: 0x060042CC RID: 17100 RVA: 0x000E4F04 File Offset: 0x000E3104
		private void AddObjectContentToHashBuilder(object content)
		{
			if (content == null)
			{
				this.m_hashSourceBuilder.AppendLine("NULL");
				return;
			}
			IFormattable formattable = content as IFormattable;
			if (formattable != null)
			{
				this.m_hashSourceBuilder.AppendLine(formattable.ToString(null, CultureInfo.InvariantCulture));
				return;
			}
			this.m_hashSourceBuilder.AppendLine(content.ToString());
		}

		// Token: 0x060042CD RID: 17101 RVA: 0x000E4F58 File Offset: 0x000E3158
		private void AddV2ObjectContentToHashBuilder(object content, double version)
		{
			if (version >= 2.0)
			{
				this.AddObjectContentToHashBuilder(content);
			}
		}

		// Token: 0x060042CE RID: 17102 RVA: 0x000E4F6D File Offset: 0x000E316D
		internal static string GetMappingClosureHash(double mappingVersion, EntityContainerMapping entityContainerMapping, bool sortSequence = true)
		{
			MetadataMappingHasherVisitor metadataMappingHasherVisitor = new MetadataMappingHasherVisitor(mappingVersion, sortSequence);
			metadataMappingHasherVisitor.Visit(entityContainerMapping);
			return metadataMappingHasherVisitor.HashValue;
		}

		// Token: 0x0400177D RID: 6013
		private CompressingHashBuilder m_hashSourceBuilder;

		// Token: 0x0400177E RID: 6014
		private Dictionary<object, int> m_itemsAlreadySeen = new Dictionary<object, int>();

		// Token: 0x0400177F RID: 6015
		private int m_instanceNumber;

		// Token: 0x04001780 RID: 6016
		private EdmItemCollection m_EdmItemCollection;

		// Token: 0x04001781 RID: 6017
		private double m_MappingVersion;
	}
}
