﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Query.PlanCompiler;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003F3 RID: 1011
	internal class TableMD
	{
		// Token: 0x06002F49 RID: 12105 RVA: 0x00094B66 File Offset: 0x00092D66
		private TableMD(EntitySetBase extent)
		{
			this.m_columns = new List<ColumnMD>();
			this.m_keys = new List<ColumnMD>();
			this.m_extent = extent;
		}

		// Token: 0x06002F4A RID: 12106 RVA: 0x00094B8B File Offset: 0x00092D8B
		internal TableMD(TypeUsage type, EntitySetBase extent)
			: this(extent)
		{
			this.m_columns.Add(new ColumnMD("element", type));
			this.m_flattened = !TypeUtils.IsStructuredType(type);
		}

		// Token: 0x06002F4B RID: 12107 RVA: 0x00094BBC File Offset: 0x00092DBC
		internal TableMD(IEnumerable<EdmProperty> properties, IEnumerable<EdmMember> keyProperties, EntitySetBase extent)
			: this(extent)
		{
			Dictionary<string, ColumnMD> dictionary = new Dictionary<string, ColumnMD>();
			this.m_flattened = true;
			foreach (EdmProperty edmProperty in properties)
			{
				ColumnMD columnMD = new ColumnMD(edmProperty);
				this.m_columns.Add(columnMD);
				dictionary[edmProperty.Name] = columnMD;
			}
			foreach (EdmMember edmMember in keyProperties)
			{
				ColumnMD columnMD2;
				if (dictionary.TryGetValue(edmMember.Name, out columnMD2))
				{
					this.m_keys.Add(columnMD2);
				}
			}
		}

		// Token: 0x17000953 RID: 2387
		// (get) Token: 0x06002F4C RID: 12108 RVA: 0x00094C88 File Offset: 0x00092E88
		internal EntitySetBase Extent
		{
			get
			{
				return this.m_extent;
			}
		}

		// Token: 0x17000954 RID: 2388
		// (get) Token: 0x06002F4D RID: 12109 RVA: 0x00094C90 File Offset: 0x00092E90
		internal List<ColumnMD> Columns
		{
			get
			{
				return this.m_columns;
			}
		}

		// Token: 0x17000955 RID: 2389
		// (get) Token: 0x06002F4E RID: 12110 RVA: 0x00094C98 File Offset: 0x00092E98
		internal List<ColumnMD> Keys
		{
			get
			{
				return this.m_keys;
			}
		}

		// Token: 0x17000956 RID: 2390
		// (get) Token: 0x06002F4F RID: 12111 RVA: 0x00094CA0 File Offset: 0x00092EA0
		internal bool Flattened
		{
			get
			{
				return this.m_flattened;
			}
		}

		// Token: 0x06002F50 RID: 12112 RVA: 0x00094CA8 File Offset: 0x00092EA8
		public override string ToString()
		{
			if (this.m_extent == null)
			{
				return "Transient";
			}
			return this.m_extent.Name;
		}

		// Token: 0x04000FF5 RID: 4085
		private readonly List<ColumnMD> m_columns;

		// Token: 0x04000FF6 RID: 4086
		private readonly List<ColumnMD> m_keys;

		// Token: 0x04000FF7 RID: 4087
		private readonly EntitySetBase m_extent;

		// Token: 0x04000FF8 RID: 4088
		private readonly bool m_flattened;
	}
}
