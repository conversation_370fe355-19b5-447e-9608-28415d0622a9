﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.CommandTrees.Internal;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006D9 RID: 1753
	public sealed class DbQueryCommandTree : DbCommandTree
	{
		// Token: 0x06005180 RID: 20864 RVA: 0x00123018 File Offset: 0x00121218
		public DbQueryCommandTree(MetadataWorkspace metadata, DataSpace dataSpace, DbExpression query, bool validate, bool useDatabaseNullSemantics, bool disableFilterOverProjectionSimplificationForCustomFunctions)
			: base(metadata, dataSpace, useDatabaseNullSemantics, disableFilterOverProjectionSimplificationForCustomFunctions)
		{
			Check.NotNull<DbExpression>(query, "query");
			if (validate)
			{
				DbExpressionValidator dbExpressionValidator = new DbExpressionValidator(metadata, dataSpace);
				dbExpressionValidator.ValidateExpression(query, "query");
				this._parameters = new ReadOnlyCollection<DbParameterReferenceExpression>(dbExpressionValidator.Parameters.Select((KeyValuePair<string, DbParameterReferenceExpression> paramInfo) => paramInfo.Value).ToList<DbParameterReferenceExpression>());
			}
			this._query = query;
		}

		// Token: 0x06005181 RID: 20865 RVA: 0x00123096 File Offset: 0x00121296
		public DbQueryCommandTree(MetadataWorkspace metadata, DataSpace dataSpace, DbExpression query, bool validate, bool useDatabaseNullSemantics)
			: this(metadata, dataSpace, query, validate, useDatabaseNullSemantics, false)
		{
		}

		// Token: 0x06005182 RID: 20866 RVA: 0x001230A6 File Offset: 0x001212A6
		public DbQueryCommandTree(MetadataWorkspace metadata, DataSpace dataSpace, DbExpression query, bool validate)
			: this(metadata, dataSpace, query, validate, true, false)
		{
		}

		// Token: 0x06005183 RID: 20867 RVA: 0x001230B5 File Offset: 0x001212B5
		public DbQueryCommandTree(MetadataWorkspace metadata, DataSpace dataSpace, DbExpression query)
			: this(metadata, dataSpace, query, true, true, false)
		{
		}

		// Token: 0x17000FE5 RID: 4069
		// (get) Token: 0x06005184 RID: 20868 RVA: 0x001230C3 File Offset: 0x001212C3
		public DbExpression Query
		{
			get
			{
				return this._query;
			}
		}

		// Token: 0x17000FE6 RID: 4070
		// (get) Token: 0x06005185 RID: 20869 RVA: 0x001230CB File Offset: 0x001212CB
		public override DbCommandTreeKind CommandTreeKind
		{
			get
			{
				return DbCommandTreeKind.Query;
			}
		}

		// Token: 0x06005186 RID: 20870 RVA: 0x001230CE File Offset: 0x001212CE
		internal override IEnumerable<KeyValuePair<string, TypeUsage>> GetParameters()
		{
			if (this._parameters == null)
			{
				this._parameters = ParameterRetriever.GetParameters(this);
			}
			return this._parameters.Select((DbParameterReferenceExpression p) => new KeyValuePair<string, TypeUsage>(p.ParameterName, p.ResultType));
		}

		// Token: 0x06005187 RID: 20871 RVA: 0x0012310E File Offset: 0x0012130E
		internal override void DumpStructure(ExpressionDumper dumper)
		{
			if (this.Query != null)
			{
				dumper.Dump(this.Query, "Query");
			}
		}

		// Token: 0x06005188 RID: 20872 RVA: 0x00123129 File Offset: 0x00121329
		internal override string PrintTree(ExpressionPrinter printer)
		{
			return printer.Print(this);
		}

		// Token: 0x06005189 RID: 20873 RVA: 0x00123132 File Offset: 0x00121332
		internal static DbQueryCommandTree FromValidExpression(MetadataWorkspace metadata, DataSpace dataSpace, DbExpression query, bool useDatabaseNullSemantics, bool disableFilterOverProjectionSimplificationForCustomFunctions)
		{
			return new DbQueryCommandTree(metadata, dataSpace, query, false, useDatabaseNullSemantics, disableFilterOverProjectionSimplificationForCustomFunctions);
		}

		// Token: 0x04001DC6 RID: 7622
		private readonly DbExpression _query;

		// Token: 0x04001DC7 RID: 7623
		private ReadOnlyCollection<DbParameterReferenceExpression> _parameters;
	}
}
