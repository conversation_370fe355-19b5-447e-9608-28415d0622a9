﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006E8 RID: 1768
	public class DefaultExpressionVisitor : DbExpressionVisitor<DbExpression>
	{
		// Token: 0x060051CC RID: 20940 RVA: 0x0012372E File Offset: 0x0012192E
		protected DefaultExpressionVisitor()
		{
		}

		// Token: 0x060051CD RID: 20941 RVA: 0x00123741 File Offset: 0x00121941
		protected virtual void OnExpressionReplaced(DbExpression oldExpression, DbExpression newExpression)
		{
		}

		// Token: 0x060051CE RID: 20942 RVA: 0x00123743 File Offset: 0x00121943
		protected virtual void OnVariableRebound(DbVariableReferenceExpression fromVarRef, DbVariableReferenceExpression toVarRef)
		{
		}

		// Token: 0x060051CF RID: 20943 RVA: 0x00123745 File Offset: 0x00121945
		protected virtual void OnEnterScope(IEnumerable<DbVariableReferenceExpression> scopeVariables)
		{
		}

		// Token: 0x060051D0 RID: 20944 RVA: 0x00123747 File Offset: 0x00121947
		protected virtual void OnExitScope()
		{
		}

		// Token: 0x060051D1 RID: 20945 RVA: 0x0012374C File Offset: 0x0012194C
		protected virtual DbExpression VisitExpression(DbExpression expression)
		{
			DbExpression dbExpression = null;
			if (expression != null)
			{
				dbExpression = expression.Accept<DbExpression>(this);
			}
			return dbExpression;
		}

		// Token: 0x060051D2 RID: 20946 RVA: 0x00123767 File Offset: 0x00121967
		protected virtual IList<DbExpression> VisitExpressionList(IList<DbExpression> list)
		{
			return DefaultExpressionVisitor.VisitList<DbExpression>(list, new Func<DbExpression, DbExpression>(this.VisitExpression));
		}

		// Token: 0x060051D3 RID: 20947 RVA: 0x0012377C File Offset: 0x0012197C
		protected virtual DbExpressionBinding VisitExpressionBinding(DbExpressionBinding binding)
		{
			DbExpressionBinding dbExpressionBinding = binding;
			if (binding != null)
			{
				DbExpression dbExpression = this.VisitExpression(binding.Expression);
				if (binding.Expression != dbExpression)
				{
					dbExpressionBinding = dbExpression.BindAs(binding.VariableName);
					this.RebindVariable(binding.Variable, dbExpressionBinding.Variable);
				}
			}
			return dbExpressionBinding;
		}

		// Token: 0x060051D4 RID: 20948 RVA: 0x001237C4 File Offset: 0x001219C4
		protected virtual IList<DbExpressionBinding> VisitExpressionBindingList(IList<DbExpressionBinding> list)
		{
			return DefaultExpressionVisitor.VisitList<DbExpressionBinding>(list, new Func<DbExpressionBinding, DbExpressionBinding>(this.VisitExpressionBinding));
		}

		// Token: 0x060051D5 RID: 20949 RVA: 0x001237DC File Offset: 0x001219DC
		protected virtual DbGroupExpressionBinding VisitGroupExpressionBinding(DbGroupExpressionBinding binding)
		{
			DbGroupExpressionBinding dbGroupExpressionBinding = binding;
			if (binding != null)
			{
				DbExpression dbExpression = this.VisitExpression(binding.Expression);
				if (binding.Expression != dbExpression)
				{
					dbGroupExpressionBinding = dbExpression.GroupBindAs(binding.VariableName, binding.GroupVariableName);
					this.RebindVariable(binding.Variable, dbGroupExpressionBinding.Variable);
					this.RebindVariable(binding.GroupVariable, dbGroupExpressionBinding.GroupVariable);
				}
			}
			return dbGroupExpressionBinding;
		}

		// Token: 0x060051D6 RID: 20950 RVA: 0x0012383C File Offset: 0x00121A3C
		protected virtual DbSortClause VisitSortClause(DbSortClause clause)
		{
			DbSortClause dbSortClause = clause;
			if (clause != null)
			{
				DbExpression dbExpression = this.VisitExpression(clause.Expression);
				if (clause.Expression != dbExpression)
				{
					if (!string.IsNullOrEmpty(clause.Collation))
					{
						dbSortClause = (clause.Ascending ? dbExpression.ToSortClause(clause.Collation) : dbExpression.ToSortClauseDescending(clause.Collation));
					}
					else
					{
						dbSortClause = (clause.Ascending ? dbExpression.ToSortClause() : dbExpression.ToSortClauseDescending());
					}
				}
			}
			return dbSortClause;
		}

		// Token: 0x060051D7 RID: 20951 RVA: 0x001238AE File Offset: 0x00121AAE
		protected virtual IList<DbSortClause> VisitSortOrder(IList<DbSortClause> sortOrder)
		{
			return DefaultExpressionVisitor.VisitList<DbSortClause>(sortOrder, new Func<DbSortClause, DbSortClause>(this.VisitSortClause));
		}

		// Token: 0x060051D8 RID: 20952 RVA: 0x001238C4 File Offset: 0x00121AC4
		protected virtual DbAggregate VisitAggregate(DbAggregate aggregate)
		{
			DbFunctionAggregate dbFunctionAggregate = aggregate as DbFunctionAggregate;
			if (dbFunctionAggregate != null)
			{
				return this.VisitFunctionAggregate(dbFunctionAggregate);
			}
			DbGroupAggregate dbGroupAggregate = (DbGroupAggregate)aggregate;
			return this.VisitGroupAggregate(dbGroupAggregate);
		}

		// Token: 0x060051D9 RID: 20953 RVA: 0x001238F4 File Offset: 0x00121AF4
		protected virtual DbFunctionAggregate VisitFunctionAggregate(DbFunctionAggregate aggregate)
		{
			DbFunctionAggregate dbFunctionAggregate = aggregate;
			if (aggregate != null)
			{
				EdmFunction edmFunction = this.VisitFunction(aggregate.Function);
				IList<DbExpression> list = this.VisitExpressionList(aggregate.Arguments);
				if (aggregate.Function != edmFunction || aggregate.Arguments != list)
				{
					if (aggregate.Distinct)
					{
						dbFunctionAggregate = edmFunction.AggregateDistinct(list);
					}
					else
					{
						dbFunctionAggregate = edmFunction.Aggregate(list);
					}
				}
			}
			return dbFunctionAggregate;
		}

		// Token: 0x060051DA RID: 20954 RVA: 0x00123950 File Offset: 0x00121B50
		protected virtual DbGroupAggregate VisitGroupAggregate(DbGroupAggregate aggregate)
		{
			DbGroupAggregate dbGroupAggregate = aggregate;
			if (aggregate != null)
			{
				IList<DbExpression> list = this.VisitExpressionList(aggregate.Arguments);
				if (aggregate.Arguments != list)
				{
					dbGroupAggregate = DbExpressionBuilder.GroupAggregate(list[0]);
				}
			}
			return dbGroupAggregate;
		}

		// Token: 0x060051DB RID: 20955 RVA: 0x00123988 File Offset: 0x00121B88
		protected virtual DbLambda VisitLambda(DbLambda lambda)
		{
			Check.NotNull<DbLambda>(lambda, "lambda");
			DbLambda dbLambda = lambda;
			IList<DbVariableReferenceExpression> list = DefaultExpressionVisitor.VisitList<DbVariableReferenceExpression>(lambda.Variables, delegate(DbVariableReferenceExpression varRef)
			{
				TypeUsage typeUsage = this.VisitTypeUsage(varRef.ResultType);
				if (varRef.ResultType != typeUsage)
				{
					return typeUsage.Variable(varRef.VariableName);
				}
				return varRef;
			});
			this.EnterScope(list.ToArray<DbVariableReferenceExpression>());
			DbExpression dbExpression = this.VisitExpression(lambda.Body);
			this.ExitScope();
			if (lambda.Variables != list || lambda.Body != dbExpression)
			{
				dbLambda = DbExpressionBuilder.Lambda(dbExpression, list);
			}
			return dbLambda;
		}

		// Token: 0x060051DC RID: 20956 RVA: 0x001239F5 File Offset: 0x00121BF5
		protected virtual EdmType VisitType(EdmType type)
		{
			return type;
		}

		// Token: 0x060051DD RID: 20957 RVA: 0x001239F8 File Offset: 0x00121BF8
		protected virtual TypeUsage VisitTypeUsage(TypeUsage type)
		{
			return type;
		}

		// Token: 0x060051DE RID: 20958 RVA: 0x001239FB File Offset: 0x00121BFB
		protected virtual EntitySetBase VisitEntitySet(EntitySetBase entitySet)
		{
			return entitySet;
		}

		// Token: 0x060051DF RID: 20959 RVA: 0x001239FE File Offset: 0x00121BFE
		protected virtual EdmFunction VisitFunction(EdmFunction functionMetadata)
		{
			return functionMetadata;
		}

		// Token: 0x060051E0 RID: 20960 RVA: 0x00123A01 File Offset: 0x00121C01
		private void NotifyIfChanged(DbExpression originalExpression, DbExpression newExpression)
		{
			if (originalExpression != newExpression)
			{
				this.OnExpressionReplaced(originalExpression, newExpression);
			}
		}

		// Token: 0x060051E1 RID: 20961 RVA: 0x00123A10 File Offset: 0x00121C10
		private static IList<TElement> VisitList<TElement>(IList<TElement> list, Func<TElement, TElement> map)
		{
			IList<TElement> list2 = list;
			if (list != null)
			{
				List<TElement> list3 = null;
				for (int i = 0; i < list.Count; i++)
				{
					TElement telement = map(list[i]);
					if (list3 == null && list[i] != telement)
					{
						list3 = new List<TElement>(list);
						list2 = list3;
					}
					if (list3 != null)
					{
						list3[i] = telement;
					}
				}
			}
			return list2;
		}

		// Token: 0x060051E2 RID: 20962 RVA: 0x00123A70 File Offset: 0x00121C70
		private DbExpression VisitUnary(DbUnaryExpression expression, Func<DbExpression, DbExpression> callback)
		{
			DbExpression dbExpression = expression;
			DbExpression dbExpression2 = this.VisitExpression(expression.Argument);
			if (expression.Argument != dbExpression2)
			{
				dbExpression = callback(dbExpression2);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x060051E3 RID: 20963 RVA: 0x00123AA8 File Offset: 0x00121CA8
		private DbExpression VisitTypeUnary(DbUnaryExpression expression, TypeUsage type, Func<DbExpression, TypeUsage, DbExpression> callback)
		{
			DbExpression dbExpression = expression;
			DbExpression dbExpression2 = this.VisitExpression(expression.Argument);
			TypeUsage typeUsage = this.VisitTypeUsage(type);
			if (expression.Argument != dbExpression2 || type != typeUsage)
			{
				dbExpression = callback(dbExpression2, typeUsage);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x060051E4 RID: 20964 RVA: 0x00123AEC File Offset: 0x00121CEC
		private DbExpression VisitBinary(DbBinaryExpression expression, Func<DbExpression, DbExpression, DbExpression> callback)
		{
			DbExpression dbExpression = expression;
			DbExpression dbExpression2 = this.VisitExpression(expression.Left);
			DbExpression dbExpression3 = this.VisitExpression(expression.Right);
			if (expression.Left != dbExpression2 || expression.Right != dbExpression3)
			{
				dbExpression = callback(dbExpression2, dbExpression3);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x060051E5 RID: 20965 RVA: 0x00123B3C File Offset: 0x00121D3C
		private DbRelatedEntityRef VisitRelatedEntityRef(DbRelatedEntityRef entityRef)
		{
			RelationshipEndMember relationshipEndMember;
			RelationshipEndMember relationshipEndMember2;
			this.VisitRelationshipEnds(entityRef.SourceEnd, entityRef.TargetEnd, out relationshipEndMember, out relationshipEndMember2);
			DbExpression dbExpression = this.VisitExpression(entityRef.TargetEntityReference);
			if (entityRef.SourceEnd != relationshipEndMember || entityRef.TargetEnd != relationshipEndMember2 || entityRef.TargetEntityReference != dbExpression)
			{
				return DbExpressionBuilder.CreateRelatedEntityRef(relationshipEndMember, relationshipEndMember2, dbExpression);
			}
			return entityRef;
		}

		// Token: 0x060051E6 RID: 20966 RVA: 0x00123B94 File Offset: 0x00121D94
		private void VisitRelationshipEnds(RelationshipEndMember source, RelationshipEndMember target, out RelationshipEndMember newSource, out RelationshipEndMember newTarget)
		{
			RelationshipType relationshipType = (RelationshipType)this.VisitType(target.DeclaringType);
			newSource = relationshipType.RelationshipEndMembers[source.Name];
			newTarget = relationshipType.RelationshipEndMembers[target.Name];
		}

		// Token: 0x060051E7 RID: 20967 RVA: 0x00123BDC File Offset: 0x00121DDC
		private DbExpression VisitTerminal(DbExpression expression, Func<TypeUsage, DbExpression> reconstructor)
		{
			DbExpression dbExpression = expression;
			TypeUsage typeUsage = this.VisitTypeUsage(expression.ResultType);
			if (expression.ResultType != typeUsage)
			{
				dbExpression = reconstructor(typeUsage);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x060051E8 RID: 20968 RVA: 0x00123C14 File Offset: 0x00121E14
		private void RebindVariable(DbVariableReferenceExpression from, DbVariableReferenceExpression to)
		{
			if (!from.VariableName.Equals(to.VariableName, StringComparison.Ordinal) || from.ResultType.EdmType != to.ResultType.EdmType || !from.ResultType.EdmEquals(to.ResultType))
			{
				this.varMappings[from] = to;
				this.OnVariableRebound(from, to);
			}
		}

		// Token: 0x060051E9 RID: 20969 RVA: 0x00123C78 File Offset: 0x00121E78
		private DbExpressionBinding VisitExpressionBindingEnterScope(DbExpressionBinding binding)
		{
			DbExpressionBinding dbExpressionBinding = this.VisitExpressionBinding(binding);
			this.OnEnterScope(new DbVariableReferenceExpression[] { dbExpressionBinding.Variable });
			return dbExpressionBinding;
		}

		// Token: 0x060051EA RID: 20970 RVA: 0x00123CA3 File Offset: 0x00121EA3
		private void EnterScope(params DbVariableReferenceExpression[] scopeVars)
		{
			this.OnEnterScope(scopeVars);
		}

		// Token: 0x060051EB RID: 20971 RVA: 0x00123CAC File Offset: 0x00121EAC
		private void ExitScope()
		{
			this.OnExitScope();
		}

		// Token: 0x060051EC RID: 20972 RVA: 0x00123CB4 File Offset: 0x00121EB4
		public override DbExpression Visit(DbExpression expression)
		{
			Check.NotNull<DbExpression>(expression, "expression");
			throw new NotSupportedException(Strings.Cqt_General_UnsupportedExpression(expression.GetType().FullName));
		}

		// Token: 0x060051ED RID: 20973 RVA: 0x00123CD8 File Offset: 0x00121ED8
		public override DbExpression Visit(DbConstantExpression expression)
		{
			Check.NotNull<DbConstantExpression>(expression, "expression");
			return this.VisitTerminal(expression, (TypeUsage newType) => newType.Constant(expression.GetValue()));
		}

		// Token: 0x060051EE RID: 20974 RVA: 0x00123D1B File Offset: 0x00121F1B
		public override DbExpression Visit(DbNullExpression expression)
		{
			Check.NotNull<DbNullExpression>(expression, "expression");
			return this.VisitTerminal(expression, new Func<TypeUsage, DbExpression>(DbExpressionBuilder.Null));
		}

		// Token: 0x060051EF RID: 20975 RVA: 0x00123D3C File Offset: 0x00121F3C
		public override DbExpression Visit(DbVariableReferenceExpression expression)
		{
			Check.NotNull<DbVariableReferenceExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbVariableReferenceExpression dbVariableReferenceExpression;
			if (this.varMappings.TryGetValue(expression, out dbVariableReferenceExpression))
			{
				dbExpression = dbVariableReferenceExpression;
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x060051F0 RID: 20976 RVA: 0x00123D74 File Offset: 0x00121F74
		public override DbExpression Visit(DbParameterReferenceExpression expression)
		{
			Check.NotNull<DbParameterReferenceExpression>(expression, "expression");
			return this.VisitTerminal(expression, (TypeUsage newType) => newType.Parameter(expression.ParameterName));
		}

		// Token: 0x060051F1 RID: 20977 RVA: 0x00123DB8 File Offset: 0x00121FB8
		public override DbExpression Visit(DbFunctionExpression expression)
		{
			Check.NotNull<DbFunctionExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			IList<DbExpression> list = this.VisitExpressionList(expression.Arguments);
			EdmFunction edmFunction = this.VisitFunction(expression.Function);
			if (expression.Arguments != list || expression.Function != edmFunction)
			{
				dbExpression = edmFunction.Invoke(list);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x060051F2 RID: 20978 RVA: 0x00123E10 File Offset: 0x00122010
		public override DbExpression Visit(DbLambdaExpression expression)
		{
			Check.NotNull<DbLambdaExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			IList<DbExpression> list = this.VisitExpressionList(expression.Arguments);
			DbLambda dbLambda = this.VisitLambda(expression.Lambda);
			if (expression.Arguments != list || expression.Lambda != dbLambda)
			{
				dbExpression = dbLambda.Invoke(list);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x060051F3 RID: 20979 RVA: 0x00123E68 File Offset: 0x00122068
		public override DbExpression Visit(DbPropertyExpression expression)
		{
			Check.NotNull<DbPropertyExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbExpression dbExpression2 = this.VisitExpression(expression.Instance);
			if (expression.Instance != dbExpression2)
			{
				dbExpression = dbExpression2.Property(expression.Property.Name);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x060051F4 RID: 20980 RVA: 0x00123EB4 File Offset: 0x001220B4
		public override DbExpression Visit(DbComparisonExpression expression)
		{
			Check.NotNull<DbComparisonExpression>(expression, "expression");
			DbExpressionKind expressionKind = expression.ExpressionKind;
			if (expressionKind <= DbExpressionKind.GreaterThanOrEquals)
			{
				if (expressionKind == DbExpressionKind.Equals)
				{
					return this.VisitBinary(expression, new Func<DbExpression, DbExpression, DbExpression>(DbExpressionBuilder.Equal));
				}
				if (expressionKind == DbExpressionKind.GreaterThan)
				{
					return this.VisitBinary(expression, new Func<DbExpression, DbExpression, DbExpression>(DbExpressionBuilder.GreaterThan));
				}
				if (expressionKind == DbExpressionKind.GreaterThanOrEquals)
				{
					return this.VisitBinary(expression, new Func<DbExpression, DbExpression, DbExpression>(DbExpressionBuilder.GreaterThanOrEqual));
				}
			}
			else
			{
				if (expressionKind == DbExpressionKind.LessThan)
				{
					return this.VisitBinary(expression, new Func<DbExpression, DbExpression, DbExpression>(DbExpressionBuilder.LessThan));
				}
				if (expressionKind == DbExpressionKind.LessThanOrEquals)
				{
					return this.VisitBinary(expression, new Func<DbExpression, DbExpression, DbExpression>(DbExpressionBuilder.LessThanOrEqual));
				}
				if (expressionKind == DbExpressionKind.NotEquals)
				{
					return this.VisitBinary(expression, new Func<DbExpression, DbExpression, DbExpression>(DbExpressionBuilder.NotEqual));
				}
			}
			throw new NotSupportedException();
		}

		// Token: 0x060051F5 RID: 20981 RVA: 0x00123F7C File Offset: 0x0012217C
		public override DbExpression Visit(DbLikeExpression expression)
		{
			Check.NotNull<DbLikeExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbExpression dbExpression2 = this.VisitExpression(expression.Argument);
			DbExpression dbExpression3 = this.VisitExpression(expression.Pattern);
			DbExpression dbExpression4 = this.VisitExpression(expression.Escape);
			if (expression.Argument != dbExpression2 || expression.Pattern != dbExpression3 || expression.Escape != dbExpression4)
			{
				dbExpression = dbExpression2.Like(dbExpression3, dbExpression4);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x060051F6 RID: 20982 RVA: 0x00123FEC File Offset: 0x001221EC
		public override DbExpression Visit(DbLimitExpression expression)
		{
			Check.NotNull<DbLimitExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbExpression dbExpression2 = this.VisitExpression(expression.Argument);
			DbExpression dbExpression3 = this.VisitExpression(expression.Limit);
			if (expression.Argument != dbExpression2 || expression.Limit != dbExpression3)
			{
				dbExpression = dbExpression2.Limit(dbExpression3);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x060051F7 RID: 20983 RVA: 0x00124044 File Offset: 0x00122244
		public override DbExpression Visit(DbIsNullExpression expression)
		{
			Check.NotNull<DbIsNullExpression>(expression, "expression");
			return this.VisitUnary(expression, new Func<DbExpression, DbExpression>(DbExpressionBuilder.IsNull));
		}

		// Token: 0x060051F8 RID: 20984 RVA: 0x00124068 File Offset: 0x00122268
		public override DbExpression Visit(DbArithmeticExpression expression)
		{
			Check.NotNull<DbArithmeticExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			IList<DbExpression> list = this.VisitExpressionList(expression.Arguments);
			if (expression.Arguments != list)
			{
				DbExpressionKind expressionKind = expression.ExpressionKind;
				if (expressionKind <= DbExpressionKind.Multiply)
				{
					if (expressionKind == DbExpressionKind.Divide)
					{
						dbExpression = list[0].Divide(list[1]);
						goto IL_00E1;
					}
					switch (expressionKind)
					{
					case DbExpressionKind.Minus:
						dbExpression = list[0].Minus(list[1]);
						goto IL_00E1;
					case DbExpressionKind.Modulo:
						dbExpression = list[0].Modulo(list[1]);
						goto IL_00E1;
					case DbExpressionKind.Multiply:
						dbExpression = list[0].Multiply(list[1]);
						goto IL_00E1;
					}
				}
				else
				{
					if (expressionKind == DbExpressionKind.Plus)
					{
						dbExpression = list[0].Plus(list[1]);
						goto IL_00E1;
					}
					if (expressionKind == DbExpressionKind.UnaryMinus)
					{
						dbExpression = list[0].UnaryMinus();
						goto IL_00E1;
					}
				}
				throw new NotSupportedException();
			}
			IL_00E1:
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x060051F9 RID: 20985 RVA: 0x0012415F File Offset: 0x0012235F
		public override DbExpression Visit(DbAndExpression expression)
		{
			Check.NotNull<DbAndExpression>(expression, "expression");
			return this.VisitBinary(expression, new Func<DbExpression, DbExpression, DbExpression>(DbExpressionBuilder.And));
		}

		// Token: 0x060051FA RID: 20986 RVA: 0x00124180 File Offset: 0x00122380
		public override DbExpression Visit(DbOrExpression expression)
		{
			Check.NotNull<DbOrExpression>(expression, "expression");
			return this.VisitBinary(expression, new Func<DbExpression, DbExpression, DbExpression>(DbExpressionBuilder.Or));
		}

		// Token: 0x060051FB RID: 20987 RVA: 0x001241A4 File Offset: 0x001223A4
		public override DbExpression Visit(DbInExpression expression)
		{
			Check.NotNull<DbInExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbExpression dbExpression2 = this.VisitExpression(expression.Item);
			IList<DbExpression> list = this.VisitExpressionList(expression.List);
			if (expression.Item != dbExpression2 || expression.List != list)
			{
				dbExpression = DbExpressionBuilder.CreateInExpression(dbExpression2, list);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x060051FC RID: 20988 RVA: 0x001241FC File Offset: 0x001223FC
		public override DbExpression Visit(DbNotExpression expression)
		{
			Check.NotNull<DbNotExpression>(expression, "expression");
			return this.VisitUnary(expression, new Func<DbExpression, DbExpression>(DbExpressionBuilder.Not));
		}

		// Token: 0x060051FD RID: 20989 RVA: 0x0012421D File Offset: 0x0012241D
		public override DbExpression Visit(DbDistinctExpression expression)
		{
			Check.NotNull<DbDistinctExpression>(expression, "expression");
			return this.VisitUnary(expression, new Func<DbExpression, DbExpression>(DbExpressionBuilder.Distinct));
		}

		// Token: 0x060051FE RID: 20990 RVA: 0x0012423E File Offset: 0x0012243E
		public override DbExpression Visit(DbElementExpression expression)
		{
			Check.NotNull<DbElementExpression>(expression, "expression");
			return this.VisitUnary(expression, expression.IsSinglePropertyUnwrapped ? new Func<DbExpression, DbExpression>(DbExpressionBuilder.CreateElementExpressionUnwrapSingleProperty) : new Func<DbExpression, DbExpression>(DbExpressionBuilder.Element));
		}

		// Token: 0x060051FF RID: 20991 RVA: 0x00124275 File Offset: 0x00122475
		public override DbExpression Visit(DbIsEmptyExpression expression)
		{
			Check.NotNull<DbIsEmptyExpression>(expression, "expression");
			return this.VisitUnary(expression, new Func<DbExpression, DbExpression>(DbExpressionBuilder.IsEmpty));
		}

		// Token: 0x06005200 RID: 20992 RVA: 0x00124296 File Offset: 0x00122496
		public override DbExpression Visit(DbUnionAllExpression expression)
		{
			Check.NotNull<DbUnionAllExpression>(expression, "expression");
			return this.VisitBinary(expression, new Func<DbExpression, DbExpression, DbExpression>(DbExpressionBuilder.UnionAll));
		}

		// Token: 0x06005201 RID: 20993 RVA: 0x001242B7 File Offset: 0x001224B7
		public override DbExpression Visit(DbIntersectExpression expression)
		{
			Check.NotNull<DbIntersectExpression>(expression, "expression");
			return this.VisitBinary(expression, new Func<DbExpression, DbExpression, DbExpression>(DbExpressionBuilder.Intersect));
		}

		// Token: 0x06005202 RID: 20994 RVA: 0x001242D8 File Offset: 0x001224D8
		public override DbExpression Visit(DbExceptExpression expression)
		{
			Check.NotNull<DbExceptExpression>(expression, "expression");
			return this.VisitBinary(expression, new Func<DbExpression, DbExpression, DbExpression>(DbExpressionBuilder.Except));
		}

		// Token: 0x06005203 RID: 20995 RVA: 0x001242F9 File Offset: 0x001224F9
		public override DbExpression Visit(DbTreatExpression expression)
		{
			Check.NotNull<DbTreatExpression>(expression, "expression");
			return this.VisitTypeUnary(expression, expression.ResultType, new Func<DbExpression, TypeUsage, DbExpression>(DbExpressionBuilder.TreatAs));
		}

		// Token: 0x06005204 RID: 20996 RVA: 0x00124320 File Offset: 0x00122520
		public override DbExpression Visit(DbIsOfExpression expression)
		{
			Check.NotNull<DbIsOfExpression>(expression, "expression");
			if (expression.ExpressionKind == DbExpressionKind.IsOfOnly)
			{
				return this.VisitTypeUnary(expression, expression.OfType, new Func<DbExpression, TypeUsage, DbExpression>(DbExpressionBuilder.IsOfOnly));
			}
			return this.VisitTypeUnary(expression, expression.OfType, new Func<DbExpression, TypeUsage, DbExpression>(DbExpressionBuilder.IsOf));
		}

		// Token: 0x06005205 RID: 20997 RVA: 0x00124376 File Offset: 0x00122576
		public override DbExpression Visit(DbCastExpression expression)
		{
			Check.NotNull<DbCastExpression>(expression, "expression");
			return this.VisitTypeUnary(expression, expression.ResultType, new Func<DbExpression, TypeUsage, DbExpression>(DbExpressionBuilder.CastTo));
		}

		// Token: 0x06005206 RID: 20998 RVA: 0x001243A0 File Offset: 0x001225A0
		public override DbExpression Visit(DbCaseExpression expression)
		{
			Check.NotNull<DbCaseExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			IList<DbExpression> list = this.VisitExpressionList(expression.When);
			IList<DbExpression> list2 = this.VisitExpressionList(expression.Then);
			DbExpression dbExpression2 = this.VisitExpression(expression.Else);
			if (expression.When != list || expression.Then != list2 || expression.Else != dbExpression2)
			{
				dbExpression = DbExpressionBuilder.Case(list, list2, dbExpression2);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x06005207 RID: 20999 RVA: 0x00124410 File Offset: 0x00122610
		public override DbExpression Visit(DbOfTypeExpression expression)
		{
			Check.NotNull<DbOfTypeExpression>(expression, "expression");
			if (expression.ExpressionKind == DbExpressionKind.OfTypeOnly)
			{
				return this.VisitTypeUnary(expression, expression.OfType, new Func<DbExpression, TypeUsage, DbExpression>(DbExpressionBuilder.OfTypeOnly));
			}
			return this.VisitTypeUnary(expression, expression.OfType, new Func<DbExpression, TypeUsage, DbExpression>(DbExpressionBuilder.OfType));
		}

		// Token: 0x06005208 RID: 21000 RVA: 0x00124468 File Offset: 0x00122668
		public override DbExpression Visit(DbNewInstanceExpression expression)
		{
			Check.NotNull<DbNewInstanceExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			TypeUsage typeUsage = this.VisitTypeUsage(expression.ResultType);
			IList<DbExpression> list = this.VisitExpressionList(expression.Arguments);
			bool flag = expression.ResultType == typeUsage && expression.Arguments == list;
			if (expression.HasRelatedEntityReferences)
			{
				IList<DbRelatedEntityRef> list2 = DefaultExpressionVisitor.VisitList<DbRelatedEntityRef>(expression.RelatedEntityReferences, new Func<DbRelatedEntityRef, DbRelatedEntityRef>(this.VisitRelatedEntityRef));
				if (!flag || expression.RelatedEntityReferences != list2)
				{
					dbExpression = DbExpressionBuilder.CreateNewEntityWithRelationshipsExpression((EntityType)typeUsage.EdmType, list, list2);
				}
			}
			else if (!flag)
			{
				dbExpression = typeUsage.New(list.ToArray<DbExpression>());
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x06005209 RID: 21001 RVA: 0x00124510 File Offset: 0x00122710
		public override DbExpression Visit(DbRefExpression expression)
		{
			Check.NotNull<DbRefExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			EntityType entityType = (EntityType)TypeHelpers.GetEdmType<RefType>(expression.ResultType).ElementType;
			DbExpression dbExpression2 = this.VisitExpression(expression.Argument);
			EntityType entityType2 = (EntityType)this.VisitType(entityType);
			EntitySet entitySet = (EntitySet)this.VisitEntitySet(expression.EntitySet);
			if (expression.Argument != dbExpression2 || entityType != entityType2 || expression.EntitySet != entitySet)
			{
				dbExpression = entitySet.RefFromKey(dbExpression2, entityType2);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x0600520A RID: 21002 RVA: 0x00124598 File Offset: 0x00122798
		public override DbExpression Visit(DbRelationshipNavigationExpression expression)
		{
			Check.NotNull<DbRelationshipNavigationExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			RelationshipEndMember relationshipEndMember;
			RelationshipEndMember relationshipEndMember2;
			this.VisitRelationshipEnds(expression.NavigateFrom, expression.NavigateTo, out relationshipEndMember, out relationshipEndMember2);
			DbExpression dbExpression2 = this.VisitExpression(expression.NavigationSource);
			if (expression.NavigateFrom != relationshipEndMember || expression.NavigateTo != relationshipEndMember2 || expression.NavigationSource != dbExpression2)
			{
				dbExpression = dbExpression2.Navigate(relationshipEndMember, relationshipEndMember2);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x0600520B RID: 21003 RVA: 0x00124603 File Offset: 0x00122803
		public override DbExpression Visit(DbDerefExpression expression)
		{
			Check.NotNull<DbDerefExpression>(expression, "expression");
			return this.VisitUnary(expression, new Func<DbExpression, DbExpression>(DbExpressionBuilder.Deref));
		}

		// Token: 0x0600520C RID: 21004 RVA: 0x00124624 File Offset: 0x00122824
		public override DbExpression Visit(DbRefKeyExpression expression)
		{
			Check.NotNull<DbRefKeyExpression>(expression, "expression");
			return this.VisitUnary(expression, new Func<DbExpression, DbExpression>(DbExpressionBuilder.GetRefKey));
		}

		// Token: 0x0600520D RID: 21005 RVA: 0x00124645 File Offset: 0x00122845
		public override DbExpression Visit(DbEntityRefExpression expression)
		{
			Check.NotNull<DbEntityRefExpression>(expression, "expression");
			return this.VisitUnary(expression, new Func<DbExpression, DbExpression>(DbExpressionBuilder.GetEntityRef));
		}

		// Token: 0x0600520E RID: 21006 RVA: 0x00124668 File Offset: 0x00122868
		public override DbExpression Visit(DbScanExpression expression)
		{
			Check.NotNull<DbScanExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			EntitySetBase entitySetBase = this.VisitEntitySet(expression.Target);
			if (expression.Target != entitySetBase)
			{
				dbExpression = entitySetBase.Scan();
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x0600520F RID: 21007 RVA: 0x001246AC File Offset: 0x001228AC
		public override DbExpression Visit(DbFilterExpression expression)
		{
			Check.NotNull<DbFilterExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbExpressionBinding dbExpressionBinding = this.VisitExpressionBindingEnterScope(expression.Input);
			DbExpression dbExpression2 = this.VisitExpression(expression.Predicate);
			this.ExitScope();
			if (expression.Input != dbExpressionBinding || expression.Predicate != dbExpression2)
			{
				dbExpression = dbExpressionBinding.Filter(dbExpression2);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x06005210 RID: 21008 RVA: 0x0012470C File Offset: 0x0012290C
		public override DbExpression Visit(DbProjectExpression expression)
		{
			Check.NotNull<DbProjectExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbExpressionBinding dbExpressionBinding = this.VisitExpressionBindingEnterScope(expression.Input);
			DbExpression dbExpression2 = this.VisitExpression(expression.Projection);
			this.ExitScope();
			if (expression.Input != dbExpressionBinding || expression.Projection != dbExpression2)
			{
				dbExpression = dbExpressionBinding.Project(dbExpression2);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x06005211 RID: 21009 RVA: 0x0012476C File Offset: 0x0012296C
		public override DbExpression Visit(DbCrossJoinExpression expression)
		{
			Check.NotNull<DbCrossJoinExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			IList<DbExpressionBinding> list = this.VisitExpressionBindingList(expression.Inputs);
			if (expression.Inputs != list)
			{
				dbExpression = DbExpressionBuilder.CrossJoin(list);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x06005212 RID: 21010 RVA: 0x001247B0 File Offset: 0x001229B0
		public override DbExpression Visit(DbJoinExpression expression)
		{
			Check.NotNull<DbJoinExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbExpressionBinding dbExpressionBinding = this.VisitExpressionBinding(expression.Left);
			DbExpressionBinding dbExpressionBinding2 = this.VisitExpressionBinding(expression.Right);
			this.EnterScope(new DbVariableReferenceExpression[] { dbExpressionBinding.Variable, dbExpressionBinding2.Variable });
			DbExpression dbExpression2 = this.VisitExpression(expression.JoinCondition);
			this.ExitScope();
			if (expression.Left != dbExpressionBinding || expression.Right != dbExpressionBinding2 || expression.JoinCondition != dbExpression2)
			{
				if (DbExpressionKind.InnerJoin == expression.ExpressionKind)
				{
					dbExpression = dbExpressionBinding.InnerJoin(dbExpressionBinding2, dbExpression2);
				}
				else if (DbExpressionKind.LeftOuterJoin == expression.ExpressionKind)
				{
					dbExpression = dbExpressionBinding.LeftOuterJoin(dbExpressionBinding2, dbExpression2);
				}
				else
				{
					dbExpression = dbExpressionBinding.FullOuterJoin(dbExpressionBinding2, dbExpression2);
				}
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x06005213 RID: 21011 RVA: 0x00124870 File Offset: 0x00122A70
		public override DbExpression Visit(DbApplyExpression expression)
		{
			Check.NotNull<DbApplyExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbExpressionBinding dbExpressionBinding = this.VisitExpressionBindingEnterScope(expression.Input);
			DbExpressionBinding dbExpressionBinding2 = this.VisitExpressionBinding(expression.Apply);
			this.ExitScope();
			if (expression.Input != dbExpressionBinding || expression.Apply != dbExpressionBinding2)
			{
				if (DbExpressionKind.CrossApply == expression.ExpressionKind)
				{
					dbExpression = dbExpressionBinding.CrossApply(dbExpressionBinding2);
				}
				else
				{
					dbExpression = dbExpressionBinding.OuterApply(dbExpressionBinding2);
				}
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x06005214 RID: 21012 RVA: 0x001248E4 File Offset: 0x00122AE4
		public override DbExpression Visit(DbGroupByExpression expression)
		{
			Check.NotNull<DbGroupByExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbGroupExpressionBinding dbGroupExpressionBinding = this.VisitGroupExpressionBinding(expression.Input);
			this.EnterScope(new DbVariableReferenceExpression[] { dbGroupExpressionBinding.Variable });
			IList<DbExpression> list = this.VisitExpressionList(expression.Keys);
			this.ExitScope();
			this.EnterScope(new DbVariableReferenceExpression[] { dbGroupExpressionBinding.GroupVariable });
			IList<DbAggregate> list2 = DefaultExpressionVisitor.VisitList<DbAggregate>(expression.Aggregates, new Func<DbAggregate, DbAggregate>(this.VisitAggregate));
			this.ExitScope();
			if (expression.Input != dbGroupExpressionBinding || expression.Keys != list || expression.Aggregates != list2)
			{
				RowType edmType = TypeHelpers.GetEdmType<RowType>(TypeHelpers.GetEdmType<CollectionType>(expression.ResultType).TypeUsage);
				List<KeyValuePair<string, DbExpression>> list3 = (from p in edmType.Properties.Take(list.Count)
					select p.Name).Zip(list).ToList<KeyValuePair<string, DbExpression>>();
				List<KeyValuePair<string, DbAggregate>> list4 = (from p in edmType.Properties.Skip(list.Count)
					select p.Name).Zip(list2).ToList<KeyValuePair<string, DbAggregate>>();
				dbExpression = dbGroupExpressionBinding.GroupBy(list3, list4);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x06005215 RID: 21013 RVA: 0x00124A34 File Offset: 0x00122C34
		public override DbExpression Visit(DbSkipExpression expression)
		{
			Check.NotNull<DbSkipExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbExpressionBinding dbExpressionBinding = this.VisitExpressionBindingEnterScope(expression.Input);
			IList<DbSortClause> list = this.VisitSortOrder(expression.SortOrder);
			this.ExitScope();
			DbExpression dbExpression2 = this.VisitExpression(expression.Count);
			if (expression.Input != dbExpressionBinding || expression.SortOrder != list || expression.Count != dbExpression2)
			{
				dbExpression = dbExpressionBinding.Skip(list, dbExpression2);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x06005216 RID: 21014 RVA: 0x00124AAC File Offset: 0x00122CAC
		public override DbExpression Visit(DbSortExpression expression)
		{
			Check.NotNull<DbSortExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbExpressionBinding dbExpressionBinding = this.VisitExpressionBindingEnterScope(expression.Input);
			IList<DbSortClause> list = this.VisitSortOrder(expression.SortOrder);
			this.ExitScope();
			if (expression.Input != dbExpressionBinding || expression.SortOrder != list)
			{
				dbExpression = dbExpressionBinding.Sort(list);
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x06005217 RID: 21015 RVA: 0x00124B0C File Offset: 0x00122D0C
		public override DbExpression Visit(DbQuantifierExpression expression)
		{
			Check.NotNull<DbQuantifierExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbExpressionBinding dbExpressionBinding = this.VisitExpressionBindingEnterScope(expression.Input);
			DbExpression dbExpression2 = this.VisitExpression(expression.Predicate);
			this.ExitScope();
			if (expression.Input != dbExpressionBinding || expression.Predicate != dbExpression2)
			{
				if (expression.ExpressionKind == DbExpressionKind.All)
				{
					dbExpression = dbExpressionBinding.All(dbExpression2);
				}
				else
				{
					dbExpression = dbExpressionBinding.Any(dbExpression2);
				}
			}
			this.NotifyIfChanged(expression, dbExpression);
			return dbExpression;
		}

		// Token: 0x04001DE0 RID: 7648
		private readonly Dictionary<DbVariableReferenceExpression, DbVariableReferenceExpression> varMappings = new Dictionary<DbVariableReferenceExpression, DbVariableReferenceExpression>();
	}
}
