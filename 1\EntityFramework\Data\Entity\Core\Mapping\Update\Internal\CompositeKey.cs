﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;

namespace System.Data.Entity.Core.Mapping.Update.Internal
{
	// Token: 0x020005C3 RID: 1475
	internal class CompositeKey
	{
		// Token: 0x06004778 RID: 18296 RVA: 0x000FB8FE File Offset: 0x000F9AFE
		internal CompositeKey(PropagatorResult[] constants)
		{
			this.KeyComponents = constants;
		}

		// Token: 0x06004779 RID: 18297 RVA: 0x000FB90D File Offset: 0x000F9B0D
		internal static IEqualityComparer<CompositeKey> CreateComparer(KeyManager keyManager)
		{
			return new CompositeKey.CompositeKeyComparer(keyManager);
		}

		// Token: 0x0600477A RID: 18298 RVA: 0x000FB918 File Offset: 0x000F9B18
		internal CompositeKey Merge(KeyManager keyManager, CompositeKey other)
		{
			PropagatorResult[] array = new PropagatorResult[this.KeyComponents.Length];
			for (int i = 0; i < this.KeyComponents.Length; i++)
			{
				array[i] = this.KeyComponents[i].Merge(keyManager, other.KeyComponents[i]);
			}
			return new CompositeKey(array);
		}

		// Token: 0x0400195D RID: 6493
		internal readonly PropagatorResult[] KeyComponents;

		// Token: 0x02000BF2 RID: 3058
		private class CompositeKeyComparer : IEqualityComparer<CompositeKey>
		{
			// Token: 0x060068C3 RID: 26819 RVA: 0x001640C5 File Offset: 0x001622C5
			internal CompositeKeyComparer(KeyManager manager)
			{
				this._manager = manager;
			}

			// Token: 0x060068C4 RID: 26820 RVA: 0x001640D4 File Offset: 0x001622D4
			public bool Equals(CompositeKey left, CompositeKey right)
			{
				if (left == right)
				{
					return true;
				}
				if (left == null || right == null)
				{
					return false;
				}
				if (left.KeyComponents.Length != right.KeyComponents.Length)
				{
					return false;
				}
				for (int i = 0; i < left.KeyComponents.Length; i++)
				{
					PropagatorResult propagatorResult = left.KeyComponents[i];
					PropagatorResult propagatorResult2 = right.KeyComponents[i];
					if (propagatorResult.Identifier != -1)
					{
						if (propagatorResult2.Identifier == -1 || this._manager.GetCliqueIdentifier(propagatorResult.Identifier) != this._manager.GetCliqueIdentifier(propagatorResult2.Identifier))
						{
							return false;
						}
					}
					else if (propagatorResult2.Identifier != -1 || !ByValueEqualityComparer.Default.Equals(propagatorResult.GetSimpleValue(), propagatorResult2.GetSimpleValue()))
					{
						return false;
					}
				}
				return true;
			}

			// Token: 0x060068C5 RID: 26821 RVA: 0x00164184 File Offset: 0x00162384
			public int GetHashCode(CompositeKey key)
			{
				int num = 0;
				foreach (PropagatorResult propagatorResult in key.KeyComponents)
				{
					num = (num << 5) ^ this.GetComponentHashCode(propagatorResult);
				}
				return num;
			}

			// Token: 0x060068C6 RID: 26822 RVA: 0x001641BC File Offset: 0x001623BC
			private int GetComponentHashCode(PropagatorResult keyComponent)
			{
				if (keyComponent.Identifier == -1)
				{
					return ByValueEqualityComparer.Default.GetHashCode(keyComponent.GetSimpleValue());
				}
				return this._manager.GetCliqueIdentifier(keyComponent.Identifier).GetHashCode();
			}

			// Token: 0x04002F39 RID: 12089
			private readonly KeyManager _manager;
		}
	}
}
