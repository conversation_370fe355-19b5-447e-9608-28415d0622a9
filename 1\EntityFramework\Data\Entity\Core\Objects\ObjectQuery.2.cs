﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.Common;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects.ELinq;
using System.Data.Entity.Core.Objects.Internal;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Internal;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace System.Data.Entity.Core.Objects
{
	// Token: 0x0200041B RID: 1051
	public class ObjectQuery<T> : ObjectQuery, IOrderedQueryable<T>, IQueryable<T>, IEnumerable<T>, IEnumerable, IQueryable, IOrderedQueryable, IDbAsyncEnumerable<T>, IDbAsyncEnumerable
	{
		// Token: 0x06003274 RID: 12916 RVA: 0x000A0DB9 File Offset: 0x0009EFB9
		private static bool IsLinqQuery(ObjectQuery query)
		{
			return query.QueryState is ELinqQueryState;
		}

		// Token: 0x06003275 RID: 12917 RVA: 0x000A0DC9 File Offset: 0x0009EFC9
		public ObjectQuery(string commandText, ObjectContext context)
			: this(new EntitySqlQueryState(typeof(T), commandText, false, context, null, null))
		{
			context.MetadataWorkspace.ImplicitLoadAssemblyForType(typeof(T), Assembly.GetCallingAssembly());
		}

		// Token: 0x06003276 RID: 12918 RVA: 0x000A0E00 File Offset: 0x0009F000
		public ObjectQuery(string commandText, ObjectContext context, MergeOption mergeOption)
			: this(new EntitySqlQueryState(typeof(T), commandText, false, context, null, null))
		{
			EntityUtil.CheckArgumentMergeOption(mergeOption);
			base.QueryState.UserSpecifiedMergeOption = new MergeOption?(mergeOption);
			context.MetadataWorkspace.ImplicitLoadAssemblyForType(typeof(T), Assembly.GetCallingAssembly());
		}

		// Token: 0x06003277 RID: 12919 RVA: 0x000A0E58 File Offset: 0x0009F058
		internal ObjectQuery(EntitySetBase entitySet, ObjectContext context, MergeOption mergeOption)
			: this(new EntitySqlQueryState(typeof(T), ObjectQuery<T>.BuildScanEntitySetEsql(entitySet), entitySet.Scan(), false, context, null, null, null))
		{
			EntityUtil.CheckArgumentMergeOption(mergeOption);
			base.QueryState.UserSpecifiedMergeOption = new MergeOption?(mergeOption);
			context.MetadataWorkspace.ImplicitLoadAssemblyForType(typeof(T), Assembly.GetCallingAssembly());
		}

		// Token: 0x06003278 RID: 12920 RVA: 0x000A0EBC File Offset: 0x0009F0BC
		private static string BuildScanEntitySetEsql(EntitySetBase entitySet)
		{
			return string.Format(CultureInfo.InvariantCulture, "{0}.{1}", new object[]
			{
				EntityUtil.QuoteIdentifier(entitySet.EntityContainer.Name),
				EntityUtil.QuoteIdentifier(entitySet.Name)
			});
		}

		// Token: 0x06003279 RID: 12921 RVA: 0x000A0EF4 File Offset: 0x0009F0F4
		internal ObjectQuery(ObjectQueryState queryState)
		{
			this._name = "it";
			base..ctor(queryState);
		}

		// Token: 0x0600327A RID: 12922 RVA: 0x000A0F08 File Offset: 0x0009F108
		internal ObjectQuery()
		{
			this._name = "it";
			base..ctor();
		}

		// Token: 0x170009C6 RID: 2502
		// (get) Token: 0x0600327B RID: 12923 RVA: 0x000A0F1B File Offset: 0x0009F11B
		// (set) Token: 0x0600327C RID: 12924 RVA: 0x000A0F23 File Offset: 0x0009F123
		public string Name
		{
			get
			{
				return this._name;
			}
			set
			{
				Check.NotNull<string>(value, "value");
				if (!ObjectParameter.ValidateParameterName(value))
				{
					throw new ArgumentException(Strings.ObjectQuery_InvalidQueryName(value), "value");
				}
				this._name = value;
			}
		}

		// Token: 0x0600327D RID: 12925 RVA: 0x000A0F51 File Offset: 0x0009F151
		public new ObjectResult<T> Execute(MergeOption mergeOption)
		{
			EntityUtil.CheckArgumentMergeOption(mergeOption);
			return this.GetResults(new MergeOption?(mergeOption));
		}

		// Token: 0x0600327E RID: 12926 RVA: 0x000A0F65 File Offset: 0x0009F165
		public new Task<ObjectResult<T>> ExecuteAsync(MergeOption mergeOption)
		{
			return this.ExecuteAsync(mergeOption, CancellationToken.None);
		}

		// Token: 0x0600327F RID: 12927 RVA: 0x000A0F73 File Offset: 0x0009F173
		public new Task<ObjectResult<T>> ExecuteAsync(MergeOption mergeOption, CancellationToken cancellationToken)
		{
			EntityUtil.CheckArgumentMergeOption(mergeOption);
			return this.GetResultsAsync(new MergeOption?(mergeOption), cancellationToken);
		}

		// Token: 0x06003280 RID: 12928 RVA: 0x000A0F88 File Offset: 0x0009F188
		public ObjectQuery<T> Include(string path)
		{
			Check.NotEmpty(path, "path");
			return new ObjectQuery<T>(base.QueryState.Include<T>(this, path));
		}

		// Token: 0x06003281 RID: 12929 RVA: 0x000A0FA8 File Offset: 0x0009F1A8
		public ObjectQuery<T> Distinct()
		{
			if (ObjectQuery<T>.IsLinqQuery(this))
			{
				return (ObjectQuery<T>)this.Distinct<T>();
			}
			return new ObjectQuery<T>(EntitySqlQueryBuilder.Distinct(base.QueryState));
		}

		// Token: 0x06003282 RID: 12930 RVA: 0x000A0FD0 File Offset: 0x0009F1D0
		public ObjectQuery<T> Except(ObjectQuery<T> query)
		{
			Check.NotNull<ObjectQuery<T>>(query, "query");
			if (ObjectQuery<T>.IsLinqQuery(this) || ObjectQuery<T>.IsLinqQuery(query))
			{
				return (ObjectQuery<T>)this.Except(query);
			}
			return new ObjectQuery<T>(EntitySqlQueryBuilder.Except(base.QueryState, query.QueryState));
		}

		// Token: 0x06003283 RID: 12931 RVA: 0x000A101C File Offset: 0x0009F21C
		public ObjectQuery<DbDataRecord> GroupBy(string keys, string projection, params ObjectParameter[] parameters)
		{
			Check.NotEmpty(keys, "keys");
			Check.NotEmpty(projection, "projection");
			Check.NotNull<ObjectParameter[]>(parameters, "parameters");
			return new ObjectQuery<DbDataRecord>(EntitySqlQueryBuilder.GroupBy(base.QueryState, this.Name, keys, projection, parameters));
		}

		// Token: 0x06003284 RID: 12932 RVA: 0x000A105C File Offset: 0x0009F25C
		public ObjectQuery<T> Intersect(ObjectQuery<T> query)
		{
			Check.NotNull<ObjectQuery<T>>(query, "query");
			if (ObjectQuery<T>.IsLinqQuery(this) || ObjectQuery<T>.IsLinqQuery(query))
			{
				return (ObjectQuery<T>)this.Intersect(query);
			}
			return new ObjectQuery<T>(EntitySqlQueryBuilder.Intersect(base.QueryState, query.QueryState));
		}

		// Token: 0x06003285 RID: 12933 RVA: 0x000A10A8 File Offset: 0x0009F2A8
		public ObjectQuery<TResultType> OfType<TResultType>()
		{
			if (ObjectQuery<T>.IsLinqQuery(this))
			{
				return (ObjectQuery<TResultType>)this.OfType<TResultType>();
			}
			base.QueryState.ObjectContext.MetadataWorkspace.ImplicitLoadAssemblyForType(typeof(TResultType), Assembly.GetCallingAssembly());
			Type typeFromHandle = typeof(TResultType);
			EdmType edmType;
			if (!base.QueryState.ObjectContext.MetadataWorkspace.GetItemCollection(DataSpace.OSpace).TryGetType(typeFromHandle.Name, typeFromHandle.NestingNamespace() ?? string.Empty, out edmType) || (!Helper.IsEntityType(edmType) && !Helper.IsComplexType(edmType)))
			{
				throw new EntitySqlException(Strings.ObjectQuery_QueryBuilder_InvalidResultType(typeof(TResultType).FullName));
			}
			return new ObjectQuery<TResultType>(EntitySqlQueryBuilder.OfType(base.QueryState, edmType, typeFromHandle));
		}

		// Token: 0x06003286 RID: 12934 RVA: 0x000A1168 File Offset: 0x0009F368
		public ObjectQuery<T> OrderBy(string keys, params ObjectParameter[] parameters)
		{
			Check.NotEmpty(keys, "keys");
			Check.NotNull<ObjectParameter[]>(parameters, "parameters");
			return new ObjectQuery<T>(EntitySqlQueryBuilder.OrderBy(base.QueryState, this.Name, keys, parameters));
		}

		// Token: 0x06003287 RID: 12935 RVA: 0x000A119A File Offset: 0x0009F39A
		public ObjectQuery<DbDataRecord> Select(string projection, params ObjectParameter[] parameters)
		{
			Check.NotEmpty(projection, "projection");
			Check.NotNull<ObjectParameter[]>(parameters, "parameters");
			return new ObjectQuery<DbDataRecord>(EntitySqlQueryBuilder.Select(base.QueryState, this.Name, projection, parameters));
		}

		// Token: 0x06003288 RID: 12936 RVA: 0x000A11CC File Offset: 0x0009F3CC
		public ObjectQuery<TResultType> SelectValue<TResultType>(string projection, params ObjectParameter[] parameters)
		{
			Check.NotEmpty(projection, "projection");
			Check.NotNull<ObjectParameter[]>(parameters, "parameters");
			base.QueryState.ObjectContext.MetadataWorkspace.ImplicitLoadAssemblyForType(typeof(TResultType), Assembly.GetCallingAssembly());
			return new ObjectQuery<TResultType>(EntitySqlQueryBuilder.SelectValue(base.QueryState, this.Name, projection, parameters, typeof(TResultType)));
		}

		// Token: 0x06003289 RID: 12937 RVA: 0x000A1237 File Offset: 0x0009F437
		public ObjectQuery<T> Skip(string keys, string count, params ObjectParameter[] parameters)
		{
			Check.NotEmpty(keys, "keys");
			Check.NotEmpty(count, "count");
			Check.NotNull<ObjectParameter[]>(parameters, "parameters");
			return new ObjectQuery<T>(EntitySqlQueryBuilder.Skip(base.QueryState, this.Name, keys, count, parameters));
		}

		// Token: 0x0600328A RID: 12938 RVA: 0x000A1276 File Offset: 0x0009F476
		public ObjectQuery<T> Top(string count, params ObjectParameter[] parameters)
		{
			Check.NotEmpty(count, "count");
			return new ObjectQuery<T>(EntitySqlQueryBuilder.Top(base.QueryState, this.Name, count, parameters));
		}

		// Token: 0x0600328B RID: 12939 RVA: 0x000A129C File Offset: 0x0009F49C
		public ObjectQuery<T> Union(ObjectQuery<T> query)
		{
			Check.NotNull<ObjectQuery<T>>(query, "query");
			if (ObjectQuery<T>.IsLinqQuery(this) || ObjectQuery<T>.IsLinqQuery(query))
			{
				return (ObjectQuery<T>)this.Union(query);
			}
			return new ObjectQuery<T>(EntitySqlQueryBuilder.Union(base.QueryState, query.QueryState));
		}

		// Token: 0x0600328C RID: 12940 RVA: 0x000A12E8 File Offset: 0x0009F4E8
		public ObjectQuery<T> UnionAll(ObjectQuery<T> query)
		{
			Check.NotNull<ObjectQuery<T>>(query, "query");
			return new ObjectQuery<T>(EntitySqlQueryBuilder.UnionAll(base.QueryState, query.QueryState));
		}

		// Token: 0x0600328D RID: 12941 RVA: 0x000A130C File Offset: 0x0009F50C
		public ObjectQuery<T> Where(string predicate, params ObjectParameter[] parameters)
		{
			Check.NotEmpty(predicate, "predicate");
			Check.NotNull<ObjectParameter[]>(parameters, "parameters");
			return new ObjectQuery<T>(EntitySqlQueryBuilder.Where(base.QueryState, this.Name, predicate, parameters));
		}

		// Token: 0x0600328E RID: 12942 RVA: 0x000A133E File Offset: 0x0009F53E
		IEnumerator<T> IEnumerable<T>.GetEnumerator()
		{
			base.QueryState.ObjectContext.AsyncMonitor.EnsureNotEntered();
			return new LazyEnumerator<T>(() => this.GetResults(null));
		}

		// Token: 0x0600328F RID: 12943 RVA: 0x000A1366 File Offset: 0x0009F566
		IDbAsyncEnumerator<T> IDbAsyncEnumerable<T>.GetAsyncEnumerator()
		{
			base.QueryState.ObjectContext.AsyncMonitor.EnsureNotEntered();
			return new LazyAsyncEnumerator<T>((CancellationToken cancellationToken) => this.GetResultsAsync(null, cancellationToken));
		}

		// Token: 0x06003290 RID: 12944 RVA: 0x000A138E File Offset: 0x0009F58E
		internal override IEnumerator GetEnumeratorInternal()
		{
			return ((IEnumerable<T>)this).GetEnumerator();
		}

		// Token: 0x06003291 RID: 12945 RVA: 0x000A1396 File Offset: 0x0009F596
		internal override IDbAsyncEnumerator GetAsyncEnumeratorInternal()
		{
			return ((IDbAsyncEnumerable<T>)this).GetAsyncEnumerator();
		}

		// Token: 0x06003292 RID: 12946 RVA: 0x000A13A0 File Offset: 0x0009F5A0
		internal override IList GetIListSourceListInternal()
		{
			return ((IListSource)this.GetResults(null)).GetList();
		}

		// Token: 0x06003293 RID: 12947 RVA: 0x000A13C1 File Offset: 0x0009F5C1
		internal override ObjectResult ExecuteInternal(MergeOption mergeOption)
		{
			return this.GetResults(new MergeOption?(mergeOption));
		}

		// Token: 0x06003294 RID: 12948 RVA: 0x000A13D0 File Offset: 0x0009F5D0
		internal override async Task<ObjectResult> ExecuteInternalAsync(MergeOption mergeOption, CancellationToken cancellationToken)
		{
			return await this.GetResultsAsync(new MergeOption?(mergeOption), cancellationToken).WithCurrentCulture<ObjectResult<T>>();
		}

		// Token: 0x06003295 RID: 12949 RVA: 0x000A1428 File Offset: 0x0009F628
		internal override Expression GetExpression()
		{
			Expression expression;
			if (!base.QueryState.TryGetExpression(out expression))
			{
				expression = Expression.Constant(this);
			}
			if (base.QueryState.UserSpecifiedMergeOption != null)
			{
				expression = TypeSystem.EnsureType(expression, typeof(ObjectQuery<T>));
				expression = Expression.Call(expression, ObjectQuery<T>.MergeAsMethod, new Expression[] { Expression.Constant(base.QueryState.UserSpecifiedMergeOption.Value) });
			}
			if (base.QueryState.Span != null)
			{
				expression = TypeSystem.EnsureType(expression, typeof(ObjectQuery<T>));
				expression = Expression.Call(expression, ObjectQuery<T>.IncludeSpanMethod, new Expression[] { Expression.Constant(base.QueryState.Span) });
			}
			return expression;
		}

		// Token: 0x06003296 RID: 12950 RVA: 0x000A14E7 File Offset: 0x0009F6E7
		internal ObjectQuery<T> MergeAs(MergeOption mergeOption)
		{
			throw new InvalidOperationException(Strings.ELinq_MethodNotDirectlyCallable);
		}

		// Token: 0x06003297 RID: 12951 RVA: 0x000A14F3 File Offset: 0x0009F6F3
		internal ObjectQuery<T> IncludeSpan(Span span)
		{
			throw new InvalidOperationException(Strings.ELinq_MethodNotDirectlyCallable);
		}

		// Token: 0x06003298 RID: 12952 RVA: 0x000A1500 File Offset: 0x0009F700
		private ObjectResult<T> GetResults(MergeOption? forMergeOption)
		{
			base.QueryState.ObjectContext.AsyncMonitor.EnsureNotEntered();
			IDbExecutionStrategy executionStrategy = base.ExecutionStrategy ?? DbProviderServices.GetExecutionStrategy(base.QueryState.ObjectContext.Connection, base.QueryState.ObjectContext.MetadataWorkspace);
			if (executionStrategy.RetriesOnFailure && base.QueryState.EffectiveStreamingBehavior)
			{
				throw new InvalidOperationException(Strings.ExecutionStrategy_StreamingNotSupported(executionStrategy.GetType().Name));
			}
			Func<ObjectResult<T>> <>9__1;
			return executionStrategy.Execute<ObjectResult<T>>(delegate
			{
				ObjectContext objectContext = this.QueryState.ObjectContext;
				Func<ObjectResult<T>> func;
				if ((func = <>9__1) == null)
				{
					func = (<>9__1 = () => this.QueryState.GetExecutionPlan(forMergeOption).Execute<T>(this.QueryState.ObjectContext, this.QueryState.Parameters));
				}
				return objectContext.ExecuteInTransaction<ObjectResult<T>>(func, executionStrategy, false, !this.QueryState.EffectiveStreamingBehavior);
			});
		}

		// Token: 0x06003299 RID: 12953 RVA: 0x000A15B8 File Offset: 0x0009F7B8
		private Task<ObjectResult<T>> GetResultsAsync(MergeOption? forMergeOption, CancellationToken cancellationToken)
		{
			cancellationToken.ThrowIfCancellationRequested();
			base.QueryState.ObjectContext.AsyncMonitor.EnsureNotEntered();
			IDbExecutionStrategy dbExecutionStrategy = base.ExecutionStrategy ?? DbProviderServices.GetExecutionStrategy(base.QueryState.ObjectContext.Connection, base.QueryState.ObjectContext.MetadataWorkspace);
			if (dbExecutionStrategy.RetriesOnFailure && base.QueryState.EffectiveStreamingBehavior)
			{
				throw new InvalidOperationException(Strings.ExecutionStrategy_StreamingNotSupported(dbExecutionStrategy.GetType().Name));
			}
			return this.GetResultsAsync(forMergeOption, dbExecutionStrategy, cancellationToken);
		}

		// Token: 0x0600329A RID: 12954 RVA: 0x000A1648 File Offset: 0x0009F848
		private async Task<ObjectResult<T>> GetResultsAsync(MergeOption? forMergeOption, IDbExecutionStrategy executionStrategy, CancellationToken cancellationToken)
		{
			MergeOption mergeOption = ((forMergeOption != null) ? forMergeOption.Value : base.QueryState.EffectiveMergeOption);
			if (mergeOption != MergeOption.NoTracking)
			{
				base.QueryState.ObjectContext.AsyncMonitor.Enter();
			}
			ObjectResult<T> objectResult;
			try
			{
				Func<Task<ObjectResult<T>>> <>9__1;
				objectResult = await executionStrategy.ExecuteAsync<ObjectResult<T>>(delegate
				{
					ObjectContext objectContext = this.QueryState.ObjectContext;
					Func<Task<ObjectResult<T>>> func;
					if ((func = <>9__1) == null)
					{
						func = (<>9__1 = () => this.QueryState.GetExecutionPlan(forMergeOption).ExecuteAsync<T>(this.QueryState.ObjectContext, this.QueryState.Parameters, cancellationToken));
					}
					return objectContext.ExecuteInTransactionAsync<ObjectResult<T>>(func, executionStrategy, false, !this.QueryState.EffectiveStreamingBehavior, cancellationToken);
				}, cancellationToken).WithCurrentCulture<ObjectResult<T>>();
			}
			finally
			{
				if (mergeOption != MergeOption.NoTracking)
				{
					base.QueryState.ObjectContext.AsyncMonitor.Exit();
				}
			}
			return objectResult;
		}

		// Token: 0x04001082 RID: 4226
		internal static readonly MethodInfo MergeAsMethod = typeof(ObjectQuery<T>).GetOnlyDeclaredMethod("MergeAs");

		// Token: 0x04001083 RID: 4227
		internal static readonly MethodInfo IncludeSpanMethod = typeof(ObjectQuery<T>).GetOnlyDeclaredMethod("IncludeSpan");

		// Token: 0x04001084 RID: 4228
		private const string DefaultName = "it";

		// Token: 0x04001085 RID: 4229
		private string _name;
	}
}
