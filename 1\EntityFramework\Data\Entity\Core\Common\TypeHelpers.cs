﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Core.Objects.ELinq;
using System.Data.Entity.Resources;
using System.Diagnostics;
using System.Globalization;

namespace System.Data.Entity.Core.Common
{
	// Token: 0x020005F0 RID: 1520
	internal static class TypeHelpers
	{
		// Token: 0x06004A66 RID: 19046 RVA: 0x00106BD0 File Offset: 0x00104DD0
		[Conditional("DEBUG")]
		internal static void AssertEdmType(TypeUsage typeUsage)
		{
			EdmType edmType = typeUsage.EdmType;
			if (!TypeSemantics.IsCollectionType(typeUsage))
			{
				if (TypeSemantics.IsStructuralType(typeUsage) && !Helper.IsComplexType(typeUsage.EdmType) && !Helper.IsEntityType(typeUsage.EdmType))
				{
					using (IEnumerator enumerator = TypeHelpers.GetDeclaredStructuralMembers(typeUsage).GetEnumerator())
					{
						while (enumerator.MoveNext())
						{
							object obj = enumerator.Current;
							EdmMember edmMember = (EdmMember)obj;
						}
						return;
					}
				}
				if (TypeSemantics.IsPrimitiveType(typeUsage))
				{
					PrimitiveType primitiveType = edmType as PrimitiveType;
					if (primitiveType != null && primitiveType.DataSpace != DataSpace.CSpace)
					{
						throw new NotSupportedException(string.Format(CultureInfo.InvariantCulture, "PrimitiveType must be CSpace '{0}'", new object[] { typeUsage }));
					}
				}
			}
		}

		// Token: 0x06004A67 RID: 19047 RVA: 0x00106C90 File Offset: 0x00104E90
		[Conditional("DEBUG")]
		internal static void AssertEdmType(DbCommandTree commandTree)
		{
			DbQueryCommandTree dbQueryCommandTree = commandTree as DbQueryCommandTree;
		}

		// Token: 0x06004A68 RID: 19048 RVA: 0x00106CA8 File Offset: 0x00104EA8
		internal static bool IsValidSortOpKeyType(TypeUsage typeUsage)
		{
			if (TypeSemantics.IsRowType(typeUsage))
			{
				using (ReadOnlyMetadataCollection<EdmProperty>.Enumerator enumerator = ((RowType)typeUsage.EdmType).Properties.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						if (!TypeHelpers.IsValidSortOpKeyType(enumerator.Current.TypeUsage))
						{
							return false;
						}
					}
				}
				return true;
			}
			return TypeSemantics.IsOrderComparable(typeUsage);
		}

		// Token: 0x06004A69 RID: 19049 RVA: 0x00106D20 File Offset: 0x00104F20
		internal static bool IsValidGroupKeyType(TypeUsage typeUsage)
		{
			return TypeHelpers.IsSetComparableOpType(typeUsage);
		}

		// Token: 0x06004A6A RID: 19050 RVA: 0x00106D28 File Offset: 0x00104F28
		internal static bool IsValidDistinctOpType(TypeUsage typeUsage)
		{
			return TypeHelpers.IsSetComparableOpType(typeUsage);
		}

		// Token: 0x06004A6B RID: 19051 RVA: 0x00106D30 File Offset: 0x00104F30
		internal static bool IsSetComparableOpType(TypeUsage typeUsage)
		{
			if (Helper.IsEntityType(typeUsage.EdmType) || Helper.IsPrimitiveType(typeUsage.EdmType) || Helper.IsEnumType(typeUsage.EdmType) || Helper.IsRefType(typeUsage.EdmType))
			{
				return true;
			}
			if (TypeSemantics.IsRowType(typeUsage))
			{
				using (ReadOnlyMetadataCollection<EdmProperty>.Enumerator enumerator = ((RowType)typeUsage.EdmType).Properties.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						if (!TypeHelpers.IsSetComparableOpType(enumerator.Current.TypeUsage))
						{
							return false;
						}
					}
				}
				return true;
			}
			return false;
		}

		// Token: 0x06004A6C RID: 19052 RVA: 0x00106DD8 File Offset: 0x00104FD8
		internal static bool IsValidIsNullOpType(TypeUsage typeUsage)
		{
			return TypeSemantics.IsReferenceType(typeUsage) || TypeSemantics.IsEntityType(typeUsage) || TypeSemantics.IsScalarType(typeUsage) || TypeSemantics.IsRowType(typeUsage);
		}

		// Token: 0x06004A6D RID: 19053 RVA: 0x00106DFA File Offset: 0x00104FFA
		internal static bool IsValidInOpType(TypeUsage typeUsage)
		{
			return TypeSemantics.IsReferenceType(typeUsage) || TypeSemantics.IsEntityType(typeUsage) || TypeSemantics.IsScalarType(typeUsage);
		}

		// Token: 0x06004A6E RID: 19054 RVA: 0x00106E14 File Offset: 0x00105014
		internal static TypeUsage GetCommonTypeUsage(TypeUsage typeUsage1, TypeUsage typeUsage2)
		{
			return TypeSemantics.GetCommonType(typeUsage1, typeUsage2);
		}

		// Token: 0x06004A6F RID: 19055 RVA: 0x00106E20 File Offset: 0x00105020
		internal static TypeUsage GetCommonTypeUsage(IEnumerable<TypeUsage> types)
		{
			TypeUsage typeUsage = null;
			foreach (TypeUsage typeUsage2 in types)
			{
				if (typeUsage2 == null)
				{
					return null;
				}
				if (typeUsage == null)
				{
					typeUsage = typeUsage2;
				}
				else
				{
					typeUsage = TypeSemantics.GetCommonType(typeUsage, typeUsage2);
					if (typeUsage == null)
					{
						break;
					}
				}
			}
			return typeUsage;
		}

		// Token: 0x06004A70 RID: 19056 RVA: 0x00106E84 File Offset: 0x00105084
		internal static bool TryGetClosestPromotableType(TypeUsage fromType, out TypeUsage promotableType)
		{
			promotableType = null;
			if (Helper.IsPrimitiveType(fromType.EdmType))
			{
				PrimitiveType primitiveType = (PrimitiveType)fromType.EdmType;
				IList<PrimitiveType> promotionTypes = EdmProviderManifest.Instance.GetPromotionTypes(primitiveType);
				int num = promotionTypes.IndexOf(primitiveType);
				if (-1 != num && num + 1 < promotionTypes.Count)
				{
					promotableType = TypeUsage.Create(promotionTypes[num + 1]);
				}
			}
			return promotableType != null;
		}

		// Token: 0x06004A71 RID: 19057 RVA: 0x00106EE8 File Offset: 0x001050E8
		internal static bool TryGetBooleanFacetValue(TypeUsage type, string facetName, out bool boolValue)
		{
			boolValue = false;
			Facet facet;
			if (type.Facets.TryGetValue(facetName, false, out facet) && facet.Value != null)
			{
				boolValue = (bool)facet.Value;
				return true;
			}
			return false;
		}

		// Token: 0x06004A72 RID: 19058 RVA: 0x00106F24 File Offset: 0x00105124
		internal static bool TryGetByteFacetValue(TypeUsage type, string facetName, out byte byteValue)
		{
			byteValue = 0;
			Facet facet;
			if (type.Facets.TryGetValue(facetName, false, out facet) && facet.Value != null && !Helper.IsUnboundedFacetValue(facet))
			{
				byteValue = (byte)facet.Value;
				return true;
			}
			return false;
		}

		// Token: 0x06004A73 RID: 19059 RVA: 0x00106F68 File Offset: 0x00105168
		internal static bool TryGetIntFacetValue(TypeUsage type, string facetName, out int intValue)
		{
			intValue = 0;
			Facet facet;
			if (type.Facets.TryGetValue(facetName, false, out facet) && facet.Value != null && !Helper.IsUnboundedFacetValue(facet) && !Helper.IsVariableFacetValue(facet))
			{
				intValue = (int)facet.Value;
				return true;
			}
			return false;
		}

		// Token: 0x06004A74 RID: 19060 RVA: 0x00106FB1 File Offset: 0x001051B1
		internal static bool TryGetIsFixedLength(TypeUsage type, out bool isFixedLength)
		{
			if (!TypeSemantics.IsPrimitiveType(type, PrimitiveTypeKind.String) && !TypeSemantics.IsPrimitiveType(type, PrimitiveTypeKind.Binary))
			{
				isFixedLength = false;
				return false;
			}
			return TypeHelpers.TryGetBooleanFacetValue(type, "FixedLength", out isFixedLength);
		}

		// Token: 0x06004A75 RID: 19061 RVA: 0x00106FD7 File Offset: 0x001051D7
		internal static bool TryGetIsUnicode(TypeUsage type, out bool isUnicode)
		{
			if (!TypeSemantics.IsPrimitiveType(type, PrimitiveTypeKind.String))
			{
				isUnicode = false;
				return false;
			}
			return TypeHelpers.TryGetBooleanFacetValue(type, "Unicode", out isUnicode);
		}

		// Token: 0x06004A76 RID: 19062 RVA: 0x00106FF4 File Offset: 0x001051F4
		internal static bool IsFacetValueConstant(TypeUsage type, string facetName)
		{
			return Helper.GetFacet(((PrimitiveType)type.EdmType).FacetDescriptions, facetName).IsConstant;
		}

		// Token: 0x06004A77 RID: 19063 RVA: 0x00107011 File Offset: 0x00105211
		internal static bool TryGetMaxLength(TypeUsage type, out int maxLength)
		{
			if (!TypeSemantics.IsPrimitiveType(type, PrimitiveTypeKind.String) && !TypeSemantics.IsPrimitiveType(type, PrimitiveTypeKind.Binary))
			{
				maxLength = 0;
				return false;
			}
			return TypeHelpers.TryGetIntFacetValue(type, "MaxLength", out maxLength);
		}

		// Token: 0x06004A78 RID: 19064 RVA: 0x00107037 File Offset: 0x00105237
		internal static bool TryGetPrecision(TypeUsage type, out byte precision)
		{
			if (!TypeSemantics.IsPrimitiveType(type, PrimitiveTypeKind.Decimal))
			{
				precision = 0;
				return false;
			}
			return TypeHelpers.TryGetByteFacetValue(type, "Precision", out precision);
		}

		// Token: 0x06004A79 RID: 19065 RVA: 0x00107053 File Offset: 0x00105253
		internal static bool TryGetScale(TypeUsage type, out byte scale)
		{
			if (!TypeSemantics.IsPrimitiveType(type, PrimitiveTypeKind.Decimal))
			{
				scale = 0;
				return false;
			}
			return TypeHelpers.TryGetByteFacetValue(type, "Scale", out scale);
		}

		// Token: 0x06004A7A RID: 19066 RVA: 0x0010706F File Offset: 0x0010526F
		internal static bool TryGetPrimitiveTypeKind(TypeUsage type, out PrimitiveTypeKind typeKind)
		{
			if (type != null && type.EdmType != null && type.EdmType.BuiltInTypeKind == BuiltInTypeKind.PrimitiveType)
			{
				typeKind = ((PrimitiveType)type.EdmType).PrimitiveTypeKind;
				return true;
			}
			typeKind = PrimitiveTypeKind.Binary;
			return false;
		}

		// Token: 0x06004A7B RID: 19067 RVA: 0x001070A3 File Offset: 0x001052A3
		internal static CollectionType CreateCollectionType(TypeUsage elementType)
		{
			return new CollectionType(elementType);
		}

		// Token: 0x06004A7C RID: 19068 RVA: 0x001070AB File Offset: 0x001052AB
		internal static TypeUsage CreateCollectionTypeUsage(TypeUsage elementType)
		{
			return TypeUsage.Create(new CollectionType(elementType));
		}

		// Token: 0x06004A7D RID: 19069 RVA: 0x001070B8 File Offset: 0x001052B8
		internal static RowType CreateRowType(IEnumerable<KeyValuePair<string, TypeUsage>> columns)
		{
			return TypeHelpers.CreateRowType(columns, null);
		}

		// Token: 0x06004A7E RID: 19070 RVA: 0x001070C4 File Offset: 0x001052C4
		internal static RowType CreateRowType(IEnumerable<KeyValuePair<string, TypeUsage>> columns, InitializerMetadata initializerMetadata)
		{
			List<EdmProperty> list = new List<EdmProperty>();
			foreach (KeyValuePair<string, TypeUsage> keyValuePair in columns)
			{
				list.Add(new EdmProperty(keyValuePair.Key, keyValuePair.Value));
			}
			return new RowType(list, initializerMetadata);
		}

		// Token: 0x06004A7F RID: 19071 RVA: 0x0010712C File Offset: 0x0010532C
		internal static TypeUsage CreateRowTypeUsage(IEnumerable<KeyValuePair<string, TypeUsage>> columns)
		{
			return TypeUsage.Create(TypeHelpers.CreateRowType(columns));
		}

		// Token: 0x06004A80 RID: 19072 RVA: 0x00107139 File Offset: 0x00105339
		internal static RefType CreateReferenceType(EntityTypeBase entityType)
		{
			return new RefType((EntityType)entityType);
		}

		// Token: 0x06004A81 RID: 19073 RVA: 0x00107146 File Offset: 0x00105346
		internal static TypeUsage CreateReferenceTypeUsage(EntityType entityType)
		{
			return TypeUsage.Create(TypeHelpers.CreateReferenceType(entityType));
		}

		// Token: 0x06004A82 RID: 19074 RVA: 0x00107154 File Offset: 0x00105354
		internal static RowType CreateKeyRowType(EntityTypeBase entityType)
		{
			IEnumerable<EdmMember> keyMembers = entityType.KeyMembers;
			if (keyMembers == null)
			{
				throw new ArgumentException(Strings.Cqt_Metadata_EntityTypeNullKeyMembersInvalid, "entityType");
			}
			List<KeyValuePair<string, TypeUsage>> list = new List<KeyValuePair<string, TypeUsage>>();
			foreach (EdmMember edmMember in keyMembers)
			{
				EdmProperty edmProperty = (EdmProperty)edmMember;
				list.Add(new KeyValuePair<string, TypeUsage>(edmProperty.Name, Helper.GetModelTypeUsage(edmProperty)));
			}
			if (list.Count < 1)
			{
				throw new ArgumentException(Strings.Cqt_Metadata_EntityTypeEmptyKeyMembersInvalid, "entityType");
			}
			return TypeHelpers.CreateRowType(list);
		}

		// Token: 0x06004A83 RID: 19075 RVA: 0x001071F0 File Offset: 0x001053F0
		internal static TypeUsage GetPrimitiveTypeUsageForScalar(TypeUsage scalarType)
		{
			if (!TypeSemantics.IsEnumerationType(scalarType))
			{
				return scalarType;
			}
			return TypeHelpers.CreateEnumUnderlyingTypeUsage(scalarType);
		}

		// Token: 0x06004A84 RID: 19076 RVA: 0x00107202 File Offset: 0x00105402
		internal static TypeUsage CreateEnumUnderlyingTypeUsage(TypeUsage enumTypeUsage)
		{
			return TypeUsage.Create(Helper.GetUnderlyingEdmTypeForEnumType(enumTypeUsage.EdmType), enumTypeUsage.Facets);
		}

		// Token: 0x06004A85 RID: 19077 RVA: 0x0010721A File Offset: 0x0010541A
		internal static TypeUsage CreateSpatialUnionTypeUsage(TypeUsage spatialTypeUsage)
		{
			return TypeUsage.Create(Helper.GetSpatialNormalizedPrimitiveType(spatialTypeUsage.EdmType), spatialTypeUsage.Facets);
		}

		// Token: 0x06004A86 RID: 19078 RVA: 0x00107232 File Offset: 0x00105432
		internal static IBaseList<EdmMember> GetAllStructuralMembers(TypeUsage type)
		{
			return TypeHelpers.GetAllStructuralMembers(type.EdmType);
		}

		// Token: 0x06004A87 RID: 19079 RVA: 0x00107240 File Offset: 0x00105440
		internal static IBaseList<EdmMember> GetAllStructuralMembers(EdmType edmType)
		{
			BuiltInTypeKind builtInTypeKind = edmType.BuiltInTypeKind;
			if (builtInTypeKind <= BuiltInTypeKind.ComplexType)
			{
				if (builtInTypeKind == BuiltInTypeKind.AssociationType)
				{
					return (IBaseList<EdmMember>)((AssociationType)edmType).AssociationEndMembers;
				}
				if (builtInTypeKind == BuiltInTypeKind.ComplexType)
				{
					return (IBaseList<EdmMember>)((ComplexType)edmType).Properties;
				}
			}
			else
			{
				if (builtInTypeKind == BuiltInTypeKind.EntityType)
				{
					return (IBaseList<EdmMember>)((EntityType)edmType).Properties;
				}
				if (builtInTypeKind == BuiltInTypeKind.RowType)
				{
					return (IBaseList<EdmMember>)((RowType)edmType).Properties;
				}
			}
			return TypeHelpers.EmptyArrayEdmProperty;
		}

		// Token: 0x06004A88 RID: 19080 RVA: 0x001072B7 File Offset: 0x001054B7
		internal static IEnumerable GetDeclaredStructuralMembers(TypeUsage type)
		{
			return TypeHelpers.GetDeclaredStructuralMembers(type.EdmType);
		}

		// Token: 0x06004A89 RID: 19081 RVA: 0x001072C4 File Offset: 0x001054C4
		internal static IEnumerable GetDeclaredStructuralMembers(EdmType edmType)
		{
			BuiltInTypeKind builtInTypeKind = edmType.BuiltInTypeKind;
			if (builtInTypeKind <= BuiltInTypeKind.ComplexType)
			{
				if (builtInTypeKind == BuiltInTypeKind.AssociationType)
				{
					return ((AssociationType)edmType).GetDeclaredOnlyMembers<AssociationEndMember>();
				}
				if (builtInTypeKind == BuiltInTypeKind.ComplexType)
				{
					return ((ComplexType)edmType).GetDeclaredOnlyMembers<EdmProperty>();
				}
			}
			else
			{
				if (builtInTypeKind == BuiltInTypeKind.EntityType)
				{
					return ((EntityType)edmType).GetDeclaredOnlyMembers<EdmProperty>();
				}
				if (builtInTypeKind == BuiltInTypeKind.RowType)
				{
					return ((RowType)edmType).GetDeclaredOnlyMembers<EdmProperty>();
				}
			}
			return TypeHelpers.EmptyArrayEdmProperty;
		}

		// Token: 0x06004A8A RID: 19082 RVA: 0x00107327 File Offset: 0x00105527
		internal static ReadOnlyMetadataCollection<EdmProperty> GetProperties(TypeUsage typeUsage)
		{
			return TypeHelpers.GetProperties(typeUsage.EdmType);
		}

		// Token: 0x06004A8B RID: 19083 RVA: 0x00107334 File Offset: 0x00105534
		internal static ReadOnlyMetadataCollection<EdmProperty> GetProperties(EdmType edmType)
		{
			BuiltInTypeKind builtInTypeKind = edmType.BuiltInTypeKind;
			if (builtInTypeKind == BuiltInTypeKind.ComplexType)
			{
				return ((ComplexType)edmType).Properties;
			}
			if (builtInTypeKind == BuiltInTypeKind.EntityType)
			{
				return ((EntityType)edmType).Properties;
			}
			if (builtInTypeKind != BuiltInTypeKind.RowType)
			{
				return TypeHelpers.EmptyArrayEdmProperty;
			}
			return ((RowType)edmType).Properties;
		}

		// Token: 0x06004A8C RID: 19084 RVA: 0x00107381 File Offset: 0x00105581
		internal static TypeUsage GetElementTypeUsage(TypeUsage type)
		{
			if (TypeSemantics.IsCollectionType(type))
			{
				return ((CollectionType)type.EdmType).TypeUsage;
			}
			if (TypeSemantics.IsReferenceType(type))
			{
				return TypeUsage.Create(((RefType)type.EdmType).ElementType);
			}
			return null;
		}

		// Token: 0x06004A8D RID: 19085 RVA: 0x001073BC File Offset: 0x001055BC
		internal static RowType GetTvfReturnType(EdmFunction tvf)
		{
			if (tvf.ReturnParameter != null && TypeSemantics.IsCollectionType(tvf.ReturnParameter.TypeUsage))
			{
				TypeUsage typeUsage = ((CollectionType)tvf.ReturnParameter.TypeUsage.EdmType).TypeUsage;
				if (TypeSemantics.IsRowType(typeUsage))
				{
					return (RowType)typeUsage.EdmType;
				}
			}
			return null;
		}

		// Token: 0x06004A8E RID: 19086 RVA: 0x00107414 File Offset: 0x00105614
		internal static bool TryGetCollectionElementType(TypeUsage type, out TypeUsage elementType)
		{
			CollectionType collectionType;
			if (TypeHelpers.TryGetEdmType<CollectionType>(type, out collectionType))
			{
				elementType = collectionType.TypeUsage;
				return elementType != null;
			}
			elementType = null;
			return false;
		}

		// Token: 0x06004A8F RID: 19087 RVA: 0x00107440 File Offset: 0x00105640
		internal static bool TryGetRefEntityType(TypeUsage type, out EntityType referencedEntityType)
		{
			RefType refType;
			if (TypeHelpers.TryGetEdmType<RefType>(type, out refType) && Helper.IsEntityType(refType.ElementType))
			{
				referencedEntityType = (EntityType)refType.ElementType;
				return true;
			}
			referencedEntityType = null;
			return false;
		}

		// Token: 0x06004A90 RID: 19088 RVA: 0x00107477 File Offset: 0x00105677
		internal static TEdmType GetEdmType<TEdmType>(TypeUsage typeUsage) where TEdmType : EdmType
		{
			return (TEdmType)((object)typeUsage.EdmType);
		}

		// Token: 0x06004A91 RID: 19089 RVA: 0x00107484 File Offset: 0x00105684
		internal static bool TryGetEdmType<TEdmType>(TypeUsage typeUsage, out TEdmType type) where TEdmType : EdmType
		{
			type = typeUsage.EdmType as TEdmType;
			return type != null;
		}

		// Token: 0x06004A92 RID: 19090 RVA: 0x001074AA File Offset: 0x001056AA
		internal static TypeUsage GetReadOnlyType(TypeUsage type)
		{
			if (!type.IsReadOnly)
			{
				type.SetReadOnly();
			}
			return type;
		}

		// Token: 0x06004A93 RID: 19091 RVA: 0x001074BC File Offset: 0x001056BC
		internal static string GetFullName(string qualifier, string name)
		{
			if (!string.IsNullOrEmpty(qualifier))
			{
				return string.Format(CultureInfo.InvariantCulture, "{0}.{1}", new object[] { qualifier, name });
			}
			return string.Format(CultureInfo.InvariantCulture, "{0}", new object[] { name });
		}

		// Token: 0x06004A94 RID: 19092 RVA: 0x00107508 File Offset: 0x00105708
		internal static DbType ConvertClrTypeToDbType(Type clrType)
		{
			switch (Type.GetTypeCode(clrType))
			{
			case TypeCode.Empty:
				throw new ArgumentException(Strings.ADP_InvalidDataType(TypeCode.Empty.ToString()));
			case TypeCode.Object:
				if (clrType == typeof(byte[]))
				{
					return DbType.Binary;
				}
				if (clrType == typeof(char[]))
				{
					return DbType.String;
				}
				if (clrType == typeof(Guid))
				{
					return DbType.Guid;
				}
				if (clrType == typeof(TimeSpan))
				{
					return DbType.Time;
				}
				if (clrType == typeof(DateTimeOffset))
				{
					return DbType.DateTimeOffset;
				}
				return DbType.Object;
			case TypeCode.DBNull:
				return DbType.Object;
			case TypeCode.Boolean:
				return DbType.Boolean;
			case TypeCode.Char:
				return DbType.String;
			case TypeCode.SByte:
				return DbType.SByte;
			case TypeCode.Byte:
				return DbType.Byte;
			case TypeCode.Int16:
				return DbType.Int16;
			case TypeCode.UInt16:
				return DbType.UInt16;
			case TypeCode.Int32:
				return DbType.Int32;
			case TypeCode.UInt32:
				return DbType.UInt32;
			case TypeCode.Int64:
				return DbType.Int64;
			case TypeCode.UInt64:
				return DbType.UInt64;
			case TypeCode.Single:
				return DbType.Single;
			case TypeCode.Double:
				return DbType.Double;
			case TypeCode.Decimal:
				return DbType.Decimal;
			case TypeCode.DateTime:
				return DbType.DateTime;
			case TypeCode.String:
				return DbType.String;
			}
			throw new ArgumentException(Strings.ADP_UnknownDataTypeCode(((int)Type.GetTypeCode(clrType)).ToString(CultureInfo.InvariantCulture), clrType.FullName));
		}

		// Token: 0x06004A95 RID: 19093 RVA: 0x00107648 File Offset: 0x00105848
		internal static bool IsIntegerConstant(TypeUsage valueType, object value, long expectedValue)
		{
			if (!TypeSemantics.IsIntegerNumericType(valueType))
			{
				return false;
			}
			if (value == null)
			{
				return false;
			}
			PrimitiveTypeKind primitiveTypeKind = ((PrimitiveType)valueType.EdmType).PrimitiveTypeKind;
			if (primitiveTypeKind == PrimitiveTypeKind.Byte)
			{
				return expectedValue == (long)((ulong)((byte)value));
			}
			switch (primitiveTypeKind)
			{
			case PrimitiveTypeKind.SByte:
				return expectedValue == (long)((sbyte)value);
			case PrimitiveTypeKind.Int16:
				return expectedValue == (long)((short)value);
			case PrimitiveTypeKind.Int32:
				return expectedValue == (long)((int)value);
			case PrimitiveTypeKind.Int64:
				return expectedValue == (long)value;
			default:
				return false;
			}
		}

		// Token: 0x06004A96 RID: 19094 RVA: 0x001076CA File Offset: 0x001058CA
		internal static TypeUsage GetLiteralTypeUsage(PrimitiveTypeKind primitiveTypeKind)
		{
			return TypeHelpers.GetLiteralTypeUsage(primitiveTypeKind, true);
		}

		// Token: 0x06004A97 RID: 19095 RVA: 0x001076D4 File Offset: 0x001058D4
		internal static TypeUsage GetLiteralTypeUsage(PrimitiveTypeKind primitiveTypeKind, bool isUnicode)
		{
			PrimitiveType primitiveType = EdmProviderManifest.Instance.GetPrimitiveType(primitiveTypeKind);
			TypeUsage typeUsage;
			if (primitiveTypeKind == PrimitiveTypeKind.String)
			{
				typeUsage = TypeUsage.Create(primitiveType, new FacetValues
				{
					Unicode = new bool?(isUnicode),
					MaxLength = TypeUsage.DefaultMaxLengthFacetValue,
					FixedLength = new bool?(false),
					Nullable = new bool?(false)
				});
			}
			else
			{
				typeUsage = TypeUsage.Create(primitiveType, new FacetValues
				{
					Nullable = new bool?(false)
				});
			}
			return typeUsage;
		}

		// Token: 0x06004A98 RID: 19096 RVA: 0x00107761 File Offset: 0x00105961
		internal static bool IsCanonicalFunction(EdmFunction function)
		{
			return function.DataSpace == DataSpace.CSpace && function.NamespaceName == "Edm";
		}

		// Token: 0x04001A39 RID: 6713
		internal static readonly ReadOnlyMetadataCollection<EdmMember> EmptyArrayEdmMember = new ReadOnlyMetadataCollection<EdmMember>(new MetadataCollection<EdmMember>().SetReadOnly());

		// Token: 0x04001A3A RID: 6714
		internal static readonly FilteredReadOnlyMetadataCollection<EdmProperty, EdmMember> EmptyArrayEdmProperty = new FilteredReadOnlyMetadataCollection<EdmProperty, EdmMember>(TypeHelpers.EmptyArrayEdmMember, null);
	}
}
