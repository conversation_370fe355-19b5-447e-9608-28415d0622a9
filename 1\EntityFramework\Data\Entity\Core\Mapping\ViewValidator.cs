﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x02000562 RID: 1378
	internal static class ViewValidator
	{
		// Token: 0x06004356 RID: 17238 RVA: 0x000E6918 File Offset: 0x000E4B18
		internal static IEnumerable<EdmSchemaError> ValidateQueryView(DbQueryCommandTree view, EntitySetBaseMapping setMapping, EntityTypeBase elementType, bool includeSubtypes)
		{
			ViewValidator.ViewExpressionValidator viewExpressionValidator = new ViewValidator.ViewExpressionValidator(setMapping, elementType, includeSubtypes);
			viewExpressionValidator.VisitExpression(view.Query);
			if (viewExpressionValidator.Errors.Count<EdmSchemaError>() == 0 && setMapping.Set.BuiltInTypeKind == BuiltInTypeKind.AssociationSet)
			{
				ViewValidator.AssociationSetViewValidator associationSetViewValidator = new ViewValidator.AssociationSetViewValidator(setMapping);
				associationSetViewValidator.VisitExpression(view.Query);
				return associationSetViewValidator.Errors;
			}
			return viewExpressionValidator.Errors;
		}

		// Token: 0x02000B6B RID: 2923
		private sealed class ViewExpressionValidator : BasicExpressionVisitor
		{
			// Token: 0x17001103 RID: 4355
			// (get) Token: 0x0600660C RID: 26124 RVA: 0x0015E03E File Offset: 0x0015C23E
			private EdmItemCollection EdmItemCollection
			{
				get
				{
					return this._setMapping.EntityContainerMapping.StorageMappingItemCollection.EdmItemCollection;
				}
			}

			// Token: 0x17001104 RID: 4356
			// (get) Token: 0x0600660D RID: 26125 RVA: 0x0015E055 File Offset: 0x0015C255
			private StoreItemCollection StoreItemCollection
			{
				get
				{
					return this._setMapping.EntityContainerMapping.StorageMappingItemCollection.StoreItemCollection;
				}
			}

			// Token: 0x0600660E RID: 26126 RVA: 0x0015E06C File Offset: 0x0015C26C
			internal ViewExpressionValidator(EntitySetBaseMapping setMapping, EntityTypeBase elementType, bool includeSubtypes)
			{
				this._setMapping = setMapping;
				this._elementType = elementType;
				this._includeSubtypes = includeSubtypes;
				this._errors = new List<EdmSchemaError>();
			}

			// Token: 0x17001105 RID: 4357
			// (get) Token: 0x0600660F RID: 26127 RVA: 0x0015E094 File Offset: 0x0015C294
			internal IEnumerable<EdmSchemaError> Errors
			{
				get
				{
					return this._errors;
				}
			}

			// Token: 0x06006610 RID: 26128 RVA: 0x0015E09C File Offset: 0x0015C29C
			public override void VisitExpression(DbExpression expression)
			{
				Check.NotNull<DbExpression>(expression, "expression");
				this.ValidateExpressionKind(expression.ExpressionKind);
				base.VisitExpression(expression);
			}

			// Token: 0x06006611 RID: 26129 RVA: 0x0015E0C0 File Offset: 0x0015C2C0
			private void ValidateExpressionKind(DbExpressionKind expressionKind)
			{
				switch (expressionKind)
				{
				case DbExpressionKind.And:
				case DbExpressionKind.Case:
				case DbExpressionKind.Cast:
				case DbExpressionKind.Constant:
				case DbExpressionKind.EntityRef:
				case DbExpressionKind.Equals:
				case DbExpressionKind.Filter:
				case DbExpressionKind.FullOuterJoin:
				case DbExpressionKind.Function:
				case DbExpressionKind.GreaterThan:
				case DbExpressionKind.GreaterThanOrEquals:
				case DbExpressionKind.InnerJoin:
				case DbExpressionKind.IsNull:
				case DbExpressionKind.LeftOuterJoin:
				case DbExpressionKind.LessThan:
				case DbExpressionKind.LessThanOrEquals:
				case DbExpressionKind.NewInstance:
				case DbExpressionKind.Not:
				case DbExpressionKind.NotEquals:
				case DbExpressionKind.Null:
				case DbExpressionKind.Or:
				case DbExpressionKind.Project:
				case DbExpressionKind.Property:
				case DbExpressionKind.Ref:
					return;
				case DbExpressionKind.Any:
				case DbExpressionKind.CrossApply:
				case DbExpressionKind.CrossJoin:
				case DbExpressionKind.Deref:
				case DbExpressionKind.Distinct:
				case DbExpressionKind.Divide:
				case DbExpressionKind.Element:
				case DbExpressionKind.Except:
				case DbExpressionKind.GroupBy:
				case DbExpressionKind.Intersect:
				case DbExpressionKind.IsEmpty:
				case DbExpressionKind.IsOf:
				case DbExpressionKind.IsOfOnly:
				case DbExpressionKind.Like:
				case DbExpressionKind.Limit:
				case DbExpressionKind.Minus:
				case DbExpressionKind.Modulo:
				case DbExpressionKind.Multiply:
				case DbExpressionKind.OfType:
				case DbExpressionKind.OfTypeOnly:
				case DbExpressionKind.OuterApply:
				case DbExpressionKind.ParameterReference:
				case DbExpressionKind.Plus:
					break;
				default:
					if (expressionKind == DbExpressionKind.Scan || expressionKind - DbExpressionKind.UnionAll <= 1)
					{
						return;
					}
					break;
				}
				string text;
				if (!this._includeSubtypes)
				{
					text = this._elementType.ToString();
				}
				else
				{
					string text2 = "IsTypeOf(";
					EntityTypeBase elementType = this._elementType;
					text = text2 + ((elementType != null) ? elementType.ToString() : null) + ")";
				}
				string text3 = text;
				this._errors.Add(new EdmSchemaError(Strings.Mapping_UnsupportedExpressionKind_QueryView(this._setMapping.Set.Name, text3, expressionKind), 2071, EdmSchemaErrorSeverity.Error, this._setMapping.EntityContainerMapping.SourceLocation, this._setMapping.StartLineNumber, this._setMapping.StartLinePosition));
			}

			// Token: 0x06006612 RID: 26130 RVA: 0x0015E234 File Offset: 0x0015C434
			public override void Visit(DbPropertyExpression expression)
			{
				Check.NotNull<DbPropertyExpression>(expression, "expression");
				base.Visit(expression);
				if (expression.Property.BuiltInTypeKind != BuiltInTypeKind.EdmProperty)
				{
					this._errors.Add(new EdmSchemaError(Strings.Mapping_UnsupportedPropertyKind_QueryView(this._setMapping.Set.Name, expression.Property.Name, expression.Property.BuiltInTypeKind), 2073, EdmSchemaErrorSeverity.Error, this._setMapping.EntityContainerMapping.SourceLocation, this._setMapping.StartLineNumber, this._setMapping.StartLinePosition));
				}
			}

			// Token: 0x06006613 RID: 26131 RVA: 0x0015E2D0 File Offset: 0x0015C4D0
			public override void Visit(DbNewInstanceExpression expression)
			{
				Check.NotNull<DbNewInstanceExpression>(expression, "expression");
				base.Visit(expression);
				EdmType edmType = expression.ResultType.EdmType;
				if (edmType.BuiltInTypeKind != BuiltInTypeKind.RowType && edmType != this._elementType && (!this._includeSubtypes || !this._elementType.IsAssignableFrom(edmType)) && (edmType.BuiltInTypeKind != BuiltInTypeKind.ComplexType || !this.GetComplexTypes().Contains((ComplexType)edmType)))
				{
					this._errors.Add(new EdmSchemaError(Strings.Mapping_UnsupportedInitialization_QueryView(this._setMapping.Set.Name, edmType.FullName), 2074, EdmSchemaErrorSeverity.Error, this._setMapping.EntityContainerMapping.SourceLocation, this._setMapping.StartLineNumber, this._setMapping.StartLinePosition));
				}
			}

			// Token: 0x06006614 RID: 26132 RVA: 0x0015E3A0 File Offset: 0x0015C5A0
			private IEnumerable<ComplexType> GetComplexTypes()
			{
				IEnumerable<EdmProperty> enumerable = this.GetEntityTypes().SelectMany((EntityType entityType) => entityType.Properties).Distinct<EdmProperty>();
				return this.GetComplexTypes(enumerable);
			}

			// Token: 0x06006615 RID: 26133 RVA: 0x0015E3E4 File Offset: 0x0015C5E4
			private IEnumerable<ComplexType> GetComplexTypes(IEnumerable<EdmProperty> properties)
			{
				foreach (ComplexType complexType in properties.Select((EdmProperty p) => p.TypeUsage.EdmType).OfType<ComplexType>())
				{
					yield return complexType;
					foreach (ComplexType complexType2 in this.GetComplexTypes(complexType.Properties))
					{
						yield return complexType2;
					}
					IEnumerator<ComplexType> enumerator2 = null;
					complexType = null;
				}
				IEnumerator<ComplexType> enumerator = null;
				yield break;
				yield break;
			}

			// Token: 0x06006616 RID: 26134 RVA: 0x0015E3FC File Offset: 0x0015C5FC
			private IEnumerable<EntityType> GetEntityTypes()
			{
				if (this._includeSubtypes)
				{
					return MetadataHelper.GetTypeAndSubtypesOf(this._elementType, this.EdmItemCollection, true).OfType<EntityType>();
				}
				if (this._elementType.BuiltInTypeKind == BuiltInTypeKind.EntityType)
				{
					return Enumerable.Repeat<EntityType>((EntityType)this._elementType, 1);
				}
				return Enumerable.Empty<EntityType>();
			}

			// Token: 0x06006617 RID: 26135 RVA: 0x0015E450 File Offset: 0x0015C650
			public override void Visit(DbFunctionExpression expression)
			{
				Check.NotNull<DbFunctionExpression>(expression, "expression");
				base.Visit(expression);
				if (!ViewValidator.ViewExpressionValidator.IsStoreSpaceOrCanonicalFunction(this.StoreItemCollection, expression.Function))
				{
					this._errors.Add(new EdmSchemaError(Strings.Mapping_UnsupportedFunctionCall_QueryView(this._setMapping.Set.Name, expression.Function.Identity), 2112, EdmSchemaErrorSeverity.Error, this._setMapping.EntityContainerMapping.SourceLocation, this._setMapping.StartLineNumber, this._setMapping.StartLinePosition));
				}
			}

			// Token: 0x06006618 RID: 26136 RVA: 0x0015E4DF File Offset: 0x0015C6DF
			internal static bool IsStoreSpaceOrCanonicalFunction(StoreItemCollection sSpace, EdmFunction function)
			{
				return TypeHelpers.IsCanonicalFunction(function) || sSpace.GetCTypeFunctions(function.FullName, false).Contains(function);
			}

			// Token: 0x06006619 RID: 26137 RVA: 0x0015E500 File Offset: 0x0015C700
			public override void Visit(DbScanExpression expression)
			{
				Check.NotNull<DbScanExpression>(expression, "expression");
				base.Visit(expression);
				EntitySetBase target = expression.Target;
				if (target.EntityContainer.DataSpace != DataSpace.SSpace)
				{
					this._errors.Add(new EdmSchemaError(Strings.Mapping_UnsupportedScanTarget_QueryView(this._setMapping.Set.Name, target.Name), 2072, EdmSchemaErrorSeverity.Error, this._setMapping.EntityContainerMapping.SourceLocation, this._setMapping.StartLineNumber, this._setMapping.StartLinePosition));
				}
			}

			// Token: 0x04002DCC RID: 11724
			private readonly EntitySetBaseMapping _setMapping;

			// Token: 0x04002DCD RID: 11725
			private readonly List<EdmSchemaError> _errors;

			// Token: 0x04002DCE RID: 11726
			private readonly EntityTypeBase _elementType;

			// Token: 0x04002DCF RID: 11727
			private readonly bool _includeSubtypes;
		}

		// Token: 0x02000B6C RID: 2924
		private class AssociationSetViewValidator : DbExpressionVisitor<ViewValidator.DbExpressionEntitySetInfo>
		{
			// Token: 0x0600661A RID: 26138 RVA: 0x0015E58C File Offset: 0x0015C78C
			internal AssociationSetViewValidator(EntitySetBaseMapping setMapping)
			{
				this._setMapping = setMapping;
			}

			// Token: 0x17001106 RID: 4358
			// (get) Token: 0x0600661B RID: 26139 RVA: 0x0015E5B1 File Offset: 0x0015C7B1
			internal List<EdmSchemaError> Errors
			{
				get
				{
					return this._errors;
				}
			}

			// Token: 0x0600661C RID: 26140 RVA: 0x0015E5B9 File Offset: 0x0015C7B9
			internal ViewValidator.DbExpressionEntitySetInfo VisitExpression(DbExpression expression)
			{
				return expression.Accept<ViewValidator.DbExpressionEntitySetInfo>(this);
			}

			// Token: 0x0600661D RID: 26141 RVA: 0x0015E5C2 File Offset: 0x0015C7C2
			private ViewValidator.DbExpressionEntitySetInfo VisitExpressionBinding(DbExpressionBinding binding)
			{
				if (binding != null)
				{
					return this.VisitExpression(binding.Expression);
				}
				return null;
			}

			// Token: 0x0600661E RID: 26142 RVA: 0x0015E5D8 File Offset: 0x0015C7D8
			private void VisitExpressionBindingEnterScope(DbExpressionBinding binding)
			{
				ViewValidator.DbExpressionEntitySetInfo dbExpressionEntitySetInfo = this.VisitExpressionBinding(binding);
				this.variableScopes.Push(new KeyValuePair<string, ViewValidator.DbExpressionEntitySetInfo>(binding.VariableName, dbExpressionEntitySetInfo));
			}

			// Token: 0x0600661F RID: 26143 RVA: 0x0015E604 File Offset: 0x0015C804
			private void VisitExpressionBindingExitScope()
			{
				this.variableScopes.Pop();
			}

			// Token: 0x06006620 RID: 26144 RVA: 0x0015E614 File Offset: 0x0015C814
			private void ValidateEntitySetsMappedForAssociationSetMapping(ViewValidator.DbExpressionStructuralTypeEntitySetInfo setInfos)
			{
				AssociationSet associationSet = this._setMapping.Set as AssociationSet;
				int num = 0;
				if (setInfos.SetInfos.All((KeyValuePair<string, ViewValidator.DbExpressionEntitySetInfo> it) => it.Value != null && it.Value is ViewValidator.DbExpressionSimpleTypeEntitySetInfo) && setInfos.SetInfos.Count<KeyValuePair<string, ViewValidator.DbExpressionEntitySetInfo>>() == 2)
				{
					foreach (ViewValidator.DbExpressionEntitySetInfo dbExpressionEntitySetInfo in setInfos.SetInfos.Select((KeyValuePair<string, ViewValidator.DbExpressionEntitySetInfo> it) => it.Value))
					{
						ViewValidator.DbExpressionSimpleTypeEntitySetInfo dbExpressionSimpleTypeEntitySetInfo = (ViewValidator.DbExpressionSimpleTypeEntitySetInfo)dbExpressionEntitySetInfo;
						AssociationSetEnd associationSetEnd = associationSet.AssociationSetEnds[num];
						EntitySet entitySet = associationSetEnd.EntitySet;
						if (!entitySet.Equals(dbExpressionSimpleTypeEntitySetInfo.EntitySet))
						{
							this._errors.Add(new EdmSchemaError(Strings.Mapping_EntitySetMismatchOnAssociationSetEnd_QueryView(dbExpressionSimpleTypeEntitySetInfo.EntitySet.Name, entitySet.Name, associationSetEnd.Name, this._setMapping.Set.Name), 2074, EdmSchemaErrorSeverity.Error, this._setMapping.EntityContainerMapping.SourceLocation, this._setMapping.StartLineNumber, this._setMapping.StartLinePosition));
						}
						num++;
					}
				}
			}

			// Token: 0x06006621 RID: 26145 RVA: 0x0015E770 File Offset: 0x0015C970
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbExpression expression)
			{
				Check.NotNull<DbExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006622 RID: 26146 RVA: 0x0015E780 File Offset: 0x0015C980
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbVariableReferenceExpression expression)
			{
				Check.NotNull<DbVariableReferenceExpression>(expression, "expression");
				return (from it in this.variableScopes
					where it.Key == expression.VariableName
					select it.Value).FirstOrDefault<ViewValidator.DbExpressionEntitySetInfo>();
			}

			// Token: 0x06006623 RID: 26147 RVA: 0x0015E7EC File Offset: 0x0015C9EC
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbPropertyExpression expression)
			{
				Check.NotNull<DbPropertyExpression>(expression, "expression");
				ViewValidator.DbExpressionStructuralTypeEntitySetInfo dbExpressionStructuralTypeEntitySetInfo = this.VisitExpression(expression.Instance) as ViewValidator.DbExpressionStructuralTypeEntitySetInfo;
				if (dbExpressionStructuralTypeEntitySetInfo != null)
				{
					return dbExpressionStructuralTypeEntitySetInfo.GetEntitySetInfoForMember(expression.Property.Name);
				}
				return null;
			}

			// Token: 0x06006624 RID: 26148 RVA: 0x0015E82D File Offset: 0x0015CA2D
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbProjectExpression expression)
			{
				Check.NotNull<DbProjectExpression>(expression, "expression");
				this.VisitExpressionBindingEnterScope(expression.Input);
				ViewValidator.DbExpressionEntitySetInfo dbExpressionEntitySetInfo = this.VisitExpression(expression.Projection);
				this.VisitExpressionBindingExitScope();
				return dbExpressionEntitySetInfo;
			}

			// Token: 0x06006625 RID: 26149 RVA: 0x0015E85C File Offset: 0x0015CA5C
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbNewInstanceExpression expression)
			{
				Check.NotNull<DbNewInstanceExpression>(expression, "expression");
				ViewValidator.DbExpressionMemberCollectionEntitySetInfo dbExpressionMemberCollectionEntitySetInfo = this.VisitExpressionList(expression.Arguments);
				StructuralType structuralType = expression.ResultType.EdmType as StructuralType;
				if (dbExpressionMemberCollectionEntitySetInfo != null && structuralType != null)
				{
					ViewValidator.DbExpressionStructuralTypeEntitySetInfo dbExpressionStructuralTypeEntitySetInfo = new ViewValidator.DbExpressionStructuralTypeEntitySetInfo();
					int num = 0;
					foreach (ViewValidator.DbExpressionEntitySetInfo dbExpressionEntitySetInfo in dbExpressionMemberCollectionEntitySetInfo.entitySetInfos)
					{
						dbExpressionStructuralTypeEntitySetInfo.Add(structuralType.Members[num].Name, dbExpressionEntitySetInfo);
						num++;
					}
					if (expression.ResultType.EdmType.BuiltInTypeKind == BuiltInTypeKind.AssociationType)
					{
						this.ValidateEntitySetsMappedForAssociationSetMapping(dbExpressionStructuralTypeEntitySetInfo);
					}
					return dbExpressionStructuralTypeEntitySetInfo;
				}
				return null;
			}

			// Token: 0x06006626 RID: 26150 RVA: 0x0015E91C File Offset: 0x0015CB1C
			private ViewValidator.DbExpressionMemberCollectionEntitySetInfo VisitExpressionList(IList<DbExpression> list)
			{
				return new ViewValidator.DbExpressionMemberCollectionEntitySetInfo(list.Select((DbExpression it) => this.VisitExpression(it)));
			}

			// Token: 0x06006627 RID: 26151 RVA: 0x0015E935 File Offset: 0x0015CB35
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbRefExpression expression)
			{
				Check.NotNull<DbRefExpression>(expression, "expression");
				return new ViewValidator.DbExpressionSimpleTypeEntitySetInfo(expression.EntitySet);
			}

			// Token: 0x06006628 RID: 26152 RVA: 0x0015E94E File Offset: 0x0015CB4E
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbComparisonExpression expression)
			{
				Check.NotNull<DbComparisonExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006629 RID: 26153 RVA: 0x0015E95D File Offset: 0x0015CB5D
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbLikeExpression expression)
			{
				Check.NotNull<DbLikeExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600662A RID: 26154 RVA: 0x0015E96C File Offset: 0x0015CB6C
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbLimitExpression expression)
			{
				Check.NotNull<DbLimitExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600662B RID: 26155 RVA: 0x0015E97B File Offset: 0x0015CB7B
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbIsNullExpression expression)
			{
				Check.NotNull<DbIsNullExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600662C RID: 26156 RVA: 0x0015E98A File Offset: 0x0015CB8A
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbArithmeticExpression expression)
			{
				Check.NotNull<DbArithmeticExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600662D RID: 26157 RVA: 0x0015E999 File Offset: 0x0015CB99
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbAndExpression expression)
			{
				Check.NotNull<DbAndExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600662E RID: 26158 RVA: 0x0015E9A8 File Offset: 0x0015CBA8
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbOrExpression expression)
			{
				Check.NotNull<DbOrExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600662F RID: 26159 RVA: 0x0015E9B7 File Offset: 0x0015CBB7
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbInExpression expression)
			{
				Check.NotNull<DbInExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006630 RID: 26160 RVA: 0x0015E9C6 File Offset: 0x0015CBC6
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbNotExpression expression)
			{
				Check.NotNull<DbNotExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006631 RID: 26161 RVA: 0x0015E9D5 File Offset: 0x0015CBD5
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbDistinctExpression expression)
			{
				Check.NotNull<DbDistinctExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006632 RID: 26162 RVA: 0x0015E9E4 File Offset: 0x0015CBE4
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbElementExpression expression)
			{
				Check.NotNull<DbElementExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006633 RID: 26163 RVA: 0x0015E9F3 File Offset: 0x0015CBF3
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbIsEmptyExpression expression)
			{
				Check.NotNull<DbIsEmptyExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006634 RID: 26164 RVA: 0x0015EA02 File Offset: 0x0015CC02
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbUnionAllExpression expression)
			{
				Check.NotNull<DbUnionAllExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006635 RID: 26165 RVA: 0x0015EA11 File Offset: 0x0015CC11
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbIntersectExpression expression)
			{
				Check.NotNull<DbIntersectExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006636 RID: 26166 RVA: 0x0015EA20 File Offset: 0x0015CC20
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbExceptExpression expression)
			{
				Check.NotNull<DbExceptExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006637 RID: 26167 RVA: 0x0015EA2F File Offset: 0x0015CC2F
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbTreatExpression expression)
			{
				Check.NotNull<DbTreatExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006638 RID: 26168 RVA: 0x0015EA3E File Offset: 0x0015CC3E
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbIsOfExpression expression)
			{
				Check.NotNull<DbIsOfExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006639 RID: 26169 RVA: 0x0015EA4D File Offset: 0x0015CC4D
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbCastExpression expression)
			{
				Check.NotNull<DbCastExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600663A RID: 26170 RVA: 0x0015EA5C File Offset: 0x0015CC5C
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbCaseExpression expression)
			{
				Check.NotNull<DbCaseExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600663B RID: 26171 RVA: 0x0015EA6B File Offset: 0x0015CC6B
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbOfTypeExpression expression)
			{
				Check.NotNull<DbOfTypeExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600663C RID: 26172 RVA: 0x0015EA7A File Offset: 0x0015CC7A
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbRelationshipNavigationExpression expression)
			{
				Check.NotNull<DbRelationshipNavigationExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600663D RID: 26173 RVA: 0x0015EA89 File Offset: 0x0015CC89
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbDerefExpression expression)
			{
				Check.NotNull<DbDerefExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600663E RID: 26174 RVA: 0x0015EA98 File Offset: 0x0015CC98
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbRefKeyExpression expression)
			{
				Check.NotNull<DbRefKeyExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600663F RID: 26175 RVA: 0x0015EAA7 File Offset: 0x0015CCA7
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbEntityRefExpression expression)
			{
				Check.NotNull<DbEntityRefExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006640 RID: 26176 RVA: 0x0015EAB6 File Offset: 0x0015CCB6
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbScanExpression expression)
			{
				Check.NotNull<DbScanExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006641 RID: 26177 RVA: 0x0015EAC5 File Offset: 0x0015CCC5
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbFilterExpression expression)
			{
				Check.NotNull<DbFilterExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006642 RID: 26178 RVA: 0x0015EAD4 File Offset: 0x0015CCD4
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbConstantExpression expression)
			{
				Check.NotNull<DbConstantExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006643 RID: 26179 RVA: 0x0015EAE3 File Offset: 0x0015CCE3
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbNullExpression expression)
			{
				Check.NotNull<DbNullExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006644 RID: 26180 RVA: 0x0015EAF2 File Offset: 0x0015CCF2
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbCrossJoinExpression expression)
			{
				Check.NotNull<DbCrossJoinExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006645 RID: 26181 RVA: 0x0015EB01 File Offset: 0x0015CD01
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbJoinExpression expression)
			{
				Check.NotNull<DbJoinExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006646 RID: 26182 RVA: 0x0015EB10 File Offset: 0x0015CD10
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbParameterReferenceExpression expression)
			{
				Check.NotNull<DbParameterReferenceExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006647 RID: 26183 RVA: 0x0015EB1F File Offset: 0x0015CD1F
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbFunctionExpression expression)
			{
				Check.NotNull<DbFunctionExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006648 RID: 26184 RVA: 0x0015EB2E File Offset: 0x0015CD2E
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbLambdaExpression expression)
			{
				Check.NotNull<DbLambdaExpression>(expression, "expression");
				return null;
			}

			// Token: 0x06006649 RID: 26185 RVA: 0x0015EB3D File Offset: 0x0015CD3D
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbApplyExpression expression)
			{
				Check.NotNull<DbApplyExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600664A RID: 26186 RVA: 0x0015EB4C File Offset: 0x0015CD4C
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbGroupByExpression expression)
			{
				Check.NotNull<DbGroupByExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600664B RID: 26187 RVA: 0x0015EB5B File Offset: 0x0015CD5B
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbSkipExpression expression)
			{
				Check.NotNull<DbSkipExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600664C RID: 26188 RVA: 0x0015EB6A File Offset: 0x0015CD6A
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbSortExpression expression)
			{
				Check.NotNull<DbSortExpression>(expression, "expression");
				return null;
			}

			// Token: 0x0600664D RID: 26189 RVA: 0x0015EB79 File Offset: 0x0015CD79
			public override ViewValidator.DbExpressionEntitySetInfo Visit(DbQuantifierExpression expression)
			{
				Check.NotNull<DbQuantifierExpression>(expression, "expression");
				return null;
			}

			// Token: 0x04002DD0 RID: 11728
			private readonly Stack<KeyValuePair<string, ViewValidator.DbExpressionEntitySetInfo>> variableScopes = new Stack<KeyValuePair<string, ViewValidator.DbExpressionEntitySetInfo>>();

			// Token: 0x04002DD1 RID: 11729
			private readonly EntitySetBaseMapping _setMapping;

			// Token: 0x04002DD2 RID: 11730
			private readonly List<EdmSchemaError> _errors = new List<EdmSchemaError>();
		}

		// Token: 0x02000B6D RID: 2925
		internal abstract class DbExpressionEntitySetInfo
		{
		}

		// Token: 0x02000B6E RID: 2926
		private class DbExpressionSimpleTypeEntitySetInfo : ViewValidator.DbExpressionEntitySetInfo
		{
			// Token: 0x17001107 RID: 4359
			// (get) Token: 0x06006650 RID: 26192 RVA: 0x0015EB99 File Offset: 0x0015CD99
			internal EntitySet EntitySet
			{
				get
				{
					return this.m_entitySet;
				}
			}

			// Token: 0x06006651 RID: 26193 RVA: 0x0015EBA1 File Offset: 0x0015CDA1
			internal DbExpressionSimpleTypeEntitySetInfo(EntitySet entitySet)
			{
				this.m_entitySet = entitySet;
			}

			// Token: 0x04002DD3 RID: 11731
			private readonly EntitySet m_entitySet;
		}

		// Token: 0x02000B6F RID: 2927
		private class DbExpressionStructuralTypeEntitySetInfo : ViewValidator.DbExpressionEntitySetInfo
		{
			// Token: 0x06006652 RID: 26194 RVA: 0x0015EBB0 File Offset: 0x0015CDB0
			internal DbExpressionStructuralTypeEntitySetInfo()
			{
				this.m_entitySetInfos = new Dictionary<string, ViewValidator.DbExpressionEntitySetInfo>();
			}

			// Token: 0x06006653 RID: 26195 RVA: 0x0015EBC3 File Offset: 0x0015CDC3
			internal void Add(string key, ViewValidator.DbExpressionEntitySetInfo value)
			{
				this.m_entitySetInfos.Add(key, value);
			}

			// Token: 0x17001108 RID: 4360
			// (get) Token: 0x06006654 RID: 26196 RVA: 0x0015EBD2 File Offset: 0x0015CDD2
			internal IEnumerable<KeyValuePair<string, ViewValidator.DbExpressionEntitySetInfo>> SetInfos
			{
				get
				{
					return this.m_entitySetInfos;
				}
			}

			// Token: 0x06006655 RID: 26197 RVA: 0x0015EBDA File Offset: 0x0015CDDA
			internal ViewValidator.DbExpressionEntitySetInfo GetEntitySetInfoForMember(string memberName)
			{
				return this.m_entitySetInfos[memberName];
			}

			// Token: 0x04002DD4 RID: 11732
			private readonly Dictionary<string, ViewValidator.DbExpressionEntitySetInfo> m_entitySetInfos;
		}

		// Token: 0x02000B70 RID: 2928
		private class DbExpressionMemberCollectionEntitySetInfo : ViewValidator.DbExpressionEntitySetInfo
		{
			// Token: 0x06006656 RID: 26198 RVA: 0x0015EBE8 File Offset: 0x0015CDE8
			internal DbExpressionMemberCollectionEntitySetInfo(IEnumerable<ViewValidator.DbExpressionEntitySetInfo> entitySetInfos)
			{
				this.m_entitySets = entitySetInfos;
			}

			// Token: 0x17001109 RID: 4361
			// (get) Token: 0x06006657 RID: 26199 RVA: 0x0015EBF7 File Offset: 0x0015CDF7
			internal IEnumerable<ViewValidator.DbExpressionEntitySetInfo> entitySetInfos
			{
				get
				{
					return this.m_entitySets;
				}
			}

			// Token: 0x04002DD5 RID: 11733
			private readonly IEnumerable<ViewValidator.DbExpressionEntitySetInfo> m_entitySets;
		}
	}
}
