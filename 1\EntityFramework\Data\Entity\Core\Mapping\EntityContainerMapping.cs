﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Data.Entity.Core.Mapping.ViewGeneration.Validation;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x02000529 RID: 1321
	public class EntityContainerMapping : MappingBase
	{
		// Token: 0x06004133 RID: 16691 RVA: 0x000DBFB4 File Offset: 0x000DA1B4
		public EntityContainerMapping(EntityContainer conceptualEntityContainer, EntityContainer storeEntityContainer, StorageMappingItemCollection mappingItemCollection, bool generateUpdateViews)
			: this(conceptualEntityContainer, storeEntityContainer, mappingItemCollection, true, generateUpdateViews)
		{
		}

		// Token: 0x06004134 RID: 16692 RVA: 0x000DBFC4 File Offset: 0x000DA1C4
		internal EntityContainerMapping(EntityContainer entityContainer, EntityContainer storageEntityContainer, StorageMappingItemCollection storageMappingItemCollection, bool validate, bool generateUpdateViews)
		{
			this.m_entitySetMappings = new Dictionary<string, EntitySetBaseMapping>(StringComparer.Ordinal);
			this.m_associationSetMappings = new Dictionary<string, EntitySetBaseMapping>(StringComparer.Ordinal);
			this.m_functionImportMappings = new Dictionary<EdmFunction, FunctionImportMapping>();
			base..ctor(MetadataItem.MetadataFlags.CSSpace);
			Check.NotNull<EntityContainer>(entityContainer, "entityContainer");
			this.m_entityContainer = entityContainer;
			this.m_storageEntityContainer = storageEntityContainer;
			this.m_storageMappingItemCollection = storageMappingItemCollection;
			this.m_memoizedCellGroupEvaluator = new Memoizer<InputForComputingCellGroups, OutputFromComputeCellGroups>(new Func<InputForComputingCellGroups, OutputFromComputeCellGroups>(this.ComputeCellGroups), default(InputForComputingCellGroups));
			this.identity = entityContainer.Identity;
			this.m_validate = validate;
			this.m_generateUpdateViews = generateUpdateViews;
		}

		// Token: 0x06004135 RID: 16693 RVA: 0x000DC065 File Offset: 0x000DA265
		internal EntityContainerMapping(EntityContainer entityContainer)
			: this(entityContainer, null, null, false, false)
		{
		}

		// Token: 0x06004136 RID: 16694 RVA: 0x000DC072 File Offset: 0x000DA272
		internal EntityContainerMapping()
		{
			this.m_entitySetMappings = new Dictionary<string, EntitySetBaseMapping>(StringComparer.Ordinal);
			this.m_associationSetMappings = new Dictionary<string, EntitySetBaseMapping>(StringComparer.Ordinal);
			this.m_functionImportMappings = new Dictionary<EdmFunction, FunctionImportMapping>();
			base..ctor();
		}

		// Token: 0x17000CC3 RID: 3267
		// (get) Token: 0x06004137 RID: 16695 RVA: 0x000DC0A5 File Offset: 0x000DA2A5
		public StorageMappingItemCollection MappingItemCollection
		{
			get
			{
				return this.m_storageMappingItemCollection;
			}
		}

		// Token: 0x17000CC4 RID: 3268
		// (get) Token: 0x06004138 RID: 16696 RVA: 0x000DC0AD File Offset: 0x000DA2AD
		internal StorageMappingItemCollection StorageMappingItemCollection
		{
			get
			{
				return this.MappingItemCollection;
			}
		}

		// Token: 0x17000CC5 RID: 3269
		// (get) Token: 0x06004139 RID: 16697 RVA: 0x000DC0B5 File Offset: 0x000DA2B5
		public override BuiltInTypeKind BuiltInTypeKind
		{
			get
			{
				return BuiltInTypeKind.MetadataItem;
			}
		}

		// Token: 0x17000CC6 RID: 3270
		// (get) Token: 0x0600413A RID: 16698 RVA: 0x000DC0B9 File Offset: 0x000DA2B9
		internal override MetadataItem EdmItem
		{
			get
			{
				return this.m_entityContainer;
			}
		}

		// Token: 0x17000CC7 RID: 3271
		// (get) Token: 0x0600413B RID: 16699 RVA: 0x000DC0C1 File Offset: 0x000DA2C1
		internal override string Identity
		{
			get
			{
				return this.identity;
			}
		}

		// Token: 0x17000CC8 RID: 3272
		// (get) Token: 0x0600413C RID: 16700 RVA: 0x000DC0C9 File Offset: 0x000DA2C9
		internal bool IsEmpty
		{
			get
			{
				return this.m_entitySetMappings.Count == 0 && this.m_associationSetMappings.Count == 0;
			}
		}

		// Token: 0x17000CC9 RID: 3273
		// (get) Token: 0x0600413D RID: 16701 RVA: 0x000DC0E8 File Offset: 0x000DA2E8
		internal bool HasViews
		{
			get
			{
				if (!this.HasMappingFragments())
				{
					return this.AllSetMaps.Any((EntitySetBaseMapping setMap) => setMap.QueryView != null);
				}
				return true;
			}
		}

		// Token: 0x17000CCA RID: 3274
		// (get) Token: 0x0600413E RID: 16702 RVA: 0x000DC11E File Offset: 0x000DA31E
		// (set) Token: 0x0600413F RID: 16703 RVA: 0x000DC126 File Offset: 0x000DA326
		internal string SourceLocation { get; set; }

		// Token: 0x17000CCB RID: 3275
		// (get) Token: 0x06004140 RID: 16704 RVA: 0x000DC12F File Offset: 0x000DA32F
		public EntityContainer ConceptualEntityContainer
		{
			get
			{
				return this.m_entityContainer;
			}
		}

		// Token: 0x17000CCC RID: 3276
		// (get) Token: 0x06004141 RID: 16705 RVA: 0x000DC137 File Offset: 0x000DA337
		internal EntityContainer EdmEntityContainer
		{
			get
			{
				return this.ConceptualEntityContainer;
			}
		}

		// Token: 0x17000CCD RID: 3277
		// (get) Token: 0x06004142 RID: 16706 RVA: 0x000DC13F File Offset: 0x000DA33F
		public EntityContainer StoreEntityContainer
		{
			get
			{
				return this.m_storageEntityContainer;
			}
		}

		// Token: 0x17000CCE RID: 3278
		// (get) Token: 0x06004143 RID: 16707 RVA: 0x000DC147 File Offset: 0x000DA347
		internal EntityContainer StorageEntityContainer
		{
			get
			{
				return this.StoreEntityContainer;
			}
		}

		// Token: 0x17000CCF RID: 3279
		// (get) Token: 0x06004144 RID: 16708 RVA: 0x000DC14F File Offset: 0x000DA34F
		internal ReadOnlyCollection<EntitySetBaseMapping> EntitySetMaps
		{
			get
			{
				return new ReadOnlyCollection<EntitySetBaseMapping>(new List<EntitySetBaseMapping>(this.m_entitySetMappings.Values));
			}
		}

		// Token: 0x17000CD0 RID: 3280
		// (get) Token: 0x06004145 RID: 16709 RVA: 0x000DC166 File Offset: 0x000DA366
		public virtual IEnumerable<EntitySetMapping> EntitySetMappings
		{
			get
			{
				return this.EntitySetMaps.OfType<EntitySetMapping>();
			}
		}

		// Token: 0x17000CD1 RID: 3281
		// (get) Token: 0x06004146 RID: 16710 RVA: 0x000DC173 File Offset: 0x000DA373
		public virtual IEnumerable<AssociationSetMapping> AssociationSetMappings
		{
			get
			{
				return this.RelationshipSetMaps.OfType<AssociationSetMapping>();
			}
		}

		// Token: 0x17000CD2 RID: 3282
		// (get) Token: 0x06004147 RID: 16711 RVA: 0x000DC180 File Offset: 0x000DA380
		public IEnumerable<FunctionImportMapping> FunctionImportMappings
		{
			get
			{
				return this.m_functionImportMappings.Values;
			}
		}

		// Token: 0x17000CD3 RID: 3283
		// (get) Token: 0x06004148 RID: 16712 RVA: 0x000DC18D File Offset: 0x000DA38D
		internal ReadOnlyCollection<EntitySetBaseMapping> RelationshipSetMaps
		{
			get
			{
				return new ReadOnlyCollection<EntitySetBaseMapping>(new List<EntitySetBaseMapping>(this.m_associationSetMappings.Values));
			}
		}

		// Token: 0x17000CD4 RID: 3284
		// (get) Token: 0x06004149 RID: 16713 RVA: 0x000DC1A4 File Offset: 0x000DA3A4
		internal IEnumerable<EntitySetBaseMapping> AllSetMaps
		{
			get
			{
				return this.m_entitySetMappings.Values.Concat(this.m_associationSetMappings.Values);
			}
		}

		// Token: 0x17000CD5 RID: 3285
		// (get) Token: 0x0600414A RID: 16714 RVA: 0x000DC1C1 File Offset: 0x000DA3C1
		// (set) Token: 0x0600414B RID: 16715 RVA: 0x000DC1C9 File Offset: 0x000DA3C9
		internal int StartLineNumber { get; set; }

		// Token: 0x17000CD6 RID: 3286
		// (get) Token: 0x0600414C RID: 16716 RVA: 0x000DC1D2 File Offset: 0x000DA3D2
		// (set) Token: 0x0600414D RID: 16717 RVA: 0x000DC1DA File Offset: 0x000DA3DA
		internal int StartLinePosition { get; set; }

		// Token: 0x17000CD7 RID: 3287
		// (get) Token: 0x0600414E RID: 16718 RVA: 0x000DC1E3 File Offset: 0x000DA3E3
		internal bool Validate
		{
			get
			{
				return this.m_validate;
			}
		}

		// Token: 0x17000CD8 RID: 3288
		// (get) Token: 0x0600414F RID: 16719 RVA: 0x000DC1EB File Offset: 0x000DA3EB
		public bool GenerateUpdateViews
		{
			get
			{
				return this.m_generateUpdateViews;
			}
		}

		// Token: 0x06004150 RID: 16720 RVA: 0x000DC1F4 File Offset: 0x000DA3F4
		internal EntitySetBaseMapping GetEntitySetMapping(string setName)
		{
			EntitySetBaseMapping entitySetBaseMapping = null;
			this.m_entitySetMappings.TryGetValue(setName, out entitySetBaseMapping);
			return entitySetBaseMapping;
		}

		// Token: 0x06004151 RID: 16721 RVA: 0x000DC214 File Offset: 0x000DA414
		internal EntitySetBaseMapping GetAssociationSetMapping(string setName)
		{
			EntitySetBaseMapping entitySetBaseMapping = null;
			this.m_associationSetMappings.TryGetValue(setName, out entitySetBaseMapping);
			return entitySetBaseMapping;
		}

		// Token: 0x06004152 RID: 16722 RVA: 0x000DC234 File Offset: 0x000DA434
		internal IEnumerable<AssociationSetMapping> GetRelationshipSetMappingsFor(EntitySetBase edmEntitySet, EntitySetBase storeEntitySet)
		{
			Func<AssociationSetEnd, bool> <>9__2;
			return (from AssociationSetMapping w in this.m_associationSetMappings.Values
				where w.StoreEntitySet != null && w.StoreEntitySet == storeEntitySet
				select w).Where(delegate(AssociationSetMapping associationSetMap)
			{
				IEnumerable<AssociationSetEnd> associationSetEnds = (associationSetMap.Set as AssociationSet).AssociationSetEnds;
				Func<AssociationSetEnd, bool> func;
				if ((func = <>9__2) == null)
				{
					func = (<>9__2 = (AssociationSetEnd associationSetEnd) => associationSetEnd.EntitySet == edmEntitySet);
				}
				return associationSetEnds.Any(func);
			});
		}

		// Token: 0x06004153 RID: 16723 RVA: 0x000DC288 File Offset: 0x000DA488
		internal EntitySetBaseMapping GetSetMapping(string setName)
		{
			EntitySetBaseMapping entitySetBaseMapping = this.GetEntitySetMapping(setName);
			if (entitySetBaseMapping == null)
			{
				entitySetBaseMapping = this.GetAssociationSetMapping(setName);
			}
			return entitySetBaseMapping;
		}

		// Token: 0x06004154 RID: 16724 RVA: 0x000DC2AC File Offset: 0x000DA4AC
		public void AddSetMapping(EntitySetMapping setMapping)
		{
			Check.NotNull<EntitySetMapping>(setMapping, "setMapping");
			Util.ThrowIfReadOnly(this);
			if (!this.m_entitySetMappings.ContainsKey(setMapping.Set.Name))
			{
				this.m_entitySetMappings.Add(setMapping.Set.Name, setMapping);
			}
		}

		// Token: 0x06004155 RID: 16725 RVA: 0x000DC2FA File Offset: 0x000DA4FA
		public void RemoveSetMapping(EntitySetMapping setMapping)
		{
			Check.NotNull<EntitySetMapping>(setMapping, "setMapping");
			Util.ThrowIfReadOnly(this);
			this.m_entitySetMappings.Remove(setMapping.Set.Name);
		}

		// Token: 0x06004156 RID: 16726 RVA: 0x000DC328 File Offset: 0x000DA528
		public void AddSetMapping(AssociationSetMapping setMapping)
		{
			Check.NotNull<AssociationSetMapping>(setMapping, "setMapping");
			Util.ThrowIfReadOnly(this);
			if (!this.m_associationSetMappings.ContainsKey(setMapping.Set.Name))
			{
				this.m_associationSetMappings.Add(setMapping.Set.Name, setMapping);
			}
		}

		// Token: 0x06004157 RID: 16727 RVA: 0x000DC376 File Offset: 0x000DA576
		public void RemoveSetMapping(AssociationSetMapping setMapping)
		{
			Check.NotNull<AssociationSetMapping>(setMapping, "setMapping");
			Util.ThrowIfReadOnly(this);
			this.m_associationSetMappings.Remove(setMapping.Set.Name);
		}

		// Token: 0x06004158 RID: 16728 RVA: 0x000DC3A1 File Offset: 0x000DA5A1
		internal bool ContainsAssociationSetMapping(AssociationSet associationSet)
		{
			return this.m_associationSetMappings.ContainsKey(associationSet.Name);
		}

		// Token: 0x06004159 RID: 16729 RVA: 0x000DC3B4 File Offset: 0x000DA5B4
		public void AddFunctionImportMapping(FunctionImportMapping functionImportMapping)
		{
			Check.NotNull<FunctionImportMapping>(functionImportMapping, "functionImportMapping");
			Util.ThrowIfReadOnly(this);
			this.m_functionImportMappings.Add(functionImportMapping.FunctionImport, functionImportMapping);
		}

		// Token: 0x0600415A RID: 16730 RVA: 0x000DC3DA File Offset: 0x000DA5DA
		public void RemoveFunctionImportMapping(FunctionImportMapping functionImportMapping)
		{
			Check.NotNull<FunctionImportMapping>(functionImportMapping, "functionImportMapping");
			Util.ThrowIfReadOnly(this);
			this.m_functionImportMappings.Remove(functionImportMapping.FunctionImport);
		}

		// Token: 0x0600415B RID: 16731 RVA: 0x000DC400 File Offset: 0x000DA600
		internal override void SetReadOnly()
		{
			MappingItem.SetReadOnly(this.m_entitySetMappings.Values);
			MappingItem.SetReadOnly(this.m_associationSetMappings.Values);
			MappingItem.SetReadOnly(this.m_functionImportMappings.Values);
			base.SetReadOnly();
		}

		// Token: 0x0600415C RID: 16732 RVA: 0x000DC438 File Offset: 0x000DA638
		internal bool HasQueryViewForSetMap(string setName)
		{
			EntitySetBaseMapping setMapping = this.GetSetMapping(setName);
			return setMapping != null && setMapping.QueryView != null;
		}

		// Token: 0x0600415D RID: 16733 RVA: 0x000DC45C File Offset: 0x000DA65C
		internal bool HasMappingFragments()
		{
			foreach (EntitySetBaseMapping entitySetBaseMapping in this.AllSetMaps)
			{
				using (IEnumerator<TypeMapping> enumerator2 = entitySetBaseMapping.TypeMappings.GetEnumerator())
				{
					while (enumerator2.MoveNext())
					{
						if (enumerator2.Current.MappingFragments.Count > 0)
						{
							return true;
						}
					}
				}
			}
			return false;
		}

		// Token: 0x0600415E RID: 16734 RVA: 0x000DC4E8 File Offset: 0x000DA6E8
		internal virtual bool TryGetFunctionImportMapping(EdmFunction functionImport, out FunctionImportMapping mapping)
		{
			return this.m_functionImportMappings.TryGetValue(functionImport, out mapping);
		}

		// Token: 0x0600415F RID: 16735 RVA: 0x000DC4F7 File Offset: 0x000DA6F7
		internal OutputFromComputeCellGroups GetCellgroups(InputForComputingCellGroups args)
		{
			return this.m_memoizedCellGroupEvaluator.Evaluate(args);
		}

		// Token: 0x06004160 RID: 16736 RVA: 0x000DC508 File Offset: 0x000DA708
		private OutputFromComputeCellGroups ComputeCellGroups(InputForComputingCellGroups args)
		{
			OutputFromComputeCellGroups outputFromComputeCellGroups = default(OutputFromComputeCellGroups);
			outputFromComputeCellGroups.Success = true;
			CellCreator cellCreator = new CellCreator(args.ContainerMapping);
			outputFromComputeCellGroups.Cells = cellCreator.GenerateCells();
			outputFromComputeCellGroups.Identifiers = cellCreator.Identifiers;
			if (outputFromComputeCellGroups.Cells.Count <= 0)
			{
				outputFromComputeCellGroups.Success = false;
				return outputFromComputeCellGroups;
			}
			outputFromComputeCellGroups.ForeignKeyConstraints = ForeignConstraint.GetForeignConstraints(args.ContainerMapping.StorageEntityContainer);
			List<Set<Cell>> list = new CellPartitioner(outputFromComputeCellGroups.Cells, outputFromComputeCellGroups.ForeignKeyConstraints).GroupRelatedCells();
			outputFromComputeCellGroups.CellGroups = list.Select((Set<Cell> setOfCells) => new Set<Cell>(setOfCells.Select((Cell cell) => new Cell(cell)))).ToList<Set<Cell>>();
			return outputFromComputeCellGroups;
		}

		// Token: 0x04001699 RID: 5785
		private readonly string identity;

		// Token: 0x0400169A RID: 5786
		private readonly bool m_validate;

		// Token: 0x0400169B RID: 5787
		private readonly bool m_generateUpdateViews;

		// Token: 0x0400169C RID: 5788
		private readonly EntityContainer m_entityContainer;

		// Token: 0x0400169D RID: 5789
		private readonly EntityContainer m_storageEntityContainer;

		// Token: 0x0400169E RID: 5790
		private readonly Dictionary<string, EntitySetBaseMapping> m_entitySetMappings;

		// Token: 0x0400169F RID: 5791
		private readonly Dictionary<string, EntitySetBaseMapping> m_associationSetMappings;

		// Token: 0x040016A0 RID: 5792
		private readonly Dictionary<EdmFunction, FunctionImportMapping> m_functionImportMappings;

		// Token: 0x040016A1 RID: 5793
		private readonly StorageMappingItemCollection m_storageMappingItemCollection;

		// Token: 0x040016A2 RID: 5794
		private readonly Memoizer<InputForComputingCellGroups, OutputFromComputeCellGroups> m_memoizedCellGroupEvaluator;
	}
}
