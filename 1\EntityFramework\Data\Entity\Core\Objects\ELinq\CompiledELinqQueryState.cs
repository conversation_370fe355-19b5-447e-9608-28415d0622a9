﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.QueryCache;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects.Internal;
using System.Linq;
using System.Linq.Expressions;

namespace System.Data.Entity.Core.Objects.ELinq
{
	// Token: 0x0200045E RID: 1118
	internal sealed class CompiledELinqQueryState : ELinqQueryState
	{
		// Token: 0x060036ED RID: 14061 RVA: 0x000B060C File Offset: 0x000AE80C
		internal CompiledELinqQueryState(Type elementType, ObjectContext context, LambdaExpression lambda, Guid cacheToken, object[] parameterValues, ObjectQueryExecutionPlanFactory objectQueryExecutionPlanFactory = null)
			: base(elementType, context, lambda, null)
		{
			this._cacheToken = cacheToken;
			this._parameterValues = parameterValues;
			base.EnsureParameters();
			base.Parameters.SetReadOnly(true);
			this._objectQueryExecutionPlanFactory = objectQueryExecutionPlanFactory ?? new ObjectQueryExecutionPlanFactory(null);
		}

		// Token: 0x060036EE RID: 14062 RVA: 0x000B0658 File Offset: 0x000AE858
		internal override ObjectQueryExecutionPlan GetExecutionPlan(MergeOption? forMergeOption)
		{
			ObjectQueryExecutionPlan objectQueryExecutionPlan = null;
			CompiledQueryCacheEntry compiledQueryCacheEntry = this._cacheEntry;
			bool useCSharpNullComparisonBehavior = base.ObjectContext.ContextOptions.UseCSharpNullComparisonBehavior;
			bool disableFilterOverProjectionSimplificationForCustomFunctions = base.ObjectContext.ContextOptions.DisableFilterOverProjectionSimplificationForCustomFunctions;
			if (compiledQueryCacheEntry != null)
			{
				MergeOption mergeOption = ObjectQueryState.EnsureMergeOption(new MergeOption?[] { forMergeOption, base.UserSpecifiedMergeOption, compiledQueryCacheEntry.PropagatedMergeOption });
				objectQueryExecutionPlan = compiledQueryCacheEntry.GetExecutionPlan(mergeOption, useCSharpNullComparisonBehavior);
				if (objectQueryExecutionPlan == null)
				{
					ExpressionConverter expressionConverter = this.CreateExpressionConverter();
					DbExpression dbExpression = expressionConverter.Convert();
					IEnumerable<Tuple<ObjectParameter, QueryParameterExpression>> parameters = expressionConverter.GetParameters();
					DbQueryCommandTree dbQueryCommandTree = DbQueryCommandTree.FromValidExpression(base.ObjectContext.MetadataWorkspace, DataSpace.CSpace, dbExpression, !useCSharpNullComparisonBehavior, disableFilterOverProjectionSimplificationForCustomFunctions);
					objectQueryExecutionPlan = this._objectQueryExecutionPlanFactory.Prepare(base.ObjectContext, dbQueryCommandTree, base.ElementType, mergeOption, base.EffectiveStreamingBehavior, expressionConverter.PropagatedSpan, parameters, expressionConverter.AliasGenerator);
					objectQueryExecutionPlan = compiledQueryCacheEntry.SetExecutionPlan(objectQueryExecutionPlan, useCSharpNullComparisonBehavior);
				}
			}
			else
			{
				QueryCacheManager queryCacheManager = base.ObjectContext.MetadataWorkspace.GetQueryCacheManager();
				CompiledQueryCacheKey compiledQueryCacheKey = new CompiledQueryCacheKey(this._cacheToken);
				if (queryCacheManager.TryCacheLookup<CompiledQueryCacheKey, CompiledQueryCacheEntry>(compiledQueryCacheKey, out compiledQueryCacheEntry))
				{
					this._cacheEntry = compiledQueryCacheEntry;
					MergeOption mergeOption2 = ObjectQueryState.EnsureMergeOption(new MergeOption?[] { forMergeOption, base.UserSpecifiedMergeOption, compiledQueryCacheEntry.PropagatedMergeOption });
					objectQueryExecutionPlan = compiledQueryCacheEntry.GetExecutionPlan(mergeOption2, useCSharpNullComparisonBehavior);
				}
				if (objectQueryExecutionPlan == null)
				{
					ExpressionConverter expressionConverter2 = this.CreateExpressionConverter();
					DbExpression dbExpression2 = expressionConverter2.Convert();
					IEnumerable<Tuple<ObjectParameter, QueryParameterExpression>> parameters2 = expressionConverter2.GetParameters();
					DbQueryCommandTree dbQueryCommandTree2 = DbQueryCommandTree.FromValidExpression(base.ObjectContext.MetadataWorkspace, DataSpace.CSpace, dbExpression2, !useCSharpNullComparisonBehavior, disableFilterOverProjectionSimplificationForCustomFunctions);
					if (compiledQueryCacheEntry == null)
					{
						compiledQueryCacheEntry = new CompiledQueryCacheEntry(compiledQueryCacheKey, expressionConverter2.PropagatedMergeOption);
						QueryCacheEntry queryCacheEntry;
						if (queryCacheManager.TryLookupAndAdd(compiledQueryCacheEntry, out queryCacheEntry))
						{
							compiledQueryCacheEntry = (CompiledQueryCacheEntry)queryCacheEntry;
						}
						this._cacheEntry = compiledQueryCacheEntry;
					}
					MergeOption mergeOption3 = ObjectQueryState.EnsureMergeOption(new MergeOption?[] { forMergeOption, base.UserSpecifiedMergeOption, compiledQueryCacheEntry.PropagatedMergeOption });
					objectQueryExecutionPlan = compiledQueryCacheEntry.GetExecutionPlan(mergeOption3, useCSharpNullComparisonBehavior);
					if (objectQueryExecutionPlan == null)
					{
						objectQueryExecutionPlan = this._objectQueryExecutionPlanFactory.Prepare(base.ObjectContext, dbQueryCommandTree2, base.ElementType, mergeOption3, base.EffectiveStreamingBehavior, expressionConverter2.PropagatedSpan, parameters2, expressionConverter2.AliasGenerator);
						objectQueryExecutionPlan = compiledQueryCacheEntry.SetExecutionPlan(objectQueryExecutionPlan, useCSharpNullComparisonBehavior);
					}
				}
			}
			ObjectParameterCollection objectParameterCollection = base.EnsureParameters();
			if (objectQueryExecutionPlan.CompiledQueryParameters != null && objectQueryExecutionPlan.CompiledQueryParameters.Any<Tuple<ObjectParameter, QueryParameterExpression>>())
			{
				objectParameterCollection.SetReadOnly(false);
				objectParameterCollection.Clear();
				foreach (Tuple<ObjectParameter, QueryParameterExpression> tuple in objectQueryExecutionPlan.CompiledQueryParameters)
				{
					ObjectParameter objectParameter = tuple.Item1.ShallowCopy();
					QueryParameterExpression item = tuple.Item2;
					objectParameterCollection.Add(objectParameter);
					if (item != null)
					{
						objectParameter.Value = item.EvaluateParameter(this._parameterValues);
					}
				}
			}
			objectParameterCollection.SetReadOnly(true);
			return objectQueryExecutionPlan;
		}

		// Token: 0x060036EF RID: 14063 RVA: 0x000B093C File Offset: 0x000AEB3C
		protected override TypeUsage GetResultType()
		{
			CompiledQueryCacheEntry cacheEntry = this._cacheEntry;
			TypeUsage typeUsage;
			if (cacheEntry != null && cacheEntry.TryGetResultType(out typeUsage))
			{
				return typeUsage;
			}
			return base.GetResultType();
		}

		// Token: 0x17000A96 RID: 2710
		// (get) Token: 0x060036F0 RID: 14064 RVA: 0x000B0965 File Offset: 0x000AEB65
		internal override Expression Expression
		{
			get
			{
				return CompiledELinqQueryState.CreateDonateableExpressionVisitor.Replace((LambdaExpression)base.Expression, base.ObjectContext, this._parameterValues);
			}
		}

		// Token: 0x060036F1 RID: 14065 RVA: 0x000B0984 File Offset: 0x000AEB84
		protected override ExpressionConverter CreateExpressionConverter()
		{
			LambdaExpression lambdaExpression = (LambdaExpression)base.Expression;
			return new ExpressionConverter(Funcletizer.CreateCompiledQueryEvaluationFuncletizer(base.ObjectContext, lambdaExpression.Parameters.First<ParameterExpression>(), new ReadOnlyCollection<ParameterExpression>(lambdaExpression.Parameters.Skip(1).ToList<ParameterExpression>())), lambdaExpression.Body);
		}

		// Token: 0x040011D0 RID: 4560
		private readonly Guid _cacheToken;

		// Token: 0x040011D1 RID: 4561
		private readonly object[] _parameterValues;

		// Token: 0x040011D2 RID: 4562
		private CompiledQueryCacheEntry _cacheEntry;

		// Token: 0x040011D3 RID: 4563
		private readonly ObjectQueryExecutionPlanFactory _objectQueryExecutionPlanFactory;

		// Token: 0x02000A71 RID: 2673
		private sealed class CreateDonateableExpressionVisitor : EntityExpressionVisitor
		{
			// Token: 0x06006204 RID: 25092 RVA: 0x00153094 File Offset: 0x00151294
			private CreateDonateableExpressionVisitor(Dictionary<ParameterExpression, object> parameterToValueLookup)
			{
				this._parameterToValueLookup = parameterToValueLookup;
			}

			// Token: 0x06006205 RID: 25093 RVA: 0x001530A4 File Offset: 0x001512A4
			internal static Expression Replace(LambdaExpression query, ObjectContext objectContext, object[] parameterValues)
			{
				Dictionary<ParameterExpression, object> dictionary = query.Parameters.Skip(1).Zip(parameterValues).ToDictionary((KeyValuePair<ParameterExpression, object> pair) => pair.Key, (KeyValuePair<ParameterExpression, object> pair) => pair.Value);
				dictionary.Add(query.Parameters.First<ParameterExpression>(), objectContext);
				return new CompiledELinqQueryState.CreateDonateableExpressionVisitor(dictionary).Visit(query.Body);
			}

			// Token: 0x06006206 RID: 25094 RVA: 0x00153128 File Offset: 0x00151328
			internal override Expression VisitParameter(ParameterExpression p)
			{
				object obj;
				Expression expression;
				if (this._parameterToValueLookup.TryGetValue(p, out obj))
				{
					expression = Expression.Constant(obj, p.Type);
				}
				else
				{
					expression = base.VisitParameter(p);
				}
				return expression;
			}

			// Token: 0x04002B26 RID: 11046
			private readonly Dictionary<ParameterExpression, object> _parameterToValueLookup;
		}
	}
}
