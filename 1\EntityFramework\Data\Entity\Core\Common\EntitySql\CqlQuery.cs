﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.EntitySql.AST;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.EntitySql
{
	// Token: 0x02000649 RID: 1609
	internal static class CqlQuery
	{
		// Token: 0x06004DE9 RID: 19945 RVA: 0x00117610 File Offset: 0x00115810
		internal static ParseResult Compile(string commandText, Perspective perspective, ParserOptions parserOptions, IEnumerable<DbParameterReferenceExpression> parameters)
		{
			return CqlQuery.CompileCommon<ParseResult>(commandText, parserOptions, (Node astCommand, ParserOptions validatedParserOptions) => CqlQuery.AnalyzeCommandSemantics(astCommand, perspective, validatedParserOptions, parameters));
		}

		// Token: 0x06004DEA RID: 19946 RVA: 0x00117644 File Offset: 0x00115844
		internal static DbLambda CompileQueryCommandLambda(string queryCommandText, Perspective perspective, ParserOptions parserOptions, IEnumerable<DbParameterReferenceExpression> parameters, IEnumerable<DbVariableReferenceExpression> variables)
		{
			return CqlQuery.CompileCommon<DbLambda>(queryCommandText, parserOptions, (Node astCommand, ParserOptions validatedParserOptions) => CqlQuery.AnalyzeQueryExpressionSemantics(astCommand, perspective, validatedParserOptions, parameters, variables));
		}

		// Token: 0x06004DEB RID: 19947 RVA: 0x00117680 File Offset: 0x00115880
		private static Node Parse(string commandText, ParserOptions parserOptions)
		{
			Check.NotEmpty(commandText, "commandText");
			Node node = new CqlParser(parserOptions, true).Parse(commandText);
			if (node == null)
			{
				throw EntitySqlException.Create(commandText, Strings.InvalidEmptyQuery, 0, null, false, null);
			}
			return node;
		}

		// Token: 0x06004DEC RID: 19948 RVA: 0x001176BB File Offset: 0x001158BB
		private static TResult CompileCommon<TResult>(string commandText, ParserOptions parserOptions, Func<Node, ParserOptions, TResult> compilationFunction) where TResult : class
		{
			parserOptions = parserOptions ?? new ParserOptions();
			return compilationFunction(CqlQuery.Parse(commandText, parserOptions), parserOptions);
		}

		// Token: 0x06004DED RID: 19949 RVA: 0x001176D7 File Offset: 0x001158D7
		private static ParseResult AnalyzeCommandSemantics(Node astExpr, Perspective perspective, ParserOptions parserOptions, IEnumerable<DbParameterReferenceExpression> parameters)
		{
			return CqlQuery.AnalyzeSemanticsCommon<ParseResult>(astExpr, perspective, parserOptions, parameters, null, (SemanticAnalyzer analyzer, Node astExpression) => analyzer.AnalyzeCommand(astExpression));
		}

		// Token: 0x06004DEE RID: 19950 RVA: 0x00117702 File Offset: 0x00115902
		private static DbLambda AnalyzeQueryExpressionSemantics(Node astQueryCommand, Perspective perspective, ParserOptions parserOptions, IEnumerable<DbParameterReferenceExpression> parameters, IEnumerable<DbVariableReferenceExpression> variables)
		{
			return CqlQuery.AnalyzeSemanticsCommon<DbLambda>(astQueryCommand, perspective, parserOptions, parameters, variables, (SemanticAnalyzer analyzer, Node astExpr) => analyzer.AnalyzeQueryCommand(astExpr));
		}

		// Token: 0x06004DEF RID: 19951 RVA: 0x00117730 File Offset: 0x00115930
		private static TResult AnalyzeSemanticsCommon<TResult>(Node astExpr, Perspective perspective, ParserOptions parserOptions, IEnumerable<DbParameterReferenceExpression> parameters, IEnumerable<DbVariableReferenceExpression> variables, Func<SemanticAnalyzer, Node, TResult> analysisFunction) where TResult : class
		{
			TResult tresult = default(TResult);
			try
			{
				SemanticAnalyzer semanticAnalyzer = new SemanticAnalyzer(SemanticResolver.Create(perspective, parserOptions, parameters, variables));
				tresult = analysisFunction(semanticAnalyzer, astExpr);
			}
			catch (MetadataException ex)
			{
				throw new EntitySqlException(Strings.GeneralExceptionAsQueryInnerException("Metadata"), ex);
			}
			catch (MappingException ex2)
			{
				throw new EntitySqlException(Strings.GeneralExceptionAsQueryInnerException("Mapping"), ex2);
			}
			return tresult;
		}
	}
}
