﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EntityDatabase.ServerModels
{
	// Token: 0x0200001A RID: 26
	public class TS_UltimateLuxuryTurntable
	{
		// Token: 0x170000AF RID: 175
		// (get) Token: 0x06000176 RID: 374 RVA: 0x00002CDA File Offset: 0x00000EDA
		// (set) Token: 0x06000177 RID: 375 RVA: 0x00002CE2 File Offset: 0x00000EE2
		[Key]
		public int ID { get; set; }

		// Token: 0x170000B0 RID: 176
		// (get) Token: 0x06000178 RID: 376 RVA: 0x00002CEB File Offset: 0x00000EEB
		// (set) Token: 0x06000179 RID: 377 RVA: 0x00002CF3 File Offset: 0x00000EF3
		public int ItemID { get; set; }

		// Token: 0x170000B1 RID: 177
		// (get) Token: 0x0600017A RID: 378 RVA: 0x00002CFC File Offset: 0x00000EFC
		// (set) Token: 0x0600017B RID: 379 RVA: 0x00002D04 File Offset: 0x00000F04
		public int Validay { get; set; }

		// Token: 0x170000B2 RID: 178
		// (get) Token: 0x0600017C RID: 380 RVA: 0x00002D0D File Offset: 0x00000F0D
		// (set) Token: 0x0600017D RID: 381 RVA: 0x00002D15 File Offset: 0x00000F15
		public int Count { get; set; }

		// Token: 0x170000B3 RID: 179
		// (get) Token: 0x0600017E RID: 382 RVA: 0x00002D1E File Offset: 0x00000F1E
		// (set) Token: 0x0600017F RID: 383 RVA: 0x00002D26 File Offset: 0x00000F26
		public int Weight { get; set; }

		// Token: 0x170000B4 RID: 180
		// (get) Token: 0x06000180 RID: 384 RVA: 0x00002D2F File Offset: 0x00000F2F
		// (set) Token: 0x06000181 RID: 385 RVA: 0x00002D37 File Offset: 0x00000F37
		public bool IsBind { get; set; }

		// Token: 0x170000B5 RID: 181
		// (get) Token: 0x06000182 RID: 386 RVA: 0x00002D40 File Offset: 0x00000F40
		// (set) Token: 0x06000183 RID: 387 RVA: 0x00002D48 File Offset: 0x00000F48
		public int Round { get; set; }
	}
}
