﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.CqlGeneration;
using System.Data.Entity.Core.Metadata.Edm;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005A9 RID: 1449
	internal sealed class MemberProjectedSlot : ProjectedSlot
	{
		// Token: 0x06004686 RID: 18054 RVA: 0x000F828D File Offset: 0x000F648D
		internal MemberProjectedSlot(MemberPath node)
		{
			this.m_memberPath = node;
		}

		// Token: 0x17000DFC RID: 3580
		// (get) Token: 0x06004687 RID: 18055 RVA: 0x000F829C File Offset: 0x000F649C
		internal MemberPath MemberPath
		{
			get
			{
				return this.m_memberPath;
			}
		}

		// Token: 0x06004688 RID: 18056 RVA: 0x000F82A4 File Offset: 0x000F64A4
		internal override StringBuilder AsEsql(StringBuilder builder, MemberPath outputMember, string blockAlias, int indentLevel)
		{
			TypeUsage typeUsage;
			if (this.NeedToCastCqlValue(outputMember, out typeUsage))
			{
				builder.Append("CAST(");
				this.m_memberPath.AsEsql(builder, blockAlias);
				builder.Append(" AS ");
				CqlWriter.AppendEscapedTypeName(builder, typeUsage.EdmType);
				builder.Append(')');
			}
			else
			{
				this.m_memberPath.AsEsql(builder, blockAlias);
			}
			return builder;
		}

		// Token: 0x06004689 RID: 18057 RVA: 0x000F8308 File Offset: 0x000F6508
		internal override DbExpression AsCqt(DbExpression row, MemberPath outputMember)
		{
			DbExpression dbExpression = this.m_memberPath.AsCqt(row);
			TypeUsage typeUsage;
			if (this.NeedToCastCqlValue(outputMember, out typeUsage))
			{
				dbExpression = dbExpression.CastTo(typeUsage);
			}
			return dbExpression;
		}

		// Token: 0x0600468A RID: 18058 RVA: 0x000F8336 File Offset: 0x000F6536
		private bool NeedToCastCqlValue(MemberPath outputMember, out TypeUsage outputMemberTypeUsage)
		{
			TypeUsage modelTypeUsage = Helper.GetModelTypeUsage(this.m_memberPath.LeafEdmMember);
			outputMemberTypeUsage = Helper.GetModelTypeUsage(outputMember.LeafEdmMember);
			return !modelTypeUsage.EdmType.Equals(outputMemberTypeUsage.EdmType);
		}

		// Token: 0x0600468B RID: 18059 RVA: 0x000F8369 File Offset: 0x000F6569
		internal override void ToCompactString(StringBuilder builder)
		{
			this.m_memberPath.ToCompactString(builder);
		}

		// Token: 0x0600468C RID: 18060 RVA: 0x000F8377 File Offset: 0x000F6577
		internal string ToUserString()
		{
			return this.m_memberPath.PathToString(new bool?(false));
		}

		// Token: 0x0600468D RID: 18061 RVA: 0x000F838C File Offset: 0x000F658C
		protected override bool IsEqualTo(ProjectedSlot right)
		{
			MemberProjectedSlot memberProjectedSlot = right as MemberProjectedSlot;
			return memberProjectedSlot != null && MemberPath.EqualityComparer.Equals(this.m_memberPath, memberProjectedSlot.m_memberPath);
		}

		// Token: 0x0600468E RID: 18062 RVA: 0x000F83BB File Offset: 0x000F65BB
		protected override int GetHash()
		{
			return MemberPath.EqualityComparer.GetHashCode(this.m_memberPath);
		}

		// Token: 0x0600468F RID: 18063 RVA: 0x000F83D0 File Offset: 0x000F65D0
		internal MemberProjectedSlot RemapSlot(Dictionary<MemberPath, MemberPath> remap)
		{
			MemberPath memberPath = null;
			if (remap.TryGetValue(this.MemberPath, out memberPath))
			{
				return new MemberProjectedSlot(memberPath);
			}
			return new MemberProjectedSlot(this.MemberPath);
		}

		// Token: 0x06004690 RID: 18064 RVA: 0x000F8404 File Offset: 0x000F6604
		internal static List<MemberProjectedSlot> GetKeySlots(IEnumerable<MemberProjectedSlot> slots, MemberPath prefix)
		{
			EntitySet entitySet = prefix.EntitySet;
			List<ExtentKey> keysForEntityType = ExtentKey.GetKeysForEntityType(prefix, entitySet.ElementType);
			return MemberProjectedSlot.GetSlots(slots, keysForEntityType[0].KeyFields);
		}

		// Token: 0x06004691 RID: 18065 RVA: 0x000F8438 File Offset: 0x000F6638
		internal static List<MemberProjectedSlot> GetSlots(IEnumerable<MemberProjectedSlot> slots, IEnumerable<MemberPath> members)
		{
			List<MemberProjectedSlot> list = new List<MemberProjectedSlot>();
			foreach (MemberPath memberPath in members)
			{
				MemberProjectedSlot slotForMember = MemberProjectedSlot.GetSlotForMember(Helpers.AsSuperTypeList<MemberProjectedSlot, ProjectedSlot>(slots), memberPath);
				if (slotForMember == null)
				{
					return null;
				}
				list.Add(slotForMember);
			}
			return list;
		}

		// Token: 0x06004692 RID: 18066 RVA: 0x000F84A0 File Offset: 0x000F66A0
		internal static MemberProjectedSlot GetSlotForMember(IEnumerable<ProjectedSlot> slots, MemberPath member)
		{
			foreach (ProjectedSlot projectedSlot in slots)
			{
				MemberProjectedSlot memberProjectedSlot = (MemberProjectedSlot)projectedSlot;
				if (MemberPath.EqualityComparer.Equals(memberProjectedSlot.MemberPath, member))
				{
					return memberProjectedSlot;
				}
			}
			return null;
		}

		// Token: 0x04001925 RID: 6437
		private readonly MemberPath m_memberPath;
	}
}
