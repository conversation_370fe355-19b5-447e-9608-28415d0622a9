﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{E155BC47-CCD1-472F-AFC6-7F30627564C3}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>EntityDatabase</RootNamespace>
    <AssemblyName>EntityDatabase</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoStdLib>true</NoStdLib>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoStdLib>true</NoStdLib>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System.ComponentModel.DataAnnotations" />
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Microsoft\CodeAnalysis\EmbeddedAttribute.cs" />
    <Compile Include="PlayerModels\PlayerData.cs" />
    <Compile Include="PlayerModels\Sys_Carnival_Goods.cs" />
    <Compile Include="PlayerModels\Sys_Carnival_Server.cs" />
    <Compile Include="PlayerModels\Sys_Carnival_Users.cs" />
    <Compile Include="PlayerModels\Sys_FirstKill_Data.cs" />
    <Compile Include="PlayerModels\Sys_MonthCard_Info.cs" />
    <Compile Include="PlayerModels\Sys_Users_CaveLoot_MineRecord.cs" />
    <Compile Include="PlayerModels\Sys_Users_CaveLoot_PlayerInfo.cs" />
    <Compile Include="PlayerModels\Sys_Users_CaveLoot_Progress.cs" />
    <Compile Include="PlayerModels\Sys_Users_CaveLoot_Rank.cs" />
    <Compile Include="PlayerModels\Sys_Users_CaveLoot_TempBag.cs" />
    <Compile Include="PlayerModels\Sys_Users_DevilTurn.cs" />
    <Compile Include="PlayerModels\Sys_Users_DevilTurn_Rank.cs" />
    <Compile Include="PlayerModels\Sys_Users_FirstPay.cs" />
    <Compile Include="PlayerModels\Sys_Users_FirstPayTemp.cs" />
    <Compile Include="PlayerModels\Sys_Users_ListBox.cs" />
    <Compile Include="PlayerModels\Sys_Users_UiTimateLuxuryCarnival.cs" />
    <Compile Include="PlayerModels\Sys_Users_UltimateLuxuryPinTuan.cs" />
    <Compile Include="PlayerModels\Sys_Users_UltimateLuxuryRecharge.cs" />
    <Compile Include="PlayerModels\Sys_Users_UltimateLuxuryTurnTable.cs" />
    <Compile Include="PlayerModels\Sys_Users_WarOrder_Info.cs" />
    <Compile Include="PlayerModels\Sys_User_Info.cs" />
    <Compile Include="PlayerModels\Sys_User_RelicItemInfo.cs" />
    <Compile Include="PlayerModels\Sys_User_RelicItemTemplate.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ServerModels\DevilTreasItemList.cs" />
    <Compile Include="ServerModels\DevilTreasPointsList.cs" />
    <Compile Include="ServerModels\DevilTreasRankRewardList.cs" />
    <Compile Include="ServerModels\DevilTreasSarahToBoxList.cs" />
    <Compile Include="ServerModels\DevilTurnOpenBox.cs" />
    <Compile Include="ServerModels\DevilturnRankAward.cs" />
    <Compile Include="ServerModels\ServerData.cs" />
    <Compile Include="ServerModels\TS_Carnival_Items.cs" />
    <Compile Include="ServerModels\TS_CaveLootLotteryPool.cs" />
    <Compile Include="ServerModels\TS_CaveLootProgressAward.cs" />
    <Compile Include="ServerModels\TS_FirstCopy.cs" />
    <Compile Include="ServerModels\TS_FirstPayCode.cs" />
    <Compile Include="ServerModels\TS_FirstPayShopTemp.cs" />
    <Compile Include="ServerModels\TS_PveMissTicket.cs" />
    <Compile Include="ServerModels\TS_Relic_AdvanceTemplate.cs" />
    <Compile Include="ServerModels\TS_Relic_AdvanceValue.cs" />
    <Compile Include="ServerModels\TS_Relic_DegreeTemplate.cs" />
    <Compile Include="ServerModels\TS_Relic_ItemTemplate.cs" />
    <Compile Include="ServerModels\TS_Relic_UpgradeTemplate.cs" />
    <Compile Include="ServerModels\TS_UltimateLuxuryCarnival.cs" />
    <Compile Include="ServerModels\TS_UltimateLuxuryLimitBuy.cs" />
    <Compile Include="ServerModels\TS_UltimateLuxuryRecharge.cs" />
    <Compile Include="ServerModels\TS_UltimateLuxuryTurntable.cs" />
    <Compile Include="ServerModels\TS_Upgrade.cs" />
    <Compile Include="ServerModels\TS_WarPass_QuestTemplate.cs" />
    <Compile Include="System\Runtime\CompilerServices\RefSafetyRulesAttribute.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\EntityFramework\EntityFramework.csproj">
      <Project>{E155BC47-CCD1-472F-AFC6-7F30627564C4}</Project>
      <Name>EntityFramework</Name>
    </ProjectReference>
    <ProjectReference Include="..\mscorlib\mscorlib.csproj">
      <Project>{E155BC47-CCD1-472F-AFC6-7F30627564C8}</Project>
      <Name>mscorlib</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>