﻿using System;
using System.Linq;

namespace System.Data.Entity.Core.Common.Utils.Boolean
{
	// Token: 0x02000619 RID: 1561
	internal static class NegationPusher
	{
		// Token: 0x06004BD0 RID: 19408 RVA: 0x0010A810 File Offset: 0x00108A10
		internal static BoolExpr<DomainConstraint<T_Variable, T_Element>> EliminateNot<T_Variable, T_Element>(BoolExpr<DomainConstraint<T_Variable, T_Element>> expression)
		{
			return expression.Accept<BoolExpr<DomainConstraint<T_Variable, T_Element>>>(NegationPusher.NonNegatedDomainConstraintTreeVisitor<T_Variable, T_Element>.Instance);
		}

		// Token: 0x02000C55 RID: 3157
		private class NonNegatedTreeVisitor<T_Identifier> : BasicVisitor<T_Identifier>
		{
			// Token: 0x06006AB6 RID: 27318 RVA: 0x0016BC0C File Offset: 0x00169E0C
			protected NonNegatedTreeVisitor()
			{
			}

			// Token: 0x06006AB7 RID: 27319 RVA: 0x0016BC14 File Offset: 0x00169E14
			internal override BoolExpr<T_Identifier> VisitNot(NotExpr<T_Identifier> expression)
			{
				return expression.Child.Accept<BoolExpr<T_Identifier>>(NegationPusher.NegatedTreeVisitor<T_Identifier>.Instance);
			}

			// Token: 0x040030E0 RID: 12512
			internal static readonly NegationPusher.NonNegatedTreeVisitor<T_Identifier> Instance = new NegationPusher.NonNegatedTreeVisitor<T_Identifier>();
		}

		// Token: 0x02000C56 RID: 3158
		private class NegatedTreeVisitor<T_Identifier> : Visitor<T_Identifier, BoolExpr<T_Identifier>>
		{
			// Token: 0x06006AB9 RID: 27321 RVA: 0x0016BC32 File Offset: 0x00169E32
			protected NegatedTreeVisitor()
			{
			}

			// Token: 0x06006ABA RID: 27322 RVA: 0x0016BC3A File Offset: 0x00169E3A
			internal override BoolExpr<T_Identifier> VisitTrue(TrueExpr<T_Identifier> expression)
			{
				return FalseExpr<T_Identifier>.Value;
			}

			// Token: 0x06006ABB RID: 27323 RVA: 0x0016BC41 File Offset: 0x00169E41
			internal override BoolExpr<T_Identifier> VisitFalse(FalseExpr<T_Identifier> expression)
			{
				return TrueExpr<T_Identifier>.Value;
			}

			// Token: 0x06006ABC RID: 27324 RVA: 0x0016BC48 File Offset: 0x00169E48
			internal override BoolExpr<T_Identifier> VisitTerm(TermExpr<T_Identifier> expression)
			{
				return new NotExpr<T_Identifier>(expression);
			}

			// Token: 0x06006ABD RID: 27325 RVA: 0x0016BC50 File Offset: 0x00169E50
			internal override BoolExpr<T_Identifier> VisitNot(NotExpr<T_Identifier> expression)
			{
				return expression.Child.Accept<BoolExpr<T_Identifier>>(NegationPusher.NonNegatedTreeVisitor<T_Identifier>.Instance);
			}

			// Token: 0x06006ABE RID: 27326 RVA: 0x0016BC62 File Offset: 0x00169E62
			internal override BoolExpr<T_Identifier> VisitAnd(AndExpr<T_Identifier> expression)
			{
				return new OrExpr<T_Identifier>(expression.Children.Select((BoolExpr<T_Identifier> child) => child.Accept<BoolExpr<T_Identifier>>(this)));
			}

			// Token: 0x06006ABF RID: 27327 RVA: 0x0016BC80 File Offset: 0x00169E80
			internal override BoolExpr<T_Identifier> VisitOr(OrExpr<T_Identifier> expression)
			{
				return new AndExpr<T_Identifier>(expression.Children.Select((BoolExpr<T_Identifier> child) => child.Accept<BoolExpr<T_Identifier>>(this)));
			}

			// Token: 0x040030E1 RID: 12513
			internal static readonly NegationPusher.NegatedTreeVisitor<T_Identifier> Instance = new NegationPusher.NegatedTreeVisitor<T_Identifier>();
		}

		// Token: 0x02000C57 RID: 3159
		private class NonNegatedDomainConstraintTreeVisitor<T_Variable, T_Element> : NegationPusher.NonNegatedTreeVisitor<DomainConstraint<T_Variable, T_Element>>
		{
			// Token: 0x06006AC3 RID: 27331 RVA: 0x0016BCBC File Offset: 0x00169EBC
			private NonNegatedDomainConstraintTreeVisitor()
			{
			}

			// Token: 0x06006AC4 RID: 27332 RVA: 0x0016BCC4 File Offset: 0x00169EC4
			internal override BoolExpr<DomainConstraint<T_Variable, T_Element>> VisitNot(NotExpr<DomainConstraint<T_Variable, T_Element>> expression)
			{
				return expression.Child.Accept<BoolExpr<DomainConstraint<T_Variable, T_Element>>>(NegationPusher.NegatedDomainConstraintTreeVisitor<T_Variable, T_Element>.Instance);
			}

			// Token: 0x040030E2 RID: 12514
			internal new static readonly NegationPusher.NonNegatedDomainConstraintTreeVisitor<T_Variable, T_Element> Instance = new NegationPusher.NonNegatedDomainConstraintTreeVisitor<T_Variable, T_Element>();
		}

		// Token: 0x02000C58 RID: 3160
		private class NegatedDomainConstraintTreeVisitor<T_Variable, T_Element> : NegationPusher.NegatedTreeVisitor<DomainConstraint<T_Variable, T_Element>>
		{
			// Token: 0x06006AC6 RID: 27334 RVA: 0x0016BCE2 File Offset: 0x00169EE2
			private NegatedDomainConstraintTreeVisitor()
			{
			}

			// Token: 0x06006AC7 RID: 27335 RVA: 0x0016BCEA File Offset: 0x00169EEA
			internal override BoolExpr<DomainConstraint<T_Variable, T_Element>> VisitNot(NotExpr<DomainConstraint<T_Variable, T_Element>> expression)
			{
				return expression.Child.Accept<BoolExpr<DomainConstraint<T_Variable, T_Element>>>(NegationPusher.NonNegatedDomainConstraintTreeVisitor<T_Variable, T_Element>.Instance);
			}

			// Token: 0x06006AC8 RID: 27336 RVA: 0x0016BCFC File Offset: 0x00169EFC
			internal override BoolExpr<DomainConstraint<T_Variable, T_Element>> VisitTerm(TermExpr<DomainConstraint<T_Variable, T_Element>> expression)
			{
				return new TermExpr<DomainConstraint<T_Variable, T_Element>>(expression.Identifier.InvertDomainConstraint());
			}

			// Token: 0x040030E3 RID: 12515
			internal new static readonly NegationPusher.NegatedDomainConstraintTreeVisitor<T_Variable, T_Element> Instance = new NegationPusher.NegatedDomainConstraintTreeVisitor<T_Variable, T_Element>();
		}
	}
}
