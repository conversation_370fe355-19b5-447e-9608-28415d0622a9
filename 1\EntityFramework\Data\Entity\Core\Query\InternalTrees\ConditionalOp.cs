﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x02000392 RID: 914
	internal sealed class ConditionalOp : ScalarOp
	{
		// Token: 0x06002CBA RID: 11450 RVA: 0x0008F12A File Offset: 0x0008D32A
		internal ConditionalOp(OpType optype, TypeUsage type)
			: base(optype, type)
		{
		}

		// Token: 0x06002CBB RID: 11451 RVA: 0x0008F134 File Offset: 0x0008D334
		private ConditionalOp(OpType opType)
			: base(opType)
		{
		}

		// Token: 0x06002CBC RID: 11452 RVA: 0x0008F13D File Offset: 0x0008D33D
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002CBD RID: 11453 RVA: 0x0008F147 File Offset: 0x0008D347
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000F07 RID: 3847
		internal static readonly ConditionalOp PatternAnd = new ConditionalOp(OpType.And);

		// Token: 0x04000F08 RID: 3848
		internal static readonly ConditionalOp PatternOr = new ConditionalOp(OpType.Or);

		// Token: 0x04000F09 RID: 3849
		internal static readonly ConditionalOp PatternIn = new ConditionalOp(OpType.In);

		// Token: 0x04000F0A RID: 3850
		internal static readonly ConditionalOp PatternNot = new ConditionalOp(OpType.Not);

		// Token: 0x04000F0B RID: 3851
		internal static readonly ConditionalOp PatternIsNull = new ConditionalOp(OpType.IsNull);
	}
}
