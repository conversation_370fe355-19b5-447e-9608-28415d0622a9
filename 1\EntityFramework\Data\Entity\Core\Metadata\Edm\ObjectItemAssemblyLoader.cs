﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Resources;
using System.Reflection;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x02000512 RID: 1298
	internal abstract class ObjectItemAssemblyLoader
	{
		// Token: 0x06003FF4 RID: 16372 RVA: 0x000D3CA9 File Offset: 0x000D1EA9
		protected ObjectItemAssemblyLoader(Assembly assembly, AssemblyCacheEntry cacheEntry, ObjectItemLoadingSessionData sessionData)
		{
			this._assembly = assembly;
			this._cacheEntry = cacheEntry;
			this._sessionData = sessionData;
		}

		// Token: 0x06003FF5 RID: 16373 RVA: 0x000D3CC6 File Offset: 0x000D1EC6
		internal virtual void Load()
		{
			this.AddToAssembliesLoaded();
			this.LoadTypesFromAssembly();
			this.AddToKnownAssemblies();
			this.LoadClosureAssemblies();
		}

		// Token: 0x06003FF6 RID: 16374
		protected abstract void AddToAssembliesLoaded();

		// Token: 0x06003FF7 RID: 16375
		protected abstract void LoadTypesFromAssembly();

		// Token: 0x06003FF8 RID: 16376 RVA: 0x000D3CE0 File Offset: 0x000D1EE0
		protected virtual void LoadClosureAssemblies()
		{
			ObjectItemAssemblyLoader.LoadAssemblies(this.CacheEntry.ClosureAssemblies, this.SessionData);
		}

		// Token: 0x06003FF9 RID: 16377 RVA: 0x000D3CF8 File Offset: 0x000D1EF8
		internal virtual void OnLevel1SessionProcessing()
		{
		}

		// Token: 0x06003FFA RID: 16378 RVA: 0x000D3CFA File Offset: 0x000D1EFA
		internal virtual void OnLevel2SessionProcessing()
		{
		}

		// Token: 0x06003FFB RID: 16379 RVA: 0x000D3CFC File Offset: 0x000D1EFC
		internal static ObjectItemAssemblyLoader CreateLoader(Assembly assembly, ObjectItemLoadingSessionData sessionData)
		{
			if (sessionData.KnownAssemblies.Contains(assembly, sessionData.ObjectItemAssemblyLoaderFactory, sessionData.EdmItemCollection))
			{
				return new ObjectItemNoOpAssemblyLoader(assembly, sessionData);
			}
			ImmutableAssemblyCacheEntry immutableAssemblyCacheEntry;
			if (sessionData.LockedAssemblyCache.TryGetValue(assembly, out immutableAssemblyCacheEntry))
			{
				if (sessionData.ObjectItemAssemblyLoaderFactory == null)
				{
					if (immutableAssemblyCacheEntry.TypesInAssembly.Count != 0)
					{
						sessionData.ObjectItemAssemblyLoaderFactory = new Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader>(ObjectItemAttributeAssemblyLoader.Create);
					}
				}
				else if (sessionData.ObjectItemAssemblyLoaderFactory != new Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader>(ObjectItemAttributeAssemblyLoader.Create))
				{
					sessionData.EdmItemErrors.Add(new EdmItemError(Strings.Validator_OSpace_Convention_AttributeAssemblyReferenced(assembly.FullName)));
				}
				return new ObjectItemCachedAssemblyLoader(assembly, immutableAssemblyCacheEntry, sessionData);
			}
			if (sessionData.EdmItemCollection != null && sessionData.EdmItemCollection.ConventionalOcCache.TryGetConventionalOcCacheFromAssemblyCache(assembly, out immutableAssemblyCacheEntry))
			{
				sessionData.ObjectItemAssemblyLoaderFactory = new Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader>(ObjectItemConventionAssemblyLoader.Create);
				return new ObjectItemCachedAssemblyLoader(assembly, immutableAssemblyCacheEntry, sessionData);
			}
			if (sessionData.ObjectItemAssemblyLoaderFactory == null)
			{
				if (ObjectItemAttributeAssemblyLoader.IsSchemaAttributePresent(assembly))
				{
					sessionData.ObjectItemAssemblyLoaderFactory = new Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader>(ObjectItemAttributeAssemblyLoader.Create);
				}
				else if (ObjectItemConventionAssemblyLoader.SessionContainsConventionParameters(sessionData))
				{
					sessionData.ObjectItemAssemblyLoaderFactory = new Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader>(ObjectItemConventionAssemblyLoader.Create);
				}
			}
			if (sessionData.ObjectItemAssemblyLoaderFactory != null)
			{
				return sessionData.ObjectItemAssemblyLoaderFactory(assembly, sessionData);
			}
			return new ObjectItemNoOpAssemblyLoader(assembly, sessionData);
		}

		// Token: 0x06003FFC RID: 16380 RVA: 0x000D3E34 File Offset: 0x000D2034
		internal static bool IsAttributeLoader(object loaderCookie)
		{
			return ObjectItemAssemblyLoader.IsAttributeLoader(loaderCookie as Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader>);
		}

		// Token: 0x06003FFD RID: 16381 RVA: 0x000D3E41 File Offset: 0x000D2041
		internal static bool IsAttributeLoader(Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader> loaderFactory)
		{
			return loaderFactory != null && loaderFactory == new Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader>(ObjectItemAttributeAssemblyLoader.Create);
		}

		// Token: 0x06003FFE RID: 16382 RVA: 0x000D3E5A File Offset: 0x000D205A
		internal static bool IsConventionLoader(Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader> loaderFactory)
		{
			return loaderFactory != null && loaderFactory == new Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader>(ObjectItemConventionAssemblyLoader.Create);
		}

		// Token: 0x06003FFF RID: 16383 RVA: 0x000D3E73 File Offset: 0x000D2073
		protected virtual void AddToKnownAssemblies()
		{
			this._sessionData.KnownAssemblies.Add(this._assembly, new KnownAssemblyEntry(this.CacheEntry, this.SessionData.EdmItemCollection != null));
		}

		// Token: 0x06004000 RID: 16384 RVA: 0x000D3EA4 File Offset: 0x000D20A4
		protected static void LoadAssemblies(IEnumerable<Assembly> assemblies, ObjectItemLoadingSessionData sessionData)
		{
			foreach (Assembly assembly in assemblies)
			{
				ObjectItemAssemblyLoader.CreateLoader(assembly, sessionData).Load();
			}
		}

		// Token: 0x06004001 RID: 16385 RVA: 0x000D3EF0 File Offset: 0x000D20F0
		protected static bool TryGetPrimitiveType(Type type, out PrimitiveType primitiveType)
		{
			return ClrProviderManifest.Instance.TryGetPrimitiveType(Nullable.GetUnderlyingType(type) ?? type, out primitiveType);
		}

		// Token: 0x17000C82 RID: 3202
		// (get) Token: 0x06004002 RID: 16386 RVA: 0x000D3F08 File Offset: 0x000D2108
		protected ObjectItemLoadingSessionData SessionData
		{
			get
			{
				return this._sessionData;
			}
		}

		// Token: 0x17000C83 RID: 3203
		// (get) Token: 0x06004003 RID: 16387 RVA: 0x000D3F10 File Offset: 0x000D2110
		protected Assembly SourceAssembly
		{
			get
			{
				return this._assembly;
			}
		}

		// Token: 0x17000C84 RID: 3204
		// (get) Token: 0x06004004 RID: 16388 RVA: 0x000D3F18 File Offset: 0x000D2118
		protected AssemblyCacheEntry CacheEntry
		{
			get
			{
				return this._cacheEntry;
			}
		}

		// Token: 0x04001654 RID: 5716
		private readonly ObjectItemLoadingSessionData _sessionData;

		// Token: 0x04001655 RID: 5717
		private readonly Assembly _assembly;

		// Token: 0x04001656 RID: 5718
		private readonly AssemblyCacheEntry _cacheEntry;
	}
}
