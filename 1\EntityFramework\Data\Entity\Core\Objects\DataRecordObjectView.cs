﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Data.Common;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Reflection;

namespace System.Data.Entity.Core.Objects
{
	// Token: 0x02000404 RID: 1028
	internal sealed class DataRecordObjectView : ObjectView<DbDataRecord>, ITypedList
	{
		// Token: 0x06002FEF RID: 12271 RVA: 0x000968A0 File Offset: 0x00094AA0
		internal DataRecordObjectView(IObjectViewData<DbDataRecord> viewData, object eventDataSource, RowType rowType, Type propertyComponentType)
			: base(viewData, eventDataSource)
		{
			if (!typeof(IDataRecord).IsAssignableFrom(propertyComponentType))
			{
				propertyComponentType = typeof(IDataRecord);
			}
			this._rowType = rowType;
			this._propertyDescriptorsCache = MaterializedDataRecord.CreatePropertyDescriptorCollection(this._rowType, propertyComponentType, true);
		}

		// Token: 0x06002FF0 RID: 12272 RVA: 0x000968F0 File Offset: 0x00094AF0
		private static PropertyInfo GetTypedIndexer(Type type)
		{
			PropertyInfo propertyInfo = null;
			if (typeof(IList).IsAssignableFrom(type) || typeof(ITypedList).IsAssignableFrom(type) || typeof(IListSource).IsAssignableFrom(type))
			{
				foreach (PropertyInfo propertyInfo2 in from p in type.GetInstanceProperties()
					where p.IsPublic()
					select p)
				{
					if (propertyInfo2.GetIndexParameters().Length != 0 && propertyInfo2.PropertyType != typeof(object))
					{
						propertyInfo = propertyInfo2;
						if (propertyInfo.Name == "Item")
						{
							break;
						}
					}
				}
			}
			return propertyInfo;
		}

		// Token: 0x06002FF1 RID: 12273 RVA: 0x000969CC File Offset: 0x00094BCC
		private static Type GetListItemType(Type type)
		{
			Type type2;
			if (typeof(Array).IsAssignableFrom(type))
			{
				type2 = type.GetElementType();
			}
			else
			{
				PropertyInfo typedIndexer = DataRecordObjectView.GetTypedIndexer(type);
				if (typedIndexer != null)
				{
					type2 = typedIndexer.PropertyType;
				}
				else
				{
					type2 = type;
				}
			}
			return type2;
		}

		// Token: 0x06002FF2 RID: 12274 RVA: 0x00096A10 File Offset: 0x00094C10
		PropertyDescriptorCollection ITypedList.GetItemProperties(PropertyDescriptor[] listAccessors)
		{
			PropertyDescriptorCollection propertyDescriptorCollection;
			if (listAccessors == null || listAccessors.Length == 0)
			{
				propertyDescriptorCollection = this._propertyDescriptorsCache;
			}
			else
			{
				PropertyDescriptor propertyDescriptor = listAccessors[listAccessors.Length - 1];
				FieldDescriptor fieldDescriptor = propertyDescriptor as FieldDescriptor;
				if (fieldDescriptor != null && fieldDescriptor.EdmProperty != null && fieldDescriptor.EdmProperty.TypeUsage.EdmType.BuiltInTypeKind == BuiltInTypeKind.RowType)
				{
					propertyDescriptorCollection = MaterializedDataRecord.CreatePropertyDescriptorCollection((RowType)fieldDescriptor.EdmProperty.TypeUsage.EdmType, typeof(IDataRecord), true);
				}
				else
				{
					propertyDescriptorCollection = TypeDescriptor.GetProperties(DataRecordObjectView.GetListItemType(propertyDescriptor.PropertyType));
				}
			}
			return propertyDescriptorCollection;
		}

		// Token: 0x06002FF3 RID: 12275 RVA: 0x00096A9A File Offset: 0x00094C9A
		string ITypedList.GetListName(PropertyDescriptor[] listAccessors)
		{
			return this._rowType.Name;
		}

		// Token: 0x0400101D RID: 4125
		private readonly PropertyDescriptorCollection _propertyDescriptorsCache;

		// Token: 0x0400101E RID: 4126
		private readonly RowType _rowType;
	}
}
