﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Common.EntitySql;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006E9 RID: 1769
	public class DbExpressionRebinder : DefaultExpressionVisitor
	{
		// Token: 0x06005219 RID: 21017 RVA: 0x00124BAD File Offset: 0x00122DAD
		internal DbExpressionRebinder()
		{
		}

		// Token: 0x0600521A RID: 21018 RVA: 0x00124BB5 File Offset: 0x00122DB5
		protected DbExpressionRebinder(MetadataWorkspace targetWorkspace)
		{
			this._metadata = targetWorkspace;
			this._perspective = new ModelPerspective(targetWorkspace);
		}

		// Token: 0x0600521B RID: 21019 RVA: 0x00124BD0 File Offset: 0x00122DD0
		protected override EntitySetBase VisitEntitySet(EntitySetBase entitySet)
		{
			EntityContainer entityContainer;
			if (!this._metadata.TryGetEntityContainer(entitySet.EntityContainer.Name, entitySet.EntityContainer.DataSpace, out entityContainer))
			{
				throw new ArgumentException(Strings.Cqt_Copier_EntityContainerNotFound(entitySet.EntityContainer.Name));
			}
			EntitySetBase entitySetBase = null;
			if (entityContainer.BaseEntitySets.TryGetValue(entitySet.Name, false, out entitySetBase) && entitySetBase != null && entitySet.BuiltInTypeKind == entitySetBase.BuiltInTypeKind)
			{
				return entitySetBase;
			}
			throw new ArgumentException(Strings.Cqt_Copier_EntitySetNotFound(entitySet.EntityContainer.Name, entitySet.Name));
		}

		// Token: 0x0600521C RID: 21020 RVA: 0x00124C60 File Offset: 0x00122E60
		protected override EdmFunction VisitFunction(EdmFunction functionMetadata)
		{
			List<TypeUsage> list = new List<TypeUsage>(functionMetadata.Parameters.Count);
			foreach (FunctionParameter functionParameter in functionMetadata.Parameters)
			{
				TypeUsage typeUsage = this.VisitTypeUsage(functionParameter.TypeUsage);
				list.Add(typeUsage);
			}
			IList<EdmFunction> list2;
			if (DataSpace.SSpace == functionMetadata.DataSpace)
			{
				EdmFunction edmFunction = null;
				if (this._metadata.TryGetFunction(functionMetadata.Name, functionMetadata.NamespaceName, list.ToArray(), false, functionMetadata.DataSpace, out edmFunction) && edmFunction != null)
				{
					return edmFunction;
				}
			}
			else if (this._perspective.TryGetFunctionByName(functionMetadata.NamespaceName, functionMetadata.Name, false, out list2))
			{
				bool flag;
				EdmFunction edmFunction2 = FunctionOverloadResolver.ResolveFunctionOverloads(list2, list, false, out flag);
				if (!flag && edmFunction2 != null)
				{
					return edmFunction2;
				}
			}
			throw new ArgumentException(Strings.Cqt_Copier_FunctionNotFound(TypeHelpers.GetFullName(functionMetadata.NamespaceName, functionMetadata.Name)));
		}

		// Token: 0x0600521D RID: 21021 RVA: 0x00124D5C File Offset: 0x00122F5C
		protected override EdmType VisitType(EdmType type)
		{
			EdmType edmType = type;
			if (BuiltInTypeKind.RefType == type.BuiltInTypeKind)
			{
				RefType refType = (RefType)type;
				EntityType entityType = (EntityType)this.VisitType(refType.ElementType);
				if (refType.ElementType != entityType)
				{
					edmType = new RefType(entityType);
				}
			}
			else if (BuiltInTypeKind.CollectionType == type.BuiltInTypeKind)
			{
				CollectionType collectionType = (CollectionType)type;
				TypeUsage typeUsage = this.VisitTypeUsage(collectionType.TypeUsage);
				if (collectionType.TypeUsage != typeUsage)
				{
					edmType = new CollectionType(typeUsage);
				}
			}
			else if (BuiltInTypeKind.RowType == type.BuiltInTypeKind)
			{
				RowType rowType = (RowType)type;
				List<KeyValuePair<string, TypeUsage>> list = null;
				for (int i = 0; i < rowType.Properties.Count; i++)
				{
					EdmProperty edmProperty = rowType.Properties[i];
					TypeUsage typeUsage2 = this.VisitTypeUsage(edmProperty.TypeUsage);
					if (edmProperty.TypeUsage != typeUsage2)
					{
						if (list == null)
						{
							list = new List<KeyValuePair<string, TypeUsage>>(rowType.Properties.Select((EdmProperty prop) => new KeyValuePair<string, TypeUsage>(prop.Name, prop.TypeUsage)));
						}
						list[i] = new KeyValuePair<string, TypeUsage>(edmProperty.Name, typeUsage2);
					}
				}
				if (list != null)
				{
					edmType = new RowType(list.Select((KeyValuePair<string, TypeUsage> propInfo) => new EdmProperty(propInfo.Key, propInfo.Value)), rowType.InitializerMetadata);
				}
			}
			else if (!this._metadata.TryGetType(type.Name, type.NamespaceName, type.DataSpace, out edmType) || edmType == null)
			{
				throw new ArgumentException(Strings.Cqt_Copier_TypeNotFound(TypeHelpers.GetFullName(type.NamespaceName, type.Name)));
			}
			return edmType;
		}

		// Token: 0x0600521E RID: 21022 RVA: 0x00124F08 File Offset: 0x00123108
		protected override TypeUsage VisitTypeUsage(TypeUsage type)
		{
			EdmType edmType = this.VisitType(type.EdmType);
			if (edmType == type.EdmType)
			{
				return type;
			}
			Facet[] array = new Facet[type.Facets.Count];
			int num = 0;
			foreach (Facet facet in type.Facets)
			{
				array[num] = facet;
				num++;
			}
			return TypeUsage.Create(edmType, array);
		}

		// Token: 0x0600521F RID: 21023 RVA: 0x00124F90 File Offset: 0x00123190
		private static bool TryGetMember<TMember>(DbExpression instance, string memberName, out TMember member) where TMember : EdmMember
		{
			member = default(TMember);
			StructuralType structuralType = instance.ResultType.EdmType as StructuralType;
			if (structuralType != null)
			{
				EdmMember edmMember = null;
				if (structuralType.Members.TryGetValue(memberName, false, out edmMember))
				{
					member = edmMember as TMember;
				}
			}
			return member != null;
		}

		// Token: 0x06005220 RID: 21024 RVA: 0x00124FEC File Offset: 0x001231EC
		public override DbExpression Visit(DbPropertyExpression expression)
		{
			Check.NotNull<DbPropertyExpression>(expression, "expression");
			DbExpression dbExpression = expression;
			DbExpression dbExpression2 = this.VisitExpression(expression.Instance);
			if (expression.Instance != dbExpression2)
			{
				if (Helper.IsRelationshipEndMember(expression.Property))
				{
					RelationshipEndMember relationshipEndMember;
					if (!DbExpressionRebinder.TryGetMember<RelationshipEndMember>(dbExpression2, expression.Property.Name, out relationshipEndMember))
					{
						EdmType edmType = dbExpression2.ResultType.EdmType;
						throw new ArgumentException(Strings.Cqt_Copier_EndNotFound(expression.Property.Name, TypeHelpers.GetFullName(edmType.NamespaceName, edmType.Name)));
					}
					dbExpression = dbExpression2.Property(relationshipEndMember);
				}
				else if (Helper.IsNavigationProperty(expression.Property))
				{
					NavigationProperty navigationProperty;
					if (!DbExpressionRebinder.TryGetMember<NavigationProperty>(dbExpression2, expression.Property.Name, out navigationProperty))
					{
						EdmType edmType2 = dbExpression2.ResultType.EdmType;
						throw new ArgumentException(Strings.Cqt_Copier_NavPropertyNotFound(expression.Property.Name, TypeHelpers.GetFullName(edmType2.NamespaceName, edmType2.Name)));
					}
					dbExpression = dbExpression2.Property(navigationProperty);
				}
				else
				{
					EdmProperty edmProperty;
					if (!DbExpressionRebinder.TryGetMember<EdmProperty>(dbExpression2, expression.Property.Name, out edmProperty))
					{
						EdmType edmType3 = dbExpression2.ResultType.EdmType;
						throw new ArgumentException(Strings.Cqt_Copier_PropertyNotFound(expression.Property.Name, TypeHelpers.GetFullName(edmType3.NamespaceName, edmType3.Name)));
					}
					dbExpression = dbExpression2.Property(edmProperty);
				}
			}
			return dbExpression;
		}

		// Token: 0x04001DE1 RID: 7649
		private readonly MetadataWorkspace _metadata;

		// Token: 0x04001DE2 RID: 7650
		private readonly Perspective _perspective;
	}
}
