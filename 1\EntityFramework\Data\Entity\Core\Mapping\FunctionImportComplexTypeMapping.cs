﻿using System;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x02000532 RID: 1330
	public sealed class FunctionImportComplexTypeMapping : FunctionImportStructuralTypeMapping
	{
		// Token: 0x060041B5 RID: 16821 RVA: 0x000DCF52 File Offset: 0x000DB152
		public FunctionImportComplexTypeMapping(ComplexType returnType, Collection<FunctionImportReturnTypePropertyMapping> properties)
			: this(Check.NotNull<ComplexType>(returnType, "returnType"), Check.NotNull<Collection<FunctionImportReturnTypePropertyMapping>>(properties, "properties"), LineInfo.Empty)
		{
		}

		// Token: 0x060041B6 RID: 16822 RVA: 0x000DCF75 File Offset: 0x000DB175
		internal FunctionImportComplexTypeMapping(ComplexType returnType, Collection<FunctionImportReturnTypePropertyMapping> properties, LineInfo lineInfo)
			: base(properties, lineInfo)
		{
			this._returnType = returnType;
		}

		// Token: 0x17000CFF RID: 3327
		// (get) Token: 0x060041B7 RID: 16823 RVA: 0x000DCF86 File Offset: 0x000DB186
		public ComplexType ReturnType
		{
			get
			{
				return this._returnType;
			}
		}

		// Token: 0x040016C6 RID: 5830
		private readonly ComplexType _returnType;
	}
}
