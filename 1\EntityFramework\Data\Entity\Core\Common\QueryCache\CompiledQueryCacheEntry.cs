﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Core.Objects.Internal;

namespace System.Data.Entity.Core.Common.QueryCache
{
	// Token: 0x02000628 RID: 1576
	internal sealed class CompiledQueryCacheEntry : QueryCacheEntry
	{
		// Token: 0x06004C39 RID: 19513 RVA: 0x0010B430 File Offset: 0x00109630
		internal CompiledQueryCacheEntry(QueryCacheKey queryCacheKey, MergeOption? mergeOption)
			: base(queryCache<PERSON>ey, null)
		{
			this.PropagatedMergeOption = mergeOption;
			this._plans = new ConcurrentDictionary<string, ObjectQueryExecutionPlan>();
		}

		// Token: 0x06004C3A RID: 19514 RVA: 0x0010B44C File Offset: 0x0010964C
		internal ObjectQueryExecutionPlan GetExecutionPlan(MergeOption mergeOption, bool useCSharpNullComparisonBehavior)
		{
			string text = CompiledQueryCacheEntry.GenerateLocalCacheKey(mergeOption, useCSharpNullComparisonBehavior);
			ObjectQueryExecutionPlan objectQueryExecutionPlan;
			this._plans.TryGetValue(text, out objectQueryExecutionPlan);
			return objectQueryExecutionPlan;
		}

		// Token: 0x06004C3B RID: 19515 RVA: 0x0010B474 File Offset: 0x00109674
		internal ObjectQueryExecutionPlan SetExecutionPlan(ObjectQueryExecutionPlan newPlan, bool useCSharpNullComparisonBehavior)
		{
			string text = CompiledQueryCacheEntry.GenerateLocalCacheKey(newPlan.MergeOption, useCSharpNullComparisonBehavior);
			return this._plans.GetOrAdd(text, newPlan);
		}

		// Token: 0x06004C3C RID: 19516 RVA: 0x0010B49C File Offset: 0x0010969C
		internal bool TryGetResultType(out TypeUsage resultType)
		{
			using (IEnumerator<ObjectQueryExecutionPlan> enumerator = this._plans.Values.GetEnumerator())
			{
				if (enumerator.MoveNext())
				{
					ObjectQueryExecutionPlan objectQueryExecutionPlan = enumerator.Current;
					resultType = objectQueryExecutionPlan.ResultType;
					return true;
				}
			}
			resultType = null;
			return false;
		}

		// Token: 0x06004C3D RID: 19517 RVA: 0x0010B4FC File Offset: 0x001096FC
		internal override object GetTarget()
		{
			return this;
		}

		// Token: 0x06004C3E RID: 19518 RVA: 0x0010B500 File Offset: 0x00109700
		private static string GenerateLocalCacheKey(MergeOption mergeOption, bool useCSharpNullComparisonBehavior)
		{
			if (mergeOption <= MergeOption.NoTracking)
			{
				return string.Join("", new object[]
				{
					Enum.GetName(typeof(MergeOption), mergeOption),
					useCSharpNullComparisonBehavior
				});
			}
			throw new ArgumentOutOfRangeException("newPlan.MergeOption");
		}

		// Token: 0x04001A95 RID: 6805
		public readonly MergeOption? PropagatedMergeOption;

		// Token: 0x04001A96 RID: 6806
		private readonly ConcurrentDictionary<string, ObjectQueryExecutionPlan> _plans;
	}
}
