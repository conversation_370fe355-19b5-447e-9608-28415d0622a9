﻿using System;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.CqlGeneration
{
	// Token: 0x020005B9 RID: 1465
	internal sealed class BooleanProjectedSlot : ProjectedSlot
	{
		// Token: 0x06004738 RID: 18232 RVA: 0x000FAA62 File Offset: 0x000F8C62
		internal BooleanProjectedSlot(BoolExpression expr, CqlIdentifiers identifiers, int originalCellNum)
		{
			this.m_expr = expr;
			this.m_originalCell = new CellIdBoolean(identifiers, originalCellNum);
		}

		// Token: 0x06004739 RID: 18233 RVA: 0x000FAA7E File Offset: 0x000F8C7E
		internal override string GetCqlFieldAlias(MemberPath outputMember)
		{
			return this.m_originalCell.SlotName;
		}

		// Token: 0x0600473A RID: 18234 RVA: 0x000FAA8C File Offset: 0x000F8C8C
		internal override StringBuilder AsEsql(StringBuilder builder, MemberPath outputMember, string blockAlias, int indentLevel)
		{
			if (this.m_expr.IsTrue || this.m_expr.IsFalse)
			{
				this.m_expr.AsEsql(builder, blockAlias);
			}
			else
			{
				builder.Append("CASE WHEN ");
				this.m_expr.AsEsql(builder, blockAlias);
				builder.Append(" THEN True ELSE False END");
			}
			return builder;
		}

		// Token: 0x0600473B RID: 18235 RVA: 0x000FAAEC File Offset: 0x000F8CEC
		internal override DbExpression AsCqt(DbExpression row, MemberPath outputMember)
		{
			if (this.m_expr.IsTrue || this.m_expr.IsFalse)
			{
				return this.m_expr.AsCqt(row);
			}
			return DbExpressionBuilder.Case(new DbExpression[] { this.m_expr.AsCqt(row) }, new DbExpression[] { DbExpressionBuilder.True }, DbExpressionBuilder.False);
		}

		// Token: 0x0600473C RID: 18236 RVA: 0x000FAB4D File Offset: 0x000F8D4D
		internal override void ToCompactString(StringBuilder builder)
		{
			StringUtil.FormatStringBuilder(builder, "<{0}, ", new object[] { this.m_originalCell.SlotName });
			this.m_expr.ToCompactString(builder);
			builder.Append('>');
		}

		// Token: 0x04001942 RID: 6466
		private readonly BoolExpression m_expr;

		// Token: 0x04001943 RID: 6467
		private readonly CellIdBoolean m_originalCell;
	}
}
