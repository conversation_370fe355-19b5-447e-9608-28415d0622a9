﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x02000382 RID: 898
	internal sealed class CaseOp : ScalarOp
	{
		// Token: 0x06002BB9 RID: 11193 RVA: 0x0008CF7A File Offset: 0x0008B17A
		internal CaseOp(TypeUsage type)
			: base(OpType.Case, type)
		{
		}

		// Token: 0x06002BBA RID: 11194 RVA: 0x0008CF85 File Offset: 0x0008B185
		private CaseOp()
			: base(OpType.Case)
		{
		}

		// Token: 0x06002BBB RID: 11195 RVA: 0x0008CF8F File Offset: 0x0008B18F
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002BBC RID: 11196 RVA: 0x0008CF99 File Offset: 0x0008B199
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000EDD RID: 3805
		internal static readonly CaseOp Pattern = new CaseOp();
	}
}
