﻿using System;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x02000380 RID: 896
	internal abstract class BasicOpVisitorOfNode : BasicOpVisitorOfT<Node>
	{
		// Token: 0x06002B64 RID: 11108 RVA: 0x0008CB30 File Offset: 0x0008AD30
		protected override void VisitChildren(Node n)
		{
			for (int i = 0; i < n.Children.Count; i++)
			{
				n.Children[i] = base.VisitNode(n.Children[i]);
			}
		}

		// Token: 0x06002B65 RID: 11109 RVA: 0x0008CB74 File Offset: 0x0008AD74
		protected override void VisitChildrenReverse(Node n)
		{
			for (int i = n.Children.Count - 1; i >= 0; i--)
			{
				n.Children[i] = base.VisitNode(n.Children[i]);
			}
		}

		// Token: 0x06002B66 RID: 11110 RVA: 0x0008CBB7 File Offset: 0x0008ADB7
		protected override Node VisitDefault(Node n)
		{
			this.VisitChildren(n);
			return n;
		}

		// Token: 0x06002B67 RID: 11111 RVA: 0x0008CBC1 File Offset: 0x0008ADC1
		protected override Node VisitAncillaryOpDefault(AncillaryOp op, Node n)
		{
			return this.VisitDefault(n);
		}

		// Token: 0x06002B68 RID: 11112 RVA: 0x0008CBCA File Offset: 0x0008ADCA
		protected override Node VisitPhysicalOpDefault(PhysicalOp op, Node n)
		{
			return this.VisitDefault(n);
		}

		// Token: 0x06002B69 RID: 11113 RVA: 0x0008CBD3 File Offset: 0x0008ADD3
		protected override Node VisitRelOpDefault(RelOp op, Node n)
		{
			return this.VisitDefault(n);
		}

		// Token: 0x06002B6A RID: 11114 RVA: 0x0008CBDC File Offset: 0x0008ADDC
		protected override Node VisitScalarOpDefault(ScalarOp op, Node n)
		{
			return this.VisitDefault(n);
		}
	}
}
