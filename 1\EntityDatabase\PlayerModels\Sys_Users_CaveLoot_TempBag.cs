﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000027 RID: 39
	public class Sys_Users_CaveLoot_TempBag
	{
		// Token: 0x1700012C RID: 300
		// (get) Token: 0x0600027E RID: 638 RVA: 0x000035A5 File Offset: 0x000017A5
		// (set) Token: 0x0600027F RID: 639 RVA: 0x000035AD File Offset: 0x000017AD
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x1700012D RID: 301
		// (get) Token: 0x06000280 RID: 640 RVA: 0x000035B6 File Offset: 0x000017B6
		// (set) Token: 0x06000281 RID: 641 RVA: 0x000035BE File Offset: 0x000017BE
		public int UserID { get; set; }

		// Token: 0x1700012E RID: 302
		// (get) Token: 0x06000282 RID: 642 RVA: 0x000035C7 File Offset: 0x000017C7
		// (set) Token: 0x06000283 RID: 643 RVA: 0x000035CF File Offset: 0x000017CF
		public int ItemID { get; set; }

		// Token: 0x1700012F RID: 303
		// (get) Token: 0x06000284 RID: 644 RVA: 0x000035D8 File Offset: 0x000017D8
		// (set) Token: 0x06000285 RID: 645 RVA: 0x000035E0 File Offset: 0x000017E0
		public int Quantity { get; set; }

		// Token: 0x17000130 RID: 304
		// (get) Token: 0x06000286 RID: 646 RVA: 0x000035E9 File Offset: 0x000017E9
		// (set) Token: 0x06000287 RID: 647 RVA: 0x000035F1 File Offset: 0x000017F1
		public bool IsBind { get; set; }

		// Token: 0x17000131 RID: 305
		// (get) Token: 0x06000288 RID: 648 RVA: 0x000035FA File Offset: 0x000017FA
		// (set) Token: 0x06000289 RID: 649 RVA: 0x00003602 File Offset: 0x00001802
		public int Type { get; set; }
	}
}
