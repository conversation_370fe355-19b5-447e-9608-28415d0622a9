﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.IO;
using System.Text;
using System.Xml;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x0200039E RID: 926
	internal class Dump : BasicOpVisitor, IDisposable
	{
		// Token: 0x06002CFF RID: 11519 RVA: 0x0008F535 File Offset: 0x0008D735
		private Dump(Stream stream)
			: this(stream, Dump.DefaultEncoding)
		{
		}

		// Token: 0x06002D00 RID: 11520 RVA: 0x0008F544 File Offset: 0x0008D744
		private Dump(Stream stream, Encoding encoding)
		{
			this._writer = XmlWriter.Create(stream, new XmlWriterSettings
			{
				CheckCharacters = false,
				Indent = true,
				Encoding = encoding
			});
			this._writer.WriteStartDocument(true);
		}

		// Token: 0x06002D01 RID: 11521 RVA: 0x0008F58B File Offset: 0x0008D78B
		internal static string ToXml(Command itree)
		{
			return Dump.ToXml(itree.Root);
		}

		// Token: 0x06002D02 RID: 11522 RVA: 0x0008F598 File Offset: 0x0008D798
		internal static string ToXml(Node subtree)
		{
			MemoryStream memoryStream = new MemoryStream();
			using (Dump dump = new Dump(memoryStream))
			{
				using (new Dump.AutoXml(dump, "nodes"))
				{
					dump.VisitNode(subtree);
				}
			}
			return Dump.DefaultEncoding.GetString(memoryStream.ToArray());
		}

		// Token: 0x06002D03 RID: 11523 RVA: 0x0008F60C File Offset: 0x0008D80C
		void IDisposable.Dispose()
		{
			GC.SuppressFinalize(this);
			try
			{
				this._writer.WriteEndDocument();
				this._writer.Flush();
				this._writer.Close();
			}
			catch (Exception ex)
			{
				if (!ex.IsCatchableExceptionType())
				{
					throw;
				}
			}
		}

		// Token: 0x06002D04 RID: 11524 RVA: 0x0008F660 File Offset: 0x0008D860
		internal void Begin(string name, Dictionary<string, object> attrs)
		{
			this._writer.WriteStartElement(name);
			if (attrs != null)
			{
				foreach (KeyValuePair<string, object> keyValuePair in attrs)
				{
					this._writer.WriteAttributeString(keyValuePair.Key, keyValuePair.Value.ToString());
				}
			}
		}

		// Token: 0x06002D05 RID: 11525 RVA: 0x0008F6D4 File Offset: 0x0008D8D4
		internal void BeginExpression()
		{
			this.WriteString("(");
		}

		// Token: 0x06002D06 RID: 11526 RVA: 0x0008F6E1 File Offset: 0x0008D8E1
		internal void EndExpression()
		{
			this.WriteString(")");
		}

		// Token: 0x06002D07 RID: 11527 RVA: 0x0008F6EE File Offset: 0x0008D8EE
		internal void End()
		{
			this._writer.WriteEndElement();
		}

		// Token: 0x06002D08 RID: 11528 RVA: 0x0008F6FB File Offset: 0x0008D8FB
		internal void WriteString(string value)
		{
			this._writer.WriteString(value);
		}

		// Token: 0x06002D09 RID: 11529 RVA: 0x0008F70C File Offset: 0x0008D90C
		protected override void VisitDefault(Node n)
		{
			using (new Dump.AutoXml(this, n.Op))
			{
				base.VisitDefault(n);
			}
		}

		// Token: 0x06002D0A RID: 11530 RVA: 0x0008F750 File Offset: 0x0008D950
		protected override void VisitScalarOpDefault(ScalarOp op, Node n)
		{
			using (new Dump.AutoString(this, op))
			{
				string text = string.Empty;
				foreach (Node node in n.Children)
				{
					this.WriteString(text);
					this.VisitNode(node);
					text = ",";
				}
			}
		}

		// Token: 0x06002D0B RID: 11531 RVA: 0x0008F7DC File Offset: 0x0008D9DC
		protected override void VisitJoinOp(JoinBaseOp op, Node n)
		{
			using (new Dump.AutoXml(this, op))
			{
				if (n.Children.Count > 2)
				{
					using (new Dump.AutoXml(this, "condition"))
					{
						this.VisitNode(n.Child2);
					}
				}
				using (new Dump.AutoXml(this, "input"))
				{
					this.VisitNode(n.Child0);
				}
				using (new Dump.AutoXml(this, "input"))
				{
					this.VisitNode(n.Child1);
				}
			}
		}

		// Token: 0x06002D0C RID: 11532 RVA: 0x0008F8BC File Offset: 0x0008DABC
		public override void Visit(CaseOp op, Node n)
		{
			using (new Dump.AutoXml(this, op))
			{
				int i = 0;
				while (i < n.Children.Count)
				{
					if (i + 1 < n.Children.Count)
					{
						using (new Dump.AutoXml(this, "when"))
						{
							this.VisitNode(n.Children[i++]);
						}
						using (new Dump.AutoXml(this, "then"))
						{
							this.VisitNode(n.Children[i++]);
							continue;
						}
					}
					using (new Dump.AutoXml(this, "else"))
					{
						this.VisitNode(n.Children[i++]);
					}
				}
			}
		}

		// Token: 0x06002D0D RID: 11533 RVA: 0x0008F9D4 File Offset: 0x0008DBD4
		public override void Visit(CollectOp op, Node n)
		{
			using (new Dump.AutoXml(this, op))
			{
				this.VisitChildren(n);
			}
		}

		// Token: 0x06002D0E RID: 11534 RVA: 0x0008FA14 File Offset: 0x0008DC14
		protected override void VisitConstantOp(ConstantBaseOp op, Node n)
		{
			using (new Dump.AutoString(this, op))
			{
				if (op.Value == null)
				{
					this.WriteString("null");
				}
				else
				{
					this.WriteString("(");
					this.WriteString(op.Type.EdmType.FullName);
					this.WriteString(")");
					this.WriteString(string.Format(CultureInfo.InvariantCulture, "{0}", new object[] { op.Value }));
				}
				this.VisitChildren(n);
			}
		}

		// Token: 0x06002D0F RID: 11535 RVA: 0x0008FAB8 File Offset: 0x0008DCB8
		public override void Visit(DistinctOp op, Node n)
		{
			Dictionary<string, object> dictionary = new Dictionary<string, object>();
			StringBuilder stringBuilder = new StringBuilder();
			string text = string.Empty;
			foreach (Var var in op.Keys)
			{
				stringBuilder.Append(text);
				stringBuilder.Append(var.Id);
				text = ",";
			}
			if (stringBuilder.Length != 0)
			{
				dictionary.Add("Keys", stringBuilder.ToString());
			}
			using (new Dump.AutoXml(this, op, dictionary))
			{
				this.VisitChildren(n);
			}
		}

		// Token: 0x06002D10 RID: 11536 RVA: 0x0008FB74 File Offset: 0x0008DD74
		protected override void VisitGroupByOp(GroupByBaseOp op, Node n)
		{
			Dictionary<string, object> dictionary = new Dictionary<string, object>();
			StringBuilder stringBuilder = new StringBuilder();
			string text = string.Empty;
			foreach (Var var in op.Keys)
			{
				stringBuilder.Append(text);
				stringBuilder.Append(var.Id);
				text = ",";
			}
			if (stringBuilder.Length != 0)
			{
				dictionary.Add("Keys", stringBuilder.ToString());
			}
			using (new Dump.AutoXml(this, op, dictionary))
			{
				using (new Dump.AutoXml(this, "outputs"))
				{
					foreach (Var var2 in op.Outputs)
					{
						this.DumpVar(var2);
					}
				}
				this.VisitChildren(n);
			}
		}

		// Token: 0x06002D11 RID: 11537 RVA: 0x0008FC94 File Offset: 0x0008DE94
		public override void Visit(IsOfOp op, Node n)
		{
			using (new Dump.AutoXml(this, op.IsOfOnly ? "IsOfOnly" : "IsOf"))
			{
				string text = string.Empty;
				foreach (Node node in n.Children)
				{
					this.WriteString(text);
					this.VisitNode(node);
					text = ",";
				}
			}
		}

		// Token: 0x06002D12 RID: 11538 RVA: 0x0008FD30 File Offset: 0x0008DF30
		protected override void VisitNestOp(NestBaseOp op, Node n)
		{
			Dictionary<string, object> dictionary = new Dictionary<string, object>();
			SingleStreamNestOp singleStreamNestOp = op as SingleStreamNestOp;
			if (singleStreamNestOp != null)
			{
				dictionary.Add("Discriminator", (singleStreamNestOp.Discriminator == null) ? "<null>" : singleStreamNestOp.Discriminator.ToString());
			}
			StringBuilder stringBuilder = new StringBuilder();
			if (singleStreamNestOp != null)
			{
				stringBuilder.Length = 0;
				string text = string.Empty;
				foreach (Var var in singleStreamNestOp.Keys)
				{
					stringBuilder.Append(text);
					stringBuilder.Append(var.Id);
					text = ",";
				}
				if (stringBuilder.Length != 0)
				{
					dictionary.Add("Keys", stringBuilder.ToString());
				}
			}
			using (new Dump.AutoXml(this, op, dictionary))
			{
				using (new Dump.AutoXml(this, "outputs"))
				{
					foreach (Var var2 in op.Outputs)
					{
						this.DumpVar(var2);
					}
				}
				foreach (CollectionInfo collectionInfo in op.CollectionInfo)
				{
					Dictionary<string, object> dictionary2 = new Dictionary<string, object>();
					dictionary2.Add("CollectionVar", collectionInfo.CollectionVar);
					if (collectionInfo.DiscriminatorValue != null)
					{
						dictionary2.Add("DiscriminatorValue", collectionInfo.DiscriminatorValue);
					}
					if (collectionInfo.FlattenedElementVars.Count != 0)
					{
						dictionary2.Add("FlattenedElementVars", Dump.FormatVarList(stringBuilder, collectionInfo.FlattenedElementVars));
					}
					if (collectionInfo.Keys.Count != 0)
					{
						dictionary2.Add("Keys", collectionInfo.Keys);
					}
					if (collectionInfo.SortKeys.Count != 0)
					{
						dictionary2.Add("SortKeys", Dump.FormatVarList(stringBuilder, collectionInfo.SortKeys));
					}
					using (new Dump.AutoXml(this, "collection", dictionary2))
					{
						collectionInfo.ColumnMap.Accept<Dump>(Dump.ColumnMapDumper.Instance, this);
					}
				}
				this.VisitChildren(n);
			}
		}

		// Token: 0x06002D13 RID: 11539 RVA: 0x00090000 File Offset: 0x0008E200
		private static string FormatVarList(StringBuilder sb, VarList varList)
		{
			sb.Length = 0;
			string text = string.Empty;
			foreach (Var var in varList)
			{
				sb.Append(text);
				sb.Append(var.Id);
				text = ",";
			}
			return sb.ToString();
		}

		// Token: 0x06002D14 RID: 11540 RVA: 0x00090078 File Offset: 0x0008E278
		private static string FormatVarList(StringBuilder sb, List<SortKey> varList)
		{
			sb.Length = 0;
			string text = string.Empty;
			foreach (SortKey sortKey in varList)
			{
				sb.Append(text);
				sb.Append(sortKey.Var.Id);
				text = ",";
			}
			return sb.ToString();
		}

		// Token: 0x06002D15 RID: 11541 RVA: 0x000900F4 File Offset: 0x0008E2F4
		private void VisitNewOp(Op op, Node n)
		{
			using (new Dump.AutoXml(this, op))
			{
				foreach (Node node in n.Children)
				{
					using (new Dump.AutoXml(this, "argument", null))
					{
						this.VisitNode(node);
					}
				}
			}
		}

		// Token: 0x06002D16 RID: 11542 RVA: 0x00090194 File Offset: 0x0008E394
		public override void Visit(NewEntityOp op, Node n)
		{
			this.VisitNewOp(op, n);
		}

		// Token: 0x06002D17 RID: 11543 RVA: 0x0009019E File Offset: 0x0008E39E
		public override void Visit(NewInstanceOp op, Node n)
		{
			this.VisitNewOp(op, n);
		}

		// Token: 0x06002D18 RID: 11544 RVA: 0x000901A8 File Offset: 0x0008E3A8
		public override void Visit(DiscriminatedNewEntityOp op, Node n)
		{
			this.VisitNewOp(op, n);
		}

		// Token: 0x06002D19 RID: 11545 RVA: 0x000901B2 File Offset: 0x0008E3B2
		public override void Visit(NewMultisetOp op, Node n)
		{
			this.VisitNewOp(op, n);
		}

		// Token: 0x06002D1A RID: 11546 RVA: 0x000901BC File Offset: 0x0008E3BC
		public override void Visit(NewRecordOp op, Node n)
		{
			this.VisitNewOp(op, n);
		}

		// Token: 0x06002D1B RID: 11547 RVA: 0x000901C8 File Offset: 0x0008E3C8
		public override void Visit(PhysicalProjectOp op, Node n)
		{
			using (new Dump.AutoXml(this, op))
			{
				using (new Dump.AutoXml(this, "outputs"))
				{
					foreach (Var var in op.Outputs)
					{
						this.DumpVar(var);
					}
				}
				using (new Dump.AutoXml(this, "columnMap"))
				{
					op.ColumnMap.Accept<Dump>(Dump.ColumnMapDumper.Instance, this);
				}
				using (new Dump.AutoXml(this, "input"))
				{
					this.VisitChildren(n);
				}
			}
		}

		// Token: 0x06002D1C RID: 11548 RVA: 0x000902CC File Offset: 0x0008E4CC
		public override void Visit(ProjectOp op, Node n)
		{
			using (new Dump.AutoXml(this, op))
			{
				using (new Dump.AutoXml(this, "outputs"))
				{
					foreach (Var var in op.Outputs)
					{
						this.DumpVar(var);
					}
				}
				this.VisitChildren(n);
			}
		}

		// Token: 0x06002D1D RID: 11549 RVA: 0x0009036C File Offset: 0x0008E56C
		public override void Visit(PropertyOp op, Node n)
		{
			using (new Dump.AutoString(this, op))
			{
				this.VisitChildren(n);
				this.WriteString(".");
				this.WriteString(op.PropertyInfo.Name);
			}
		}

		// Token: 0x06002D1E RID: 11550 RVA: 0x000903C8 File Offset: 0x0008E5C8
		public override void Visit(RelPropertyOp op, Node n)
		{
			using (new Dump.AutoString(this, op))
			{
				this.VisitChildren(n);
				this.WriteString(".NAVIGATE(");
				this.WriteString(op.PropertyInfo.Relationship.Name);
				this.WriteString(",");
				this.WriteString(op.PropertyInfo.FromEnd.Name);
				this.WriteString(",");
				this.WriteString(op.PropertyInfo.ToEnd.Name);
				this.WriteString(")");
			}
		}

		// Token: 0x06002D1F RID: 11551 RVA: 0x00090474 File Offset: 0x0008E674
		public override void Visit(ScanTableOp op, Node n)
		{
			using (new Dump.AutoXml(this, op))
			{
				this.DumpTable(op.Table);
				this.VisitChildren(n);
			}
		}

		// Token: 0x06002D20 RID: 11552 RVA: 0x000904C0 File Offset: 0x0008E6C0
		public override void Visit(ScanViewOp op, Node n)
		{
			using (new Dump.AutoXml(this, op))
			{
				this.DumpTable(op.Table);
				this.VisitChildren(n);
			}
		}

		// Token: 0x06002D21 RID: 11553 RVA: 0x0009050C File Offset: 0x0008E70C
		protected override void VisitSetOp(SetOp op, Node n)
		{
			Dictionary<string, object> dictionary = new Dictionary<string, object>();
			if (OpType.UnionAll == op.OpType)
			{
				UnionAllOp unionAllOp = (UnionAllOp)op;
				if (unionAllOp.BranchDiscriminator != null)
				{
					dictionary.Add("branchDiscriminator", unionAllOp.BranchDiscriminator);
				}
			}
			using (new Dump.AutoXml(this, op, dictionary))
			{
				using (new Dump.AutoXml(this, "outputs"))
				{
					foreach (Var var in op.Outputs)
					{
						this.DumpVar(var);
					}
				}
				int num = 0;
				foreach (Node node in n.Children)
				{
					using (new Dump.AutoXml(this, "input", new Dictionary<string, object> { 
					{
						"VarMap",
						op.VarMap[num++].ToString()
					} }))
					{
						this.VisitNode(node);
					}
				}
			}
		}

		// Token: 0x06002D22 RID: 11554 RVA: 0x00090674 File Offset: 0x0008E874
		public override void Visit(SortOp op, Node n)
		{
			using (new Dump.AutoXml(this, op))
			{
				base.Visit(op, n);
			}
		}

		// Token: 0x06002D23 RID: 11555 RVA: 0x000906B4 File Offset: 0x0008E8B4
		public override void Visit(ConstrainedSortOp op, Node n)
		{
			using (new Dump.AutoXml(this, op, new Dictionary<string, object> { { "WithTies", op.WithTies } }))
			{
				base.Visit(op, n);
			}
		}

		// Token: 0x06002D24 RID: 11556 RVA: 0x00090710 File Offset: 0x0008E910
		protected override void VisitSortOp(SortBaseOp op, Node n)
		{
			using (new Dump.AutoXml(this, "keys"))
			{
				foreach (SortKey sortKey in op.Keys)
				{
					using (new Dump.AutoXml(this, "sortKey", new Dictionary<string, object>
					{
						{ "Var", sortKey.Var },
						{ "Ascending", sortKey.AscendingSort },
						{ "Collation", sortKey.Collation }
					}))
					{
					}
				}
			}
			this.VisitChildren(n);
		}

		// Token: 0x06002D25 RID: 11557 RVA: 0x000907F4 File Offset: 0x0008E9F4
		public override void Visit(UnnestOp op, Node n)
		{
			Dictionary<string, object> dictionary = new Dictionary<string, object>();
			if (op.Var != null)
			{
				dictionary.Add("Var", op.Var.Id);
			}
			using (new Dump.AutoXml(this, op, dictionary))
			{
				this.DumpTable(op.Table);
				this.VisitChildren(n);
			}
		}

		// Token: 0x06002D26 RID: 11558 RVA: 0x00090868 File Offset: 0x0008EA68
		public override void Visit(VarDefOp op, Node n)
		{
			using (new Dump.AutoXml(this, op, new Dictionary<string, object> { 
			{
				"Var",
				op.Var.Id
			} }))
			{
				this.VisitChildren(n);
			}
		}

		// Token: 0x06002D27 RID: 11559 RVA: 0x000908C8 File Offset: 0x0008EAC8
		public override void Visit(VarRefOp op, Node n)
		{
			using (new Dump.AutoString(this, op))
			{
				this.VisitChildren(n);
				if (op.Type != null)
				{
					this.WriteString("Type=");
					this.WriteString(op.Type.ToString());
					this.WriteString(", ");
				}
				this.WriteString("Var=");
				this.WriteString(op.Var.Id.ToString(CultureInfo.InvariantCulture));
			}
		}

		// Token: 0x06002D28 RID: 11560 RVA: 0x00090960 File Offset: 0x0008EB60
		private void DumpVar(Var v)
		{
			Dictionary<string, object> dictionary = new Dictionary<string, object>();
			dictionary.Add("Var", v.Id);
			ColumnVar columnVar = v as ColumnVar;
			if (columnVar != null)
			{
				dictionary.Add("Name", columnVar.ColumnMetadata.Name);
				dictionary.Add("Type", columnVar.ColumnMetadata.Type.ToString());
			}
			using (new Dump.AutoXml(this, v.GetType().Name, dictionary))
			{
			}
		}

		// Token: 0x06002D29 RID: 11561 RVA: 0x000909F8 File Offset: 0x0008EBF8
		private void DumpVars(List<Var> vars)
		{
			foreach (Var var in vars)
			{
				this.DumpVar(var);
			}
		}

		// Token: 0x06002D2A RID: 11562 RVA: 0x00090A48 File Offset: 0x0008EC48
		private void DumpTable(Table table)
		{
			Dictionary<string, object> dictionary = new Dictionary<string, object>();
			dictionary.Add("Table", table.TableId);
			if (table.TableMetadata.Extent != null)
			{
				dictionary.Add("Extent", table.TableMetadata.Extent.Name);
			}
			using (new Dump.AutoXml(this, "Table", dictionary))
			{
				this.DumpVars(table.Columns);
			}
		}

		// Token: 0x04000F1E RID: 3870
		private readonly XmlWriter _writer;

		// Token: 0x04000F1F RID: 3871
		internal static readonly Encoding DefaultEncoding = Encoding.UTF8;

		// Token: 0x02000A03 RID: 2563
		internal class ColumnMapDumper : ColumnMapVisitor<Dump>
		{
			// Token: 0x06006093 RID: 24723 RVA: 0x0014AB43 File Offset: 0x00148D43
			private ColumnMapDumper()
			{
			}

			// Token: 0x06006094 RID: 24724 RVA: 0x0014AB4C File Offset: 0x00148D4C
			private void DumpCollection(CollectionColumnMap columnMap, Dump dumper)
			{
				if (columnMap.ForeignKeys.Length != 0)
				{
					using (new Dump.AutoXml(dumper, "foreignKeys"))
					{
						base.VisitList<SimpleColumnMap>(columnMap.ForeignKeys, dumper);
					}
				}
				if (columnMap.Keys.Length != 0)
				{
					using (new Dump.AutoXml(dumper, "keys"))
					{
						base.VisitList<SimpleColumnMap>(columnMap.Keys, dumper);
					}
				}
				using (new Dump.AutoXml(dumper, "element"))
				{
					columnMap.Element.Accept<Dump>(this, dumper);
				}
			}

			// Token: 0x06006095 RID: 24725 RVA: 0x0014AC14 File Offset: 0x00148E14
			private static Dictionary<string, object> GetAttributes(ColumnMap columnMap)
			{
				return new Dictionary<string, object> { 
				{
					"Type",
					columnMap.Type.ToString()
				} };
			}

			// Token: 0x06006096 RID: 24726 RVA: 0x0014AC34 File Offset: 0x00148E34
			internal override void Visit(ComplexTypeColumnMap columnMap, Dump dumper)
			{
				using (new Dump.AutoXml(dumper, "ComplexType", Dump.ColumnMapDumper.GetAttributes(columnMap)))
				{
					if (columnMap.NullSentinel != null)
					{
						using (new Dump.AutoXml(dumper, "nullSentinel"))
						{
							columnMap.NullSentinel.Accept<Dump>(this, dumper);
						}
					}
					base.VisitList<ColumnMap>(columnMap.Properties, dumper);
				}
			}

			// Token: 0x06006097 RID: 24727 RVA: 0x0014ACC0 File Offset: 0x00148EC0
			internal override void Visit(DiscriminatedCollectionColumnMap columnMap, Dump dumper)
			{
				using (new Dump.AutoXml(dumper, "DiscriminatedCollection", Dump.ColumnMapDumper.GetAttributes(columnMap)))
				{
					using (new Dump.AutoXml(dumper, "discriminator", new Dictionary<string, object> { { "Value", columnMap.DiscriminatorValue } }))
					{
						columnMap.Discriminator.Accept<Dump>(this, dumper);
					}
					this.DumpCollection(columnMap, dumper);
				}
			}

			// Token: 0x06006098 RID: 24728 RVA: 0x0014AD58 File Offset: 0x00148F58
			internal override void Visit(EntityColumnMap columnMap, Dump dumper)
			{
				using (new Dump.AutoXml(dumper, "Entity", Dump.ColumnMapDumper.GetAttributes(columnMap)))
				{
					using (new Dump.AutoXml(dumper, "entityIdentity"))
					{
						base.VisitEntityIdentity(columnMap.EntityIdentity, dumper);
					}
					base.VisitList<ColumnMap>(columnMap.Properties, dumper);
				}
			}

			// Token: 0x06006099 RID: 24729 RVA: 0x0014ADDC File Offset: 0x00148FDC
			internal override void Visit(SimplePolymorphicColumnMap columnMap, Dump dumper)
			{
				using (new Dump.AutoXml(dumper, "SimplePolymorphic", Dump.ColumnMapDumper.GetAttributes(columnMap)))
				{
					using (new Dump.AutoXml(dumper, "typeDiscriminator"))
					{
						columnMap.TypeDiscriminator.Accept<Dump>(this, dumper);
					}
					Dictionary<string, object> dictionary = new Dictionary<string, object>();
					foreach (KeyValuePair<object, TypedColumnMap> keyValuePair in columnMap.TypeChoices)
					{
						dictionary.Clear();
						dictionary.Add("DiscriminatorValue", keyValuePair.Key);
						using (new Dump.AutoXml(dumper, "choice", dictionary))
						{
							keyValuePair.Value.Accept<Dump>(this, dumper);
						}
					}
					using (new Dump.AutoXml(dumper, "default"))
					{
						base.VisitList<ColumnMap>(columnMap.Properties, dumper);
					}
				}
			}

			// Token: 0x0600609A RID: 24730 RVA: 0x0014AF18 File Offset: 0x00149118
			internal override void Visit(MultipleDiscriminatorPolymorphicColumnMap columnMap, Dump dumper)
			{
				using (new Dump.AutoXml(dumper, "MultipleDiscriminatorPolymorphic", Dump.ColumnMapDumper.GetAttributes(columnMap)))
				{
					using (new Dump.AutoXml(dumper, "typeDiscriminators"))
					{
						base.VisitList<SimpleColumnMap>(columnMap.TypeDiscriminators, dumper);
					}
					Dictionary<string, object> dictionary = new Dictionary<string, object>();
					foreach (KeyValuePair<EntityType, TypedColumnMap> keyValuePair in columnMap.TypeChoices)
					{
						dictionary.Clear();
						dictionary.Add("EntityType", keyValuePair.Key);
						using (new Dump.AutoXml(dumper, "choice", dictionary))
						{
							keyValuePair.Value.Accept<Dump>(this, dumper);
						}
					}
					using (new Dump.AutoXml(dumper, "default"))
					{
						base.VisitList<ColumnMap>(columnMap.Properties, dumper);
					}
				}
			}

			// Token: 0x0600609B RID: 24731 RVA: 0x0014B054 File Offset: 0x00149254
			internal override void Visit(RecordColumnMap columnMap, Dump dumper)
			{
				using (new Dump.AutoXml(dumper, "Record", Dump.ColumnMapDumper.GetAttributes(columnMap)))
				{
					if (columnMap.NullSentinel != null)
					{
						using (new Dump.AutoXml(dumper, "nullSentinel"))
						{
							columnMap.NullSentinel.Accept<Dump>(this, dumper);
						}
					}
					base.VisitList<ColumnMap>(columnMap.Properties, dumper);
				}
			}

			// Token: 0x0600609C RID: 24732 RVA: 0x0014B0E0 File Offset: 0x001492E0
			internal override void Visit(RefColumnMap columnMap, Dump dumper)
			{
				using (new Dump.AutoXml(dumper, "Ref", Dump.ColumnMapDumper.GetAttributes(columnMap)))
				{
					using (new Dump.AutoXml(dumper, "entityIdentity"))
					{
						base.VisitEntityIdentity(columnMap.EntityIdentity, dumper);
					}
				}
			}

			// Token: 0x0600609D RID: 24733 RVA: 0x0014B154 File Offset: 0x00149354
			internal override void Visit(SimpleCollectionColumnMap columnMap, Dump dumper)
			{
				using (new Dump.AutoXml(dumper, "SimpleCollection", Dump.ColumnMapDumper.GetAttributes(columnMap)))
				{
					this.DumpCollection(columnMap, dumper);
				}
			}

			// Token: 0x0600609E RID: 24734 RVA: 0x0014B19C File Offset: 0x0014939C
			internal override void Visit(ScalarColumnMap columnMap, Dump dumper)
			{
				Dictionary<string, object> attributes = Dump.ColumnMapDumper.GetAttributes(columnMap);
				attributes.Add("CommandId", columnMap.CommandId);
				attributes.Add("ColumnPos", columnMap.ColumnPos);
				using (new Dump.AutoXml(dumper, "AssignedSimple", attributes))
				{
				}
			}

			// Token: 0x0600609F RID: 24735 RVA: 0x0014B20C File Offset: 0x0014940C
			internal override void Visit(VarRefColumnMap columnMap, Dump dumper)
			{
				Dictionary<string, object> attributes = Dump.ColumnMapDumper.GetAttributes(columnMap);
				attributes.Add("Var", columnMap.Var.Id);
				using (new Dump.AutoXml(dumper, "VarRef", attributes))
				{
				}
			}

			// Token: 0x060060A0 RID: 24736 RVA: 0x0014B26C File Offset: 0x0014946C
			protected override void VisitEntityIdentity(DiscriminatedEntityIdentity entityIdentity, Dump dumper)
			{
				using (new Dump.AutoXml(dumper, "DiscriminatedEntityIdentity"))
				{
					using (new Dump.AutoXml(dumper, "entitySetId"))
					{
						entityIdentity.EntitySetColumnMap.Accept<Dump>(this, dumper);
					}
					if (entityIdentity.Keys.Length != 0)
					{
						using (new Dump.AutoXml(dumper, "keys"))
						{
							base.VisitList<SimpleColumnMap>(entityIdentity.Keys, dumper);
						}
					}
				}
			}

			// Token: 0x060060A1 RID: 24737 RVA: 0x0014B31C File Offset: 0x0014951C
			protected override void VisitEntityIdentity(SimpleEntityIdentity entityIdentity, Dump dumper)
			{
				using (new Dump.AutoXml(dumper, "SimpleEntityIdentity"))
				{
					if (entityIdentity.Keys.Length != 0)
					{
						using (new Dump.AutoXml(dumper, "keys"))
						{
							base.VisitList<SimpleColumnMap>(entityIdentity.Keys, dumper);
						}
					}
				}
			}

			// Token: 0x04002905 RID: 10501
			internal static Dump.ColumnMapDumper Instance = new Dump.ColumnMapDumper();
		}

		// Token: 0x02000A04 RID: 2564
		internal struct AutoString : IDisposable
		{
			// Token: 0x060060A3 RID: 24739 RVA: 0x0014B3A0 File Offset: 0x001495A0
			internal AutoString(Dump dumper, Op op)
			{
				this._dumper = dumper;
				this._dumper.WriteString(Dump.AutoString.ToString(op.OpType));
				this._dumper.BeginExpression();
			}

			// Token: 0x060060A4 RID: 24740 RVA: 0x0014B3CC File Offset: 0x001495CC
			public void Dispose()
			{
				try
				{
					this._dumper.EndExpression();
				}
				catch (Exception ex)
				{
					if (!ex.IsCatchableExceptionType())
					{
						throw;
					}
				}
			}

			// Token: 0x060060A5 RID: 24741 RVA: 0x0014B404 File Offset: 0x00149604
			internal static string ToString(OpType op)
			{
				switch (op)
				{
				case OpType.Constant:
					return "Constant";
				case OpType.InternalConstant:
					return "InternalConstant";
				case OpType.NullSentinel:
					return "NullSentinel";
				case OpType.Null:
					return "Null";
				case OpType.ConstantPredicate:
					return "ConstantPredicate";
				case OpType.VarRef:
					return "VarRef";
				case OpType.GT:
					return "GT";
				case OpType.GE:
					return "GE";
				case OpType.LE:
					return "LE";
				case OpType.LT:
					return "LT";
				case OpType.EQ:
					return "EQ";
				case OpType.NE:
					return "NE";
				case OpType.Like:
					return "Like";
				case OpType.Plus:
					return "Plus";
				case OpType.Minus:
					return "Minus";
				case OpType.Multiply:
					return "Multiply";
				case OpType.Divide:
					return "Divide";
				case OpType.Modulo:
					return "Modulo";
				case OpType.UnaryMinus:
					return "UnaryMinus";
				case OpType.And:
					return "And";
				case OpType.Or:
					return "Or";
				case OpType.In:
					return "In";
				case OpType.Not:
					return "Not";
				case OpType.IsNull:
					return "IsNull";
				case OpType.Case:
					return "Case";
				case OpType.Treat:
					return "Treat";
				case OpType.IsOf:
					return "IsOf";
				case OpType.Cast:
					return "Cast";
				case OpType.SoftCast:
					return "SoftCast";
				case OpType.Aggregate:
					return "Aggregate";
				case OpType.Function:
					return "Function";
				case OpType.RelProperty:
					return "RelProperty";
				case OpType.Property:
					return "Property";
				case OpType.NewEntity:
					return "NewEntity";
				case OpType.NewInstance:
					return "NewInstance";
				case OpType.DiscriminatedNewEntity:
					return "DiscriminatedNewEntity";
				case OpType.NewMultiset:
					return "NewMultiset";
				case OpType.NewRecord:
					return "NewRecord";
				case OpType.GetRefKey:
					return "GetRefKey";
				case OpType.GetEntityRef:
					return "GetEntityRef";
				case OpType.Ref:
					return "Ref";
				case OpType.Exists:
					return "Exists";
				case OpType.Element:
					return "Element";
				case OpType.Collect:
					return "Collect";
				case OpType.Deref:
					return "Deref";
				case OpType.Navigate:
					return "Navigate";
				case OpType.ScanTable:
					return "ScanTable";
				case OpType.ScanView:
					return "ScanView";
				case OpType.Filter:
					return "Filter";
				case OpType.Project:
					return "Project";
				case OpType.InnerJoin:
					return "InnerJoin";
				case OpType.LeftOuterJoin:
					return "LeftOuterJoin";
				case OpType.FullOuterJoin:
					return "FullOuterJoin";
				case OpType.CrossJoin:
					return "CrossJoin";
				case OpType.CrossApply:
					return "CrossApply";
				case OpType.OuterApply:
					return "OuterApply";
				case OpType.Unnest:
					return "Unnest";
				case OpType.Sort:
					return "Sort";
				case OpType.ConstrainedSort:
					return "ConstrainedSort";
				case OpType.GroupBy:
					return "GroupBy";
				case OpType.GroupByInto:
					return "GroupByInto";
				case OpType.UnionAll:
					return "UnionAll";
				case OpType.Intersect:
					return "Intersect";
				case OpType.Except:
					return "Except";
				case OpType.Distinct:
					return "Distinct";
				case OpType.SingleRow:
					return "SingleRow";
				case OpType.SingleRowTable:
					return "SingleRowTable";
				case OpType.VarDef:
					return "VarDef";
				case OpType.VarDefList:
					return "VarDefList";
				case OpType.Leaf:
					return "Leaf";
				case OpType.PhysicalProject:
					return "PhysicalProject";
				case OpType.SingleStreamNest:
					return "SingleStreamNest";
				case OpType.MultiStreamNest:
					return "MultiStreamNest";
				default:
					return op.ToString();
				}
			}

			// Token: 0x04002906 RID: 10502
			private readonly Dump _dumper;
		}

		// Token: 0x02000A05 RID: 2565
		internal struct AutoXml : IDisposable
		{
			// Token: 0x060060A6 RID: 24742 RVA: 0x0014B704 File Offset: 0x00149904
			internal AutoXml(Dump dumper, Op op)
			{
				this._dumper = dumper;
				this._nodeName = Dump.AutoString.ToString(op.OpType);
				Dictionary<string, object> dictionary = new Dictionary<string, object>();
				if (op.Type != null)
				{
					dictionary.Add("Type", op.Type.ToString());
				}
				this._dumper.Begin(this._nodeName, dictionary);
			}

			// Token: 0x060060A7 RID: 24743 RVA: 0x0014B760 File Offset: 0x00149960
			internal AutoXml(Dump dumper, Op op, Dictionary<string, object> attrs)
			{
				this._dumper = dumper;
				this._nodeName = Dump.AutoString.ToString(op.OpType);
				Dictionary<string, object> dictionary = new Dictionary<string, object>();
				if (op.Type != null)
				{
					dictionary.Add("Type", op.Type.ToString());
				}
				foreach (KeyValuePair<string, object> keyValuePair in attrs)
				{
					dictionary.Add(keyValuePair.Key, keyValuePair.Value);
				}
				this._dumper.Begin(this._nodeName, dictionary);
			}

			// Token: 0x060060A8 RID: 24744 RVA: 0x0014B80C File Offset: 0x00149A0C
			internal AutoXml(Dump dumper, string nodeName)
			{
				this = new Dump.AutoXml(dumper, nodeName, null);
			}

			// Token: 0x060060A9 RID: 24745 RVA: 0x0014B817 File Offset: 0x00149A17
			internal AutoXml(Dump dumper, string nodeName, Dictionary<string, object> attrs)
			{
				this._dumper = dumper;
				this._nodeName = nodeName;
				this._dumper.Begin(this._nodeName, attrs);
			}

			// Token: 0x060060AA RID: 24746 RVA: 0x0014B839 File Offset: 0x00149A39
			public void Dispose()
			{
				this._dumper.End();
			}

			// Token: 0x04002907 RID: 10503
			private readonly string _nodeName;

			// Token: 0x04002908 RID: 10504
			private readonly Dump _dumper;
		}
	}
}
