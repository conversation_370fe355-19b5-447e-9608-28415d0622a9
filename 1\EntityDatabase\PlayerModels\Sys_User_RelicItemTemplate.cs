﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000034 RID: 52
	public class Sys_User_RelicItemTemplate
	{
		// Token: 0x17000199 RID: 409
		// (get) Token: 0x06000365 RID: 869 RVA: 0x00003D57 File Offset: 0x00001F57
		// (set) Token: 0x06000366 RID: 870 RVA: 0x00003D5F File Offset: 0x00001F5F
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x1700019A RID: 410
		// (get) Token: 0x06000367 RID: 871 RVA: 0x00003D68 File Offset: 0x00001F68
		// (set) Token: 0x06000368 RID: 872 RVA: 0x00003D70 File Offset: 0x00001F70
		public int UserID { get; set; }

		// Token: 0x1700019B RID: 411
		// (get) Token: 0x06000369 RID: 873 RVA: 0x00003D79 File Offset: 0x00001F79
		// (set) Token: 0x0600036A RID: 874 RVA: 0x00003D81 File Offset: 0x00001F81
		public int Type { get; set; }

		// Token: 0x1700019C RID: 412
		// (get) Token: 0x0600036B RID: 875 RVA: 0x00003D8A File Offset: 0x00001F8A
		// (set) Token: 0x0600036C RID: 876 RVA: 0x00003D92 File Offset: 0x00001F92
		public int itemID { get; set; }

		// Token: 0x1700019D RID: 413
		// (get) Token: 0x0600036D RID: 877 RVA: 0x00003D9B File Offset: 0x00001F9B
		// (set) Token: 0x0600036E RID: 878 RVA: 0x00003DA3 File Offset: 0x00001FA3
		public int level { get; set; }

		// Token: 0x1700019E RID: 414
		// (get) Token: 0x0600036F RID: 879 RVA: 0x00003DAC File Offset: 0x00001FAC
		// (set) Token: 0x06000370 RID: 880 RVA: 0x00003DB4 File Offset: 0x00001FB4
		public int stage { get; set; }

		// Token: 0x1700019F RID: 415
		// (get) Token: 0x06000371 RID: 881 RVA: 0x00003DBD File Offset: 0x00001FBD
		// (set) Token: 0x06000372 RID: 882 RVA: 0x00003DC5 File Offset: 0x00001FC5
		public int curExp { get; set; }

		// Token: 0x170001A0 RID: 416
		// (get) Token: 0x06000373 RID: 883 RVA: 0x00003DCE File Offset: 0x00001FCE
		// (set) Token: 0x06000374 RID: 884 RVA: 0x00003DD6 File Offset: 0x00001FD6
		public int ShardNum { get; set; }

		// Token: 0x170001A1 RID: 417
		// (get) Token: 0x06000375 RID: 885 RVA: 0x00003DDF File Offset: 0x00001FDF
		// (set) Token: 0x06000376 RID: 886 RVA: 0x00003DE7 File Offset: 0x00001FE7
		public int Quality { get; set; }

		// Token: 0x170001A2 RID: 418
		// (get) Token: 0x06000377 RID: 887 RVA: 0x00003DF0 File Offset: 0x00001FF0
		// (set) Token: 0x06000378 RID: 888 RVA: 0x00003DF8 File Offset: 0x00001FF8
		public string _exProArr { get; set; }

		// Token: 0x170001A3 RID: 419
		// (get) Token: 0x06000379 RID: 889 RVA: 0x00003E01 File Offset: 0x00002001
		// (set) Token: 0x0600037A RID: 890 RVA: 0x00003E09 File Offset: 0x00002009
		public string ProArr { get; set; }
	}
}
