﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x02000533 RID: 1331
	public sealed class FunctionImportEntityTypeMapping : FunctionImportStructuralTypeMapping
	{
		// Token: 0x060041B8 RID: 16824 RVA: 0x000DCF8E File Offset: 0x000DB18E
		public FunctionImportEntityTypeMapping(IEnumerable<EntityType> isOfTypeEntityTypes, IEnumerable<EntityType> entityTypes, Collection<FunctionImportReturnTypePropertyMapping> properties, IEnumerable<FunctionImportEntityTypeMappingCondition> conditions)
			: this(Check.NotNull<IEnumerable<EntityType>>(isOfTypeEntityTypes, "isOfTypeEntityTypes"), Check.NotNull<IEnumerable<EntityType>>(entityTypes, "entityTypes"), Check.NotNull<IEnumerable<FunctionImportEntityTypeMappingCondition>>(conditions, "conditions"), Check.NotNull<Collection<FunctionImportReturnTypePropertyMapping>>(properties, "properties"), LineInfo.Empty)
		{
		}

		// Token: 0x060041B9 RID: 16825 RVA: 0x000DCFC8 File Offset: 0x000DB1C8
		internal FunctionImportEntityTypeMapping(IEnumerable<EntityType> isOfTypeEntityTypes, IEnumerable<EntityType> entityTypes, IEnumerable<FunctionImportEntityTypeMappingCondition> conditions, Collection<FunctionImportReturnTypePropertyMapping> columnsRenameList, LineInfo lineInfo)
			: base(columnsRenameList, lineInfo)
		{
			this._isOfTypeEntityTypes = new ReadOnlyCollection<EntityType>(isOfTypeEntityTypes.ToList<EntityType>());
			this._entityTypes = new ReadOnlyCollection<EntityType>(entityTypes.ToList<EntityType>());
			this._conditions = new ReadOnlyCollection<FunctionImportEntityTypeMappingCondition>(conditions.ToList<FunctionImportEntityTypeMappingCondition>());
		}

		// Token: 0x17000D00 RID: 3328
		// (get) Token: 0x060041BA RID: 16826 RVA: 0x000DD007 File Offset: 0x000DB207
		public ReadOnlyCollection<EntityType> EntityTypes
		{
			get
			{
				return this._entityTypes;
			}
		}

		// Token: 0x17000D01 RID: 3329
		// (get) Token: 0x060041BB RID: 16827 RVA: 0x000DD00F File Offset: 0x000DB20F
		public ReadOnlyCollection<EntityType> IsOfTypeEntityTypes
		{
			get
			{
				return this._isOfTypeEntityTypes;
			}
		}

		// Token: 0x17000D02 RID: 3330
		// (get) Token: 0x060041BC RID: 16828 RVA: 0x000DD017 File Offset: 0x000DB217
		public ReadOnlyCollection<FunctionImportEntityTypeMappingCondition> Conditions
		{
			get
			{
				return this._conditions;
			}
		}

		// Token: 0x060041BD RID: 16829 RVA: 0x000DD01F File Offset: 0x000DB21F
		internal override void SetReadOnly()
		{
			MappingItem.SetReadOnly(this._conditions);
			base.SetReadOnly();
		}

		// Token: 0x060041BE RID: 16830 RVA: 0x000DD034 File Offset: 0x000DB234
		internal IEnumerable<EntityType> GetMappedEntityTypes(ItemCollection itemCollection)
		{
			return this.EntityTypes.Concat(this.IsOfTypeEntityTypes.SelectMany((EntityType entityType) => MetadataHelper.GetTypeAndSubtypesOf(entityType, itemCollection, false).Cast<EntityType>()));
		}

		// Token: 0x060041BF RID: 16831 RVA: 0x000DD070 File Offset: 0x000DB270
		internal IEnumerable<string> GetDiscriminatorColumns()
		{
			return this.Conditions.Select((FunctionImportEntityTypeMappingCondition condition) => condition.ColumnName);
		}

		// Token: 0x040016C7 RID: 5831
		private readonly ReadOnlyCollection<EntityType> _entityTypes;

		// Token: 0x040016C8 RID: 5832
		private readonly ReadOnlyCollection<EntityType> _isOfTypeEntityTypes;

		// Token: 0x040016C9 RID: 5833
		private readonly ReadOnlyCollection<FunctionImportEntityTypeMappingCondition> _conditions;
	}
}
