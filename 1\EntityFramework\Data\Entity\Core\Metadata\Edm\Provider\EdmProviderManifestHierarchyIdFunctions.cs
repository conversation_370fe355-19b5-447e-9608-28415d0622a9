﻿using System;

namespace System.Data.Entity.Core.Metadata.Edm.Provider
{
	// Token: 0x0200051C RID: 1308
	internal static class EdmProviderManifestHierarchyIdFunctions
	{
		// Token: 0x06004095 RID: 16533 RVA: 0x000D8700 File Offset: 0x000D6900
		internal static void AddFunctions(EdmProviderManifestFunctionBuilder functions)
		{
			functions.AddFunction(PrimitiveTypeKind.HierarchyId, "HierarchyIdGetRoot");
			functions.AddFunction(PrimitiveTypeKind.HierarchyId, "HierarchyIdParse", PrimitiveTypeKind.String, "input");
			functions.AddFunction(PrimitiveTypeKind.HierarchyId, "GetAncestor", PrimitiveTypeKind.HierarchyId, "hierarchyIdValue", PrimitiveTypeKind.Int32, "n");
			functions.AddFunction(PrimitiveTypeKind.HierarchyId, "GetDescendant", PrimitiveTypeKind.HierarchyId, "hierarchyIdValue", PrimitiveTypeKind.HierarchyId, "child1", PrimitiveTypeKind.HierarchyId, "child2");
			functions.AddFunction(PrimitiveTypeKind.Int16, "GetLevel", PrimitiveTypeKind.HierarchyId, "hierarchyIdValue");
			functions.AddFunction(PrimitiveTypeKind.Boolean, "IsDescendantOf", PrimitiveTypeKind.HierarchyId, "hierarchyIdValue", PrimitiveTypeKind.HierarchyId, "parent");
			functions.AddFunction(PrimitiveTypeKind.HierarchyId, "GetReparentedValue", PrimitiveTypeKind.HierarchyId, "hierarchyIdValue", PrimitiveTypeKind.HierarchyId, "oldRoot", PrimitiveTypeKind.HierarchyId, "newRoot");
		}
	}
}
