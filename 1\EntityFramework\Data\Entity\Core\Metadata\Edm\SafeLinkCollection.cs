﻿using System;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004F9 RID: 1273
	internal class SafeLinkCollection<TParent, TChild> : ReadOnlyMetadataCollection<TChild> where TParent : class where TChild : MetadataItem
	{
		// Token: 0x06003F1B RID: 16155 RVA: 0x000D0E40 File Offset: 0x000CF040
		public SafeLinkCollection(TParent parent, Func<TChild, SafeLink<TParent>> getLink, MetadataCollection<TChild> children)
			: base((MetadataCollection<TChild>)SafeLink<TParent>.BindChildren<TChild>(parent, getLink, children))
		{
		}
	}
}
