﻿using System;
using System.Collections.Generic;
using System.Reflection;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004D0 RID: 1232
	internal abstract class MetadataArtifactAssemblyResolver
	{
		// Token: 0x06003D22 RID: 15650
		internal abstract bool TryResolveAssemblyReference(AssemblyName referenceName, out Assembly assembly);

		// Token: 0x06003D23 RID: 15651
		internal abstract IEnumerable<Assembly> GetWildcardAssemblies();
	}
}
