﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006C6 RID: 1734
	public sealed class DbIsEmptyExpression : DbUnaryExpression
	{
		// Token: 0x06005113 RID: 20755 RVA: 0x00121B60 File Offset: 0x0011FD60
		internal DbIsEmptyExpression(TypeUsage booleanResultType, DbExpression argument)
			: base(DbExpressionKind.IsEmpty, booleanResultType, argument)
		{
		}

		// Token: 0x06005114 RID: 20756 RVA: 0x00121B6C File Offset: 0x0011FD6C
		public override void Accept(DbExpressionVisitor visitor)
		{
			Check.NotNull<DbExpressionVisitor>(visitor, "visitor");
			visitor.Visit(this);
		}

		// Token: 0x06005115 RID: 20757 RVA: 0x00121B81 File Offset: 0x0011FD81
		public override TResultType Accept<TResultType>(DbExpressionVisitor<TResultType> visitor)
		{
			Check.NotNull<DbExpressionVisitor<TResultType>>(visitor, "visitor");
			return visitor.Visit(this);
		}
	}
}
