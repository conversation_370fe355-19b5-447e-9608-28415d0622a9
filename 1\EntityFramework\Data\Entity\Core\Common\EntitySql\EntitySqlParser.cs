﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.EntitySql
{
	// Token: 0x0200064C RID: 1612
	public sealed class EntitySqlParser
	{
		// Token: 0x06004DF5 RID: 19957 RVA: 0x001177E4 File Offset: 0x001159E4
		internal EntitySqlParser(Perspective perspective)
		{
			this._perspective = perspective;
		}

		// Token: 0x06004DF6 RID: 19958 RVA: 0x001177F4 File Offset: 0x001159F4
		public ParseResult Parse(string query, params DbParameterReferenceExpression[] parameters)
		{
			Check.NotNull<string>(query, "query");
			if (parameters != null)
			{
				IEnumerable<DbParameterReferenceExpression> enumerable = parameters;
				EntityUtil.CheckArgumentContainsNull<DbParameterReferenceExpression>(ref enumerable, "parameters");
			}
			return CqlQuery.Compile(query, this._perspective, null, parameters);
		}

		// Token: 0x06004DF7 RID: 19959 RVA: 0x00117830 File Offset: 0x00115A30
		public DbLambda ParseLambda(string query, params DbVariableReferenceExpression[] variables)
		{
			Check.NotNull<string>(query, "query");
			if (variables != null)
			{
				IEnumerable<DbVariableReferenceExpression> enumerable = variables;
				EntityUtil.CheckArgumentContainsNull<DbVariableReferenceExpression>(ref enumerable, "variables");
			}
			return CqlQuery.CompileQueryCommandLambda(query, this._perspective, null, null, variables);
		}

		// Token: 0x04001C2C RID: 7212
		private readonly Perspective _perspective;
	}
}
