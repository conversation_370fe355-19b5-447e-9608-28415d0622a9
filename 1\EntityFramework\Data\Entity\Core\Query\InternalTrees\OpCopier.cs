﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Resources;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003C7 RID: 967
	internal class OpCopier : BasicOpVisitorOfNode
	{
		// Token: 0x06002E39 RID: 11833 RVA: 0x00092DE4 File Offset: 0x00090FE4
		internal static Node Copy(Command cmd, Node n)
		{
			VarMap varMap;
			return OpCopier.Copy(cmd, n, out varMap);
		}

		// Token: 0x06002E3A RID: 11834 RVA: 0x00092DFC File Offset: 0x00090FFC
		internal static Node Copy(Command cmd, Node node, VarList varList, out VarList newVarList)
		{
			VarMap varMap;
			Node node2 = OpCopier.Copy(cmd, node, out varMap);
			newVarList = Command.CreateVarList();
			foreach (Var var in varList)
			{
				Var var2 = varMap[var];
				newVarList.Add(var2);
			}
			return node2;
		}

		// Token: 0x06002E3B RID: 11835 RVA: 0x00092E68 File Offset: 0x00091068
		internal static Node Copy(Command cmd, Node n, out VarMap varMap)
		{
			OpCopier opCopier = new OpCopier(cmd);
			Node node = opCopier.CopyNode(n);
			varMap = opCopier.m_varMap;
			return node;
		}

		// Token: 0x06002E3C RID: 11836 RVA: 0x00092E8B File Offset: 0x0009108B
		internal static List<SortKey> Copy(Command cmd, List<SortKey> sortKeys)
		{
			return new OpCopier(cmd).Copy(sortKeys);
		}

		// Token: 0x06002E3D RID: 11837 RVA: 0x00092E99 File Offset: 0x00091099
		protected OpCopier(Command cmd)
			: this(cmd, cmd)
		{
		}

		// Token: 0x06002E3E RID: 11838 RVA: 0x00092EA3 File Offset: 0x000910A3
		private OpCopier(Command destCommand, Command sourceCommand)
		{
			this.m_srcCmd = sourceCommand;
			this.m_destCmd = destCommand;
			this.m_varMap = new VarMap();
		}

		// Token: 0x06002E3F RID: 11839 RVA: 0x00092EC4 File Offset: 0x000910C4
		private Var GetMappedVar(Var v)
		{
			Var var;
			if (this.m_varMap.TryGetValue(v, out var))
			{
				return var;
			}
			if (this.m_destCmd != this.m_srcCmd)
			{
				throw EntityUtil.InternalError(EntityUtil.InternalErrorCode.UnknownVar, 6, null);
			}
			return v;
		}

		// Token: 0x06002E40 RID: 11840 RVA: 0x00092EFF File Offset: 0x000910FF
		private void SetMappedVar(Var v, Var mappedVar)
		{
			this.m_varMap.Add(v, mappedVar);
		}

		// Token: 0x06002E41 RID: 11841 RVA: 0x00092F10 File Offset: 0x00091110
		private void MapTable(Table newTable, Table oldTable)
		{
			for (int i = 0; i < oldTable.Columns.Count; i++)
			{
				this.SetMappedVar(oldTable.Columns[i], newTable.Columns[i]);
			}
		}

		// Token: 0x06002E42 RID: 11842 RVA: 0x00092F51 File Offset: 0x00091151
		private IEnumerable<Var> MapVars(IEnumerable<Var> vars)
		{
			foreach (Var var in vars)
			{
				Var mappedVar = this.GetMappedVar(var);
				yield return mappedVar;
			}
			IEnumerator<Var> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x06002E43 RID: 11843 RVA: 0x00092F68 File Offset: 0x00091168
		private VarVec Copy(VarVec vars)
		{
			return this.m_destCmd.CreateVarVec(this.MapVars(vars));
		}

		// Token: 0x06002E44 RID: 11844 RVA: 0x00092F7C File Offset: 0x0009117C
		private VarList Copy(VarList varList)
		{
			return Command.CreateVarList(this.MapVars(varList));
		}

		// Token: 0x06002E45 RID: 11845 RVA: 0x00092F8A File Offset: 0x0009118A
		private SortKey Copy(SortKey sortKey)
		{
			return Command.CreateSortKey(this.GetMappedVar(sortKey.Var), sortKey.AscendingSort, sortKey.Collation);
		}

		// Token: 0x06002E46 RID: 11846 RVA: 0x00092FAC File Offset: 0x000911AC
		private List<SortKey> Copy(List<SortKey> sortKeys)
		{
			List<SortKey> list = new List<SortKey>();
			foreach (SortKey sortKey in sortKeys)
			{
				list.Add(this.Copy(sortKey));
			}
			return list;
		}

		// Token: 0x06002E47 RID: 11847 RVA: 0x00093008 File Offset: 0x00091208
		protected Node CopyNode(Node n)
		{
			return n.Op.Accept<Node>(this, n);
		}

		// Token: 0x06002E48 RID: 11848 RVA: 0x00093018 File Offset: 0x00091218
		private List<Node> ProcessChildren(Node n)
		{
			List<Node> list = new List<Node>();
			foreach (Node node in n.Children)
			{
				list.Add(this.CopyNode(node));
			}
			return list;
		}

		// Token: 0x06002E49 RID: 11849 RVA: 0x00093078 File Offset: 0x00091278
		private Node CopyDefault(Op op, Node original)
		{
			return this.m_destCmd.CreateNode(op, this.ProcessChildren(original));
		}

		// Token: 0x06002E4A RID: 11850 RVA: 0x0009308D File Offset: 0x0009128D
		public override Node Visit(Op op, Node n)
		{
			throw new NotSupportedException(Strings.Iqt_General_UnsupportedOp(op.GetType().FullName));
		}

		// Token: 0x06002E4B RID: 11851 RVA: 0x000930A4 File Offset: 0x000912A4
		public override Node Visit(ConstantOp op, Node n)
		{
			ConstantBaseOp constantBaseOp = this.m_destCmd.CreateConstantOp(op.Type, op.Value);
			return this.m_destCmd.CreateNode(constantBaseOp);
		}

		// Token: 0x06002E4C RID: 11852 RVA: 0x000930D5 File Offset: 0x000912D5
		public override Node Visit(NullOp op, Node n)
		{
			return this.m_destCmd.CreateNode(this.m_destCmd.CreateNullOp(op.Type));
		}

		// Token: 0x06002E4D RID: 11853 RVA: 0x000930F3 File Offset: 0x000912F3
		public override Node Visit(ConstantPredicateOp op, Node n)
		{
			return this.m_destCmd.CreateNode(this.m_destCmd.CreateConstantPredicateOp(op.Value));
		}

		// Token: 0x06002E4E RID: 11854 RVA: 0x00093114 File Offset: 0x00091314
		public override Node Visit(InternalConstantOp op, Node n)
		{
			InternalConstantOp internalConstantOp = this.m_destCmd.CreateInternalConstantOp(op.Type, op.Value);
			return this.m_destCmd.CreateNode(internalConstantOp);
		}

		// Token: 0x06002E4F RID: 11855 RVA: 0x00093148 File Offset: 0x00091348
		public override Node Visit(NullSentinelOp op, Node n)
		{
			NullSentinelOp nullSentinelOp = this.m_destCmd.CreateNullSentinelOp();
			return this.m_destCmd.CreateNode(nullSentinelOp);
		}

		// Token: 0x06002E50 RID: 11856 RVA: 0x0009316D File Offset: 0x0009136D
		public override Node Visit(FunctionOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateFunctionOp(op.Function), n);
		}

		// Token: 0x06002E51 RID: 11857 RVA: 0x00093187 File Offset: 0x00091387
		public override Node Visit(PropertyOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreatePropertyOp(op.PropertyInfo), n);
		}

		// Token: 0x06002E52 RID: 11858 RVA: 0x000931A1 File Offset: 0x000913A1
		public override Node Visit(RelPropertyOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateRelPropertyOp(op.PropertyInfo), n);
		}

		// Token: 0x06002E53 RID: 11859 RVA: 0x000931BB File Offset: 0x000913BB
		public override Node Visit(CaseOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateCaseOp(op.Type), n);
		}

		// Token: 0x06002E54 RID: 11860 RVA: 0x000931D5 File Offset: 0x000913D5
		public override Node Visit(ComparisonOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateComparisonOp(op.OpType, op.UseDatabaseNullSemantics), n);
		}

		// Token: 0x06002E55 RID: 11861 RVA: 0x000931F5 File Offset: 0x000913F5
		public override Node Visit(LikeOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateLikeOp(), n);
		}

		// Token: 0x06002E56 RID: 11862 RVA: 0x00093209 File Offset: 0x00091409
		public override Node Visit(AggregateOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateAggregateOp(op.AggFunc, op.IsDistinctAggregate), n);
		}

		// Token: 0x06002E57 RID: 11863 RVA: 0x00093229 File Offset: 0x00091429
		public override Node Visit(NewInstanceOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateNewInstanceOp(op.Type), n);
		}

		// Token: 0x06002E58 RID: 11864 RVA: 0x00093244 File Offset: 0x00091444
		public override Node Visit(NewEntityOp op, Node n)
		{
			NewEntityOp newEntityOp;
			if (op.Scoped)
			{
				newEntityOp = this.m_destCmd.CreateScopedNewEntityOp(op.Type, op.RelationshipProperties, op.EntitySet);
			}
			else
			{
				newEntityOp = this.m_destCmd.CreateNewEntityOp(op.Type, op.RelationshipProperties);
			}
			return this.CopyDefault(newEntityOp, n);
		}

		// Token: 0x06002E59 RID: 11865 RVA: 0x00093299 File Offset: 0x00091499
		public override Node Visit(DiscriminatedNewEntityOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateDiscriminatedNewEntityOp(op.Type, op.DiscriminatorMap, op.EntitySet, op.RelationshipProperties), n);
		}

		// Token: 0x06002E5A RID: 11866 RVA: 0x000932C5 File Offset: 0x000914C5
		public override Node Visit(NewMultisetOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateNewMultisetOp(op.Type), n);
		}

		// Token: 0x06002E5B RID: 11867 RVA: 0x000932DF File Offset: 0x000914DF
		public override Node Visit(NewRecordOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateNewRecordOp(op.Type), n);
		}

		// Token: 0x06002E5C RID: 11868 RVA: 0x000932F9 File Offset: 0x000914F9
		public override Node Visit(RefOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateRefOp(op.EntitySet, op.Type), n);
		}

		// Token: 0x06002E5D RID: 11869 RVA: 0x0009331C File Offset: 0x0009151C
		public override Node Visit(VarRefOp op, Node n)
		{
			Var var;
			if (!this.m_varMap.TryGetValue(op.Var, out var))
			{
				var = op.Var;
			}
			return this.m_destCmd.CreateNode(this.m_destCmd.CreateVarRefOp(var));
		}

		// Token: 0x06002E5E RID: 11870 RVA: 0x0009335C File Offset: 0x0009155C
		public override Node Visit(ConditionalOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateConditionalOp(op.OpType), n);
		}

		// Token: 0x06002E5F RID: 11871 RVA: 0x00093376 File Offset: 0x00091576
		public override Node Visit(ArithmeticOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateArithmeticOp(op.OpType, op.Type), n);
		}

		// Token: 0x06002E60 RID: 11872 RVA: 0x00093398 File Offset: 0x00091598
		public override Node Visit(TreatOp op, Node n)
		{
			TreatOp treatOp = (op.IsFakeTreat ? this.m_destCmd.CreateFakeTreatOp(op.Type) : this.m_destCmd.CreateTreatOp(op.Type));
			return this.CopyDefault(treatOp, n);
		}

		// Token: 0x06002E61 RID: 11873 RVA: 0x000933DA File Offset: 0x000915DA
		public override Node Visit(CastOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateCastOp(op.Type), n);
		}

		// Token: 0x06002E62 RID: 11874 RVA: 0x000933F4 File Offset: 0x000915F4
		public override Node Visit(SoftCastOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateSoftCastOp(op.Type), n);
		}

		// Token: 0x06002E63 RID: 11875 RVA: 0x0009340E File Offset: 0x0009160E
		public override Node Visit(DerefOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateDerefOp(op.Type), n);
		}

		// Token: 0x06002E64 RID: 11876 RVA: 0x00093428 File Offset: 0x00091628
		public override Node Visit(NavigateOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateNavigateOp(op.Type, op.RelProperty), n);
		}

		// Token: 0x06002E65 RID: 11877 RVA: 0x00093448 File Offset: 0x00091648
		public override Node Visit(IsOfOp op, Node n)
		{
			if (op.IsOfOnly)
			{
				return this.CopyDefault(this.m_destCmd.CreateIsOfOnlyOp(op.IsOfType), n);
			}
			return this.CopyDefault(this.m_destCmd.CreateIsOfOp(op.IsOfType), n);
		}

		// Token: 0x06002E66 RID: 11878 RVA: 0x00093483 File Offset: 0x00091683
		public override Node Visit(ExistsOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateExistsOp(), n);
		}

		// Token: 0x06002E67 RID: 11879 RVA: 0x00093497 File Offset: 0x00091697
		public override Node Visit(ElementOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateElementOp(op.Type), n);
		}

		// Token: 0x06002E68 RID: 11880 RVA: 0x000934B1 File Offset: 0x000916B1
		public override Node Visit(GetRefKeyOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateGetRefKeyOp(op.Type), n);
		}

		// Token: 0x06002E69 RID: 11881 RVA: 0x000934CB File Offset: 0x000916CB
		public override Node Visit(GetEntityRefOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateGetEntityRefOp(op.Type), n);
		}

		// Token: 0x06002E6A RID: 11882 RVA: 0x000934E5 File Offset: 0x000916E5
		public override Node Visit(CollectOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateCollectOp(op.Type), n);
		}

		// Token: 0x06002E6B RID: 11883 RVA: 0x00093500 File Offset: 0x00091700
		public override Node Visit(ScanTableOp op, Node n)
		{
			ScanTableOp scanTableOp = this.m_destCmd.CreateScanTableOp(op.Table.TableMetadata);
			this.MapTable(scanTableOp.Table, op.Table);
			return this.m_destCmd.CreateNode(scanTableOp);
		}

		// Token: 0x06002E6C RID: 11884 RVA: 0x00093544 File Offset: 0x00091744
		public override Node Visit(ScanViewOp op, Node n)
		{
			ScanViewOp scanViewOp = this.m_destCmd.CreateScanViewOp(op.Table.TableMetadata);
			this.MapTable(scanViewOp.Table, op.Table);
			List<Node> list = this.ProcessChildren(n);
			return this.m_destCmd.CreateNode(scanViewOp, list);
		}

		// Token: 0x06002E6D RID: 11885 RVA: 0x00093590 File Offset: 0x00091790
		public override Node Visit(UnnestOp op, Node n)
		{
			List<Node> list = this.ProcessChildren(n);
			Var mappedVar = this.GetMappedVar(op.Var);
			Table table = this.m_destCmd.CreateTableInstance(op.Table.TableMetadata);
			UnnestOp unnestOp = this.m_destCmd.CreateUnnestOp(mappedVar, table);
			this.MapTable(unnestOp.Table, op.Table);
			return this.m_destCmd.CreateNode(unnestOp, list);
		}

		// Token: 0x06002E6E RID: 11886 RVA: 0x000935F8 File Offset: 0x000917F8
		public override Node Visit(ProjectOp op, Node n)
		{
			List<Node> list = this.ProcessChildren(n);
			VarVec varVec = this.Copy(op.Outputs);
			ProjectOp projectOp = this.m_destCmd.CreateProjectOp(varVec);
			return this.m_destCmd.CreateNode(projectOp, list);
		}

		// Token: 0x06002E6F RID: 11887 RVA: 0x00093634 File Offset: 0x00091834
		public override Node Visit(FilterOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateFilterOp(), n);
		}

		// Token: 0x06002E70 RID: 11888 RVA: 0x00093648 File Offset: 0x00091848
		public override Node Visit(SortOp op, Node n)
		{
			List<Node> list = this.ProcessChildren(n);
			List<SortKey> list2 = this.Copy(op.Keys);
			SortOp sortOp = this.m_destCmd.CreateSortOp(list2);
			return this.m_destCmd.CreateNode(sortOp, list);
		}

		// Token: 0x06002E71 RID: 11889 RVA: 0x00093684 File Offset: 0x00091884
		public override Node Visit(ConstrainedSortOp op, Node n)
		{
			List<Node> list = this.ProcessChildren(n);
			List<SortKey> list2 = this.Copy(op.Keys);
			ConstrainedSortOp constrainedSortOp = this.m_destCmd.CreateConstrainedSortOp(list2, op.WithTies);
			return this.m_destCmd.CreateNode(constrainedSortOp, list);
		}

		// Token: 0x06002E72 RID: 11890 RVA: 0x000936C8 File Offset: 0x000918C8
		public override Node Visit(GroupByOp op, Node n)
		{
			List<Node> list = this.ProcessChildren(n);
			GroupByOp groupByOp = this.m_destCmd.CreateGroupByOp(this.Copy(op.Keys), this.Copy(op.Outputs));
			return this.m_destCmd.CreateNode(groupByOp, list);
		}

		// Token: 0x06002E73 RID: 11891 RVA: 0x00093710 File Offset: 0x00091910
		public override Node Visit(GroupByIntoOp op, Node n)
		{
			List<Node> list = this.ProcessChildren(n);
			GroupByIntoOp groupByIntoOp = this.m_destCmd.CreateGroupByIntoOp(this.Copy(op.Keys), this.Copy(op.Inputs), this.Copy(op.Outputs));
			return this.m_destCmd.CreateNode(groupByIntoOp, list);
		}

		// Token: 0x06002E74 RID: 11892 RVA: 0x00093762 File Offset: 0x00091962
		public override Node Visit(CrossJoinOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateCrossJoinOp(), n);
		}

		// Token: 0x06002E75 RID: 11893 RVA: 0x00093776 File Offset: 0x00091976
		public override Node Visit(InnerJoinOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateInnerJoinOp(), n);
		}

		// Token: 0x06002E76 RID: 11894 RVA: 0x0009378A File Offset: 0x0009198A
		public override Node Visit(LeftOuterJoinOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateLeftOuterJoinOp(), n);
		}

		// Token: 0x06002E77 RID: 11895 RVA: 0x0009379E File Offset: 0x0009199E
		public override Node Visit(FullOuterJoinOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateFullOuterJoinOp(), n);
		}

		// Token: 0x06002E78 RID: 11896 RVA: 0x000937B2 File Offset: 0x000919B2
		public override Node Visit(CrossApplyOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateCrossApplyOp(), n);
		}

		// Token: 0x06002E79 RID: 11897 RVA: 0x000937C6 File Offset: 0x000919C6
		public override Node Visit(OuterApplyOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateOuterApplyOp(), n);
		}

		// Token: 0x06002E7A RID: 11898 RVA: 0x000937DC File Offset: 0x000919DC
		private Node CopySetOp(SetOp op, Node n)
		{
			List<Node> list = this.ProcessChildren(n);
			VarMap varMap = new VarMap();
			VarMap varMap2 = new VarMap();
			foreach (KeyValuePair<Var, Var> keyValuePair in op.VarMap[0])
			{
				Var var = this.m_destCmd.CreateSetOpVar(keyValuePair.Key.Type);
				this.SetMappedVar(keyValuePair.Key, var);
				varMap.Add(var, this.GetMappedVar(keyValuePair.Value));
				varMap2.Add(var, this.GetMappedVar(op.VarMap[1][keyValuePair.Key]));
			}
			SetOp setOp = null;
			switch (op.OpType)
			{
			case OpType.UnionAll:
			{
				Var var2 = ((UnionAllOp)op).BranchDiscriminator;
				if (var2 != null)
				{
					var2 = this.GetMappedVar(var2);
				}
				setOp = this.m_destCmd.CreateUnionAllOp(varMap, varMap2, var2);
				break;
			}
			case OpType.Intersect:
				setOp = this.m_destCmd.CreateIntersectOp(varMap, varMap2);
				break;
			case OpType.Except:
				setOp = this.m_destCmd.CreateExceptOp(varMap, varMap2);
				break;
			}
			return this.m_destCmd.CreateNode(setOp, list);
		}

		// Token: 0x06002E7B RID: 11899 RVA: 0x00093918 File Offset: 0x00091B18
		public override Node Visit(UnionAllOp op, Node n)
		{
			return this.CopySetOp(op, n);
		}

		// Token: 0x06002E7C RID: 11900 RVA: 0x00093922 File Offset: 0x00091B22
		public override Node Visit(IntersectOp op, Node n)
		{
			return this.CopySetOp(op, n);
		}

		// Token: 0x06002E7D RID: 11901 RVA: 0x0009392C File Offset: 0x00091B2C
		public override Node Visit(ExceptOp op, Node n)
		{
			return this.CopySetOp(op, n);
		}

		// Token: 0x06002E7E RID: 11902 RVA: 0x00093938 File Offset: 0x00091B38
		public override Node Visit(DistinctOp op, Node n)
		{
			List<Node> list = this.ProcessChildren(n);
			VarVec varVec = this.Copy(op.Keys);
			DistinctOp distinctOp = this.m_destCmd.CreateDistinctOp(varVec);
			return this.m_destCmd.CreateNode(distinctOp, list);
		}

		// Token: 0x06002E7F RID: 11903 RVA: 0x00093974 File Offset: 0x00091B74
		public override Node Visit(SingleRowOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateSingleRowOp(), n);
		}

		// Token: 0x06002E80 RID: 11904 RVA: 0x00093988 File Offset: 0x00091B88
		public override Node Visit(SingleRowTableOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateSingleRowTableOp(), n);
		}

		// Token: 0x06002E81 RID: 11905 RVA: 0x0009399C File Offset: 0x00091B9C
		public override Node Visit(VarDefOp op, Node n)
		{
			List<Node> list = this.ProcessChildren(n);
			Var var = this.m_destCmd.CreateComputedVar(op.Var.Type);
			this.SetMappedVar(op.Var, var);
			return this.m_destCmd.CreateNode(this.m_destCmd.CreateVarDefOp(var), list);
		}

		// Token: 0x06002E82 RID: 11906 RVA: 0x000939ED File Offset: 0x00091BED
		public override Node Visit(VarDefListOp op, Node n)
		{
			return this.CopyDefault(this.m_destCmd.CreateVarDefListOp(), n);
		}

		// Token: 0x06002E83 RID: 11907 RVA: 0x00093A01 File Offset: 0x00091C01
		private ColumnMap Copy(ColumnMap columnMap)
		{
			return ColumnMapCopier.Copy(columnMap, this.m_varMap);
		}

		// Token: 0x06002E84 RID: 11908 RVA: 0x00093A10 File Offset: 0x00091C10
		public override Node Visit(PhysicalProjectOp op, Node n)
		{
			List<Node> list = this.ProcessChildren(n);
			VarList varList = this.Copy(op.Outputs);
			SimpleCollectionColumnMap simpleCollectionColumnMap = this.Copy(op.ColumnMap) as SimpleCollectionColumnMap;
			PhysicalProjectOp physicalProjectOp = this.m_destCmd.CreatePhysicalProjectOp(varList, simpleCollectionColumnMap);
			return this.m_destCmd.CreateNode(physicalProjectOp, list);
		}

		// Token: 0x06002E85 RID: 11909 RVA: 0x00093A60 File Offset: 0x00091C60
		private Node VisitNestOp(Node n)
		{
			NestBaseOp nestBaseOp = n.Op as NestBaseOp;
			SingleStreamNestOp singleStreamNestOp = nestBaseOp as SingleStreamNestOp;
			List<Node> list = this.ProcessChildren(n);
			Var var = null;
			if (singleStreamNestOp != null)
			{
				var = this.GetMappedVar(singleStreamNestOp.Discriminator);
			}
			List<CollectionInfo> list2 = new List<CollectionInfo>();
			foreach (CollectionInfo collectionInfo in nestBaseOp.CollectionInfo)
			{
				ColumnMap columnMap = this.Copy(collectionInfo.ColumnMap);
				Var var2 = this.m_destCmd.CreateComputedVar(collectionInfo.CollectionVar.Type);
				this.SetMappedVar(collectionInfo.CollectionVar, var2);
				VarList varList = this.Copy(collectionInfo.FlattenedElementVars);
				VarVec varVec = this.Copy(collectionInfo.Keys);
				List<SortKey> list3 = this.Copy(collectionInfo.SortKeys);
				CollectionInfo collectionInfo2 = Command.CreateCollectionInfo(var2, columnMap, varList, varVec, list3, collectionInfo.DiscriminatorValue);
				list2.Add(collectionInfo2);
			}
			VarVec varVec2 = this.Copy(nestBaseOp.Outputs);
			List<SortKey> list4 = this.Copy(nestBaseOp.PrefixSortKeys);
			NestBaseOp nestBaseOp2;
			if (singleStreamNestOp != null)
			{
				VarVec varVec3 = this.Copy(singleStreamNestOp.Keys);
				List<SortKey> list5 = this.Copy(singleStreamNestOp.PostfixSortKeys);
				nestBaseOp2 = this.m_destCmd.CreateSingleStreamNestOp(varVec3, list4, list5, varVec2, list2, var);
			}
			else
			{
				nestBaseOp2 = this.m_destCmd.CreateMultiStreamNestOp(list4, varVec2, list2);
			}
			return this.m_destCmd.CreateNode(nestBaseOp2, list);
		}

		// Token: 0x06002E86 RID: 11910 RVA: 0x00093BE8 File Offset: 0x00091DE8
		public override Node Visit(SingleStreamNestOp op, Node n)
		{
			return this.VisitNestOp(n);
		}

		// Token: 0x06002E87 RID: 11911 RVA: 0x00093BF1 File Offset: 0x00091DF1
		public override Node Visit(MultiStreamNestOp op, Node n)
		{
			return this.VisitNestOp(n);
		}

		// Token: 0x04000F65 RID: 3941
		private readonly Command m_srcCmd;

		// Token: 0x04000F66 RID: 3942
		protected Command m_destCmd;

		// Token: 0x04000F67 RID: 3943
		protected VarMap m_varMap;
	}
}
