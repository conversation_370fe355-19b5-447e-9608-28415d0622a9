﻿using System;
using System.Collections.Generic;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003EF RID: 1007
	internal sealed class SortOp : SortBaseOp
	{
		// Token: 0x06002F34 RID: 12084 RVA: 0x00094890 File Offset: 0x00092A90
		private SortOp()
			: base(OpType.Sort)
		{
		}

		// Token: 0x06002F35 RID: 12085 RVA: 0x0009489A File Offset: 0x00092A9A
		internal SortOp(List<SortKey> sortKeys)
			: base(OpType.Sort, sortKeys)
		{
		}

		// Token: 0x1700094A RID: 2378
		// (get) Token: 0x06002F36 RID: 12086 RVA: 0x000948A5 File Offset: 0x00092AA5
		internal override int Arity
		{
			get
			{
				return 1;
			}
		}

		// Token: 0x06002F37 RID: 12087 RVA: 0x000948A8 File Offset: 0x00092AA8
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002F38 RID: 12088 RVA: 0x000948B2 File Offset: 0x00092AB2
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000FE9 RID: 4073
		internal static readonly SortOp Pattern = new SortOp();
	}
}
