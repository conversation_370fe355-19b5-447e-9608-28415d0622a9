﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder
{
	// Token: 0x020006F9 RID: 1785
	public sealed class Row
	{
		// Token: 0x060053ED RID: 21485 RVA: 0x0012CDB1 File Offset: 0x0012AFB1
		public Row(KeyValuePair<string, DbExpression> columnValue, params KeyValuePair<string, DbExpression>[] columnValues)
		{
			this.arguments = new ReadOnlyCollection<KeyValuePair<string, DbExpression>>(Helpers.Prepend<KeyValuePair<string, DbExpression>>(columnValues, columnValue));
		}

		// Token: 0x060053EE RID: 21486 RVA: 0x0012CDCB File Offset: 0x0012AFCB
		public DbNewInstanceExpression ToExpression()
		{
			return DbExpressionBuilder.NewRow(this.arguments);
		}

		// Token: 0x060053EF RID: 21487 RVA: 0x0012CDD8 File Offset: 0x0012AFD8
		public static implicit operator DbExpression(Row row)
		{
			Check.NotNull<Row>(row, "row");
			return row.ToExpression();
		}

		// Token: 0x04001E0E RID: 7694
		private readonly ReadOnlyCollection<KeyValuePair<string, DbExpression>> arguments;
	}
}
