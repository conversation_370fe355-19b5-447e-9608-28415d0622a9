﻿using System;
using System.Collections.Generic;
using System.Reflection;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x02000507 RID: 1287
	internal static class AssemblyCache
	{
		// Token: 0x06003FB8 RID: 16312 RVA: 0x000D3335 File Offset: 0x000D1535
		internal static LockedAssemblyCache AcquireLockedAssemblyCache()
		{
			return new LockedAssemblyCache(AssemblyCache._assemblyCacheLock, AssemblyCache._globalAssemblyCache);
		}

		// Token: 0x06003FB9 RID: 16313 RVA: 0x000D3348 File Offset: 0x000D1548
		internal static void LoadAssembly(Assembly assembly, bool loadReferencedAssemblies, KnownAssembliesSet knownAssemblies, out Dictionary<string, EdmType> typesInLoading, out List<EdmItemError> errors)
		{
			object obj = null;
			AssemblyCache.LoadAssembly(assembly, loadReferencedAssemblies, knownAssemblies, null, null, ref obj, out typesInLoading, out errors);
		}

		// Token: 0x06003FBA RID: 16314 RVA: 0x000D3368 File Offset: 0x000D1568
		internal static void LoadAssembly(Assembly assembly, bool loadReferencedAssemblies, KnownAssembliesSet knownAssemblies, EdmItemCollection edmItemCollection, Action<string> logLoadMessage, ref object loaderCookie, out Dictionary<string, EdmType> typesInLoading, out List<EdmItemError> errors)
		{
			typesInLoading = null;
			errors = null;
			using (LockedAssemblyCache lockedAssemblyCache = AssemblyCache.AcquireLockedAssemblyCache())
			{
				ObjectItemLoadingSessionData objectItemLoadingSessionData = new ObjectItemLoadingSessionData(knownAssemblies, lockedAssemblyCache, edmItemCollection, logLoadMessage, loaderCookie);
				AssemblyCache.LoadAssembly(assembly, loadReferencedAssemblies, objectItemLoadingSessionData);
				loaderCookie = objectItemLoadingSessionData.LoaderCookie;
				objectItemLoadingSessionData.CompleteSession();
				if (objectItemLoadingSessionData.EdmItemErrors.Count == 0)
				{
					new EdmValidator
					{
						SkipReadOnlyItems = true
					}.Validate<EdmType>(objectItemLoadingSessionData.TypesInLoading.Values, objectItemLoadingSessionData.EdmItemErrors);
					if (objectItemLoadingSessionData.EdmItemErrors.Count == 0)
					{
						if (ObjectItemAssemblyLoader.IsAttributeLoader(objectItemLoadingSessionData.ObjectItemAssemblyLoaderFactory))
						{
							AssemblyCache.UpdateCache(lockedAssemblyCache, objectItemLoadingSessionData.AssembliesLoaded);
						}
						else if (objectItemLoadingSessionData.EdmItemCollection != null && ObjectItemAssemblyLoader.IsConventionLoader(objectItemLoadingSessionData.ObjectItemAssemblyLoaderFactory))
						{
							AssemblyCache.UpdateCache(objectItemLoadingSessionData.EdmItemCollection, objectItemLoadingSessionData.AssembliesLoaded);
						}
					}
				}
				if (objectItemLoadingSessionData.TypesInLoading.Count > 0)
				{
					foreach (EdmType edmType in objectItemLoadingSessionData.TypesInLoading.Values)
					{
						edmType.SetReadOnly();
					}
				}
				typesInLoading = objectItemLoadingSessionData.TypesInLoading;
				errors = objectItemLoadingSessionData.EdmItemErrors;
			}
		}

		// Token: 0x06003FBB RID: 16315 RVA: 0x000D34A8 File Offset: 0x000D16A8
		private static void LoadAssembly(Assembly assembly, bool loadReferencedAssemblies, ObjectItemLoadingSessionData loadingData)
		{
			KnownAssemblyEntry knownAssemblyEntry;
			bool flag;
			if (loadingData.KnownAssemblies.TryGetKnownAssembly(assembly, loadingData.ObjectItemAssemblyLoaderFactory, loadingData.EdmItemCollection, out knownAssemblyEntry))
			{
				flag = !knownAssemblyEntry.ReferencedAssembliesAreLoaded && loadReferencedAssemblies;
			}
			else
			{
				ObjectItemAssemblyLoader.CreateLoader(assembly, loadingData).Load();
				flag = loadReferencedAssemblies;
			}
			if (flag)
			{
				if ((knownAssemblyEntry == null && loadingData.KnownAssemblies.TryGetKnownAssembly(assembly, loadingData.ObjectItemAssemblyLoaderFactory, loadingData.EdmItemCollection, out knownAssemblyEntry)) || knownAssemblyEntry != null)
				{
					knownAssemblyEntry.ReferencedAssembliesAreLoaded = true;
				}
				foreach (Assembly assembly2 in MetadataAssemblyHelper.GetNonSystemReferencedAssemblies(assembly))
				{
					AssemblyCache.LoadAssembly(assembly2, loadReferencedAssemblies, loadingData);
				}
			}
		}

		// Token: 0x06003FBC RID: 16316 RVA: 0x000D355C File Offset: 0x000D175C
		private static void UpdateCache(EdmItemCollection edmItemCollection, Dictionary<Assembly, MutableAssemblyCacheEntry> assemblies)
		{
			foreach (KeyValuePair<Assembly, MutableAssemblyCacheEntry> keyValuePair in assemblies)
			{
				edmItemCollection.ConventionalOcCache.AddAssemblyToOcCacheFromAssemblyCache(keyValuePair.Key, new ImmutableAssemblyCacheEntry(keyValuePair.Value));
			}
		}

		// Token: 0x06003FBD RID: 16317 RVA: 0x000D35C4 File Offset: 0x000D17C4
		private static void UpdateCache(LockedAssemblyCache lockedAssemblyCache, Dictionary<Assembly, MutableAssemblyCacheEntry> assemblies)
		{
			foreach (KeyValuePair<Assembly, MutableAssemblyCacheEntry> keyValuePair in assemblies)
			{
				lockedAssemblyCache.Add(keyValuePair.Key, new ImmutableAssemblyCacheEntry(keyValuePair.Value));
			}
		}

		// Token: 0x0400163D RID: 5693
		private static readonly Dictionary<Assembly, ImmutableAssemblyCacheEntry> _globalAssemblyCache = new Dictionary<Assembly, ImmutableAssemblyCacheEntry>();

		// Token: 0x0400163E RID: 5694
		private static readonly object _assemblyCacheLock = new object();
	}
}
