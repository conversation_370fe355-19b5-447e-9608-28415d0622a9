﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x02000524 RID: 1316
	public class ComplexTypeMapping : StructuralTypeMapping
	{
		// Token: 0x060040EE RID: 16622 RVA: 0x000DA788 File Offset: 0x000D8988
		public ComplexTypeMapping(ComplexType complexType)
		{
			Check.NotNull<ComplexType>(complexType, "complexType");
			this.AddType(complexType);
		}

		// Token: 0x060040EF RID: 16623 RVA: 0x000DA7F0 File Offset: 0x000D89F0
		internal ComplexTypeMapping(bool isPartial)
		{
		}

		// Token: 0x17000CB4 RID: 3252
		// (get) Token: 0x060040F0 RID: 16624 RVA: 0x000DA843 File Offset: 0x000D8A43
		public ComplexType ComplexType
		{
			get
			{
				return this.m_types.Values.SingleOrDefault<ComplexType>();
			}
		}

		// Token: 0x17000CB5 RID: 3253
		// (get) Token: 0x060040F1 RID: 16625 RVA: 0x000DA855 File Offset: 0x000D8A55
		internal ReadOnlyCollection<ComplexType> Types
		{
			get
			{
				return new ReadOnlyCollection<ComplexType>(new List<ComplexType>(this.m_types.Values));
			}
		}

		// Token: 0x17000CB6 RID: 3254
		// (get) Token: 0x060040F2 RID: 16626 RVA: 0x000DA86C File Offset: 0x000D8A6C
		internal ReadOnlyCollection<ComplexType> IsOfTypes
		{
			get
			{
				return new ReadOnlyCollection<ComplexType>(new List<ComplexType>(this.m_isOfTypes.Values));
			}
		}

		// Token: 0x17000CB7 RID: 3255
		// (get) Token: 0x060040F3 RID: 16627 RVA: 0x000DA883 File Offset: 0x000D8A83
		public override ReadOnlyCollection<PropertyMapping> PropertyMappings
		{
			get
			{
				return new ReadOnlyCollection<PropertyMapping>(new List<PropertyMapping>(this.m_properties.Values));
			}
		}

		// Token: 0x17000CB8 RID: 3256
		// (get) Token: 0x060040F4 RID: 16628 RVA: 0x000DA89A File Offset: 0x000D8A9A
		public override ReadOnlyCollection<ConditionPropertyMapping> Conditions
		{
			get
			{
				return new ReadOnlyCollection<ConditionPropertyMapping>(new List<ConditionPropertyMapping>(this.m_conditionProperties.Values));
			}
		}

		// Token: 0x17000CB9 RID: 3257
		// (get) Token: 0x060040F5 RID: 16629 RVA: 0x000DA8B1 File Offset: 0x000D8AB1
		internal ReadOnlyCollection<PropertyMapping> AllProperties
		{
			get
			{
				List<PropertyMapping> list = new List<PropertyMapping>();
				list.AddRange(this.m_properties.Values);
				list.AddRange(this.m_conditionProperties.Values);
				return new ReadOnlyCollection<PropertyMapping>(list);
			}
		}

		// Token: 0x060040F6 RID: 16630 RVA: 0x000DA8DF File Offset: 0x000D8ADF
		internal void AddType(ComplexType type)
		{
			this.m_types.Add(type.FullName, type);
		}

		// Token: 0x060040F7 RID: 16631 RVA: 0x000DA8F3 File Offset: 0x000D8AF3
		internal void AddIsOfType(ComplexType type)
		{
			this.m_isOfTypes.Add(type.FullName, type);
		}

		// Token: 0x060040F8 RID: 16632 RVA: 0x000DA907 File Offset: 0x000D8B07
		public override void AddPropertyMapping(PropertyMapping propertyMapping)
		{
			Check.NotNull<PropertyMapping>(propertyMapping, "propertyMapping");
			base.ThrowIfReadOnly();
			this.m_properties.Add(propertyMapping.Property.Name, propertyMapping);
		}

		// Token: 0x060040F9 RID: 16633 RVA: 0x000DA932 File Offset: 0x000D8B32
		public override void RemovePropertyMapping(PropertyMapping propertyMapping)
		{
			Check.NotNull<PropertyMapping>(propertyMapping, "propertyMapping");
			base.ThrowIfReadOnly();
			this.m_properties.Remove(propertyMapping.Property.Name);
		}

		// Token: 0x060040FA RID: 16634 RVA: 0x000DA95D File Offset: 0x000D8B5D
		public override void AddCondition(ConditionPropertyMapping condition)
		{
			Check.NotNull<ConditionPropertyMapping>(condition, "condition");
			base.ThrowIfReadOnly();
			this.AddConditionProperty(condition, delegate(EdmMember _)
			{
			});
		}

		// Token: 0x060040FB RID: 16635 RVA: 0x000DA997 File Offset: 0x000D8B97
		public override void RemoveCondition(ConditionPropertyMapping condition)
		{
			Check.NotNull<ConditionPropertyMapping>(condition, "condition");
			base.ThrowIfReadOnly();
			this.m_conditionProperties.Remove(condition.Property ?? condition.Column);
		}

		// Token: 0x060040FC RID: 16636 RVA: 0x000DA9C7 File Offset: 0x000D8BC7
		internal override void SetReadOnly()
		{
			MappingItem.SetReadOnly(this.m_properties.Values);
			MappingItem.SetReadOnly(this.m_conditionProperties.Values);
			base.SetReadOnly();
		}

		// Token: 0x060040FD RID: 16637 RVA: 0x000DA9F0 File Offset: 0x000D8BF0
		internal void AddConditionProperty(ConditionPropertyMapping conditionPropertyMap, Action<EdmMember> duplicateMemberConditionError)
		{
			EdmProperty edmProperty = ((conditionPropertyMap.Property != null) ? conditionPropertyMap.Property : conditionPropertyMap.Column);
			if (!this.m_conditionProperties.ContainsKey(edmProperty))
			{
				this.m_conditionProperties.Add(edmProperty, conditionPropertyMap);
				return;
			}
			duplicateMemberConditionError(edmProperty);
		}

		// Token: 0x060040FE RID: 16638 RVA: 0x000DAA38 File Offset: 0x000D8C38
		internal ComplexType GetOwnerType(string memberName)
		{
			foreach (ComplexType complexType in this.m_types.Values)
			{
				EdmMember edmMember;
				if (complexType.Members.TryGetValue(memberName, false, out edmMember) && edmMember is EdmProperty)
				{
					return complexType;
				}
			}
			foreach (ComplexType complexType2 in this.m_isOfTypes.Values)
			{
				EdmMember edmMember2;
				if (complexType2.Members.TryGetValue(memberName, false, out edmMember2) && edmMember2 is EdmProperty)
				{
					return complexType2;
				}
			}
			return null;
		}

		// Token: 0x04001687 RID: 5767
		private readonly Dictionary<string, PropertyMapping> m_properties = new Dictionary<string, PropertyMapping>(StringComparer.Ordinal);

		// Token: 0x04001688 RID: 5768
		private readonly Dictionary<EdmProperty, ConditionPropertyMapping> m_conditionProperties = new Dictionary<EdmProperty, ConditionPropertyMapping>(EqualityComparer<EdmProperty>.Default);

		// Token: 0x04001689 RID: 5769
		private readonly Dictionary<string, ComplexType> m_types = new Dictionary<string, ComplexType>(StringComparer.Ordinal);

		// Token: 0x0400168A RID: 5770
		private readonly Dictionary<string, ComplexType> m_isOfTypes = new Dictionary<string, ComplexType>(StringComparer.Ordinal);
	}
}
