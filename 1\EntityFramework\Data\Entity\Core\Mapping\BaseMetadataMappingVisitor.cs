﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Metadata.Edm;
using System.Linq;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x02000521 RID: 1313
	internal abstract class BaseMetadataMappingVisitor
	{
		// Token: 0x060040BE RID: 16574 RVA: 0x000D95EF File Offset: 0x000D77EF
		protected BaseMetadataMappingVisitor(bool sortSequence)
		{
			this._sortSequence = sortSequence;
		}

		// Token: 0x060040BF RID: 16575 RVA: 0x000D9600 File Offset: 0x000D7800
		protected virtual void Visit(EntityContainerMapping entityContainerMapping)
		{
			this.Visit(entityContainerMapping.EdmEntityContainer);
			this.Visit(entityContainerMapping.StorageEntityContainer);
			foreach (EntitySetBaseMapping entitySetBaseMapping in this.GetSequence<EntitySetBaseMapping>(entityContainerMapping.EntitySetMaps, (EntitySetBaseMapping it) => BaseMetadataMappingVisitor.IdentityHelper.GetIdentity(it)))
			{
				this.Visit(entitySetBaseMapping);
			}
		}

		// Token: 0x060040C0 RID: 16576 RVA: 0x000D968C File Offset: 0x000D788C
		protected virtual void Visit(EntitySetBase entitySetBase)
		{
			BuiltInTypeKind builtInTypeKind = entitySetBase.BuiltInTypeKind;
			if (builtInTypeKind != BuiltInTypeKind.AssociationSet)
			{
				if (builtInTypeKind == BuiltInTypeKind.EntitySet)
				{
					this.Visit((EntitySet)entitySetBase);
					return;
				}
			}
			else
			{
				this.Visit((AssociationSet)entitySetBase);
			}
		}

		// Token: 0x060040C1 RID: 16577 RVA: 0x000D96C4 File Offset: 0x000D78C4
		protected virtual void Visit(EntitySetBaseMapping setMapping)
		{
			foreach (TypeMapping typeMapping in this.GetSequence<TypeMapping>(setMapping.TypeMappings, (TypeMapping it) => BaseMetadataMappingVisitor.IdentityHelper.GetIdentity(it)))
			{
				this.Visit(typeMapping);
			}
			this.Visit(setMapping.EntityContainerMapping);
		}

		// Token: 0x060040C2 RID: 16578 RVA: 0x000D9744 File Offset: 0x000D7944
		protected virtual void Visit(EntityContainer entityContainer)
		{
			foreach (EntitySetBase entitySetBase in this.GetSequence<EntitySetBase>(entityContainer.BaseEntitySets, (EntitySetBase it) => it.Identity))
			{
				this.Visit(entitySetBase);
			}
		}

		// Token: 0x060040C3 RID: 16579 RVA: 0x000D97B8 File Offset: 0x000D79B8
		protected virtual void Visit(EntitySet entitySet)
		{
			this.Visit(entitySet.ElementType);
			this.Visit(entitySet.EntityContainer);
		}

		// Token: 0x060040C4 RID: 16580 RVA: 0x000D97D4 File Offset: 0x000D79D4
		protected virtual void Visit(AssociationSet associationSet)
		{
			this.Visit(associationSet.ElementType);
			this.Visit(associationSet.EntityContainer);
			foreach (AssociationSetEnd associationSetEnd in this.GetSequence<AssociationSetEnd>(associationSet.AssociationSetEnds, (AssociationSetEnd it) => it.Identity))
			{
				this.Visit(associationSetEnd);
			}
		}

		// Token: 0x060040C5 RID: 16581 RVA: 0x000D9860 File Offset: 0x000D7A60
		protected virtual void Visit(EntityType entityType)
		{
			foreach (EdmMember edmMember in this.GetSequence<EdmMember>(entityType.KeyMembers, (EdmMember it) => it.Identity))
			{
				this.Visit(edmMember);
			}
			foreach (EdmMember edmMember2 in this.GetSequence<EdmMember>(entityType.GetDeclaredOnlyMembers<EdmMember>(), (EdmMember it) => it.Identity))
			{
				this.Visit(edmMember2);
			}
			foreach (NavigationProperty navigationProperty in this.GetSequence<NavigationProperty>(entityType.NavigationProperties, (NavigationProperty it) => it.Identity))
			{
				this.Visit(navigationProperty);
			}
			foreach (EdmProperty edmProperty in this.GetSequence<EdmProperty>(entityType.Properties, (EdmProperty it) => it.Identity))
			{
				this.Visit(edmProperty);
			}
		}

		// Token: 0x060040C6 RID: 16582 RVA: 0x000D9A00 File Offset: 0x000D7C00
		protected virtual void Visit(AssociationType associationType)
		{
			foreach (AssociationEndMember associationEndMember in this.GetSequence<AssociationEndMember>(associationType.AssociationEndMembers, (AssociationEndMember it) => it.Identity))
			{
				this.Visit(associationEndMember);
			}
			this.Visit(associationType.BaseType);
			foreach (EdmMember edmMember in this.GetSequence<EdmMember>(associationType.KeyMembers, (EdmMember it) => it.Identity))
			{
				this.Visit(edmMember);
			}
			foreach (EdmMember edmMember2 in this.GetSequence<EdmMember>(associationType.GetDeclaredOnlyMembers<EdmMember>(), (EdmMember it) => it.Identity))
			{
				this.Visit(edmMember2);
			}
			foreach (ReferentialConstraint referentialConstraint in this.GetSequence<ReferentialConstraint>(associationType.ReferentialConstraints, (ReferentialConstraint it) => it.Identity))
			{
				this.Visit(referentialConstraint);
			}
			foreach (RelationshipEndMember relationshipEndMember in this.GetSequence<RelationshipEndMember>(associationType.RelationshipEndMembers, (RelationshipEndMember it) => it.Identity))
			{
				this.Visit(relationshipEndMember);
			}
		}

		// Token: 0x060040C7 RID: 16583 RVA: 0x000D9C14 File Offset: 0x000D7E14
		protected virtual void Visit(AssociationSetEnd associationSetEnd)
		{
			this.Visit(associationSetEnd.CorrespondingAssociationEndMember);
			this.Visit(associationSetEnd.EntitySet);
			this.Visit(associationSetEnd.ParentAssociationSet);
		}

		// Token: 0x060040C8 RID: 16584 RVA: 0x000D9C3A File Offset: 0x000D7E3A
		protected virtual void Visit(EdmProperty edmProperty)
		{
			this.Visit(edmProperty.TypeUsage);
		}

		// Token: 0x060040C9 RID: 16585 RVA: 0x000D9C48 File Offset: 0x000D7E48
		protected virtual void Visit(NavigationProperty navigationProperty)
		{
			this.Visit(navigationProperty.FromEndMember);
			this.Visit(navigationProperty.RelationshipType);
			this.Visit(navigationProperty.ToEndMember);
			this.Visit(navigationProperty.TypeUsage);
		}

		// Token: 0x060040CA RID: 16586 RVA: 0x000D9C7A File Offset: 0x000D7E7A
		protected virtual void Visit(EdmMember edmMember)
		{
			this.Visit(edmMember.TypeUsage);
		}

		// Token: 0x060040CB RID: 16587 RVA: 0x000D9C88 File Offset: 0x000D7E88
		protected virtual void Visit(AssociationEndMember associationEndMember)
		{
			this.Visit(associationEndMember.TypeUsage);
		}

		// Token: 0x060040CC RID: 16588 RVA: 0x000D9C98 File Offset: 0x000D7E98
		protected virtual void Visit(ReferentialConstraint referentialConstraint)
		{
			foreach (EdmProperty edmProperty in this.GetSequence<EdmProperty>(referentialConstraint.FromProperties, (EdmProperty it) => it.Identity))
			{
				this.Visit(edmProperty);
			}
			this.Visit(referentialConstraint.FromRole);
			foreach (EdmProperty edmProperty2 in this.GetSequence<EdmProperty>(referentialConstraint.ToProperties, (EdmProperty it) => it.Identity))
			{
				this.Visit(edmProperty2);
			}
			this.Visit(referentialConstraint.ToRole);
		}

		// Token: 0x060040CD RID: 16589 RVA: 0x000D9D84 File Offset: 0x000D7F84
		protected virtual void Visit(RelationshipEndMember relationshipEndMember)
		{
			this.Visit(relationshipEndMember.TypeUsage);
		}

		// Token: 0x060040CE RID: 16590 RVA: 0x000D9D94 File Offset: 0x000D7F94
		protected virtual void Visit(TypeUsage typeUsage)
		{
			this.Visit(typeUsage.EdmType);
			foreach (Facet facet in this.GetSequence<Facet>(typeUsage.Facets, (Facet it) => it.Identity))
			{
				this.Visit(facet);
			}
		}

		// Token: 0x060040CF RID: 16591 RVA: 0x000D9E14 File Offset: 0x000D8014
		protected virtual void Visit(RelationshipType relationshipType)
		{
			if (relationshipType == null)
			{
				return;
			}
			if (relationshipType.BuiltInTypeKind == BuiltInTypeKind.AssociationType)
			{
				this.Visit((AssociationType)relationshipType);
			}
		}

		// Token: 0x060040D0 RID: 16592 RVA: 0x000D9E30 File Offset: 0x000D8030
		protected virtual void Visit(EdmType edmType)
		{
			if (edmType == null)
			{
				return;
			}
			BuiltInTypeKind builtInTypeKind = edmType.BuiltInTypeKind;
			if (builtInTypeKind > BuiltInTypeKind.ComplexType)
			{
				switch (builtInTypeKind)
				{
				case BuiltInTypeKind.EntityType:
					this.Visit((EntityType)edmType);
					return;
				case BuiltInTypeKind.EnumType:
					this.Visit((EnumType)edmType);
					break;
				case BuiltInTypeKind.EnumMember:
				case BuiltInTypeKind.Facet:
					break;
				case BuiltInTypeKind.EdmFunction:
					this.Visit((EdmFunction)edmType);
					return;
				default:
					if (builtInTypeKind == BuiltInTypeKind.PrimitiveType)
					{
						this.Visit((PrimitiveType)edmType);
						return;
					}
					if (builtInTypeKind != BuiltInTypeKind.RefType)
					{
						return;
					}
					this.Visit((RefType)edmType);
					return;
				}
				return;
			}
			if (builtInTypeKind == BuiltInTypeKind.AssociationType)
			{
				this.Visit((AssociationType)edmType);
				return;
			}
			if (builtInTypeKind == BuiltInTypeKind.CollectionType)
			{
				this.Visit((CollectionType)edmType);
				return;
			}
			if (builtInTypeKind != BuiltInTypeKind.ComplexType)
			{
				return;
			}
			this.Visit((ComplexType)edmType);
		}

		// Token: 0x060040D1 RID: 16593 RVA: 0x000D9EE8 File Offset: 0x000D80E8
		protected virtual void Visit(Facet facet)
		{
			this.Visit(facet.FacetType);
		}

		// Token: 0x060040D2 RID: 16594 RVA: 0x000D9EF8 File Offset: 0x000D80F8
		protected virtual void Visit(EdmFunction edmFunction)
		{
			this.Visit(edmFunction.BaseType);
			foreach (EntitySet entitySet in this.GetSequence<EntitySet>(edmFunction.EntitySets, (EntitySet it) => it.Identity))
			{
				if (entitySet != null)
				{
					this.Visit(entitySet);
				}
			}
			foreach (FunctionParameter functionParameter in this.GetSequence<FunctionParameter>(edmFunction.Parameters, (FunctionParameter it) => it.Identity))
			{
				this.Visit(functionParameter);
			}
			foreach (FunctionParameter functionParameter2 in this.GetSequence<FunctionParameter>(edmFunction.ReturnParameters, (FunctionParameter it) => it.Identity))
			{
				this.Visit(functionParameter2);
			}
		}

		// Token: 0x060040D3 RID: 16595 RVA: 0x000DA040 File Offset: 0x000D8240
		protected virtual void Visit(PrimitiveType primitiveType)
		{
		}

		// Token: 0x060040D4 RID: 16596 RVA: 0x000DA044 File Offset: 0x000D8244
		protected virtual void Visit(ComplexType complexType)
		{
			this.Visit(complexType.BaseType);
			foreach (EdmMember edmMember in this.GetSequence<EdmMember>(complexType.Members, (EdmMember it) => it.Identity))
			{
				this.Visit(edmMember);
			}
			foreach (EdmProperty edmProperty in this.GetSequence<EdmProperty>(complexType.Properties, (EdmProperty it) => it.Identity))
			{
				this.Visit(edmProperty);
			}
		}

		// Token: 0x060040D5 RID: 16597 RVA: 0x000DA124 File Offset: 0x000D8324
		protected virtual void Visit(RefType refType)
		{
			this.Visit(refType.BaseType);
			this.Visit(refType.ElementType);
		}

		// Token: 0x060040D6 RID: 16598 RVA: 0x000DA140 File Offset: 0x000D8340
		protected virtual void Visit(EnumType enumType)
		{
			foreach (EnumMember enumMember in this.GetSequence<EnumMember>(enumType.Members, (EnumMember it) => it.Identity))
			{
				this.Visit(enumMember);
			}
		}

		// Token: 0x060040D7 RID: 16599 RVA: 0x000DA1B4 File Offset: 0x000D83B4
		protected virtual void Visit(EnumMember enumMember)
		{
		}

		// Token: 0x060040D8 RID: 16600 RVA: 0x000DA1B6 File Offset: 0x000D83B6
		protected virtual void Visit(CollectionType collectionType)
		{
			this.Visit(collectionType.BaseType);
			this.Visit(collectionType.TypeUsage);
		}

		// Token: 0x060040D9 RID: 16601 RVA: 0x000DA1D0 File Offset: 0x000D83D0
		protected virtual void Visit(EntityTypeBase entityTypeBase)
		{
			if (entityTypeBase == null)
			{
				return;
			}
			BuiltInTypeKind builtInTypeKind = entityTypeBase.BuiltInTypeKind;
			if (builtInTypeKind == BuiltInTypeKind.AssociationType)
			{
				this.Visit((AssociationType)entityTypeBase);
				return;
			}
			if (builtInTypeKind != BuiltInTypeKind.EntityType)
			{
				return;
			}
			this.Visit((EntityType)entityTypeBase);
		}

		// Token: 0x060040DA RID: 16602 RVA: 0x000DA20B File Offset: 0x000D840B
		protected virtual void Visit(FunctionParameter functionParameter)
		{
			this.Visit(functionParameter.DeclaringFunction);
			this.Visit(functionParameter.TypeUsage);
		}

		// Token: 0x060040DB RID: 16603 RVA: 0x000DA225 File Offset: 0x000D8425
		protected virtual void Visit(DbProviderManifest providerManifest)
		{
		}

		// Token: 0x060040DC RID: 16604 RVA: 0x000DA228 File Offset: 0x000D8428
		protected virtual void Visit(TypeMapping typeMapping)
		{
			foreach (EntityTypeBase entityTypeBase in this.GetSequence<EntityTypeBase>(typeMapping.IsOfTypes, (EntityTypeBase it) => it.Identity))
			{
				this.Visit(entityTypeBase);
			}
			foreach (MappingFragment mappingFragment in this.GetSequence<MappingFragment>(typeMapping.MappingFragments, (MappingFragment it) => BaseMetadataMappingVisitor.IdentityHelper.GetIdentity(it)))
			{
				this.Visit(mappingFragment);
			}
			this.Visit(typeMapping.SetMapping);
			foreach (EntityTypeBase entityTypeBase2 in this.GetSequence<EntityTypeBase>(typeMapping.Types, (EntityTypeBase it) => it.Identity))
			{
				this.Visit(entityTypeBase2);
			}
		}

		// Token: 0x060040DD RID: 16605 RVA: 0x000DA36C File Offset: 0x000D856C
		protected virtual void Visit(MappingFragment mappingFragment)
		{
			foreach (PropertyMapping propertyMapping in this.GetSequence<PropertyMapping>(mappingFragment.AllProperties, (PropertyMapping it) => BaseMetadataMappingVisitor.IdentityHelper.GetIdentity(it)))
			{
				this.Visit(propertyMapping);
			}
			this.Visit(mappingFragment.TableSet);
		}

		// Token: 0x060040DE RID: 16606 RVA: 0x000DA3EC File Offset: 0x000D85EC
		protected virtual void Visit(PropertyMapping propertyMapping)
		{
			if (propertyMapping.GetType() == typeof(ComplexPropertyMapping))
			{
				this.Visit((ComplexPropertyMapping)propertyMapping);
				return;
			}
			if (propertyMapping.GetType() == typeof(ConditionPropertyMapping))
			{
				this.Visit((ConditionPropertyMapping)propertyMapping);
				return;
			}
			if (propertyMapping.GetType() == typeof(ScalarPropertyMapping))
			{
				this.Visit((ScalarPropertyMapping)propertyMapping);
			}
		}

		// Token: 0x060040DF RID: 16607 RVA: 0x000DA464 File Offset: 0x000D8664
		protected virtual void Visit(ComplexPropertyMapping complexPropertyMapping)
		{
			this.Visit(complexPropertyMapping.Property);
			foreach (ComplexTypeMapping complexTypeMapping in this.GetSequence<ComplexTypeMapping>(complexPropertyMapping.TypeMappings, (ComplexTypeMapping it) => BaseMetadataMappingVisitor.IdentityHelper.GetIdentity(it)))
			{
				this.Visit(complexTypeMapping);
			}
		}

		// Token: 0x060040E0 RID: 16608 RVA: 0x000DA4E4 File Offset: 0x000D86E4
		protected virtual void Visit(ConditionPropertyMapping conditionPropertyMapping)
		{
			this.Visit(conditionPropertyMapping.Column);
			this.Visit(conditionPropertyMapping.Property);
		}

		// Token: 0x060040E1 RID: 16609 RVA: 0x000DA4FE File Offset: 0x000D86FE
		protected virtual void Visit(ScalarPropertyMapping scalarPropertyMapping)
		{
			this.Visit(scalarPropertyMapping.Column);
			this.Visit(scalarPropertyMapping.Property);
		}

		// Token: 0x060040E2 RID: 16610 RVA: 0x000DA518 File Offset: 0x000D8718
		protected virtual void Visit(ComplexTypeMapping complexTypeMapping)
		{
			foreach (PropertyMapping propertyMapping in this.GetSequence<PropertyMapping>(complexTypeMapping.AllProperties, (PropertyMapping it) => BaseMetadataMappingVisitor.IdentityHelper.GetIdentity(it)))
			{
				this.Visit(propertyMapping);
			}
			foreach (ComplexType complexType in this.GetSequence<ComplexType>(complexTypeMapping.IsOfTypes, (ComplexType it) => it.Identity))
			{
				this.Visit(complexType);
			}
			foreach (ComplexType complexType2 in this.GetSequence<ComplexType>(complexTypeMapping.Types, (ComplexType it) => it.Identity))
			{
				this.Visit(complexType2);
			}
		}

		// Token: 0x060040E3 RID: 16611 RVA: 0x000DA650 File Offset: 0x000D8850
		protected IEnumerable<T> GetSequence<T>(IEnumerable<T> sequence, Func<T, string> keySelector)
		{
			if (!this._sortSequence)
			{
				return sequence;
			}
			return sequence.OrderBy(keySelector, StringComparer.Ordinal);
		}

		// Token: 0x04001682 RID: 5762
		private readonly bool _sortSequence;

		// Token: 0x02000B2E RID: 2862
		internal static class IdentityHelper
		{
			// Token: 0x06006516 RID: 25878 RVA: 0x0015B80B File Offset: 0x00159A0B
			public static string GetIdentity(EntitySetBaseMapping mapping)
			{
				return mapping.Set.Identity;
			}

			// Token: 0x06006517 RID: 25879 RVA: 0x0015B818 File Offset: 0x00159A18
			public static string GetIdentity(TypeMapping mapping)
			{
				EntityTypeMapping entityTypeMapping = mapping as EntityTypeMapping;
				if (entityTypeMapping != null)
				{
					return BaseMetadataMappingVisitor.IdentityHelper.GetIdentity(entityTypeMapping);
				}
				return BaseMetadataMappingVisitor.IdentityHelper.GetIdentity((AssociationTypeMapping)mapping);
			}

			// Token: 0x06006518 RID: 25880 RVA: 0x0015B844 File Offset: 0x00159A44
			public static string GetIdentity(EntityTypeMapping mapping)
			{
				IOrderedEnumerable<string> orderedEnumerable = mapping.Types.Select((EntityTypeBase it) => it.Identity).OrderBy((string it) => it, StringComparer.Ordinal);
				IOrderedEnumerable<string> orderedEnumerable2 = mapping.IsOfTypes.Select((EntityTypeBase it) => it.Identity).OrderBy((string it) => it, StringComparer.Ordinal);
				return string.Join(",", orderedEnumerable.Concat(orderedEnumerable2));
			}

			// Token: 0x06006519 RID: 25881 RVA: 0x0015B90A File Offset: 0x00159B0A
			public static string GetIdentity(AssociationTypeMapping mapping)
			{
				return mapping.AssociationType.Identity;
			}

			// Token: 0x0600651A RID: 25882 RVA: 0x0015B918 File Offset: 0x00159B18
			public static string GetIdentity(ComplexTypeMapping mapping)
			{
				IOrderedEnumerable<string> orderedEnumerable = mapping.AllProperties.Select((PropertyMapping it) => BaseMetadataMappingVisitor.IdentityHelper.GetIdentity(it)).OrderBy((string it) => it, StringComparer.Ordinal);
				IOrderedEnumerable<string> orderedEnumerable2 = mapping.Types.Select((ComplexType it) => it.Identity).OrderBy((string it) => it, StringComparer.Ordinal);
				IOrderedEnumerable<string> orderedEnumerable3 = mapping.IsOfTypes.Select((ComplexType it) => it.Identity).OrderBy((string it) => it, StringComparer.Ordinal);
				return string.Join(",", orderedEnumerable.Concat(orderedEnumerable2).Concat(orderedEnumerable3));
			}

			// Token: 0x0600651B RID: 25883 RVA: 0x0015BA38 File Offset: 0x00159C38
			public static string GetIdentity(MappingFragment mapping)
			{
				return mapping.TableSet.Identity;
			}

			// Token: 0x0600651C RID: 25884 RVA: 0x0015BA48 File Offset: 0x00159C48
			public static string GetIdentity(PropertyMapping mapping)
			{
				ScalarPropertyMapping scalarPropertyMapping = mapping as ScalarPropertyMapping;
				if (scalarPropertyMapping != null)
				{
					return BaseMetadataMappingVisitor.IdentityHelper.GetIdentity(scalarPropertyMapping);
				}
				ComplexPropertyMapping complexPropertyMapping = mapping as ComplexPropertyMapping;
				if (complexPropertyMapping != null)
				{
					return BaseMetadataMappingVisitor.IdentityHelper.GetIdentity(complexPropertyMapping);
				}
				EndPropertyMapping endPropertyMapping = mapping as EndPropertyMapping;
				if (endPropertyMapping != null)
				{
					return BaseMetadataMappingVisitor.IdentityHelper.GetIdentity(endPropertyMapping);
				}
				return BaseMetadataMappingVisitor.IdentityHelper.GetIdentity((ConditionPropertyMapping)mapping);
			}

			// Token: 0x0600651D RID: 25885 RVA: 0x0015BA94 File Offset: 0x00159C94
			public static string GetIdentity(ScalarPropertyMapping mapping)
			{
				return string.Concat(new string[]
				{
					"ScalarProperty(Identity=",
					mapping.Property.Identity,
					",ColumnIdentity=",
					mapping.Column.Identity,
					")"
				});
			}

			// Token: 0x0600651E RID: 25886 RVA: 0x0015BAE0 File Offset: 0x00159CE0
			public static string GetIdentity(ComplexPropertyMapping mapping)
			{
				return "ComplexProperty(Identity=" + mapping.Property.Identity + ")";
			}

			// Token: 0x0600651F RID: 25887 RVA: 0x0015BAFC File Offset: 0x00159CFC
			public static string GetIdentity(ConditionPropertyMapping mapping)
			{
				if (mapping.Property == null)
				{
					return "ConditionProperty(ColumnIdentity=" + mapping.Column.Identity + ")";
				}
				return "ConditionProperty(Identity=" + mapping.Property.Identity + ")";
			}

			// Token: 0x06006520 RID: 25888 RVA: 0x0015BB3B File Offset: 0x00159D3B
			public static string GetIdentity(EndPropertyMapping mapping)
			{
				return "EndProperty(Identity=" + mapping.AssociationEnd.Identity + ")";
			}
		}
	}
}
