﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Utilities;
using System.Threading;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004B9 RID: 1209
	public class EntitySet : EntitySetBase
	{
		// Token: 0x06003BDE RID: 15326 RVA: 0x000C5C30 File Offset: 0x000C3E30
		internal EntitySet()
		{
		}

		// Token: 0x06003BDF RID: 15327 RVA: 0x000C5C38 File Offset: 0x000C3E38
		internal EntitySet(string name, string schema, string table, string definingQuery, EntityType entityType)
			: base(name, schema, table, definingQuery, entityType)
		{
		}

		// Token: 0x17000BA2 RID: 2978
		// (get) Token: 0x06003BE0 RID: 15328 RVA: 0x000C5C47 File Offset: 0x000C3E47
		public override BuiltInTypeKind BuiltInTypeKind
		{
			get
			{
				return BuiltInTypeKind.EntitySet;
			}
		}

		// Token: 0x17000BA3 RID: 2979
		// (get) Token: 0x06003BE1 RID: 15329 RVA: 0x000C5C4B File Offset: 0x000C3E4B
		public new virtual EntityType ElementType
		{
			get
			{
				return (EntityType)base.ElementType;
			}
		}

		// Token: 0x17000BA4 RID: 2980
		// (get) Token: 0x06003BE2 RID: 15330 RVA: 0x000C5C58 File Offset: 0x000C3E58
		internal ReadOnlyCollection<Tuple<AssociationSet, ReferentialConstraint>> ForeignKeyDependents
		{
			get
			{
				if (this._foreignKeyDependents == null)
				{
					this.InitializeForeignKeyLists();
				}
				return this._foreignKeyDependents;
			}
		}

		// Token: 0x17000BA5 RID: 2981
		// (get) Token: 0x06003BE3 RID: 15331 RVA: 0x000C5C6E File Offset: 0x000C3E6E
		internal ReadOnlyCollection<Tuple<AssociationSet, ReferentialConstraint>> ForeignKeyPrincipals
		{
			get
			{
				if (this._foreignKeyPrincipals == null)
				{
					this.InitializeForeignKeyLists();
				}
				return this._foreignKeyPrincipals;
			}
		}

		// Token: 0x17000BA6 RID: 2982
		// (get) Token: 0x06003BE4 RID: 15332 RVA: 0x000C5C84 File Offset: 0x000C3E84
		internal ReadOnlyCollection<AssociationSet> AssociationSets
		{
			get
			{
				if (this._foreignKeyPrincipals == null)
				{
					this.InitializeForeignKeyLists();
				}
				return this._associationSets;
			}
		}

		// Token: 0x17000BA7 RID: 2983
		// (get) Token: 0x06003BE5 RID: 15333 RVA: 0x000C5C9A File Offset: 0x000C3E9A
		internal bool HasForeignKeyRelationships
		{
			get
			{
				if (this._foreignKeyPrincipals == null)
				{
					this.InitializeForeignKeyLists();
				}
				return this._hasForeignKeyRelationships;
			}
		}

		// Token: 0x17000BA8 RID: 2984
		// (get) Token: 0x06003BE6 RID: 15334 RVA: 0x000C5CB2 File Offset: 0x000C3EB2
		internal bool HasIndependentRelationships
		{
			get
			{
				if (this._foreignKeyPrincipals == null)
				{
					this.InitializeForeignKeyLists();
				}
				return this._hasIndependentRelationships;
			}
		}

		// Token: 0x06003BE7 RID: 15335 RVA: 0x000C5CCC File Offset: 0x000C3ECC
		private void InitializeForeignKeyLists()
		{
			List<Tuple<AssociationSet, ReferentialConstraint>> list = new List<Tuple<AssociationSet, ReferentialConstraint>>();
			List<Tuple<AssociationSet, ReferentialConstraint>> list2 = new List<Tuple<AssociationSet, ReferentialConstraint>>();
			bool flag = false;
			bool flag2 = false;
			ReadOnlyCollection<AssociationSet> readOnlyCollection = new ReadOnlyCollection<AssociationSet>(MetadataHelper.GetAssociationsForEntitySet(this));
			foreach (AssociationSet associationSet in readOnlyCollection)
			{
				if (associationSet.ElementType.IsForeignKey)
				{
					flag = true;
					ReferentialConstraint referentialConstraint = associationSet.ElementType.ReferentialConstraints[0];
					if (referentialConstraint.ToRole.GetEntityType().IsAssignableFrom(this.ElementType) || this.ElementType.IsAssignableFrom(referentialConstraint.ToRole.GetEntityType()))
					{
						list.Add(new Tuple<AssociationSet, ReferentialConstraint>(associationSet, referentialConstraint));
					}
					if (referentialConstraint.FromRole.GetEntityType().IsAssignableFrom(this.ElementType) || this.ElementType.IsAssignableFrom(referentialConstraint.FromRole.GetEntityType()))
					{
						list2.Add(new Tuple<AssociationSet, ReferentialConstraint>(associationSet, referentialConstraint));
					}
				}
				else
				{
					flag2 = true;
				}
			}
			this._hasForeignKeyRelationships = flag;
			this._hasIndependentRelationships = flag2;
			ReadOnlyCollection<Tuple<AssociationSet, ReferentialConstraint>> readOnlyCollection2 = new ReadOnlyCollection<Tuple<AssociationSet, ReferentialConstraint>>(list);
			ReadOnlyCollection<Tuple<AssociationSet, ReferentialConstraint>> readOnlyCollection3 = new ReadOnlyCollection<Tuple<AssociationSet, ReferentialConstraint>>(list2);
			Interlocked.CompareExchange<ReadOnlyCollection<Tuple<AssociationSet, ReferentialConstraint>>>(ref this._foreignKeyDependents, readOnlyCollection2, null);
			Interlocked.CompareExchange<ReadOnlyCollection<Tuple<AssociationSet, ReferentialConstraint>>>(ref this._foreignKeyPrincipals, readOnlyCollection3, null);
			Interlocked.CompareExchange<ReadOnlyCollection<AssociationSet>>(ref this._associationSets, readOnlyCollection, null);
		}

		// Token: 0x06003BE8 RID: 15336 RVA: 0x000C5E34 File Offset: 0x000C4034
		public static EntitySet Create(string name, string schema, string table, string definingQuery, EntityType entityType, IEnumerable<MetadataProperty> metadataProperties)
		{
			Check.NotEmpty(name, "name");
			Check.NotNull<EntityType>(entityType, "entityType");
			EntitySet entitySet = new EntitySet(name, schema, table, definingQuery, entityType);
			if (metadataProperties != null)
			{
				entitySet.AddMetadataProperties(metadataProperties);
			}
			entitySet.SetReadOnly();
			return entitySet;
		}

		// Token: 0x0400149C RID: 5276
		private ReadOnlyCollection<Tuple<AssociationSet, ReferentialConstraint>> _foreignKeyDependents;

		// Token: 0x0400149D RID: 5277
		private ReadOnlyCollection<Tuple<AssociationSet, ReferentialConstraint>> _foreignKeyPrincipals;

		// Token: 0x0400149E RID: 5278
		private ReadOnlyCollection<AssociationSet> _associationSets;

		// Token: 0x0400149F RID: 5279
		private volatile bool _hasForeignKeyRelationships;

		// Token: 0x040014A0 RID: 5280
		private volatile bool _hasIndependentRelationships;
	}
}
