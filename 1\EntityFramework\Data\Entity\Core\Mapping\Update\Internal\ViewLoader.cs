﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Linq;
using System.Threading;

namespace System.Data.Entity.Core.Mapping.Update.Internal
{
	// Token: 0x020005DA RID: 1498
	internal class ViewLoader
	{
		// Token: 0x0600487E RID: 18558 RVA: 0x001010DC File Offset: 0x000FF2DC
		internal ViewLoader(StorageMappingItemCollection mappingCollection)
		{
			this.m_mappingCollection = mappingCollection;
		}

		// Token: 0x0600487F RID: 18559 RVA: 0x0010113D File Offset: 0x000FF33D
		internal ModificationFunctionMappingTranslator GetFunctionMappingTranslator(EntitySetBase extent, MetadataWorkspace workspace)
		{
			return this.SyncGetValue<EntitySetBase, ModificationFunctionMappingTranslator>(extent, workspace, this.m_functionMappingTranslators, extent);
		}

		// Token: 0x06004880 RID: 18560 RVA: 0x0010114E File Offset: 0x000FF34E
		internal Set<EntitySet> GetAffectedTables(EntitySetBase extent, MetadataWorkspace workspace)
		{
			return this.SyncGetValue<EntitySetBase, Set<EntitySet>>(extent, workspace, this.m_affectedTables, extent);
		}

		// Token: 0x06004881 RID: 18561 RVA: 0x0010115F File Offset: 0x000FF35F
		internal AssociationSetMetadata GetAssociationSetMetadata(AssociationSet associationSet, MetadataWorkspace workspace)
		{
			return this.SyncGetValue<AssociationSet, AssociationSetMetadata>(associationSet, workspace, this.m_associationSetMetadata, associationSet);
		}

		// Token: 0x06004882 RID: 18562 RVA: 0x00101170 File Offset: 0x000FF370
		internal bool IsServerGen(EntitySetBase entitySetBase, MetadataWorkspace workspace, EdmMember member)
		{
			return this.SyncContains<EdmMember>(entitySetBase, workspace, this.m_serverGenProperties, member);
		}

		// Token: 0x06004883 RID: 18563 RVA: 0x00101181 File Offset: 0x000FF381
		internal bool IsNullConditionMember(EntitySetBase entitySetBase, MetadataWorkspace workspace, EdmMember member)
		{
			return this.SyncContains<EdmMember>(entitySetBase, workspace, this.m_isNullConditionProperties, member);
		}

		// Token: 0x06004884 RID: 18564 RVA: 0x00101194 File Offset: 0x000FF394
		private T_Value SyncGetValue<T_Key, T_Value>(EntitySetBase entitySetBase, MetadataWorkspace workspace, Dictionary<T_Key, T_Value> dictionary, T_Key key)
		{
			return this.SyncInitializeEntitySet<T_Key, T_Value>(entitySetBase, workspace, (T_Key k) => dictionary[k], key);
		}

		// Token: 0x06004885 RID: 18565 RVA: 0x001011C4 File Offset: 0x000FF3C4
		private bool SyncContains<T_Element>(EntitySetBase entitySetBase, MetadataWorkspace workspace, Set<T_Element> set, T_Element element)
		{
			return this.SyncInitializeEntitySet<T_Element, bool>(entitySetBase, workspace, new Func<T_Element, bool>(set.Contains), element);
		}

		// Token: 0x06004886 RID: 18566 RVA: 0x001011DC File Offset: 0x000FF3DC
		private TResult SyncInitializeEntitySet<TArg, TResult>(EntitySetBase entitySetBase, MetadataWorkspace workspace, Func<TArg, TResult> evaluate, TArg arg)
		{
			this.m_readerWriterLock.EnterReadLock();
			try
			{
				if (this.m_affectedTables.ContainsKey(entitySetBase))
				{
					return evaluate(arg);
				}
			}
			finally
			{
				this.m_readerWriterLock.ExitReadLock();
			}
			this.m_readerWriterLock.EnterWriteLock();
			TResult tresult;
			try
			{
				if (this.m_affectedTables.ContainsKey(entitySetBase))
				{
					tresult = evaluate(arg);
				}
				else
				{
					this.InitializeEntitySet(entitySetBase, workspace);
					tresult = evaluate(arg);
				}
			}
			finally
			{
				this.m_readerWriterLock.ExitWriteLock();
			}
			return tresult;
		}

		// Token: 0x06004887 RID: 18567 RVA: 0x0010127C File Offset: 0x000FF47C
		private void InitializeEntitySet(EntitySetBase entitySetBase, MetadataWorkspace workspace)
		{
			EntityContainerMapping entityContainerMapping = (EntityContainerMapping)this.m_mappingCollection.GetMap(entitySetBase.EntityContainer);
			if (entityContainerMapping.HasViews)
			{
				this.m_mappingCollection.GetGeneratedView(entitySetBase, workspace);
			}
			Set<EntitySet> set = new Set<EntitySet>();
			if (entityContainerMapping != null)
			{
				Set<EdmMember> set2 = new Set<EdmMember>();
				EntitySetBaseMapping entitySetBaseMapping;
				if (entitySetBase.BuiltInTypeKind == BuiltInTypeKind.EntitySet)
				{
					entitySetBaseMapping = entityContainerMapping.GetEntitySetMapping(entitySetBase.Name);
					this.m_serverGenProperties.Unite(ViewLoader.GetMembersWithResultBinding((EntitySetMapping)entitySetBaseMapping));
				}
				else
				{
					if (entitySetBase.BuiltInTypeKind != BuiltInTypeKind.AssociationSet)
					{
						throw new NotSupportedException();
					}
					entitySetBaseMapping = entityContainerMapping.GetAssociationSetMapping(entitySetBase.Name);
				}
				foreach (MappingFragment mappingFragment in ViewLoader.GetMappingFragments(entitySetBaseMapping))
				{
					set.Add(mappingFragment.TableSet);
					this.m_serverGenProperties.AddRange(ViewLoader.FindServerGenMembers(mappingFragment));
					set2.AddRange(ViewLoader.FindIsNullConditionColumns(mappingFragment));
				}
				if (0 < set2.Count)
				{
					foreach (MappingFragment mappingFragment2 in ViewLoader.GetMappingFragments(entitySetBaseMapping))
					{
						this.m_isNullConditionProperties.AddRange(ViewLoader.FindPropertiesMappedToColumns(set2, mappingFragment2));
					}
				}
			}
			this.m_affectedTables.Add(entitySetBase, set.MakeReadOnly());
			this.InitializeFunctionMappingTranslators(entitySetBase, entityContainerMapping);
			if (entitySetBase.BuiltInTypeKind == BuiltInTypeKind.AssociationSet)
			{
				AssociationSet associationSet = (AssociationSet)entitySetBase;
				if (!this.m_associationSetMetadata.ContainsKey(associationSet))
				{
					this.m_associationSetMetadata.Add(associationSet, new AssociationSetMetadata(this.m_affectedTables[associationSet], associationSet, workspace));
				}
			}
		}

		// Token: 0x06004888 RID: 18568 RVA: 0x00101434 File Offset: 0x000FF634
		private static IEnumerable<EdmMember> GetMembersWithResultBinding(EntitySetMapping entitySetMapping)
		{
			foreach (EntityTypeModificationFunctionMapping typeFunctionMapping in entitySetMapping.ModificationFunctionMappings)
			{
				if (typeFunctionMapping.InsertFunctionMapping != null && typeFunctionMapping.InsertFunctionMapping.ResultBindings != null)
				{
					foreach (ModificationFunctionResultBinding modificationFunctionResultBinding in typeFunctionMapping.InsertFunctionMapping.ResultBindings)
					{
						yield return modificationFunctionResultBinding.Property;
					}
					IEnumerator<ModificationFunctionResultBinding> enumerator2 = null;
				}
				if (typeFunctionMapping.UpdateFunctionMapping != null && typeFunctionMapping.UpdateFunctionMapping.ResultBindings != null)
				{
					foreach (ModificationFunctionResultBinding modificationFunctionResultBinding2 in typeFunctionMapping.UpdateFunctionMapping.ResultBindings)
					{
						yield return modificationFunctionResultBinding2.Property;
					}
					IEnumerator<ModificationFunctionResultBinding> enumerator2 = null;
				}
				typeFunctionMapping = null;
			}
			IEnumerator<EntityTypeModificationFunctionMapping> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x06004889 RID: 18569 RVA: 0x00101444 File Offset: 0x000FF644
		private void InitializeFunctionMappingTranslators(EntitySetBase entitySetBase, EntityContainerMapping mapping)
		{
			KeyToListMap<AssociationSet, AssociationEndMember> keyToListMap = new KeyToListMap<AssociationSet, AssociationEndMember>(EqualityComparer<AssociationSet>.Default);
			if (!this.m_functionMappingTranslators.ContainsKey(entitySetBase))
			{
				foreach (EntitySetBaseMapping entitySetBaseMapping in mapping.EntitySetMaps)
				{
					EntitySetMapping entitySetMapping = (EntitySetMapping)entitySetBaseMapping;
					if (0 < entitySetMapping.ModificationFunctionMappings.Count)
					{
						this.m_functionMappingTranslators.Add(entitySetMapping.Set, ModificationFunctionMappingTranslator.CreateEntitySetTranslator(entitySetMapping));
						using (IEnumerator<AssociationSetEnd> enumerator2 = entitySetMapping.ImplicitlyMappedAssociationSetEnds.GetEnumerator())
						{
							while (enumerator2.MoveNext())
							{
								AssociationSetEnd associationSetEnd = enumerator2.Current;
								AssociationSet parentAssociationSet = associationSetEnd.ParentAssociationSet;
								if (!this.m_functionMappingTranslators.ContainsKey(parentAssociationSet))
								{
									this.m_functionMappingTranslators.Add(parentAssociationSet, ModificationFunctionMappingTranslator.CreateAssociationSetTranslator(null));
								}
								AssociationSetEnd oppositeEnd = MetadataHelper.GetOppositeEnd(associationSetEnd);
								keyToListMap.Add(parentAssociationSet, oppositeEnd.CorrespondingAssociationEndMember);
							}
							continue;
						}
					}
					this.m_functionMappingTranslators.Add(entitySetMapping.Set, null);
				}
				foreach (EntitySetBaseMapping entitySetBaseMapping2 in mapping.RelationshipSetMaps)
				{
					AssociationSetMapping associationSetMapping = (AssociationSetMapping)entitySetBaseMapping2;
					if (associationSetMapping.ModificationFunctionMapping != null)
					{
						AssociationSet associationSet = (AssociationSet)associationSetMapping.Set;
						this.m_functionMappingTranslators.Add(associationSet, ModificationFunctionMappingTranslator.CreateAssociationSetTranslator(associationSetMapping));
						keyToListMap.AddRange(associationSet, Enumerable.Empty<AssociationEndMember>());
					}
					else if (!this.m_functionMappingTranslators.ContainsKey(associationSetMapping.Set))
					{
						this.m_functionMappingTranslators.Add(associationSetMapping.Set, null);
					}
				}
			}
			foreach (AssociationSet associationSet2 in keyToListMap.Keys)
			{
				this.m_associationSetMetadata.Add(associationSet2, new AssociationSetMetadata(keyToListMap.EnumerateValues(associationSet2)));
			}
		}

		// Token: 0x0600488A RID: 18570 RVA: 0x00101650 File Offset: 0x000FF850
		private static IEnumerable<EdmMember> FindServerGenMembers(MappingFragment mappingFragment)
		{
			foreach (ScalarPropertyMapping scalarPropertyMapping in ViewLoader.FlattenPropertyMappings(mappingFragment.AllProperties).OfType<ScalarPropertyMapping>())
			{
				if (MetadataHelper.GetStoreGeneratedPattern(scalarPropertyMapping.Column) != StoreGeneratedPattern.None)
				{
					yield return scalarPropertyMapping.Property;
				}
			}
			IEnumerator<ScalarPropertyMapping> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x0600488B RID: 18571 RVA: 0x00101660 File Offset: 0x000FF860
		private static IEnumerable<EdmMember> FindIsNullConditionColumns(MappingFragment mappingFragment)
		{
			foreach (ConditionPropertyMapping conditionPropertyMapping in ViewLoader.FlattenPropertyMappings(mappingFragment.AllProperties).OfType<ConditionPropertyMapping>())
			{
				if (conditionPropertyMapping.Column != null && conditionPropertyMapping.IsNull != null)
				{
					yield return conditionPropertyMapping.Column;
				}
			}
			IEnumerator<ConditionPropertyMapping> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x0600488C RID: 18572 RVA: 0x00101670 File Offset: 0x000FF870
		private static IEnumerable<EdmMember> FindPropertiesMappedToColumns(Set<EdmMember> columns, MappingFragment mappingFragment)
		{
			foreach (ScalarPropertyMapping scalarPropertyMapping in ViewLoader.FlattenPropertyMappings(mappingFragment.AllProperties).OfType<ScalarPropertyMapping>())
			{
				if (columns.Contains(scalarPropertyMapping.Column))
				{
					yield return scalarPropertyMapping.Property;
				}
			}
			IEnumerator<ScalarPropertyMapping> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x0600488D RID: 18573 RVA: 0x00101687 File Offset: 0x000FF887
		private static IEnumerable<MappingFragment> GetMappingFragments(EntitySetBaseMapping setMapping)
		{
			foreach (TypeMapping typeMapping in setMapping.TypeMappings)
			{
				foreach (MappingFragment mappingFragment in typeMapping.MappingFragments)
				{
					yield return mappingFragment;
				}
				IEnumerator<MappingFragment> enumerator2 = null;
			}
			IEnumerator<TypeMapping> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x0600488E RID: 18574 RVA: 0x00101697 File Offset: 0x000FF897
		private static IEnumerable<PropertyMapping> FlattenPropertyMappings(ReadOnlyCollection<PropertyMapping> propertyMappings)
		{
			foreach (PropertyMapping propertyMapping in propertyMappings)
			{
				ComplexPropertyMapping complexPropertyMapping = propertyMapping as ComplexPropertyMapping;
				if (complexPropertyMapping != null)
				{
					foreach (ComplexTypeMapping complexTypeMapping in complexPropertyMapping.TypeMappings)
					{
						foreach (PropertyMapping propertyMapping2 in ViewLoader.FlattenPropertyMappings(complexTypeMapping.AllProperties))
						{
							yield return propertyMapping2;
						}
						IEnumerator<PropertyMapping> enumerator3 = null;
					}
					IEnumerator<ComplexTypeMapping> enumerator2 = null;
				}
				else
				{
					yield return propertyMapping;
				}
			}
			IEnumerator<PropertyMapping> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x040019B7 RID: 6583
		private readonly StorageMappingItemCollection m_mappingCollection;

		// Token: 0x040019B8 RID: 6584
		private readonly Dictionary<AssociationSet, AssociationSetMetadata> m_associationSetMetadata = new Dictionary<AssociationSet, AssociationSetMetadata>();

		// Token: 0x040019B9 RID: 6585
		private readonly Dictionary<EntitySetBase, Set<EntitySet>> m_affectedTables = new Dictionary<EntitySetBase, Set<EntitySet>>();

		// Token: 0x040019BA RID: 6586
		private readonly Set<EdmMember> m_serverGenProperties = new Set<EdmMember>();

		// Token: 0x040019BB RID: 6587
		private readonly Set<EdmMember> m_isNullConditionProperties = new Set<EdmMember>();

		// Token: 0x040019BC RID: 6588
		private readonly Dictionary<EntitySetBase, ModificationFunctionMappingTranslator> m_functionMappingTranslators = new Dictionary<EntitySetBase, ModificationFunctionMappingTranslator>(EqualityComparer<EntitySetBase>.Default);

		// Token: 0x040019BD RID: 6589
		private readonly ReaderWriterLockSlim m_readerWriterLock = new ReaderWriterLockSlim();
	}
}
