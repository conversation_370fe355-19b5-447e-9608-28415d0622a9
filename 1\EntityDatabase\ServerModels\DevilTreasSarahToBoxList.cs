﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000007 RID: 7
	public class DevilTreasSarahToBoxList
	{
		// Token: 0x1700000F RID: 15
		// (get) Token: 0x06000022 RID: 34 RVA: 0x00002172 File Offset: 0x00000372
		// (set) Token: 0x06000023 RID: 35 RVA: 0x0000217A File Offset: 0x0000037A
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int ID { get; set; }

		// Token: 0x17000010 RID: 16
		// (get) Token: 0x06000024 RID: 36 RVA: 0x00002183 File Offset: 0x00000383
		// (set) Token: 0x06000025 RID: 37 RVA: 0x0000218B File Offset: 0x0000038B
		public string Exchange { get; set; }
	}
}
