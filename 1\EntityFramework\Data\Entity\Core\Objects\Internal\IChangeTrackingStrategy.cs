﻿using System;
using System.Data.Entity.Core.Objects.DataClasses;

namespace System.Data.Entity.Core.Objects.Internal
{
	// Token: 0x02000446 RID: 1094
	internal interface IChangeTrackingStrategy
	{
		// Token: 0x0600356A RID: 13674
		void SetChangeTracker(IEntityChangeTracker changeTracker);

		// Token: 0x0600356B RID: 13675
		void TakeSnapshot(EntityEntry entry);

		// Token: 0x0600356C RID: 13676
		void SetCurrentValue(EntityEntry entry, StateManagerMemberMetadata member, int ordinal, object target, object value);

		// Token: 0x0600356D RID: 13677
		void UpdateCurrentValueRecord(object value, EntityEntry entry);
	}
}
