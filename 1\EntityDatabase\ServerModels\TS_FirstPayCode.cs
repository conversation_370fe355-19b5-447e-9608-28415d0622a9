﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x0200000F RID: 15
	public class TS_FirstPayCode
	{
		// Token: 0x17000053 RID: 83
		// (get) Token: 0x060000B3 RID: 179 RVA: 0x0000265B File Offset: 0x0000085B
		// (set) Token: 0x060000B4 RID: 180 RVA: 0x00002663 File Offset: 0x00000863
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int ID { get; set; }

		// Token: 0x17000054 RID: 84
		// (get) Token: 0x060000B5 RID: 181 RVA: 0x0000266C File Offset: 0x0000086C
		// (set) Token: 0x060000B6 RID: 182 RVA: 0x00002674 File Offset: 0x00000874
		public int UserID { get; set; }

		// Token: 0x17000055 RID: 85
		// (get) Token: 0x060000B7 RID: 183 RVA: 0x0000267D File Offset: 0x0000087D
		// (set) Token: 0x060000B8 RID: 184 RVA: 0x00002685 File Offset: 0x00000885
		public string Code { get; set; }

		// Token: 0x17000056 RID: 86
		// (get) Token: 0x060000B9 RID: 185 RVA: 0x0000268E File Offset: 0x0000088E
		// (set) Token: 0x060000BA RID: 186 RVA: 0x00002696 File Offset: 0x00000896
		public bool IsUse { get; set; }

		// Token: 0x17000057 RID: 87
		// (get) Token: 0x060000BB RID: 187 RVA: 0x0000269F File Offset: 0x0000089F
		// (set) Token: 0x060000BC RID: 188 RVA: 0x000026A7 File Offset: 0x000008A7
		public int Type { get; set; }

		// Token: 0x17000058 RID: 88
		// (get) Token: 0x060000BD RID: 189 RVA: 0x000026B0 File Offset: 0x000008B0
		// (set) Token: 0x060000BE RID: 190 RVA: 0x000026B8 File Offset: 0x000008B8
		public DateTime UseDate { get; set; }
	}
}
