﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Objects.ELinq;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Text;
using System.Threading;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004F7 RID: 1271
	public class RowType : StructuralType
	{
		// Token: 0x06003F0A RID: 16138 RVA: 0x000D0AFD File Offset: 0x000CECFD
		internal RowType()
		{
		}

		// Token: 0x06003F0B RID: 16139 RVA: 0x000D0B05 File Offset: 0x000CED05
		internal RowType(IEnumerable<EdmProperty> properties)
			: this(properties, null)
		{
		}

		// Token: 0x06003F0C RID: 16140 RVA: 0x000D0B10 File Offset: 0x000CED10
		internal RowType(IEnumerable<EdmProperty> properties, InitializerMetadata initializerMetadata)
			: base(RowType.GetRowTypeIdentityFromProperties(RowType.CheckProperties(properties), initializerMetadata), "Transient", (DataSpace)(-1))
		{
			if (properties != null)
			{
				foreach (EdmProperty edmProperty in properties)
				{
					this.AddProperty(edmProperty);
				}
			}
			this._initializerMetadata = initializerMetadata;
			this.SetReadOnly();
		}

		// Token: 0x17000C63 RID: 3171
		// (get) Token: 0x06003F0D RID: 16141 RVA: 0x000D0B80 File Offset: 0x000CED80
		internal InitializerMetadata InitializerMetadata
		{
			get
			{
				return this._initializerMetadata;
			}
		}

		// Token: 0x17000C64 RID: 3172
		// (get) Token: 0x06003F0E RID: 16142 RVA: 0x000D0B88 File Offset: 0x000CED88
		public override BuiltInTypeKind BuiltInTypeKind
		{
			get
			{
				return BuiltInTypeKind.RowType;
			}
		}

		// Token: 0x17000C65 RID: 3173
		// (get) Token: 0x06003F0F RID: 16143 RVA: 0x000D0B8C File Offset: 0x000CED8C
		public virtual ReadOnlyMetadataCollection<EdmProperty> Properties
		{
			get
			{
				if (this._properties == null)
				{
					Interlocked.CompareExchange<ReadOnlyMetadataCollection<EdmProperty>>(ref this._properties, new FilteredReadOnlyMetadataCollection<EdmProperty, EdmMember>(base.Members, new Predicate<EdmMember>(Helper.IsEdmProperty)), null);
				}
				return this._properties;
			}
		}

		// Token: 0x17000C66 RID: 3174
		// (get) Token: 0x06003F10 RID: 16144 RVA: 0x000D0BC0 File Offset: 0x000CEDC0
		public ReadOnlyMetadataCollection<EdmProperty> DeclaredProperties
		{
			get
			{
				return base.GetDeclaredOnlyMembers<EdmProperty>();
			}
		}

		// Token: 0x06003F11 RID: 16145 RVA: 0x000D0BC8 File Offset: 0x000CEDC8
		private void AddProperty(EdmProperty property)
		{
			Check.NotNull<EdmProperty>(property, "property");
			base.AddMember(property);
		}

		// Token: 0x06003F12 RID: 16146 RVA: 0x000D0BDD File Offset: 0x000CEDDD
		internal override void ValidateMemberForAdd(EdmMember member)
		{
		}

		// Token: 0x06003F13 RID: 16147 RVA: 0x000D0BE0 File Offset: 0x000CEDE0
		private static string GetRowTypeIdentityFromProperties(IEnumerable<EdmProperty> properties, InitializerMetadata initializerMetadata)
		{
			StringBuilder stringBuilder = new StringBuilder("rowtype[");
			if (properties != null)
			{
				int num = 0;
				foreach (EdmProperty edmProperty in properties)
				{
					if (num > 0)
					{
						stringBuilder.Append(",");
					}
					stringBuilder.Append("(");
					stringBuilder.Append(edmProperty.Name);
					stringBuilder.Append(",");
					edmProperty.TypeUsage.BuildIdentity(stringBuilder);
					stringBuilder.Append(")");
					num++;
				}
			}
			stringBuilder.Append("]");
			if (initializerMetadata != null)
			{
				stringBuilder.Append(",").Append(initializerMetadata.Identity);
			}
			return stringBuilder.ToString();
		}

		// Token: 0x06003F14 RID: 16148 RVA: 0x000D0CB0 File Offset: 0x000CEEB0
		private static IEnumerable<EdmProperty> CheckProperties(IEnumerable<EdmProperty> properties)
		{
			if (properties != null)
			{
				int num = 0;
				using (IEnumerator<EdmProperty> enumerator = properties.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						if (enumerator.Current == null)
						{
							throw new ArgumentException(Strings.ADP_CollectionParameterElementIsNull("properties"));
						}
						num++;
					}
				}
			}
			return properties;
		}

		// Token: 0x06003F15 RID: 16149 RVA: 0x000D0D0C File Offset: 0x000CEF0C
		internal override bool EdmEquals(MetadataItem item)
		{
			if (this == item)
			{
				return true;
			}
			if (item == null || BuiltInTypeKind.RowType != item.BuiltInTypeKind)
			{
				return false;
			}
			RowType rowType = (RowType)item;
			if (base.Members.Count != rowType.Members.Count)
			{
				return false;
			}
			for (int i = 0; i < base.Members.Count; i++)
			{
				EdmMember edmMember = base.Members[i];
				EdmMember edmMember2 = rowType.Members[i];
				if (!edmMember.EdmEquals(edmMember2) || !edmMember.TypeUsage.EdmEquals(edmMember2.TypeUsage))
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x06003F16 RID: 16150 RVA: 0x000D0DA0 File Offset: 0x000CEFA0
		public static RowType Create(IEnumerable<EdmProperty> properties, IEnumerable<MetadataProperty> metadataProperties)
		{
			Check.NotNull<IEnumerable<EdmProperty>>(properties, "properties");
			RowType rowType = new RowType(properties);
			if (metadataProperties != null)
			{
				rowType.AddMetadataProperties(metadataProperties);
			}
			rowType.SetReadOnly();
			return rowType;
		}

		// Token: 0x04001583 RID: 5507
		private ReadOnlyMetadataCollection<EdmProperty> _properties;

		// Token: 0x04001584 RID: 5508
		private readonly InitializerMetadata _initializerMetadata;
	}
}
