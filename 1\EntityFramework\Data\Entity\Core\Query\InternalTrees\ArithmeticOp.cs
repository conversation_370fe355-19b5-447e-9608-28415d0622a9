﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x0200037E RID: 894
	internal sealed class ArithmeticOp : ScalarOp
	{
		// Token: 0x06002B15 RID: 11029 RVA: 0x0008C793 File Offset: 0x0008A993
		internal ArithmeticOp(OpType opType, TypeUsage type)
			: base(opType, type)
		{
		}

		// Token: 0x06002B16 RID: 11030 RVA: 0x0008C79D File Offset: 0x0008A99D
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002B17 RID: 11031 RVA: 0x0008C7A7 File Offset: 0x0008A9A7
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}
	}
}
