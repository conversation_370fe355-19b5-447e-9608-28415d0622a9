﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x0200000B RID: 11
	public class TS_Carnival_Items
	{
		// Token: 0x1700002F RID: 47
		// (get) Token: 0x06000067 RID: 103 RVA: 0x000023D3 File Offset: 0x000005D3
		// (set) Token: 0x06000068 RID: 104 RVA: 0x000023DB File Offset: 0x000005DB
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int ID { get; set; }

		// Token: 0x17000030 RID: 48
		// (get) Token: 0x06000069 RID: 105 RVA: 0x000023E4 File Offset: 0x000005E4
		// (set) Token: 0x0600006A RID: 106 RVA: 0x000023EC File Offset: 0x000005EC
		public int ActivityType { get; set; }

		// Token: 0x17000031 RID: 49
		// (get) Token: 0x0600006B RID: 107 RVA: 0x000023F5 File Offset: 0x000005F5
		// (set) Token: 0x0600006C RID: 108 RVA: 0x000023FD File Offset: 0x000005FD
		public int Quality { get; set; }

		// Token: 0x17000032 RID: 50
		// (get) Token: 0x0600006D RID: 109 RVA: 0x00002406 File Offset: 0x00000606
		// (set) Token: 0x0600006E RID: 110 RVA: 0x0000240E File Offset: 0x0000060E
		public int TemplateID { get; set; }

		// Token: 0x17000033 RID: 51
		// (get) Token: 0x0600006F RID: 111 RVA: 0x00002417 File Offset: 0x00000617
		// (set) Token: 0x06000070 RID: 112 RVA: 0x0000241F File Offset: 0x0000061F
		public int ValidDate { get; set; }

		// Token: 0x17000034 RID: 52
		// (get) Token: 0x06000071 RID: 113 RVA: 0x00002428 File Offset: 0x00000628
		// (set) Token: 0x06000072 RID: 114 RVA: 0x00002430 File Offset: 0x00000630
		public int Count { get; set; }

		// Token: 0x17000035 RID: 53
		// (get) Token: 0x06000073 RID: 115 RVA: 0x00002439 File Offset: 0x00000639
		// (set) Token: 0x06000074 RID: 116 RVA: 0x00002441 File Offset: 0x00000641
		public bool IsBind { get; set; }

		// Token: 0x17000036 RID: 54
		// (get) Token: 0x06000075 RID: 117 RVA: 0x0000244A File Offset: 0x0000064A
		// (set) Token: 0x06000076 RID: 118 RVA: 0x00002452 File Offset: 0x00000652
		public int Random { get; set; }
	}
}
