﻿using System;

namespace System.Data.Entity.Core.Common.EntitySql
{
	// Token: 0x02000655 RID: 1621
	internal sealed class GroupKeyAggregateInfo : GroupAggregateInfo
	{
		// Token: 0x06004E14 RID: 19988 RVA: 0x00118045 File Offset: 0x00116245
		internal GroupKeyAggregateInfo(GroupAggregateKind aggregateKind, ErrorContext errCtx, GroupAggregateInfo containingAggregate, ScopeRegion definingScopeRegion)
			: base(aggregateKind, null, errCtx, containingAggregate, definingScopeRegion)
		{
		}
	}
}
