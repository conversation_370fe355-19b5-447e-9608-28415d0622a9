﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.QueryRewriting;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Validation
{
	// Token: 0x0200057D RID: 1405
	internal class ErrorPatternMatcher
	{
		// Token: 0x06004410 RID: 17424 RVA: 0x000ECBC4 File Offset: 0x000EADC4
		private ErrorPatternMatcher(ViewgenContext context, MemberDomainMap domainMap, ErrorLog errorLog)
		{
			this.m_viewgenContext = context;
			this.m_domainMap = domainMap;
			MemberPath.GetKeyMembers(context.Extent, domainMap);
			this.m_errorLog = errorLog;
			this.m_originalErrorCount = this.m_errorLog.Count;
		}

		// Token: 0x06004411 RID: 17425 RVA: 0x000ECC00 File Offset: 0x000EAE00
		public static bool FindMappingErrors(ViewgenContext context, MemberDomainMap domainMap, ErrorLog errorLog)
		{
			if (context.ViewTarget == ViewTarget.QueryView && !context.Config.IsValidationEnabled)
			{
				return false;
			}
			ErrorPatternMatcher errorPatternMatcher = new ErrorPatternMatcher(context, domainMap, errorLog);
			errorPatternMatcher.MatchMissingMappingErrors();
			errorPatternMatcher.MatchConditionErrors();
			errorPatternMatcher.MatchSplitErrors();
			if (errorPatternMatcher.m_errorLog.Count == errorPatternMatcher.m_originalErrorCount)
			{
				errorPatternMatcher.MatchPartitionErrors();
			}
			if (errorPatternMatcher.m_errorLog.Count > errorPatternMatcher.m_originalErrorCount)
			{
				ExceptionHelpers.ThrowMappingException(errorPatternMatcher.m_errorLog, errorPatternMatcher.m_viewgenContext.Config);
			}
			return false;
		}

		// Token: 0x06004412 RID: 17426 RVA: 0x000ECC84 File Offset: 0x000EAE84
		private void MatchMissingMappingErrors()
		{
			if (this.m_viewgenContext.ViewTarget == ViewTarget.QueryView)
			{
				Set<EdmType> set = new Set<EdmType>(MetadataHelper.GetTypeAndSubtypesOf(this.m_viewgenContext.Extent.ElementType, this.m_viewgenContext.EdmItemCollection, false));
				foreach (LeftCellWrapper leftCellWrapper in this.m_viewgenContext.AllWrappersForExtent)
				{
					foreach (Cell cell in leftCellWrapper.Cells)
					{
						foreach (MemberRestriction memberRestriction in cell.CQuery.Conditions)
						{
							foreach (Constant constant in memberRestriction.Domain.Values)
							{
								TypeConstant typeConstant = constant as TypeConstant;
								if (typeConstant != null)
								{
									set.Remove(typeConstant.EdmType);
								}
							}
						}
					}
				}
				if (set.Count > 0)
				{
					this.m_errorLog.AddEntry(new ErrorLog.Record(ViewGenErrorCode.ErrorPatternMissingMappingError, Strings.ViewGen_Missing_Type_Mapping(ErrorPatternMatcher.BuildCommaSeparatedErrorString<EdmType>(set)), this.m_viewgenContext.AllWrappersForExtent, ""));
				}
			}
		}

		// Token: 0x06004413 RID: 17427 RVA: 0x000ECE10 File Offset: 0x000EB010
		private static bool HasNotNullCondition(CellQuery cellQuery, MemberPath member)
		{
			foreach (MemberRestriction memberRestriction in cellQuery.GetConjunctsFromWhereClause())
			{
				if (memberRestriction.RestrictedMemberSlot.MemberPath.Equals(member))
				{
					if (memberRestriction.Domain.Values.Contains(Constant.NotNull))
					{
						return true;
					}
					using (IEnumerator<NegatedConstant> enumerator2 = (from cellConstant in memberRestriction.Domain.Values
						select cellConstant as NegatedConstant into negated
						where negated != null
						select negated).GetEnumerator())
					{
						while (enumerator2.MoveNext())
						{
							if (enumerator2.Current.Elements.Contains(Constant.Null))
							{
								return true;
							}
						}
					}
				}
			}
			return false;
		}

		// Token: 0x06004414 RID: 17428 RVA: 0x000ECF2C File Offset: 0x000EB12C
		private static bool IsMemberPartOfNotNullCondition(IEnumerable<LeftCellWrapper> wrappers, MemberPath leftMember, ViewTarget viewTarget)
		{
			Func<MemberPath, bool> <>9__0;
			foreach (LeftCellWrapper leftCellWrapper in wrappers)
			{
				CellQuery leftQuery = leftCellWrapper.OnlyInputCell.GetLeftQuery(viewTarget);
				if (ErrorPatternMatcher.HasNotNullCondition(leftQuery, leftMember))
				{
					return true;
				}
				CellQuery rightQuery = leftCellWrapper.OnlyInputCell.GetRightQuery(viewTarget);
				IEnumerable<MemberPath> projectedMembers = leftQuery.GetProjectedMembers();
				Func<MemberPath, bool> func;
				if ((func = <>9__0) == null)
				{
					func = (<>9__0 = (MemberPath path) => !path.Equals(leftMember));
				}
				int num = projectedMembers.TakeWhile(func).Count<MemberPath>();
				if (num < leftQuery.GetProjectedMembers().Count<MemberPath>())
				{
					MemberPath memberPath = ((MemberProjectedSlot)rightQuery.ProjectedSlotAt(num)).MemberPath;
					if (ErrorPatternMatcher.HasNotNullCondition(rightQuery, memberPath))
					{
						return true;
					}
				}
			}
			return false;
		}

		// Token: 0x06004415 RID: 17429 RVA: 0x000ED01C File Offset: 0x000EB21C
		private void MatchConditionErrors()
		{
			List<LeftCellWrapper> allWrappersForExtent = this.m_viewgenContext.AllWrappersForExtent;
			Set<MemberPath> set = new Set<MemberPath>();
			Set<Dictionary<MemberPath, Set<Constant>>> set2 = new Set<Dictionary<MemberPath, Set<Constant>>>(new ConditionComparer());
			Dictionary<Dictionary<MemberPath, Set<Constant>>, LeftCellWrapper> dictionary = new Dictionary<Dictionary<MemberPath, Set<Constant>>, LeftCellWrapper>(new ConditionComparer());
			foreach (LeftCellWrapper leftCellWrapper in allWrappersForExtent)
			{
				Dictionary<MemberPath, Set<Constant>> dictionary2 = new Dictionary<MemberPath, Set<Constant>>();
				foreach (MemberRestriction memberRestriction in leftCellWrapper.OnlyInputCell.GetLeftQuery(this.m_viewgenContext.ViewTarget).GetConjunctsFromWhereClause())
				{
					MemberPath memberPath = memberRestriction.RestrictedMemberSlot.MemberPath;
					if (this.m_domainMap.IsConditionMember(memberPath))
					{
						ScalarRestriction scalarRestriction = memberRestriction as ScalarRestriction;
						if (scalarRestriction != null && !set.Contains(memberPath) && !leftCellWrapper.OnlyInputCell.CQuery.WhereClause.Equals(leftCellWrapper.OnlyInputCell.SQuery.WhereClause) && !ErrorPatternMatcher.IsMemberPartOfNotNullCondition(allWrappersForExtent, memberPath, this.m_viewgenContext.ViewTarget))
						{
							this.CheckThatConditionMemberIsNotMapped(memberPath, allWrappersForExtent, set);
						}
						if (this.m_viewgenContext.ViewTarget == ViewTarget.UpdateView && scalarRestriction != null && memberPath.IsNullable && ErrorPatternMatcher.IsMemberPartOfNotNullCondition(new LeftCellWrapper[] { leftCellWrapper }, memberPath, this.m_viewgenContext.ViewTarget))
						{
							MemberPath rightMemberPath = ErrorPatternMatcher.GetRightMemberPath(memberPath, leftCellWrapper);
							if (rightMemberPath != null && rightMemberPath.IsNullable && !ErrorPatternMatcher.IsMemberPartOfNotNullCondition(new LeftCellWrapper[] { leftCellWrapper }, rightMemberPath, this.m_viewgenContext.ViewTarget))
							{
								this.m_errorLog.AddEntry(new ErrorLog.Record(ViewGenErrorCode.ErrorPatternConditionError, Strings.Viewgen_ErrorPattern_NotNullConditionMappedToNullableMember(memberPath, rightMemberPath), leftCellWrapper.OnlyInputCell, ""));
							}
						}
						foreach (Constant constant in memberRestriction.Domain.Values)
						{
							Set<Constant> set3;
							if (!dictionary2.TryGetValue(memberPath, out set3))
							{
								set3 = new Set<Constant>(Constant.EqualityComparer);
								dictionary2.Add(memberPath, set3);
							}
							set3.Add(constant);
						}
					}
				}
				if (dictionary2.Count > 0)
				{
					if (set2.Contains(dictionary2))
					{
						if (!this.RightSideEqual(dictionary[dictionary2], leftCellWrapper))
						{
							this.m_errorLog.AddEntry(new ErrorLog.Record(ViewGenErrorCode.ErrorPatternConditionError, Strings.Viewgen_ErrorPattern_DuplicateConditionValue(ErrorPatternMatcher.BuildCommaSeparatedErrorString<MemberPath>(dictionary2.Keys)), ErrorPatternMatcher.ToIEnum(dictionary[dictionary2].OnlyInputCell, leftCellWrapper.OnlyInputCell), ""));
						}
					}
					else
					{
						set2.Add(dictionary2);
						dictionary.Add(dictionary2, leftCellWrapper);
					}
				}
			}
		}

		// Token: 0x06004416 RID: 17430 RVA: 0x000ED324 File Offset: 0x000EB524
		private static MemberPath GetRightMemberPath(MemberPath conditionMember, LeftCellWrapper leftCellWrapper)
		{
			List<int> projectedPositions = leftCellWrapper.OnlyInputCell.GetRightQuery(ViewTarget.QueryView).GetProjectedPositions(conditionMember);
			if (projectedPositions.Count != 1)
			{
				return null;
			}
			int num = projectedPositions.First<int>();
			return ((MemberProjectedSlot)leftCellWrapper.OnlyInputCell.GetLeftQuery(ViewTarget.QueryView).ProjectedSlotAt(num)).MemberPath;
		}

		// Token: 0x06004417 RID: 17431 RVA: 0x000ED374 File Offset: 0x000EB574
		private void MatchSplitErrors()
		{
			IEnumerable<LeftCellWrapper> enumerable = this.m_viewgenContext.AllWrappersForExtent.Where((LeftCellWrapper r) => !(r.LeftExtent is AssociationSet) && !(r.RightCellQuery.Extent is AssociationSet));
			if (this.m_viewgenContext.ViewTarget == ViewTarget.UpdateView && enumerable.Any<LeftCellWrapper>())
			{
				LeftCellWrapper leftCellWrapper = enumerable.First<LeftCellWrapper>();
				EntitySetBase extent = leftCellWrapper.RightCellQuery.Extent;
				foreach (LeftCellWrapper leftCellWrapper2 in enumerable)
				{
					if (!leftCellWrapper2.RightCellQuery.Extent.EdmEquals(extent) && !this.RightSideEqual(leftCellWrapper2, leftCellWrapper))
					{
						this.m_errorLog.AddEntry(new ErrorLog.Record(ViewGenErrorCode.ErrorPatternSplittingError, Strings.Viewgen_ErrorPattern_TableMappedToMultipleES(leftCellWrapper2.LeftExtent.ToString(), leftCellWrapper2.RightCellQuery.Extent.ToString(), extent.ToString()), leftCellWrapper2.Cells.First<Cell>(), ""));
					}
				}
			}
		}

		// Token: 0x06004418 RID: 17432 RVA: 0x000ED484 File Offset: 0x000EB684
		private void MatchPartitionErrors()
		{
			List<LeftCellWrapper> allWrappersForExtent = this.m_viewgenContext.AllWrappersForExtent;
			int num = 0;
			foreach (LeftCellWrapper leftCellWrapper in allWrappersForExtent)
			{
				foreach (LeftCellWrapper leftCellWrapper2 in allWrappersForExtent.Skip(++num))
				{
					FragmentQuery fragmentQuery = this.CreateRightFragmentQuery(leftCellWrapper);
					FragmentQuery fragmentQuery2 = this.CreateRightFragmentQuery(leftCellWrapper2);
					bool flag = this.CompareS(ErrorPatternMatcher.ComparisonOP.IsDisjointFrom, this.m_viewgenContext, leftCellWrapper, leftCellWrapper2, fragmentQuery, fragmentQuery2);
					bool flag2 = this.CompareC(ErrorPatternMatcher.ComparisonOP.IsDisjointFrom, this.m_viewgenContext, leftCellWrapper, leftCellWrapper2, fragmentQuery, fragmentQuery2);
					bool flag3;
					bool flag4;
					bool flag5;
					if (flag)
					{
						if (flag2)
						{
							continue;
						}
						flag3 = this.CompareC(ErrorPatternMatcher.ComparisonOP.IsContainedIn, this.m_viewgenContext, leftCellWrapper, leftCellWrapper2, fragmentQuery, fragmentQuery2);
						flag4 = this.CompareC(ErrorPatternMatcher.ComparisonOP.IsContainedIn, this.m_viewgenContext, leftCellWrapper2, leftCellWrapper, fragmentQuery2, fragmentQuery);
						flag5 = flag3 && flag4;
						StringBuilder stringBuilder = new StringBuilder();
						if (flag5)
						{
							stringBuilder.Append(Strings.Viewgen_ErrorPattern_Partition_Disj_Eq);
						}
						else if (flag3 || flag4)
						{
							if (this.CSideHasDifferentEntitySets(leftCellWrapper, leftCellWrapper2))
							{
								stringBuilder.Append(Strings.Viewgen_ErrorPattern_Partition_Disj_Subs_Ref);
							}
							else
							{
								stringBuilder.Append(Strings.Viewgen_ErrorPattern_Partition_Disj_Subs);
							}
						}
						else
						{
							stringBuilder.Append(Strings.Viewgen_ErrorPattern_Partition_Disj_Unk);
						}
						this.m_errorLog.AddEntry(new ErrorLog.Record(ViewGenErrorCode.ErrorPatternInvalidPartitionError, stringBuilder.ToString(), ErrorPatternMatcher.ToIEnum(leftCellWrapper.OnlyInputCell, leftCellWrapper2.OnlyInputCell), ""));
						if (this.FoundTooManyErrors())
						{
							return;
						}
					}
					else
					{
						flag3 = this.CompareC(ErrorPatternMatcher.ComparisonOP.IsContainedIn, this.m_viewgenContext, leftCellWrapper, leftCellWrapper2, fragmentQuery, fragmentQuery2);
						flag4 = this.CompareC(ErrorPatternMatcher.ComparisonOP.IsContainedIn, this.m_viewgenContext, leftCellWrapper2, leftCellWrapper, fragmentQuery2, fragmentQuery);
					}
					bool flag6 = this.CompareS(ErrorPatternMatcher.ComparisonOP.IsContainedIn, this.m_viewgenContext, leftCellWrapper, leftCellWrapper2, fragmentQuery, fragmentQuery2);
					bool flag7 = this.CompareS(ErrorPatternMatcher.ComparisonOP.IsContainedIn, this.m_viewgenContext, leftCellWrapper2, leftCellWrapper, fragmentQuery2, fragmentQuery);
					flag5 = flag3 && flag4;
					if (flag6 && flag7)
					{
						if (!flag5)
						{
							StringBuilder stringBuilder2 = new StringBuilder();
							if (flag2)
							{
								stringBuilder2.Append(Strings.Viewgen_ErrorPattern_Partition_Eq_Disj);
							}
							else if (flag3 || flag4)
							{
								if (this.CSideHasDifferentEntitySets(leftCellWrapper, leftCellWrapper2))
								{
									stringBuilder2.Append(Strings.Viewgen_ErrorPattern_Partition_Eq_Subs_Ref);
								}
								else
								{
									if (leftCellWrapper.LeftExtent.Equals(leftCellWrapper2.LeftExtent))
									{
										bool flag8;
										List<EdmType> list;
										ErrorPatternMatcher.GetTypesAndConditionForWrapper(leftCellWrapper, out flag8, out list);
										bool flag9;
										List<EdmType> list2;
										ErrorPatternMatcher.GetTypesAndConditionForWrapper(leftCellWrapper2, out flag9, out list2);
										if (!flag8 && !flag9 && (list.Except(list2).Count<EdmType>() != 0 || list2.Except(list).Count<EdmType>() != 0) && (!ErrorPatternMatcher.CheckForStoreConditions(leftCellWrapper) || !ErrorPatternMatcher.CheckForStoreConditions(leftCellWrapper2)))
										{
											IEnumerable<string> enumerable = list.Select((EdmType it) => it.FullName).Union(list2.Select((EdmType it) => it.FullName));
											this.m_errorLog.AddEntry(new ErrorLog.Record(ViewGenErrorCode.ErrorPatternConditionError, Strings.Viewgen_ErrorPattern_Partition_MultipleTypesMappedToSameTable_WithoutCondition(StringUtil.ToCommaSeparatedString(enumerable), leftCellWrapper.LeftExtent), ErrorPatternMatcher.ToIEnum(leftCellWrapper.OnlyInputCell, leftCellWrapper2.OnlyInputCell), ""));
											return;
										}
									}
									stringBuilder2.Append(Strings.Viewgen_ErrorPattern_Partition_Eq_Subs);
								}
							}
							else if (!this.IsQueryView() && (leftCellWrapper.OnlyInputCell.CQuery.Extent is AssociationSet || leftCellWrapper2.OnlyInputCell.CQuery.Extent is AssociationSet))
							{
								stringBuilder2.Append(Strings.Viewgen_ErrorPattern_Partition_Eq_Unk_Association);
							}
							else
							{
								stringBuilder2.Append(Strings.Viewgen_ErrorPattern_Partition_Eq_Unk);
							}
							this.m_errorLog.AddEntry(new ErrorLog.Record(ViewGenErrorCode.ErrorPatternInvalidPartitionError, stringBuilder2.ToString(), ErrorPatternMatcher.ToIEnum(leftCellWrapper.OnlyInputCell, leftCellWrapper2.OnlyInputCell), ""));
							if (this.FoundTooManyErrors())
							{
								return;
							}
						}
					}
					else if ((flag6 || flag7) && (!flag6 || !flag3 || flag4) && (!flag7 || !flag4 || flag3))
					{
						StringBuilder stringBuilder3 = new StringBuilder();
						if (flag2)
						{
							stringBuilder3.Append(Strings.Viewgen_ErrorPattern_Partition_Sub_Disj);
						}
						else if (flag5)
						{
							if (this.CSideHasDifferentEntitySets(leftCellWrapper, leftCellWrapper2))
							{
								stringBuilder3.Append(" " + Strings.Viewgen_ErrorPattern_Partition_Sub_Eq_Ref);
							}
							else
							{
								stringBuilder3.Append(Strings.Viewgen_ErrorPattern_Partition_Sub_Eq);
							}
						}
						else
						{
							stringBuilder3.Append(Strings.Viewgen_ErrorPattern_Partition_Sub_Unk);
						}
						this.m_errorLog.AddEntry(new ErrorLog.Record(ViewGenErrorCode.ErrorPatternInvalidPartitionError, stringBuilder3.ToString(), ErrorPatternMatcher.ToIEnum(leftCellWrapper.OnlyInputCell, leftCellWrapper2.OnlyInputCell), ""));
						if (this.FoundTooManyErrors())
						{
							return;
						}
					}
				}
			}
		}

		// Token: 0x06004419 RID: 17433 RVA: 0x000ED974 File Offset: 0x000EBB74
		private static void GetTypesAndConditionForWrapper(LeftCellWrapper wrapper, out bool hasCondition, out List<EdmType> edmTypes)
		{
			hasCondition = false;
			edmTypes = new List<EdmType>();
			foreach (Cell cell in wrapper.Cells)
			{
				foreach (MemberRestriction memberRestriction in cell.CQuery.Conditions)
				{
					foreach (Constant constant in memberRestriction.Domain.Values)
					{
						TypeConstant typeConstant = constant as TypeConstant;
						if (typeConstant != null)
						{
							edmTypes.Add(typeConstant.EdmType);
						}
						else
						{
							hasCondition = true;
						}
					}
				}
			}
		}

		// Token: 0x0600441A RID: 17434 RVA: 0x000EDA54 File Offset: 0x000EBC54
		private static bool CheckForStoreConditions(LeftCellWrapper wrapper)
		{
			return wrapper.Cells.SelectMany((Cell c) => c.SQuery.Conditions).Any<MemberRestriction>();
		}

		// Token: 0x0600441B RID: 17435 RVA: 0x000EDA88 File Offset: 0x000EBC88
		private void CheckThatConditionMemberIsNotMapped(MemberPath conditionMember, List<LeftCellWrapper> mappingFragments, Set<MemberPath> mappedConditionMembers)
		{
			foreach (LeftCellWrapper leftCellWrapper in mappingFragments)
			{
				foreach (Cell cell in leftCellWrapper.Cells)
				{
					if (cell.GetLeftQuery(this.m_viewgenContext.ViewTarget).GetProjectedMembers().Contains(conditionMember))
					{
						mappedConditionMembers.Add(conditionMember);
						this.m_errorLog.AddEntry(new ErrorLog.Record(ViewGenErrorCode.ErrorPatternConditionError, Strings.Viewgen_ErrorPattern_ConditionMemberIsMapped(conditionMember.ToString()), cell, ""));
					}
				}
			}
		}

		// Token: 0x0600441C RID: 17436 RVA: 0x000EDB50 File Offset: 0x000EBD50
		private bool FoundTooManyErrors()
		{
			return this.m_errorLog.Count > this.m_originalErrorCount + 5;
		}

		// Token: 0x0600441D RID: 17437 RVA: 0x000EDB68 File Offset: 0x000EBD68
		private static string BuildCommaSeparatedErrorString<T>(IEnumerable<T> members)
		{
			StringBuilder stringBuilder = new StringBuilder();
			T t = members.First<T>();
			foreach (T t2 in members)
			{
				if (!t2.Equals(t))
				{
					stringBuilder.Append(", ");
				}
				StringBuilder stringBuilder2 = stringBuilder;
				string text = "'";
				T t3 = t2;
				stringBuilder2.Append(text + ((t3 != null) ? t3.ToString() : null) + "'");
			}
			return stringBuilder.ToString();
		}

		// Token: 0x0600441E RID: 17438 RVA: 0x000EDC10 File Offset: 0x000EBE10
		private bool CSideHasDifferentEntitySets(LeftCellWrapper a, LeftCellWrapper b)
		{
			if (this.IsQueryView())
			{
				return a.LeftExtent == b.LeftExtent;
			}
			return a.RightCellQuery == b.RightCellQuery;
		}

		// Token: 0x0600441F RID: 17439 RVA: 0x000EDC37 File Offset: 0x000EBE37
		private bool CompareC(ErrorPatternMatcher.ComparisonOP op, ViewgenContext context, LeftCellWrapper leftWrapper1, LeftCellWrapper leftWrapper2, FragmentQuery rightQuery1, FragmentQuery rightQuery2)
		{
			return this.Compare(true, op, context, leftWrapper1, leftWrapper2, rightQuery1, rightQuery2);
		}

		// Token: 0x06004420 RID: 17440 RVA: 0x000EDC49 File Offset: 0x000EBE49
		private bool CompareS(ErrorPatternMatcher.ComparisonOP op, ViewgenContext context, LeftCellWrapper leftWrapper1, LeftCellWrapper leftWrapper2, FragmentQuery rightQuery1, FragmentQuery rightQuery2)
		{
			return this.Compare(false, op, context, leftWrapper1, leftWrapper2, rightQuery1, rightQuery2);
		}

		// Token: 0x06004421 RID: 17441 RVA: 0x000EDC5C File Offset: 0x000EBE5C
		private bool Compare(bool lookingForC, ErrorPatternMatcher.ComparisonOP op, ViewgenContext context, LeftCellWrapper leftWrapper1, LeftCellWrapper leftWrapper2, FragmentQuery rightQuery1, FragmentQuery rightQuery2)
		{
			LCWComparer lcwcomparer;
			if ((lookingForC && this.IsQueryView()) || (!lookingForC && !this.IsQueryView()))
			{
				if (op == ErrorPatternMatcher.ComparisonOP.IsContainedIn)
				{
					lcwcomparer = new LCWComparer(context.LeftFragmentQP.IsContainedIn);
				}
				else
				{
					if (op != ErrorPatternMatcher.ComparisonOP.IsDisjointFrom)
					{
						return false;
					}
					lcwcomparer = new LCWComparer(context.LeftFragmentQP.IsDisjointFrom);
				}
				return lcwcomparer(leftWrapper1.FragmentQuery, leftWrapper2.FragmentQuery);
			}
			if (op == ErrorPatternMatcher.ComparisonOP.IsContainedIn)
			{
				lcwcomparer = new LCWComparer(context.RightFragmentQP.IsContainedIn);
			}
			else
			{
				if (op != ErrorPatternMatcher.ComparisonOP.IsDisjointFrom)
				{
					return false;
				}
				lcwcomparer = new LCWComparer(context.RightFragmentQP.IsDisjointFrom);
			}
			return lcwcomparer(rightQuery1, rightQuery2);
		}

		// Token: 0x06004422 RID: 17442 RVA: 0x000EDD00 File Offset: 0x000EBF00
		private bool RightSideEqual(LeftCellWrapper wrapper1, LeftCellWrapper wrapper2)
		{
			FragmentQuery fragmentQuery = this.CreateRightFragmentQuery(wrapper1);
			FragmentQuery fragmentQuery2 = this.CreateRightFragmentQuery(wrapper2);
			return this.m_viewgenContext.RightFragmentQP.IsEquivalentTo(fragmentQuery, fragmentQuery2);
		}

		// Token: 0x06004423 RID: 17443 RVA: 0x000EDD2F File Offset: 0x000EBF2F
		private FragmentQuery CreateRightFragmentQuery(LeftCellWrapper wrapper)
		{
			return FragmentQuery.Create(wrapper.OnlyInputCell.CellLabel.ToString(), wrapper.CreateRoleBoolean(), wrapper.OnlyInputCell.GetRightQuery(this.m_viewgenContext.ViewTarget));
		}

		// Token: 0x06004424 RID: 17444 RVA: 0x000EDD62 File Offset: 0x000EBF62
		private static IEnumerable<Cell> ToIEnum(Cell one, Cell two)
		{
			return new List<Cell> { one, two };
		}

		// Token: 0x06004425 RID: 17445 RVA: 0x000EDD77 File Offset: 0x000EBF77
		private bool IsQueryView()
		{
			return this.m_viewgenContext.ViewTarget == ViewTarget.QueryView;
		}

		// Token: 0x0400188D RID: 6285
		private readonly ViewgenContext m_viewgenContext;

		// Token: 0x0400188E RID: 6286
		private readonly MemberDomainMap m_domainMap;

		// Token: 0x0400188F RID: 6287
		private readonly ErrorLog m_errorLog;

		// Token: 0x04001890 RID: 6288
		private readonly int m_originalErrorCount;

		// Token: 0x04001891 RID: 6289
		private const int NUM_PARTITION_ERR_TO_FIND = 5;

		// Token: 0x02000B88 RID: 2952
		private enum ComparisonOP
		{
			// Token: 0x04002E0F RID: 11791
			IsContainedIn,
			// Token: 0x04002E10 RID: 11792
			IsDisjointFrom
		}
	}
}
