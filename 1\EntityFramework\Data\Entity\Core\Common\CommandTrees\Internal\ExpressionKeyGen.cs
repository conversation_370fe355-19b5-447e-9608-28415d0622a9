﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Hierarchy;
using System.Data.Entity.Resources;
using System.Data.Entity.Spatial;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Text;

namespace System.Data.Entity.Core.Common.CommandTrees.Internal
{
	// Token: 0x020006ED RID: 1773
	internal sealed class ExpressionKeyGen : DbExpressionVisitor
	{
		// Token: 0x06005277 RID: 21111 RVA: 0x00126574 File Offset: 0x00124774
		internal static bool TryGenerateKey(DbExpression tree, out string key)
		{
			ExpressionKeyGen expressionKeyGen = new ExpressionKeyGen();
			bool flag;
			try
			{
				tree.Accept(expressionKeyGen);
				key = expressionKeyGen._key.ToString();
				flag = true;
			}
			catch (NotSupportedException)
			{
				key = null;
				flag = false;
			}
			return flag;
		}

		// Token: 0x06005278 RID: 21112 RVA: 0x001265B8 File Offset: 0x001247B8
		internal ExpressionKeyGen()
		{
		}

		// Token: 0x06005279 RID: 21113 RVA: 0x001265CC File Offset: 0x001247CC
		private static string[] InitializeExprKindNames()
		{
			string[] names = Enum.GetNames(typeof(DbExpressionKind));
			names[10] = "/";
			names[33] = "%";
			names[34] = "*";
			names[44] = "+";
			names[32] = "-";
			names[54] = "-";
			names[13] = "=";
			names[28] = "<";
			names[29] = "<=";
			names[18] = ">";
			names[19] = ">=";
			names[37] = "<>";
			names[46] = ".";
			names[21] = "IJ";
			names[16] = "FOJ";
			names[27] = "LOJ";
			names[6] = "CA";
			names[42] = "OA";
			return names;
		}

		// Token: 0x17001002 RID: 4098
		// (get) Token: 0x0600527A RID: 21114 RVA: 0x00126689 File Offset: 0x00124889
		internal string Key
		{
			get
			{
				return this._key.ToString();
			}
		}

		// Token: 0x0600527B RID: 21115 RVA: 0x00126696 File Offset: 0x00124896
		private void VisitVariableName(string varName)
		{
			this._key.Append('\'');
			this._key.Append(varName.Replace("'", "''"));
			this._key.Append('\'');
		}

		// Token: 0x0600527C RID: 21116 RVA: 0x001266D0 File Offset: 0x001248D0
		private void VisitBinding(DbExpressionBinding binding)
		{
			this._key.Append("BV");
			this.VisitVariableName(binding.VariableName);
			this._key.Append("=(");
			binding.Expression.Accept(this);
			this._key.Append(')');
		}

		// Token: 0x0600527D RID: 21117 RVA: 0x00126728 File Offset: 0x00124928
		private void VisitGroupBinding(DbGroupExpressionBinding groupBinding)
		{
			this._key.Append("GBVV");
			this.VisitVariableName(groupBinding.VariableName);
			this._key.Append(",");
			this.VisitVariableName(groupBinding.GroupVariableName);
			this._key.Append("=(");
			groupBinding.Expression.Accept(this);
			this._key.Append(')');
		}

		// Token: 0x0600527E RID: 21118 RVA: 0x0012679C File Offset: 0x0012499C
		private void VisitFunction(EdmFunction func, IList<DbExpression> args)
		{
			this._key.Append("FUNC<");
			this._key.Append(func.Identity);
			this._key.Append(">:ARGS(");
			foreach (DbExpression dbExpression in args)
			{
				this._key.Append('(');
				dbExpression.Accept(this);
				this._key.Append(')');
			}
			this._key.Append(')');
		}

		// Token: 0x0600527F RID: 21119 RVA: 0x00126840 File Offset: 0x00124A40
		private void VisitExprKind(DbExpressionKind kind)
		{
			this._key.Append('[');
			this._key.Append(ExpressionKeyGen._exprKindNames[(int)kind]);
			this._key.Append(']');
		}

		// Token: 0x06005280 RID: 21120 RVA: 0x00126871 File Offset: 0x00124A71
		private void VisitUnary(DbUnaryExpression expr)
		{
			this.VisitExprKind(expr.ExpressionKind);
			this._key.Append('(');
			expr.Argument.Accept(this);
			this._key.Append(')');
		}

		// Token: 0x06005281 RID: 21121 RVA: 0x001268A8 File Offset: 0x00124AA8
		private void VisitBinary(DbBinaryExpression expr)
		{
			this.VisitExprKind(expr.ExpressionKind);
			this._key.Append('(');
			expr.Left.Accept(this);
			this._key.Append(',');
			expr.Right.Accept(this);
			this._key.Append(')');
		}

		// Token: 0x06005282 RID: 21122 RVA: 0x00126904 File Offset: 0x00124B04
		private void VisitCastOrTreat(DbUnaryExpression e)
		{
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			e.Argument.Accept(this);
			this._key.Append(":");
			this._key.Append(e.ResultType.Identity);
			this._key.Append(')');
		}

		// Token: 0x06005283 RID: 21123 RVA: 0x0012696D File Offset: 0x00124B6D
		public override void Visit(DbExpression e)
		{
			Check.NotNull<DbExpression>(e, "e");
			throw new NotSupportedException(Strings.Cqt_General_UnsupportedExpression(e.GetType().FullName));
		}

		// Token: 0x06005284 RID: 21124 RVA: 0x00126990 File Offset: 0x00124B90
		public override void Visit(DbConstantExpression e)
		{
			Check.NotNull<DbConstantExpression>(e, "e");
			switch (((PrimitiveType)TypeHelpers.GetPrimitiveTypeUsageForScalar(e.ResultType).EdmType).PrimitiveTypeKind)
			{
			case PrimitiveTypeKind.Binary:
			{
				byte[] array = e.Value as byte[];
				if (array == null)
				{
					throw new NotSupportedException();
				}
				this._key.Append("'");
				foreach (byte b in array)
				{
					this._key.AppendFormat("{0:X2}", b);
				}
				this._key.Append("'");
				break;
			}
			case PrimitiveTypeKind.Boolean:
			case PrimitiveTypeKind.Byte:
			case PrimitiveTypeKind.Decimal:
			case PrimitiveTypeKind.Double:
			case PrimitiveTypeKind.Guid:
			case PrimitiveTypeKind.Single:
			case PrimitiveTypeKind.SByte:
			case PrimitiveTypeKind.Int16:
			case PrimitiveTypeKind.Int32:
			case PrimitiveTypeKind.Int64:
			case PrimitiveTypeKind.Time:
				this._key.AppendFormat(CultureInfo.InvariantCulture, "{0}", new object[] { e.Value });
				break;
			case PrimitiveTypeKind.DateTime:
				this._key.Append(((DateTime)e.Value).ToString("o", CultureInfo.InvariantCulture));
				break;
			case PrimitiveTypeKind.String:
			{
				string text = e.Value as string;
				if (text == null)
				{
					throw new NotSupportedException();
				}
				this._key.Append("'");
				this._key.Append(text.Replace("'", "''"));
				this._key.Append("'");
				break;
			}
			case PrimitiveTypeKind.DateTimeOffset:
				this._key.Append(((DateTimeOffset)e.Value).ToString("o", CultureInfo.InvariantCulture));
				break;
			case PrimitiveTypeKind.Geometry:
			case PrimitiveTypeKind.GeometryPoint:
			case PrimitiveTypeKind.GeometryLineString:
			case PrimitiveTypeKind.GeometryPolygon:
			case PrimitiveTypeKind.GeometryMultiPoint:
			case PrimitiveTypeKind.GeometryMultiLineString:
			case PrimitiveTypeKind.GeometryMultiPolygon:
			case PrimitiveTypeKind.GeometryCollection:
			{
				DbGeometry dbGeometry = e.Value as DbGeometry;
				if (dbGeometry == null)
				{
					throw new NotSupportedException();
				}
				this._key.Append(dbGeometry.AsText());
				break;
			}
			case PrimitiveTypeKind.Geography:
			case PrimitiveTypeKind.GeographyPoint:
			case PrimitiveTypeKind.GeographyLineString:
			case PrimitiveTypeKind.GeographyPolygon:
			case PrimitiveTypeKind.GeographyMultiPoint:
			case PrimitiveTypeKind.GeographyMultiLineString:
			case PrimitiveTypeKind.GeographyMultiPolygon:
			case PrimitiveTypeKind.GeographyCollection:
			{
				DbGeography dbGeography = e.Value as DbGeography;
				if (dbGeography == null)
				{
					throw new NotSupportedException();
				}
				this._key.Append(dbGeography.AsText());
				break;
			}
			case PrimitiveTypeKind.HierarchyId:
			{
				HierarchyId hierarchyId = e.Value as HierarchyId;
				if (!(hierarchyId != null))
				{
					throw new NotSupportedException();
				}
				this._key.Append(hierarchyId);
				break;
			}
			default:
				throw new NotSupportedException();
			}
			this._key.Append(":");
			this._key.Append(e.ResultType.Identity);
		}

		// Token: 0x06005285 RID: 21125 RVA: 0x00126C4F File Offset: 0x00124E4F
		public override void Visit(DbNullExpression e)
		{
			Check.NotNull<DbNullExpression>(e, "e");
			this._key.Append("NULL:");
			this._key.Append(e.ResultType.Identity);
		}

		// Token: 0x06005286 RID: 21126 RVA: 0x00126C85 File Offset: 0x00124E85
		public override void Visit(DbVariableReferenceExpression e)
		{
			Check.NotNull<DbVariableReferenceExpression>(e, "e");
			this._key.Append("Var(");
			this.VisitVariableName(e.VariableName);
			this._key.Append(")");
		}

		// Token: 0x06005287 RID: 21127 RVA: 0x00126CC4 File Offset: 0x00124EC4
		public override void Visit(DbParameterReferenceExpression e)
		{
			Check.NotNull<DbParameterReferenceExpression>(e, "e");
			this._key.Append("@");
			this._key.Append(e.ParameterName);
			this._key.Append(":");
			this._key.Append(e.ResultType.Identity);
		}

		// Token: 0x06005288 RID: 21128 RVA: 0x00126D28 File Offset: 0x00124F28
		public override void Visit(DbFunctionExpression e)
		{
			Check.NotNull<DbFunctionExpression>(e, "e");
			this.VisitFunction(e.Function, e.Arguments);
		}

		// Token: 0x06005289 RID: 21129 RVA: 0x00126D48 File Offset: 0x00124F48
		public override void Visit(DbLambdaExpression expression)
		{
			Check.NotNull<DbLambdaExpression>(expression, "expression");
			this._key.Append("Lambda(");
			foreach (DbVariableReferenceExpression dbVariableReferenceExpression in expression.Lambda.Variables)
			{
				this._key.Append("(V");
				this.VisitVariableName(dbVariableReferenceExpression.VariableName);
				this._key.Append(":");
				this._key.Append(dbVariableReferenceExpression.ResultType.Identity);
				this._key.Append(')');
			}
			this._key.Append("=");
			foreach (DbExpression dbExpression in expression.Arguments)
			{
				this._key.Append('(');
				dbExpression.Accept(this);
				this._key.Append(')');
			}
			this._key.Append(")Body(");
			expression.Lambda.Body.Accept(this);
			this._key.Append(")");
		}

		// Token: 0x0600528A RID: 21130 RVA: 0x00126EA0 File Offset: 0x001250A0
		public override void Visit(DbPropertyExpression e)
		{
			Check.NotNull<DbPropertyExpression>(e, "e");
			e.Instance.Accept(this);
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append(e.Property.Name);
		}

		// Token: 0x0600528B RID: 21131 RVA: 0x00126EDD File Offset: 0x001250DD
		public override void Visit(DbComparisonExpression e)
		{
			Check.NotNull<DbComparisonExpression>(e, "e");
			this.VisitBinary(e);
		}

		// Token: 0x0600528C RID: 21132 RVA: 0x00126EF4 File Offset: 0x001250F4
		public override void Visit(DbLikeExpression e)
		{
			Check.NotNull<DbLikeExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			e.Argument.Accept(this);
			this._key.Append(")(");
			e.Pattern.Accept(this);
			this._key.Append(")(");
			if (e.Escape != null)
			{
				e.Escape.Accept(this);
			}
			e.Argument.Accept(this);
			this._key.Append(')');
		}

		// Token: 0x0600528D RID: 21133 RVA: 0x00126F90 File Offset: 0x00125190
		public override void Visit(DbLimitExpression e)
		{
			Check.NotNull<DbLimitExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			if (e.WithTies)
			{
				this._key.Append("WithTies");
			}
			this._key.Append('(');
			e.Argument.Accept(this);
			this._key.Append(")(");
			e.Limit.Accept(this);
			this._key.Append(')');
		}

		// Token: 0x0600528E RID: 21134 RVA: 0x00127013 File Offset: 0x00125213
		public override void Visit(DbIsNullExpression e)
		{
			Check.NotNull<DbIsNullExpression>(e, "e");
			this.VisitUnary(e);
		}

		// Token: 0x0600528F RID: 21135 RVA: 0x00127028 File Offset: 0x00125228
		public override void Visit(DbArithmeticExpression e)
		{
			Check.NotNull<DbArithmeticExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			foreach (DbExpression dbExpression in e.Arguments)
			{
				this._key.Append('(');
				dbExpression.Accept(this);
				this._key.Append(')');
			}
		}

		// Token: 0x06005290 RID: 21136 RVA: 0x001270A8 File Offset: 0x001252A8
		public override void Visit(DbAndExpression e)
		{
			Check.NotNull<DbAndExpression>(e, "e");
			this.VisitBinary(e);
		}

		// Token: 0x06005291 RID: 21137 RVA: 0x001270BD File Offset: 0x001252BD
		public override void Visit(DbOrExpression e)
		{
			Check.NotNull<DbOrExpression>(e, "e");
			this.VisitBinary(e);
		}

		// Token: 0x06005292 RID: 21138 RVA: 0x001270D4 File Offset: 0x001252D4
		public override void Visit(DbInExpression e)
		{
			Check.NotNull<DbInExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			e.Item.Accept(this);
			this._key.Append(",(");
			bool flag = true;
			foreach (DbExpression dbExpression in e.List)
			{
				if (flag)
				{
					flag = false;
				}
				else
				{
					this._key.Append(',');
				}
				dbExpression.Accept(this);
			}
			this._key.Append("))");
		}

		// Token: 0x06005293 RID: 21139 RVA: 0x0012718C File Offset: 0x0012538C
		public override void Visit(DbNotExpression e)
		{
			Check.NotNull<DbNotExpression>(e, "e");
			this.VisitUnary(e);
		}

		// Token: 0x06005294 RID: 21140 RVA: 0x001271A1 File Offset: 0x001253A1
		public override void Visit(DbDistinctExpression e)
		{
			Check.NotNull<DbDistinctExpression>(e, "e");
			this.VisitUnary(e);
		}

		// Token: 0x06005295 RID: 21141 RVA: 0x001271B6 File Offset: 0x001253B6
		public override void Visit(DbElementExpression e)
		{
			Check.NotNull<DbElementExpression>(e, "e");
			this.VisitUnary(e);
		}

		// Token: 0x06005296 RID: 21142 RVA: 0x001271CB File Offset: 0x001253CB
		public override void Visit(DbIsEmptyExpression e)
		{
			Check.NotNull<DbIsEmptyExpression>(e, "e");
			this.VisitUnary(e);
		}

		// Token: 0x06005297 RID: 21143 RVA: 0x001271E0 File Offset: 0x001253E0
		public override void Visit(DbUnionAllExpression e)
		{
			Check.NotNull<DbUnionAllExpression>(e, "e");
			this.VisitBinary(e);
		}

		// Token: 0x06005298 RID: 21144 RVA: 0x001271F5 File Offset: 0x001253F5
		public override void Visit(DbIntersectExpression e)
		{
			Check.NotNull<DbIntersectExpression>(e, "e");
			this.VisitBinary(e);
		}

		// Token: 0x06005299 RID: 21145 RVA: 0x0012720A File Offset: 0x0012540A
		public override void Visit(DbExceptExpression e)
		{
			Check.NotNull<DbExceptExpression>(e, "e");
			this.VisitBinary(e);
		}

		// Token: 0x0600529A RID: 21146 RVA: 0x0012721F File Offset: 0x0012541F
		public override void Visit(DbTreatExpression e)
		{
			Check.NotNull<DbTreatExpression>(e, "e");
			this.VisitCastOrTreat(e);
		}

		// Token: 0x0600529B RID: 21147 RVA: 0x00127234 File Offset: 0x00125434
		public override void Visit(DbCastExpression e)
		{
			Check.NotNull<DbCastExpression>(e, "e");
			this.VisitCastOrTreat(e);
		}

		// Token: 0x0600529C RID: 21148 RVA: 0x0012724C File Offset: 0x0012544C
		public override void Visit(DbIsOfExpression e)
		{
			Check.NotNull<DbIsOfExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			e.Argument.Accept(this);
			this._key.Append(":");
			this._key.Append(e.OfType.EdmType.Identity);
			this._key.Append(')');
		}

		// Token: 0x0600529D RID: 21149 RVA: 0x001272C8 File Offset: 0x001254C8
		public override void Visit(DbOfTypeExpression e)
		{
			Check.NotNull<DbOfTypeExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			e.Argument.Accept(this);
			this._key.Append(":");
			this._key.Append(e.OfType.EdmType.Identity);
			this._key.Append(')');
		}

		// Token: 0x0600529E RID: 21150 RVA: 0x00127344 File Offset: 0x00125544
		public override void Visit(DbCaseExpression e)
		{
			Check.NotNull<DbCaseExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			for (int i = 0; i < e.When.Count; i++)
			{
				this._key.Append("WHEN:(");
				e.When[i].Accept(this);
				this._key.Append(")THEN:(");
				e.Then[i].Accept(this);
			}
			this._key.Append("ELSE:(");
			e.Else.Accept(this);
			this._key.Append("))");
		}

		// Token: 0x0600529F RID: 21151 RVA: 0x00127404 File Offset: 0x00125604
		public override void Visit(DbNewInstanceExpression e)
		{
			Check.NotNull<DbNewInstanceExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append(':');
			this._key.Append(e.ResultType.EdmType.Identity);
			this._key.Append('(');
			foreach (DbExpression dbExpression in e.Arguments)
			{
				this._key.Append('(');
				dbExpression.Accept(this);
				this._key.Append(')');
			}
			if (e.HasRelatedEntityReferences)
			{
				foreach (DbRelatedEntityRef dbRelatedEntityRef in e.RelatedEntityReferences)
				{
					this._key.Append("RE(A(");
					this._key.Append(dbRelatedEntityRef.SourceEnd.DeclaringType.Identity);
					this._key.Append(")(");
					this._key.Append(dbRelatedEntityRef.SourceEnd.Name);
					this._key.Append("->");
					this._key.Append(dbRelatedEntityRef.TargetEnd.Name);
					this._key.Append(")(");
					dbRelatedEntityRef.TargetEntityReference.Accept(this);
					this._key.Append("))");
				}
			}
			this._key.Append(')');
		}

		// Token: 0x060052A0 RID: 21152 RVA: 0x001275BC File Offset: 0x001257BC
		public override void Visit(DbRefExpression e)
		{
			Check.NotNull<DbRefExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append("(ESET(");
			this._key.Append(e.EntitySet.EntityContainer.Name);
			this._key.Append('.');
			this._key.Append(e.EntitySet.Name);
			this._key.Append(")T(");
			this._key.Append(TypeHelpers.GetEdmType<RefType>(e.ResultType).ElementType.FullName);
			this._key.Append(")(");
			e.Argument.Accept(this);
			this._key.Append(')');
		}

		// Token: 0x060052A1 RID: 21153 RVA: 0x00127690 File Offset: 0x00125890
		public override void Visit(DbRelationshipNavigationExpression e)
		{
			Check.NotNull<DbRelationshipNavigationExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			e.NavigationSource.Accept(this);
			this._key.Append(")A(");
			this._key.Append(e.NavigateFrom.DeclaringType.Identity);
			this._key.Append(")(");
			this._key.Append(e.NavigateFrom.Name);
			this._key.Append("->");
			this._key.Append(e.NavigateTo.Name);
			this._key.Append("))");
		}

		// Token: 0x060052A2 RID: 21154 RVA: 0x0012775D File Offset: 0x0012595D
		public override void Visit(DbDerefExpression e)
		{
			Check.NotNull<DbDerefExpression>(e, "e");
			this.VisitUnary(e);
		}

		// Token: 0x060052A3 RID: 21155 RVA: 0x00127772 File Offset: 0x00125972
		public override void Visit(DbRefKeyExpression e)
		{
			Check.NotNull<DbRefKeyExpression>(e, "e");
			this.VisitUnary(e);
		}

		// Token: 0x060052A4 RID: 21156 RVA: 0x00127787 File Offset: 0x00125987
		public override void Visit(DbEntityRefExpression e)
		{
			Check.NotNull<DbEntityRefExpression>(e, "e");
			this.VisitUnary(e);
		}

		// Token: 0x060052A5 RID: 21157 RVA: 0x0012779C File Offset: 0x0012599C
		public override void Visit(DbScanExpression e)
		{
			Check.NotNull<DbScanExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			this._key.Append(e.Target.EntityContainer.Name);
			this._key.Append('.');
			this._key.Append(e.Target.Name);
			this._key.Append(':');
			this._key.Append(e.ResultType.EdmType.Identity);
			this._key.Append(')');
		}

		// Token: 0x060052A6 RID: 21158 RVA: 0x00127848 File Offset: 0x00125A48
		public override void Visit(DbFilterExpression e)
		{
			Check.NotNull<DbFilterExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			this.VisitBinding(e.Input);
			this._key.Append('(');
			e.Predicate.Accept(this);
			this._key.Append("))");
		}

		// Token: 0x060052A7 RID: 21159 RVA: 0x001278B4 File Offset: 0x00125AB4
		public override void Visit(DbProjectExpression e)
		{
			Check.NotNull<DbProjectExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			this.VisitBinding(e.Input);
			this._key.Append('(');
			e.Projection.Accept(this);
			this._key.Append("))");
		}

		// Token: 0x060052A8 RID: 21160 RVA: 0x00127920 File Offset: 0x00125B20
		public override void Visit(DbCrossJoinExpression e)
		{
			Check.NotNull<DbCrossJoinExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			foreach (DbExpressionBinding dbExpressionBinding in e.Inputs)
			{
				this.VisitBinding(dbExpressionBinding);
			}
			this._key.Append(')');
		}

		// Token: 0x060052A9 RID: 21161 RVA: 0x001279A4 File Offset: 0x00125BA4
		public override void Visit(DbJoinExpression e)
		{
			Check.NotNull<DbJoinExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			this.VisitBinding(e.Left);
			this.VisitBinding(e.Right);
			this._key.Append('(');
			e.JoinCondition.Accept(this);
			this._key.Append("))");
		}

		// Token: 0x060052AA RID: 21162 RVA: 0x00127A1C File Offset: 0x00125C1C
		public override void Visit(DbApplyExpression e)
		{
			Check.NotNull<DbApplyExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			this.VisitBinding(e.Input);
			this.VisitBinding(e.Apply);
			this._key.Append(')');
		}

		// Token: 0x060052AB RID: 21163 RVA: 0x00127A78 File Offset: 0x00125C78
		public override void Visit(DbGroupByExpression e)
		{
			Check.NotNull<DbGroupByExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			this.VisitGroupBinding(e.Input);
			foreach (DbExpression dbExpression in e.Keys)
			{
				this._key.Append("K(");
				dbExpression.Accept(this);
				this._key.Append(')');
			}
			foreach (DbAggregate dbAggregate in e.Aggregates)
			{
				DbGroupAggregate dbGroupAggregate = dbAggregate as DbGroupAggregate;
				if (dbGroupAggregate != null)
				{
					this._key.Append("GA(");
					dbGroupAggregate.Arguments[0].Accept(this);
					this._key.Append(')');
				}
				else
				{
					this._key.Append("A:");
					DbFunctionAggregate dbFunctionAggregate = (DbFunctionAggregate)dbAggregate;
					if (dbFunctionAggregate.Distinct)
					{
						this._key.Append("D:");
					}
					this.VisitFunction(dbFunctionAggregate.Function, dbFunctionAggregate.Arguments);
				}
			}
			this._key.Append(')');
		}

		// Token: 0x060052AC RID: 21164 RVA: 0x00127BE4 File Offset: 0x00125DE4
		private void VisitSortOrder(IList<DbSortClause> sortOrder)
		{
			this._key.Append("SO(");
			foreach (DbSortClause dbSortClause in sortOrder)
			{
				this._key.Append(dbSortClause.Ascending ? "ASC(" : "DESC(");
				dbSortClause.Expression.Accept(this);
				this._key.Append(')');
				if (!string.IsNullOrEmpty(dbSortClause.Collation))
				{
					this._key.Append(":(");
					this._key.Append(dbSortClause.Collation);
					this._key.Append(')');
				}
			}
			this._key.Append(')');
		}

		// Token: 0x060052AD RID: 21165 RVA: 0x00127CC0 File Offset: 0x00125EC0
		public override void Visit(DbSkipExpression e)
		{
			Check.NotNull<DbSkipExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			this.VisitBinding(e.Input);
			this.VisitSortOrder(e.SortOrder);
			this._key.Append('(');
			e.Count.Accept(this);
			this._key.Append("))");
		}

		// Token: 0x060052AE RID: 21166 RVA: 0x00127D38 File Offset: 0x00125F38
		public override void Visit(DbSortExpression e)
		{
			Check.NotNull<DbSortExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			this.VisitBinding(e.Input);
			this.VisitSortOrder(e.SortOrder);
			this._key.Append(')');
		}

		// Token: 0x060052AF RID: 21167 RVA: 0x00127D94 File Offset: 0x00125F94
		public override void Visit(DbQuantifierExpression e)
		{
			Check.NotNull<DbQuantifierExpression>(e, "e");
			this.VisitExprKind(e.ExpressionKind);
			this._key.Append('(');
			this.VisitBinding(e.Input);
			this._key.Append('(');
			e.Predicate.Accept(this);
			this._key.Append("))");
		}

		// Token: 0x04001DE4 RID: 7652
		private readonly StringBuilder _key = new StringBuilder();

		// Token: 0x04001DE5 RID: 7653
		private static readonly string[] _exprKindNames = ExpressionKeyGen.InitializeExprKindNames();
	}
}
