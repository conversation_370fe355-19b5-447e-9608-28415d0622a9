﻿using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Text;

namespace System.Data.Entity.Core.Objects.Internal
{
	// Token: 0x0200043E RID: 1086
	internal static class EntitySqlQueryBuilder
	{
		// Token: 0x06003518 RID: 13592 RVA: 0x000A9CB4 File Offset: 0x000A7EB4
		private static string GetCommandText(ObjectQueryState query)
		{
			string text = null;
			if (!query.TryGetCommandText(out text))
			{
				throw new NotSupportedException(Strings.ObjectQuery_QueryBuilder_NotSupportedLinqSource);
			}
			return text;
		}

		// Token: 0x06003519 RID: 13593 RVA: 0x000A9CDC File Offset: 0x000A7EDC
		private static ObjectParameterCollection MergeParameters(ObjectContext context, ObjectParameterCollection sourceQueryParams, ObjectParameter[] builderMethodParams)
		{
			if (sourceQueryParams == null && builderMethodParams.Length == 0)
			{
				return null;
			}
			ObjectParameterCollection objectParameterCollection = ObjectParameterCollection.DeepCopy(sourceQueryParams);
			if (objectParameterCollection == null)
			{
				objectParameterCollection = new ObjectParameterCollection(context.Perspective);
			}
			foreach (ObjectParameter objectParameter in builderMethodParams)
			{
				objectParameterCollection.Add(objectParameter);
			}
			return objectParameterCollection;
		}

		// Token: 0x0600351A RID: 13594 RVA: 0x000A9D24 File Offset: 0x000A7F24
		private static ObjectParameterCollection MergeParameters(ObjectParameterCollection query1Params, ObjectParameterCollection query2Params)
		{
			if (query1Params == null && query2Params == null)
			{
				return null;
			}
			ObjectParameterCollection objectParameterCollection;
			ObjectParameterCollection objectParameterCollection2;
			if (query1Params != null)
			{
				objectParameterCollection = ObjectParameterCollection.DeepCopy(query1Params);
				objectParameterCollection2 = query2Params;
			}
			else
			{
				objectParameterCollection = ObjectParameterCollection.DeepCopy(query2Params);
				objectParameterCollection2 = query1Params;
			}
			if (objectParameterCollection2 != null)
			{
				foreach (ObjectParameter objectParameter in objectParameterCollection2)
				{
					objectParameterCollection.Add(objectParameter.ShallowCopy());
				}
			}
			return objectParameterCollection;
		}

		// Token: 0x0600351B RID: 13595 RVA: 0x000A9D94 File Offset: 0x000A7F94
		private static ObjectQueryState NewBuilderQuery(ObjectQueryState sourceQuery, Type elementType, StringBuilder queryText, Span newSpan, IEnumerable<ObjectParameter> enumerableParams)
		{
			return EntitySqlQueryBuilder.NewBuilderQuery(sourceQuery, elementType, queryText, false, newSpan, enumerableParams);
		}

		// Token: 0x0600351C RID: 13596 RVA: 0x000A9DA4 File Offset: 0x000A7FA4
		private static ObjectQueryState NewBuilderQuery(ObjectQueryState sourceQuery, Type elementType, StringBuilder queryText, bool allowsLimit, Span newSpan, IEnumerable<ObjectParameter> enumerableParams)
		{
			ObjectParameterCollection objectParameterCollection = enumerableParams as ObjectParameterCollection;
			if (objectParameterCollection == null && enumerableParams != null)
			{
				objectParameterCollection = new ObjectParameterCollection(sourceQuery.ObjectContext.Perspective);
				foreach (ObjectParameter objectParameter in enumerableParams)
				{
					objectParameterCollection.Add(objectParameter);
				}
			}
			EntitySqlQueryState entitySqlQueryState = new EntitySqlQueryState(elementType, queryText.ToString(), allowsLimit, sourceQuery.ObjectContext, objectParameterCollection, newSpan);
			sourceQuery.ApplySettingsTo(entitySqlQueryState);
			return entitySqlQueryState;
		}

		// Token: 0x0600351D RID: 13597 RVA: 0x000A9E2C File Offset: 0x000A802C
		private static ObjectQueryState BuildSetOp(ObjectQueryState leftQuery, ObjectQueryState rightQuery, Span newSpan, string setOp)
		{
			string commandText = EntitySqlQueryBuilder.GetCommandText(leftQuery);
			string commandText2 = EntitySqlQueryBuilder.GetCommandText(rightQuery);
			if (leftQuery.ObjectContext != rightQuery.ObjectContext)
			{
				throw new ArgumentException(Strings.ObjectQuery_QueryBuilder_InvalidQueryArgument, "query");
			}
			StringBuilder stringBuilder = new StringBuilder("(\r\n".Length + commandText.Length + setOp.Length + commandText2.Length + "\r\n)".Length);
			stringBuilder.Append("(\r\n");
			stringBuilder.Append(commandText);
			stringBuilder.Append(setOp);
			stringBuilder.Append(commandText2);
			stringBuilder.Append("\r\n)");
			return EntitySqlQueryBuilder.NewBuilderQuery(leftQuery, leftQuery.ElementType, stringBuilder, newSpan, EntitySqlQueryBuilder.MergeParameters(leftQuery.Parameters, rightQuery.Parameters));
		}

		// Token: 0x0600351E RID: 13598 RVA: 0x000A9EE4 File Offset: 0x000A80E4
		private static ObjectQueryState BuildSelectOrSelectValue(ObjectQueryState query, string alias, string projection, ObjectParameter[] parameters, string projectOp, Type elementType)
		{
			string commandText = EntitySqlQueryBuilder.GetCommandText(query);
			StringBuilder stringBuilder = new StringBuilder(projectOp.Length + projection.Length + "\r\nFROM (\r\n".Length + commandText.Length + "\r\n) AS ".Length + alias.Length);
			stringBuilder.Append(projectOp);
			stringBuilder.Append(projection);
			stringBuilder.Append("\r\nFROM (\r\n");
			stringBuilder.Append(commandText);
			stringBuilder.Append("\r\n) AS ");
			stringBuilder.Append(alias);
			return EntitySqlQueryBuilder.NewBuilderQuery(query, elementType, stringBuilder, null, EntitySqlQueryBuilder.MergeParameters(query.ObjectContext, query.Parameters, parameters));
		}

		// Token: 0x0600351F RID: 13599 RVA: 0x000A9F88 File Offset: 0x000A8188
		private static ObjectQueryState BuildOrderByOrWhere(ObjectQueryState query, string alias, string predicateOrKeys, ObjectParameter[] parameters, string op, string skipCount, bool allowsLimit)
		{
			string commandText = EntitySqlQueryBuilder.GetCommandText(query);
			int num = "SELECT VALUE ".Length + alias.Length + "\r\nFROM (\r\n".Length + commandText.Length + "\r\n) AS ".Length + alias.Length + op.Length + predicateOrKeys.Length;
			if (skipCount != null)
			{
				num += "\r\nSKIP\r\n".Length + skipCount.Length;
			}
			StringBuilder stringBuilder = new StringBuilder(num);
			stringBuilder.Append("SELECT VALUE ");
			stringBuilder.Append(alias);
			stringBuilder.Append("\r\nFROM (\r\n");
			stringBuilder.Append(commandText);
			stringBuilder.Append("\r\n) AS ");
			stringBuilder.Append(alias);
			stringBuilder.Append(op);
			stringBuilder.Append(predicateOrKeys);
			if (skipCount != null)
			{
				stringBuilder.Append("\r\nSKIP\r\n");
				stringBuilder.Append(skipCount);
			}
			return EntitySqlQueryBuilder.NewBuilderQuery(query, query.ElementType, stringBuilder, allowsLimit, query.Span, EntitySqlQueryBuilder.MergeParameters(query.ObjectContext, query.Parameters, parameters));
		}

		// Token: 0x06003520 RID: 13600 RVA: 0x000AA090 File Offset: 0x000A8290
		internal static ObjectQueryState Distinct(ObjectQueryState query)
		{
			string commandText = EntitySqlQueryBuilder.GetCommandText(query);
			StringBuilder stringBuilder = new StringBuilder("SET(\r\n".Length + commandText.Length + "\r\n)".Length);
			stringBuilder.Append("SET(\r\n");
			stringBuilder.Append(commandText);
			stringBuilder.Append("\r\n)");
			return EntitySqlQueryBuilder.NewBuilderQuery(query, query.ElementType, stringBuilder, query.Span, ObjectParameterCollection.DeepCopy(query.Parameters));
		}

		// Token: 0x06003521 RID: 13601 RVA: 0x000AA104 File Offset: 0x000A8304
		internal static ObjectQueryState Except(ObjectQueryState leftQuery, ObjectQueryState rightQuery)
		{
			return EntitySqlQueryBuilder.BuildSetOp(leftQuery, rightQuery, leftQuery.Span, "\r\n) EXCEPT (\r\n");
		}

		// Token: 0x06003522 RID: 13602 RVA: 0x000AA118 File Offset: 0x000A8318
		internal static ObjectQueryState GroupBy(ObjectQueryState query, string alias, string keys, string projection, ObjectParameter[] parameters)
		{
			string commandText = EntitySqlQueryBuilder.GetCommandText(query);
			StringBuilder stringBuilder = new StringBuilder("SELECT ".Length + projection.Length + "\r\nFROM (\r\n".Length + commandText.Length + "\r\n) AS ".Length + alias.Length + "\r\nGROUP BY\r\n".Length + keys.Length);
			stringBuilder.Append("SELECT ");
			stringBuilder.Append(projection);
			stringBuilder.Append("\r\nFROM (\r\n");
			stringBuilder.Append(commandText);
			stringBuilder.Append("\r\n) AS ");
			stringBuilder.Append(alias);
			stringBuilder.Append("\r\nGROUP BY\r\n");
			stringBuilder.Append(keys);
			return EntitySqlQueryBuilder.NewBuilderQuery(query, typeof(DbDataRecord), stringBuilder, null, EntitySqlQueryBuilder.MergeParameters(query.ObjectContext, query.Parameters, parameters));
		}

		// Token: 0x06003523 RID: 13603 RVA: 0x000AA1F0 File Offset: 0x000A83F0
		internal static ObjectQueryState Intersect(ObjectQueryState leftQuery, ObjectQueryState rightQuery)
		{
			Span span = Span.CopyUnion(leftQuery.Span, rightQuery.Span);
			return EntitySqlQueryBuilder.BuildSetOp(leftQuery, rightQuery, span, "\r\n) INTERSECT (\r\n");
		}

		// Token: 0x06003524 RID: 13604 RVA: 0x000AA21C File Offset: 0x000A841C
		internal static ObjectQueryState OfType(ObjectQueryState query, EdmType newType, Type clrOfType)
		{
			string commandText = EntitySqlQueryBuilder.GetCommandText(query);
			StringBuilder stringBuilder = new StringBuilder("OFTYPE(\r\n(\r\n".Length + commandText.Length + "\r\n),\r\n[".Length + newType.NamespaceName.Length + ((!string.IsNullOrEmpty(newType.NamespaceName)) ? "].[".Length : 0) + newType.Name.Length + "]\r\n)".Length);
			stringBuilder.Append("OFTYPE(\r\n(\r\n");
			stringBuilder.Append(commandText);
			stringBuilder.Append("\r\n),\r\n[");
			if (!string.IsNullOrEmpty(newType.NamespaceName))
			{
				stringBuilder.Append(newType.NamespaceName);
				stringBuilder.Append("].[");
			}
			stringBuilder.Append(newType.Name);
			stringBuilder.Append("]\r\n)");
			return EntitySqlQueryBuilder.NewBuilderQuery(query, clrOfType, stringBuilder, query.Span, ObjectParameterCollection.DeepCopy(query.Parameters));
		}

		// Token: 0x06003525 RID: 13605 RVA: 0x000AA308 File Offset: 0x000A8508
		internal static ObjectQueryState OrderBy(ObjectQueryState query, string alias, string keys, ObjectParameter[] parameters)
		{
			return EntitySqlQueryBuilder.BuildOrderByOrWhere(query, alias, keys, parameters, "\r\nORDER BY\r\n", null, true);
		}

		// Token: 0x06003526 RID: 13606 RVA: 0x000AA31A File Offset: 0x000A851A
		internal static ObjectQueryState Select(ObjectQueryState query, string alias, string projection, ObjectParameter[] parameters)
		{
			return EntitySqlQueryBuilder.BuildSelectOrSelectValue(query, alias, projection, parameters, "SELECT ", typeof(DbDataRecord));
		}

		// Token: 0x06003527 RID: 13607 RVA: 0x000AA334 File Offset: 0x000A8534
		internal static ObjectQueryState SelectValue(ObjectQueryState query, string alias, string projection, ObjectParameter[] parameters, Type projectedType)
		{
			return EntitySqlQueryBuilder.BuildSelectOrSelectValue(query, alias, projection, parameters, "SELECT VALUE ", projectedType);
		}

		// Token: 0x06003528 RID: 13608 RVA: 0x000AA346 File Offset: 0x000A8546
		internal static ObjectQueryState Skip(ObjectQueryState query, string alias, string keys, string count, ObjectParameter[] parameters)
		{
			return EntitySqlQueryBuilder.BuildOrderByOrWhere(query, alias, keys, parameters, "\r\nORDER BY\r\n", count, true);
		}

		// Token: 0x06003529 RID: 13609 RVA: 0x000AA35C File Offset: 0x000A855C
		internal static ObjectQueryState Top(ObjectQueryState query, string alias, string count, ObjectParameter[] parameters)
		{
			int num = count.Length;
			string commandText = EntitySqlQueryBuilder.GetCommandText(query);
			bool allowsLimitSubclause = ((EntitySqlQueryState)query).AllowsLimitSubclause;
			if (allowsLimitSubclause)
			{
				num += commandText.Length + "\r\nLIMIT\r\n".Length;
			}
			else
			{
				num += "SELECT VALUE TOP(\r\n".Length + "\r\n) ".Length + alias.Length + "\r\nFROM (\r\n".Length + commandText.Length + "\r\n) AS ".Length + alias.Length;
			}
			StringBuilder stringBuilder = new StringBuilder(num);
			if (allowsLimitSubclause)
			{
				stringBuilder.Append(commandText);
				stringBuilder.Append("\r\nLIMIT\r\n");
				stringBuilder.Append(count);
			}
			else
			{
				stringBuilder.Append("SELECT VALUE TOP(\r\n");
				stringBuilder.Append(count);
				stringBuilder.Append("\r\n) ");
				stringBuilder.Append(alias);
				stringBuilder.Append("\r\nFROM (\r\n");
				stringBuilder.Append(commandText);
				stringBuilder.Append("\r\n) AS ");
				stringBuilder.Append(alias);
			}
			return EntitySqlQueryBuilder.NewBuilderQuery(query, query.ElementType, stringBuilder, query.Span, EntitySqlQueryBuilder.MergeParameters(query.ObjectContext, query.Parameters, parameters));
		}

		// Token: 0x0600352A RID: 13610 RVA: 0x000AA47C File Offset: 0x000A867C
		internal static ObjectQueryState Union(ObjectQueryState leftQuery, ObjectQueryState rightQuery)
		{
			Span span = Span.CopyUnion(leftQuery.Span, rightQuery.Span);
			return EntitySqlQueryBuilder.BuildSetOp(leftQuery, rightQuery, span, "\r\n) UNION (\r\n");
		}

		// Token: 0x0600352B RID: 13611 RVA: 0x000AA4A8 File Offset: 0x000A86A8
		internal static ObjectQueryState UnionAll(ObjectQueryState leftQuery, ObjectQueryState rightQuery)
		{
			Span span = Span.CopyUnion(leftQuery.Span, rightQuery.Span);
			return EntitySqlQueryBuilder.BuildSetOp(leftQuery, rightQuery, span, "\r\n) UNION ALL (\r\n");
		}

		// Token: 0x0600352C RID: 13612 RVA: 0x000AA4D4 File Offset: 0x000A86D4
		internal static ObjectQueryState Where(ObjectQueryState query, string alias, string predicate, ObjectParameter[] parameters)
		{
			return EntitySqlQueryBuilder.BuildOrderByOrWhere(query, alias, predicate, parameters, "\r\nWHERE\r\n", null, false);
		}

		// Token: 0x04001122 RID: 4386
		private const string _setOpEpilog = "\r\n)";

		// Token: 0x04001123 RID: 4387
		private const string _setOpProlog = "(\r\n";

		// Token: 0x04001124 RID: 4388
		private const string _fromOp = "\r\nFROM (\r\n";

		// Token: 0x04001125 RID: 4389
		private const string _asOp = "\r\n) AS ";

		// Token: 0x04001126 RID: 4390
		private const string _distinctProlog = "SET(\r\n";

		// Token: 0x04001127 RID: 4391
		private const string _distinctEpilog = "\r\n)";

		// Token: 0x04001128 RID: 4392
		private const string _exceptOp = "\r\n) EXCEPT (\r\n";

		// Token: 0x04001129 RID: 4393
		private const string _groupByOp = "\r\nGROUP BY\r\n";

		// Token: 0x0400112A RID: 4394
		private const string _intersectOp = "\r\n) INTERSECT (\r\n";

		// Token: 0x0400112B RID: 4395
		private const string _ofTypeProlog = "OFTYPE(\r\n(\r\n";

		// Token: 0x0400112C RID: 4396
		private const string _ofTypeInfix = "\r\n),\r\n[";

		// Token: 0x0400112D RID: 4397
		private const string _ofTypeInfix2 = "].[";

		// Token: 0x0400112E RID: 4398
		private const string _ofTypeEpilog = "]\r\n)";

		// Token: 0x0400112F RID: 4399
		private const string _orderByOp = "\r\nORDER BY\r\n";

		// Token: 0x04001130 RID: 4400
		private const string _selectOp = "SELECT ";

		// Token: 0x04001131 RID: 4401
		private const string _selectValueOp = "SELECT VALUE ";

		// Token: 0x04001132 RID: 4402
		private const string _skipOp = "\r\nSKIP\r\n";

		// Token: 0x04001133 RID: 4403
		private const string _limitOp = "\r\nLIMIT\r\n";

		// Token: 0x04001134 RID: 4404
		private const string _topOp = "SELECT VALUE TOP(\r\n";

		// Token: 0x04001135 RID: 4405
		private const string _topInfix = "\r\n) ";

		// Token: 0x04001136 RID: 4406
		private const string _unionOp = "\r\n) UNION (\r\n";

		// Token: 0x04001137 RID: 4407
		private const string _unionAllOp = "\r\n) UNION ALL (\r\n";

		// Token: 0x04001138 RID: 4408
		private const string _whereOp = "\r\nWHERE\r\n";
	}
}
