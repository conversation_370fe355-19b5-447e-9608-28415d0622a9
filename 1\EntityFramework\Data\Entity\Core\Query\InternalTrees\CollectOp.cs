﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x02000386 RID: 902
	internal sealed class CollectOp : ScalarOp
	{
		// Token: 0x06002BCF RID: 11215 RVA: 0x0008D099 File Offset: 0x0008B299
		internal CollectOp(TypeUsage type)
			: base(OpType.Collect, type)
		{
		}

		// Token: 0x06002BD0 RID: 11216 RVA: 0x0008D0A4 File Offset: 0x0008B2A4
		private CollectOp()
			: base(OpType.Collect)
		{
		}

		// Token: 0x170008B4 RID: 2228
		// (get) Token: 0x06002BD1 RID: 11217 RVA: 0x0008D0AE File Offset: 0x0008B2AE
		internal override int Arity
		{
			get
			{
				return 1;
			}
		}

		// Token: 0x06002BD2 RID: 11218 RVA: 0x0008D0B1 File Offset: 0x0008B2B1
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002BD3 RID: 11219 RVA: 0x0008D0BB File Offset: 0x0008B2BB
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000EE8 RID: 3816
		internal static readonly CollectOp Pattern = new CollectOp();
	}
}
