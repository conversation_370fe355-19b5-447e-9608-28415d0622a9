﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Common.Utils.Boolean;
using System.Data.Entity.Core.Mapping.ViewGeneration.Structures;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Validation;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Diagnostics;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.QueryRewriting
{
	// Token: 0x0200058A RID: 1418
	internal class QueryRewriter
	{
		// Token: 0x06004495 RID: 17557 RVA: 0x000F098C File Offset: 0x000EEB8C
		internal QueryRewriter(EdmType generatedType, ViewgenContext context, ViewGenMode typesGenerationMode)
		{
			this._typesGenerationMode = typesGenerationMode;
			this._context = context;
			this._generatedType = generatedType;
			this._domainMap = context.MemberMaps.LeftDomainMap;
			this._config = context.Config;
			this._identifiers = context.CqlIdentifiers;
			this._qp = new RewritingProcessor<Tile<FragmentQuery>>(new DefaultTileProcessor<FragmentQuery>(context.LeftFragmentQP));
			this._extentPath = new MemberPath(context.Extent);
			this._keyAttributes = new List<MemberPath>(MemberPath.GetKeyMembers(context.Extent, this._domainMap));
			foreach (LeftCellWrapper leftCellWrapper in this._context.AllWrappersForExtent)
			{
				FragmentQuery fragmentQuery = leftCellWrapper.FragmentQuery;
				Tile<FragmentQuery> tile = QueryRewriter.CreateTile(fragmentQuery);
				this._fragmentQueries.Add(fragmentQuery);
				this._views.Add(tile);
			}
			this.AdjustMemberDomainsForUpdateViews();
			this._domainQuery = this.GetDomainQuery(this.FragmentQueries, generatedType);
			this._usedViews = new HashSet<FragmentQuery>();
		}

		// Token: 0x06004496 RID: 17558 RVA: 0x000F0AF0 File Offset: 0x000EECF0
		internal void GenerateViewComponents()
		{
			this.EnsureExtentIsFullyMapped(this._usedViews);
			this.GenerateCaseStatements(this._domainMap.ConditionMembers(this._extentPath.Extent), this._usedViews);
			this.AddTrivialCaseStatementsForConditionMembers();
			if (this._usedViews.Count == 0 || this._errorLog.Count > 0)
			{
				ExceptionHelpers.ThrowMappingException(this._errorLog, this._config);
			}
			this._topLevelWhereClause = this.GetTopLevelWhereClause(this._usedViews);
			ViewTarget viewTarget = this._context.ViewTarget;
			this._usedCells = this.RemapFromVariables();
			BasicViewGenerator basicViewGenerator = new BasicViewGenerator(this._context.MemberMaps.ProjectedSlotMap, this._usedCells, this._domainQuery, this._context, this._domainMap, this._errorLog, this._config);
			this._basicView = basicViewGenerator.CreateViewExpression();
			if (this._context.LeftFragmentQP.IsContainedIn(this._basicView.LeftFragmentQuery, this._domainQuery))
			{
				this._topLevelWhereClause = BoolExpression.True;
			}
			if (this._errorLog.Count > 0)
			{
				ExceptionHelpers.ThrowMappingException(this._errorLog, this._config);
			}
		}

		// Token: 0x17000D91 RID: 3473
		// (get) Token: 0x06004497 RID: 17559 RVA: 0x000F0C1A File Offset: 0x000EEE1A
		internal ViewgenContext ViewgenContext
		{
			get
			{
				return this._context;
			}
		}

		// Token: 0x17000D92 RID: 3474
		// (get) Token: 0x06004498 RID: 17560 RVA: 0x000F0C22 File Offset: 0x000EEE22
		internal Dictionary<MemberPath, CaseStatement> CaseStatements
		{
			get
			{
				return this._caseStatements;
			}
		}

		// Token: 0x17000D93 RID: 3475
		// (get) Token: 0x06004499 RID: 17561 RVA: 0x000F0C2A File Offset: 0x000EEE2A
		internal BoolExpression TopLevelWhereClause
		{
			get
			{
				return this._topLevelWhereClause;
			}
		}

		// Token: 0x17000D94 RID: 3476
		// (get) Token: 0x0600449A RID: 17562 RVA: 0x000F0C32 File Offset: 0x000EEE32
		internal CellTreeNode BasicView
		{
			get
			{
				return this._basicView.MakeCopy();
			}
		}

		// Token: 0x17000D95 RID: 3477
		// (get) Token: 0x0600449B RID: 17563 RVA: 0x000F0C3F File Offset: 0x000EEE3F
		internal List<LeftCellWrapper> UsedCells
		{
			get
			{
				return this._usedCells;
			}
		}

		// Token: 0x17000D96 RID: 3478
		// (get) Token: 0x0600449C RID: 17564 RVA: 0x000F0C47 File Offset: 0x000EEE47
		private IEnumerable<FragmentQuery> FragmentQueries
		{
			get
			{
				return this._fragmentQueries;
			}
		}

		// Token: 0x0600449D RID: 17565 RVA: 0x000F0C50 File Offset: 0x000EEE50
		private IEnumerable<Constant> GetDomain(MemberPath currentPath)
		{
			if (this._context.ViewTarget == ViewTarget.QueryView && MemberPath.EqualityComparer.Equals(currentPath, this._extentPath))
			{
				IEnumerable<EdmType> enumerable;
				if (this._typesGenerationMode == ViewGenMode.OfTypeOnlyViews)
				{
					enumerable = new HashSet<EdmType> { this._generatedType };
				}
				else
				{
					enumerable = MetadataHelper.GetTypeAndSubtypesOf(this._generatedType, this._context.EdmItemCollection, false);
				}
				return QueryRewriter.GetTypeConstants(enumerable);
			}
			return this._domainMap.GetDomain(currentPath);
		}

		// Token: 0x0600449E RID: 17566 RVA: 0x000F0CC8 File Offset: 0x000EEEC8
		private void AdjustMemberDomainsForUpdateViews()
		{
			if (this._context.ViewTarget == ViewTarget.UpdateView)
			{
				using (List<MemberPath>.Enumerator enumerator = new List<MemberPath>(this._domainMap.ConditionMembers(this._extentPath.Extent)).GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						MemberPath currentPath = enumerator.Current;
						Constant constant = this._domainMap.GetDomain(currentPath).FirstOrDefault((Constant domainValue) => QueryRewriter.IsDefaultValue(domainValue, currentPath));
						if (constant != null)
						{
							this.RemoveUnusedValueFromStoreDomain(constant, currentPath);
						}
						Constant constant2 = this._domainMap.GetDomain(currentPath).FirstOrDefault((Constant domainValue) => domainValue is NegatedConstant);
						if (constant2 != null)
						{
							this.RemoveUnusedValueFromStoreDomain(constant2, currentPath);
						}
					}
				}
			}
		}

		// Token: 0x0600449F RID: 17567 RVA: 0x000F0DC4 File Offset: 0x000EEFC4
		private void RemoveUnusedValueFromStoreDomain(Constant domainValue, MemberPath currentPath)
		{
			BoolExpression boolExpression = this.CreateMemberCondition(currentPath, domainValue);
			HashSet<FragmentQuery> hashSet = new HashSet<FragmentQuery>();
			bool flag = false;
			Tile<FragmentQuery> tile;
			if (this.FindRewritingAndUsedViews(this._keyAttributes, boolExpression, hashSet, out tile))
			{
				flag = !QueryRewriter.TileToCellTree(tile, this._context).IsEmptyRightFragmentQuery;
			}
			if (!flag)
			{
				Set<Constant> set = new Set<Constant>(this._domainMap.GetDomain(currentPath), Constant.EqualityComparer);
				set.Remove(domainValue);
				this._domainMap.UpdateConditionMemberDomain(currentPath, set);
				foreach (FragmentQuery fragmentQuery in this._fragmentQueries)
				{
					fragmentQuery.Condition.FixDomainMap(this._domainMap);
				}
			}
		}

		// Token: 0x060044A0 RID: 17568 RVA: 0x000F0E8C File Offset: 0x000EF08C
		internal FragmentQuery GetDomainQuery(IEnumerable<FragmentQuery> fragmentQueries, EdmType generatedType)
		{
			if (this._context.ViewTarget == ViewTarget.QueryView)
			{
				BoolExpression boolExpression;
				if (generatedType == null)
				{
					boolExpression = BoolExpression.True;
				}
				else
				{
					IEnumerable<EdmType> enumerable;
					if (this._typesGenerationMode == ViewGenMode.OfTypeOnlyViews)
					{
						enumerable = new HashSet<EdmType> { this._generatedType };
					}
					else
					{
						enumerable = MetadataHelper.GetTypeAndSubtypesOf(generatedType, this._context.EdmItemCollection, false);
					}
					Domain domain = new Domain(QueryRewriter.GetTypeConstants(enumerable), this._domainMap.GetDomain(this._extentPath));
					boolExpression = BoolExpression.CreateLiteral(new TypeRestriction(new MemberProjectedSlot(this._extentPath), domain), this._domainMap);
				}
				return FragmentQuery.Create(this._keyAttributes, boolExpression);
			}
			BoolExpression boolExpression2 = BoolExpression.CreateOr(fragmentQueries.Select((FragmentQuery fragmentQuery) => fragmentQuery.Condition).ToArray<BoolExpression>());
			return FragmentQuery.Create(this._keyAttributes, boolExpression2);
		}

		// Token: 0x060044A1 RID: 17569 RVA: 0x000F0F6C File Offset: 0x000EF16C
		private bool AddRewritingToCaseStatement(Tile<FragmentQuery> rewriting, CaseStatement caseStatement, MemberPath currentPath, Constant domainValue)
		{
			BoolExpression boolExpression = BoolExpression.True;
			bool flag = this._qp.IsContainedIn(QueryRewriter.CreateTile(this._domainQuery), rewriting);
			if (this._qp.IsDisjointFrom(QueryRewriter.CreateTile(this._domainQuery), rewriting))
			{
				return false;
			}
			ProjectedSlot projectedSlot;
			if (domainValue.HasNotNull())
			{
				projectedSlot = new MemberProjectedSlot(currentPath);
			}
			else
			{
				projectedSlot = new ConstantProjectedSlot(domainValue);
			}
			if (!flag)
			{
				boolExpression = QueryRewriter.TileToBoolExpr(rewriting);
			}
			else
			{
				boolExpression = BoolExpression.True;
			}
			caseStatement.AddWhenThen(boolExpression, projectedSlot);
			return flag;
		}

		// Token: 0x060044A2 RID: 17570 RVA: 0x000F0FEC File Offset: 0x000EF1EC
		private void EnsureConfigurationIsFullyMapped(MemberPath currentPath, BoolExpression currentWhereClause, HashSet<FragmentQuery> outputUsedViews, ErrorLog errorLog)
		{
			foreach (Constant constant in this.GetDomain(currentPath))
			{
				if (constant != Constant.Undefined)
				{
					BoolExpression boolExpression = this.CreateMemberCondition(currentPath, constant);
					BoolExpression boolExpression2 = BoolExpression.CreateAnd(new BoolExpression[] { currentWhereClause, boolExpression });
					Tile<FragmentQuery> tile;
					if (!this.FindRewritingAndUsedViews(this._keyAttributes, boolExpression2, outputUsedViews, out tile))
					{
						if (!ErrorPatternMatcher.FindMappingErrors(this._context, this._domainMap, this._errorLog))
						{
							StringBuilder stringBuilder = new StringBuilder();
							string text = StringUtil.FormatInvariant("{0}", new object[] { this._extentPath });
							BoolExpression condition = tile.Query.Condition;
							condition.ExpensiveSimplify();
							if (condition.RepresentsAllTypeConditions)
							{
								string viewGen_Extent = Strings.ViewGen_Extent;
								stringBuilder.AppendLine(Strings.ViewGen_Cannot_Recover_Types(viewGen_Extent, text));
							}
							else
							{
								string viewGen_Entities = Strings.ViewGen_Entities;
								stringBuilder.AppendLine(Strings.ViewGen_Cannot_Disambiguate_MultiConstant(viewGen_Entities, text));
							}
							RewritingValidator.EntityConfigurationToUserString(condition, stringBuilder);
							ErrorLog.Record record = new ErrorLog.Record(ViewGenErrorCode.AmbiguousMultiConstants, stringBuilder.ToString(), this._context.AllWrappersForExtent, string.Empty);
							errorLog.AddEntry(record);
						}
					}
					else
					{
						TypeConstant typeConstant = constant as TypeConstant;
						if (typeConstant != null)
						{
							EdmType edmType = typeConstant.EdmType;
							List<MemberPath> list = QueryRewriter.GetNonConditionalScalarMembers(edmType, currentPath, this._domainMap).Union(QueryRewriter.GetNonConditionalComplexMembers(edmType, currentPath, this._domainMap)).ToList<MemberPath>();
							IEnumerable<MemberPath> enumerable;
							if (list.Count > 0 && !this.FindRewritingAndUsedViews(list, boolExpression2, outputUsedViews, out tile, out enumerable))
							{
								list = new List<MemberPath>(list.Where((MemberPath a) => !a.IsPartOfKey));
								this.AddUnrecoverableAttributesError(enumerable, boolExpression, errorLog);
							}
							else
							{
								foreach (MemberPath memberPath in QueryRewriter.GetConditionalComplexMembers(edmType, currentPath, this._domainMap))
								{
									this.EnsureConfigurationIsFullyMapped(memberPath, boolExpression2, outputUsedViews, errorLog);
								}
								foreach (MemberPath memberPath2 in QueryRewriter.GetConditionalScalarMembers(edmType, currentPath, this._domainMap))
								{
									this.EnsureConfigurationIsFullyMapped(memberPath2, boolExpression2, outputUsedViews, errorLog);
								}
							}
						}
					}
				}
			}
		}

		// Token: 0x060044A3 RID: 17571 RVA: 0x000F1294 File Offset: 0x000EF494
		private static List<string> GetTypeBasedMemberPathList(IEnumerable<MemberPath> nonConditionalScalarAttributes)
		{
			List<string> list = new List<string>();
			foreach (MemberPath memberPath in nonConditionalScalarAttributes)
			{
				EdmMember leafEdmMember = memberPath.LeafEdmMember;
				List<string> list2 = list;
				string name = leafEdmMember.DeclaringType.Name;
				string text = ".";
				EdmMember edmMember = leafEdmMember;
				list2.Add(name + text + ((edmMember != null) ? edmMember.ToString() : null));
			}
			return list;
		}

		// Token: 0x060044A4 RID: 17572 RVA: 0x000F130C File Offset: 0x000EF50C
		private void AddUnrecoverableAttributesError(IEnumerable<MemberPath> attributes, BoolExpression domainAddedWhereClause, ErrorLog errorLog)
		{
			StringBuilder stringBuilder = new StringBuilder();
			string text = StringUtil.FormatInvariant("{0}", new object[] { this._extentPath });
			string viewGen_Extent = Strings.ViewGen_Extent;
			string text2 = StringUtil.ToCommaSeparatedString(QueryRewriter.GetTypeBasedMemberPathList(attributes));
			stringBuilder.AppendLine(Strings.ViewGen_Cannot_Recover_Attributes(text2, viewGen_Extent, text));
			RewritingValidator.EntityConfigurationToUserString(domainAddedWhereClause, stringBuilder);
			ErrorLog.Record record = new ErrorLog.Record(ViewGenErrorCode.AttributesUnrecoverable, stringBuilder.ToString(), this._context.AllWrappersForExtent, string.Empty);
			errorLog.AddEntry(record);
		}

		// Token: 0x060044A5 RID: 17573 RVA: 0x000F138C File Offset: 0x000EF58C
		private void GenerateCaseStatements(IEnumerable<MemberPath> members, HashSet<FragmentQuery> outputUsedViews)
		{
			IEnumerable<LeftCellWrapper> enumerable = this._context.AllWrappersForExtent.Where((LeftCellWrapper w) => this._usedViews.Contains(w.FragmentQuery));
			ViewgenContext context = this._context;
			CellTreeOpType cellTreeOpType = CellTreeOpType.Union;
			CellTreeNode[] array = enumerable.Select((LeftCellWrapper wrapper) => new LeafCellTreeNode(this._context, wrapper)).ToArray<LeafCellTreeNode>();
			CellTreeNode cellTreeNode = new OpCellTreeNode(context, cellTreeOpType, array);
			foreach (MemberPath memberPath in members)
			{
				List<Constant> list = this.GetDomain(memberPath).ToList<Constant>();
				CaseStatement caseStatement = new CaseStatement(memberPath);
				Tile<FragmentQuery> tile = null;
				bool flag = list.Count != 2 || !list.Contains(Constant.Null, Constant.EqualityComparer) || !list.Contains(Constant.NotNull, Constant.EqualityComparer);
				foreach (Constant constant in list)
				{
					if (constant == Constant.Undefined && this._context.ViewTarget == ViewTarget.QueryView)
					{
						caseStatement.AddWhenThen(BoolExpression.False, new ConstantProjectedSlot(Constant.Undefined));
					}
					else
					{
						FragmentQuery fragmentQuery = this.CreateMemberConditionQuery(memberPath, constant);
						Tile<FragmentQuery> tile2;
						if (this.FindRewritingAndUsedViews(fragmentQuery.Attributes, fragmentQuery.Condition, outputUsedViews, out tile2))
						{
							if (this._context.ViewTarget == ViewTarget.UpdateView)
							{
								tile = ((tile != null) ? this._qp.Union(tile, tile2) : tile2);
							}
							if (flag && this.AddRewritingToCaseStatement(tile2, caseStatement, memberPath, constant))
							{
								break;
							}
						}
						else if (!QueryRewriter.IsDefaultValue(constant, memberPath) && !ErrorPatternMatcher.FindMappingErrors(this._context, this._domainMap, this._errorLog))
						{
							StringBuilder stringBuilder = new StringBuilder();
							string text = StringUtil.FormatInvariant("{0}", new object[] { this._extentPath });
							string text2 = ((this._context.ViewTarget == ViewTarget.QueryView) ? Strings.ViewGen_Entities : Strings.ViewGen_Tuples);
							if (this._context.ViewTarget == ViewTarget.QueryView)
							{
								stringBuilder.AppendLine(Strings.Viewgen_CannotGenerateQueryViewUnderNoValidation(text));
							}
							else
							{
								stringBuilder.AppendLine(Strings.ViewGen_Cannot_Disambiguate_MultiConstant(text2, text));
							}
							RewritingValidator.EntityConfigurationToUserString(fragmentQuery.Condition, stringBuilder, this._context.ViewTarget == ViewTarget.UpdateView);
							ErrorLog.Record record = new ErrorLog.Record(ViewGenErrorCode.AmbiguousMultiConstants, stringBuilder.ToString(), this._context.AllWrappersForExtent, string.Empty);
							this._errorLog.AddEntry(record);
						}
					}
				}
				if (this._errorLog.Count == 0)
				{
					if (this._context.ViewTarget == ViewTarget.UpdateView && flag)
					{
						this.AddElseDefaultToCaseStatement(memberPath, caseStatement, list, cellTreeNode, tile);
					}
					if (caseStatement.Clauses.Count > 0)
					{
						this._caseStatements[memberPath] = caseStatement;
					}
				}
			}
		}

		// Token: 0x060044A6 RID: 17574 RVA: 0x000F168C File Offset: 0x000EF88C
		private void AddElseDefaultToCaseStatement(MemberPath currentPath, CaseStatement caseStatement, List<Constant> domain, CellTreeNode rightDomainQuery, Tile<FragmentQuery> unionCaseRewriting)
		{
			Constant constant;
			bool flag = Domain.TryGetDefaultValueForMemberPath(currentPath, out constant);
			if (!flag || !domain.Contains(constant))
			{
				CellTreeNode cellTreeNode = QueryRewriter.TileToCellTree(unionCaseRewriting, this._context);
				FragmentQuery fragmentQuery = this._context.RightFragmentQP.Difference(rightDomainQuery.RightFragmentQuery, cellTreeNode.RightFragmentQuery);
				if (this._context.RightFragmentQP.IsSatisfiable(fragmentQuery))
				{
					if (flag)
					{
						caseStatement.AddWhenThen(BoolExpression.True, new ConstantProjectedSlot(constant));
						return;
					}
					fragmentQuery.Condition.ExpensiveSimplify();
					StringBuilder stringBuilder = new StringBuilder();
					stringBuilder.AppendLine(Strings.ViewGen_No_Default_Value_For_Configuration(currentPath.PathToString(new bool?(false))));
					this._errorLog.AddEntry(new ErrorLog.Record(ViewGenErrorCode.NoDefaultValue, stringBuilder.ToString(), this._context.AllWrappersForExtent, string.Empty));
				}
			}
		}

		// Token: 0x060044A7 RID: 17575 RVA: 0x000F175C File Offset: 0x000EF95C
		private BoolExpression GetTopLevelWhereClause(HashSet<FragmentQuery> outputUsedViews)
		{
			BoolExpression boolExpression = BoolExpression.True;
			Tile<FragmentQuery> tile;
			if (this._context.ViewTarget == ViewTarget.QueryView && !this._domainQuery.Condition.IsTrue && this.FindRewritingAndUsedViews(this._keyAttributes, this._domainQuery.Condition, outputUsedViews, out tile))
			{
				boolExpression = QueryRewriter.TileToBoolExpr(tile);
				boolExpression.ExpensiveSimplify();
			}
			return boolExpression;
		}

		// Token: 0x060044A8 RID: 17576 RVA: 0x000F17B8 File Offset: 0x000EF9B8
		internal void EnsureExtentIsFullyMapped(HashSet<FragmentQuery> outputUsedViews)
		{
			if (this._context.ViewTarget == ViewTarget.QueryView && this._config.IsValidationEnabled)
			{
				this.EnsureConfigurationIsFullyMapped(this._extentPath, BoolExpression.True, outputUsedViews, this._errorLog);
				if (this._errorLog.Count > 0)
				{
					ExceptionHelpers.ThrowMappingException(this._errorLog, this._config);
					return;
				}
			}
			else
			{
				if (this._config.IsValidationEnabled)
				{
					foreach (MemberPath memberPath in this._context.MemberMaps.ProjectedSlotMap.Members)
					{
						Constant constant;
						if (memberPath.IsScalarType() && !memberPath.IsPartOfKey && !this._domainMap.IsConditionMember(memberPath) && !Domain.TryGetDefaultValueForMemberPath(memberPath, out constant))
						{
							HashSet<MemberPath> hashSet = new HashSet<MemberPath>(this._keyAttributes);
							hashSet.Add(memberPath);
							foreach (LeftCellWrapper leftCellWrapper in this._context.AllWrappersForExtent)
							{
								FragmentQuery fragmentQuery = leftCellWrapper.FragmentQuery;
								FragmentQuery fragmentQuery2 = new FragmentQuery(fragmentQuery.Description, fragmentQuery.FromVariable, hashSet, fragmentQuery.Condition);
								Tile<FragmentQuery> tile = QueryRewriter.CreateTile(FragmentQuery.Create(this._keyAttributes, BoolExpression.CreateNot(fragmentQuery.Condition)));
								Tile<FragmentQuery> tile2;
								IEnumerable<MemberPath> enumerable;
								if (!this.RewriteQuery(QueryRewriter.CreateTile(fragmentQuery2), tile, out tile2, out enumerable, false))
								{
									Domain.GetDefaultValueForMemberPath(memberPath, new LeftCellWrapper[] { leftCellWrapper }, this._config);
								}
							}
						}
					}
				}
				using (List<Tile<FragmentQuery>>.Enumerator enumerator3 = this._views.GetEnumerator())
				{
					while (enumerator3.MoveNext())
					{
						Tile<FragmentQuery> toFill = enumerator3.Current;
						Tile<FragmentQuery> tile3 = QueryRewriter.CreateTile(FragmentQuery.Create(this._keyAttributes, BoolExpression.CreateNot(toFill.Query.Condition)));
						Tile<FragmentQuery> tile4;
						IEnumerable<MemberPath> enumerable2;
						if (!this.RewriteQuery(toFill, tile3, out tile4, out enumerable2, true))
						{
							LeftCellWrapper leftCellWrapper2 = this._context.AllWrappersForExtent.First((LeftCellWrapper lcr) => lcr.FragmentQuery.Equals(toFill.Query));
							ErrorLog.Record record = new ErrorLog.Record(ViewGenErrorCode.ImpossibleCondition, Strings.Viewgen_QV_RewritingNotFound(leftCellWrapper2.RightExtent.ToString()), leftCellWrapper2.Cells, string.Empty);
							this._errorLog.AddEntry(record);
						}
						else
						{
							outputUsedViews.UnionWith(tile4.GetNamedQueries());
						}
					}
				}
			}
		}

		// Token: 0x060044A9 RID: 17577 RVA: 0x000F1A8C File Offset: 0x000EFC8C
		private List<LeftCellWrapper> RemapFromVariables()
		{
			List<LeftCellWrapper> list = new List<LeftCellWrapper>();
			int num = 0;
			Dictionary<BoolLiteral, BoolLiteral> dictionary = new Dictionary<BoolLiteral, BoolLiteral>(BoolLiteral.EqualityIdentifierComparer);
			foreach (LeftCellWrapper leftCellWrapper in this._context.AllWrappersForExtent)
			{
				if (this._usedViews.Contains(leftCellWrapper.FragmentQuery))
				{
					list.Add(leftCellWrapper);
					int cellNumber = leftCellWrapper.OnlyInputCell.CellNumber;
					if (num != cellNumber)
					{
						dictionary[new CellIdBoolean(this._identifiers, cellNumber)] = new CellIdBoolean(this._identifiers, num);
					}
					num++;
				}
			}
			if (dictionary.Count > 0)
			{
				this._topLevelWhereClause = this._topLevelWhereClause.RemapLiterals(dictionary);
				Dictionary<MemberPath, CaseStatement> dictionary2 = new Dictionary<MemberPath, CaseStatement>();
				foreach (KeyValuePair<MemberPath, CaseStatement> keyValuePair in this._caseStatements)
				{
					CaseStatement caseStatement = new CaseStatement(keyValuePair.Key);
					foreach (CaseStatement.WhenThen whenThen in keyValuePair.Value.Clauses)
					{
						caseStatement.AddWhenThen(whenThen.Condition.RemapLiterals(dictionary), whenThen.Value);
					}
					dictionary2[keyValuePair.Key] = caseStatement;
				}
				this._caseStatements = dictionary2;
			}
			return list;
		}

		// Token: 0x060044AA RID: 17578 RVA: 0x000F1C30 File Offset: 0x000EFE30
		internal void AddTrivialCaseStatementsForConditionMembers()
		{
			for (int i = 0; i < this._context.MemberMaps.ProjectedSlotMap.Count; i++)
			{
				MemberPath memberPath = this._context.MemberMaps.ProjectedSlotMap[i];
				if (!memberPath.IsScalarType() && !this._caseStatements.ContainsKey(memberPath))
				{
					Constant constant = new TypeConstant(memberPath.EdmType);
					CaseStatement caseStatement = new CaseStatement(memberPath);
					caseStatement.AddWhenThen(BoolExpression.True, new ConstantProjectedSlot(constant));
					this._caseStatements[memberPath] = caseStatement;
				}
			}
		}

		// Token: 0x060044AB RID: 17579 RVA: 0x000F1CBC File Offset: 0x000EFEBC
		private bool FindRewritingAndUsedViews(IEnumerable<MemberPath> attributes, BoolExpression whereClause, HashSet<FragmentQuery> outputUsedViews, out Tile<FragmentQuery> rewriting)
		{
			IEnumerable<MemberPath> enumerable;
			return this.FindRewritingAndUsedViews(attributes, whereClause, outputUsedViews, out rewriting, out enumerable);
		}

		// Token: 0x060044AC RID: 17580 RVA: 0x000F1CD6 File Offset: 0x000EFED6
		private bool FindRewritingAndUsedViews(IEnumerable<MemberPath> attributes, BoolExpression whereClause, HashSet<FragmentQuery> outputUsedViews, out Tile<FragmentQuery> rewriting, out IEnumerable<MemberPath> notCoveredAttributes)
		{
			if (this.FindRewriting(attributes, whereClause, out rewriting, out notCoveredAttributes))
			{
				outputUsedViews.UnionWith(rewriting.GetNamedQueries());
				return true;
			}
			return false;
		}

		// Token: 0x060044AD RID: 17581 RVA: 0x000F1CF8 File Offset: 0x000EFEF8
		private bool FindRewriting(IEnumerable<MemberPath> attributes, BoolExpression whereClause, out Tile<FragmentQuery> rewriting, out IEnumerable<MemberPath> notCoveredAttributes)
		{
			Tile<FragmentQuery> tile = QueryRewriter.CreateTile(FragmentQuery.Create(attributes, whereClause));
			Tile<FragmentQuery> tile2 = QueryRewriter.CreateTile(FragmentQuery.Create(this._keyAttributes, BoolExpression.CreateNot(whereClause)));
			bool flag = this._context.ViewTarget == ViewTarget.UpdateView;
			return this.RewriteQuery(tile, tile2, out rewriting, out notCoveredAttributes, flag);
		}

		// Token: 0x060044AE RID: 17582 RVA: 0x000F1D44 File Offset: 0x000EFF44
		private bool RewriteQuery(Tile<FragmentQuery> toFill, Tile<FragmentQuery> toAvoid, out Tile<FragmentQuery> rewriting, out IEnumerable<MemberPath> notCoveredAttributes, bool isRelaxed)
		{
			notCoveredAttributes = new List<MemberPath>();
			FragmentQuery fragmentQuery = toFill.Query;
			if (this._context.TryGetCachedRewriting(fragmentQuery, out rewriting))
			{
				return true;
			}
			IEnumerable<Tile<FragmentQuery>> relevantViews = this.GetRelevantViews(fragmentQuery);
			FragmentQuery fragmentQuery2 = fragmentQuery;
			if (!this.RewriteQueryCached(QueryRewriter.CreateTile(FragmentQuery.Create(fragmentQuery.Condition)), toAvoid, relevantViews, out rewriting))
			{
				if (!isRelaxed)
				{
					return false;
				}
				fragmentQuery = FragmentQuery.Create(fragmentQuery.Attributes, BoolExpression.CreateAndNot(fragmentQuery.Condition, rewriting.Query.Condition));
				if (this._qp.IsEmpty(QueryRewriter.CreateTile(fragmentQuery)) || !this.RewriteQueryCached(QueryRewriter.CreateTile(FragmentQuery.Create(fragmentQuery.Condition)), toAvoid, relevantViews, out rewriting))
				{
					return false;
				}
			}
			if (fragmentQuery.Attributes.Count == 0)
			{
				return true;
			}
			Dictionary<MemberPath, FragmentQuery> dictionary = new Dictionary<MemberPath, FragmentQuery>();
			foreach (MemberPath memberPath in QueryRewriter.NonKeys(fragmentQuery.Attributes))
			{
				dictionary[memberPath] = fragmentQuery;
			}
			if (dictionary.Count == 0 || this.CoverAttributes(ref rewriting, dictionary))
			{
				this.GetUsedViewsAndRemoveTrueSurrogate(ref rewriting);
				this._context.SetCachedRewriting(fragmentQuery2, rewriting);
				return true;
			}
			if (isRelaxed)
			{
				foreach (MemberPath memberPath2 in QueryRewriter.NonKeys(fragmentQuery.Attributes))
				{
					FragmentQuery fragmentQuery3;
					if (dictionary.TryGetValue(memberPath2, out fragmentQuery3))
					{
						dictionary[memberPath2] = FragmentQuery.Create(BoolExpression.CreateAndNot(fragmentQuery.Condition, fragmentQuery3.Condition));
					}
					else
					{
						dictionary[memberPath2] = fragmentQuery;
					}
				}
				if (this.CoverAttributes(ref rewriting, dictionary))
				{
					this.GetUsedViewsAndRemoveTrueSurrogate(ref rewriting);
					this._context.SetCachedRewriting(fragmentQuery2, rewriting);
					return true;
				}
			}
			notCoveredAttributes = dictionary.Keys;
			return false;
		}

		// Token: 0x060044AF RID: 17583 RVA: 0x000F1F24 File Offset: 0x000F0124
		private bool RewriteQueryCached(Tile<FragmentQuery> toFill, Tile<FragmentQuery> toAvoid, IEnumerable<Tile<FragmentQuery>> views, out Tile<FragmentQuery> rewriting)
		{
			if (!this._context.TryGetCachedRewriting(toFill.Query, out rewriting))
			{
				bool flag = this._qp.RewriteQuery(toFill, toAvoid, views, out rewriting);
				if (flag)
				{
					this._context.SetCachedRewriting(toFill.Query, rewriting);
				}
				return flag;
			}
			return true;
		}

		// Token: 0x060044B0 RID: 17584 RVA: 0x000F1F64 File Offset: 0x000F0164
		private bool CoverAttributes(ref Tile<FragmentQuery> rewriting, Dictionary<MemberPath, FragmentQuery> attributeConditions)
		{
			foreach (FragmentQuery fragmentQuery in new HashSet<FragmentQuery>(rewriting.GetNamedQueries()))
			{
				foreach (MemberPath memberPath in QueryRewriter.NonKeys(fragmentQuery.Attributes))
				{
					this.CoverAttribute(memberPath, fragmentQuery, attributeConditions);
				}
				if (attributeConditions.Count == 0)
				{
					return true;
				}
			}
			Tile<FragmentQuery> tile = null;
			foreach (FragmentQuery fragmentQuery2 in this._fragmentQueries)
			{
				foreach (MemberPath memberPath2 in QueryRewriter.NonKeys(fragmentQuery2.Attributes))
				{
					if (this.CoverAttribute(memberPath2, fragmentQuery2, attributeConditions))
					{
						tile = ((tile == null) ? QueryRewriter.CreateTile(fragmentQuery2) : this._qp.Union(tile, QueryRewriter.CreateTile(fragmentQuery2)));
					}
				}
				if (attributeConditions.Count == 0)
				{
					break;
				}
			}
			if (attributeConditions.Count == 0)
			{
				rewriting = this._qp.Join(rewriting, tile);
				return true;
			}
			return false;
		}

		// Token: 0x060044B1 RID: 17585 RVA: 0x000F20DC File Offset: 0x000F02DC
		private bool CoverAttribute(MemberPath projectedAttribute, FragmentQuery view, Dictionary<MemberPath, FragmentQuery> attributeConditions)
		{
			FragmentQuery fragmentQuery;
			if (attributeConditions.TryGetValue(projectedAttribute, out fragmentQuery))
			{
				fragmentQuery = FragmentQuery.Create(BoolExpression.CreateAndNot(fragmentQuery.Condition, view.Condition));
				if (this._qp.IsEmpty(QueryRewriter.CreateTile(fragmentQuery)))
				{
					attributeConditions.Remove(projectedAttribute);
				}
				else
				{
					attributeConditions[projectedAttribute] = fragmentQuery;
				}
				return true;
			}
			return false;
		}

		// Token: 0x060044B2 RID: 17586 RVA: 0x000F2134 File Offset: 0x000F0334
		private IEnumerable<Tile<FragmentQuery>> GetRelevantViews(FragmentQuery query)
		{
			Set<MemberPath> variables = QueryRewriter.GetVariables(query);
			Tile<FragmentQuery> tile = null;
			List<Tile<FragmentQuery>> list = new List<Tile<FragmentQuery>>();
			Tile<FragmentQuery> tile2 = null;
			foreach (Tile<FragmentQuery> tile3 in this._views)
			{
				if (QueryRewriter.GetVariables(tile3.Query).Overlaps(variables))
				{
					tile = ((tile == null) ? tile3 : this._qp.Union(tile, tile3));
					list.Add(tile3);
				}
				else if (this.IsTrue(tile3.Query) && tile2 == null)
				{
					tile2 = tile3;
				}
			}
			if (tile != null && this.IsTrue(tile.Query))
			{
				return list;
			}
			if (tile2 == null)
			{
				Tile<FragmentQuery> tile4 = null;
				foreach (FragmentQuery fragmentQuery in this._fragmentQueries)
				{
					tile4 = ((tile4 == null) ? QueryRewriter.CreateTile(fragmentQuery) : this._qp.Union(tile4, QueryRewriter.CreateTile(fragmentQuery)));
					if (this.IsTrue(tile4.Query))
					{
						tile2 = QueryRewriter._trueViewSurrogate;
						break;
					}
				}
			}
			if (tile2 != null)
			{
				list.Add(tile2);
				return list;
			}
			return this._views;
		}

		// Token: 0x060044B3 RID: 17587 RVA: 0x000F2280 File Offset: 0x000F0480
		private HashSet<FragmentQuery> GetUsedViewsAndRemoveTrueSurrogate(ref Tile<FragmentQuery> rewriting)
		{
			HashSet<FragmentQuery> hashSet = new HashSet<FragmentQuery>(rewriting.GetNamedQueries());
			if (!hashSet.Contains(QueryRewriter._trueViewSurrogate.Query))
			{
				return hashSet;
			}
			hashSet.Remove(QueryRewriter._trueViewSurrogate.Query);
			Tile<FragmentQuery> tile = null;
			foreach (FragmentQuery fragmentQuery in hashSet.Concat(this._fragmentQueries))
			{
				tile = ((tile == null) ? QueryRewriter.CreateTile(fragmentQuery) : this._qp.Union(tile, QueryRewriter.CreateTile(fragmentQuery)));
				hashSet.Add(fragmentQuery);
				if (this.IsTrue(tile.Query))
				{
					rewriting = rewriting.Replace(QueryRewriter._trueViewSurrogate, tile);
					return hashSet;
				}
			}
			return hashSet;
		}

		// Token: 0x060044B4 RID: 17588 RVA: 0x000F234C File Offset: 0x000F054C
		private BoolExpression CreateMemberCondition(MemberPath path, Constant domainValue)
		{
			return FragmentQuery.CreateMemberCondition(path, domainValue, this._domainMap);
		}

		// Token: 0x060044B5 RID: 17589 RVA: 0x000F235B File Offset: 0x000F055B
		private FragmentQuery CreateMemberConditionQuery(MemberPath currentPath, Constant domainValue)
		{
			return QueryRewriter.CreateMemberConditionQuery(currentPath, domainValue, this._keyAttributes, this._domainMap);
		}

		// Token: 0x060044B6 RID: 17590 RVA: 0x000F2370 File Offset: 0x000F0570
		internal static FragmentQuery CreateMemberConditionQuery(MemberPath currentPath, Constant domainValue, IEnumerable<MemberPath> keyAttributes, MemberDomainMap domainMap)
		{
			BoolExpression boolExpression = FragmentQuery.CreateMemberCondition(currentPath, domainValue, domainMap);
			IEnumerable<MemberPath> enumerable = keyAttributes;
			if (domainValue is NegatedConstant)
			{
				enumerable = keyAttributes.Concat(new MemberPath[] { currentPath });
			}
			return FragmentQuery.Create(enumerable, boolExpression);
		}

		// Token: 0x060044B7 RID: 17591 RVA: 0x000F23A8 File Offset: 0x000F05A8
		private static TileNamed<FragmentQuery> CreateTile(FragmentQuery query)
		{
			return new TileNamed<FragmentQuery>(query);
		}

		// Token: 0x060044B8 RID: 17592 RVA: 0x000F23B0 File Offset: 0x000F05B0
		private static IEnumerable<Constant> GetTypeConstants(IEnumerable<EdmType> types)
		{
			foreach (EdmType edmType in types)
			{
				yield return new TypeConstant(edmType);
			}
			IEnumerator<EdmType> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x060044B9 RID: 17593 RVA: 0x000F23C0 File Offset: 0x000F05C0
		private static IEnumerable<MemberPath> GetNonConditionalScalarMembers(EdmType edmType, MemberPath currentPath, MemberDomainMap domainMap)
		{
			return currentPath.GetMembers(edmType, new bool?(true), new bool?(false), null, domainMap);
		}

		// Token: 0x060044BA RID: 17594 RVA: 0x000F23EC File Offset: 0x000F05EC
		private static IEnumerable<MemberPath> GetConditionalComplexMembers(EdmType edmType, MemberPath currentPath, MemberDomainMap domainMap)
		{
			return currentPath.GetMembers(edmType, new bool?(false), new bool?(true), null, domainMap);
		}

		// Token: 0x060044BB RID: 17595 RVA: 0x000F2418 File Offset: 0x000F0618
		private static IEnumerable<MemberPath> GetNonConditionalComplexMembers(EdmType edmType, MemberPath currentPath, MemberDomainMap domainMap)
		{
			return currentPath.GetMembers(edmType, new bool?(false), new bool?(false), null, domainMap);
		}

		// Token: 0x060044BC RID: 17596 RVA: 0x000F2444 File Offset: 0x000F0644
		private static IEnumerable<MemberPath> GetConditionalScalarMembers(EdmType edmType, MemberPath currentPath, MemberDomainMap domainMap)
		{
			return currentPath.GetMembers(edmType, new bool?(true), new bool?(true), null, domainMap);
		}

		// Token: 0x060044BD RID: 17597 RVA: 0x000F246E File Offset: 0x000F066E
		private static IEnumerable<MemberPath> NonKeys(IEnumerable<MemberPath> attributes)
		{
			return attributes.Where((MemberPath attr) => !attr.IsPartOfKey);
		}

		// Token: 0x060044BE RID: 17598 RVA: 0x000F2498 File Offset: 0x000F0698
		internal static CellTreeNode TileToCellTree(Tile<FragmentQuery> tile, ViewgenContext context)
		{
			if (tile.OpKind == TileOpKind.Named)
			{
				FragmentQuery view = ((TileNamed<FragmentQuery>)tile).NamedQuery;
				LeftCellWrapper leftCellWrapper = context.AllWrappersForExtent.First((LeftCellWrapper w) => w.FragmentQuery == view);
				return new LeafCellTreeNode(context, leftCellWrapper);
			}
			CellTreeOpType cellTreeOpType;
			switch (tile.OpKind)
			{
			case TileOpKind.Union:
				cellTreeOpType = CellTreeOpType.Union;
				break;
			case TileOpKind.Join:
				cellTreeOpType = CellTreeOpType.IJ;
				break;
			case TileOpKind.AntiSemiJoin:
				cellTreeOpType = CellTreeOpType.LASJ;
				break;
			default:
				return null;
			}
			return new OpCellTreeNode(context, cellTreeOpType, new CellTreeNode[]
			{
				QueryRewriter.TileToCellTree(tile.Arg1, context),
				QueryRewriter.TileToCellTree(tile.Arg2, context)
			});
		}

		// Token: 0x060044BF RID: 17599 RVA: 0x000F253C File Offset: 0x000F073C
		private static BoolExpression TileToBoolExpr(Tile<FragmentQuery> tile)
		{
			switch (tile.OpKind)
			{
			case TileOpKind.Union:
				return BoolExpression.CreateOr(new BoolExpression[]
				{
					QueryRewriter.TileToBoolExpr(tile.Arg1),
					QueryRewriter.TileToBoolExpr(tile.Arg2)
				});
			case TileOpKind.Join:
				return BoolExpression.CreateAnd(new BoolExpression[]
				{
					QueryRewriter.TileToBoolExpr(tile.Arg1),
					QueryRewriter.TileToBoolExpr(tile.Arg2)
				});
			case TileOpKind.AntiSemiJoin:
				return BoolExpression.CreateAnd(new BoolExpression[]
				{
					QueryRewriter.TileToBoolExpr(tile.Arg1),
					BoolExpression.CreateNot(QueryRewriter.TileToBoolExpr(tile.Arg2))
				});
			case TileOpKind.Named:
			{
				FragmentQuery namedQuery = ((TileNamed<FragmentQuery>)tile).NamedQuery;
				if (namedQuery.Condition.IsAlwaysTrue())
				{
					return BoolExpression.True;
				}
				return namedQuery.FromVariable;
			}
			default:
				return null;
			}
		}

		// Token: 0x060044C0 RID: 17600 RVA: 0x000F260F File Offset: 0x000F080F
		private static bool IsDefaultValue(Constant domainValue, MemberPath path)
		{
			return (domainValue.IsNull() && path.IsNullable) || (path.DefaultValue != null && (domainValue as ScalarConstant).Value == path.DefaultValue);
		}

		// Token: 0x060044C1 RID: 17601 RVA: 0x000F2640 File Offset: 0x000F0840
		private static Set<MemberPath> GetVariables(FragmentQuery query)
		{
			return new Set<MemberPath>(from domainConstraint in query.Condition.VariableConstraints
				where domainConstraint.Variable.Identifier is MemberRestriction && !domainConstraint.Variable.Domain.All((Constant constant) => domainConstraint.Range.Contains(constant))
				select ((MemberRestriction)domainConstraint.Variable.Identifier).RestrictedMemberSlot.MemberPath, MemberPath.EqualityComparer);
		}

		// Token: 0x060044C2 RID: 17602 RVA: 0x000F26AA File Offset: 0x000F08AA
		private bool IsTrue(FragmentQuery query)
		{
			return !this._context.LeftFragmentQP.IsSatisfiable(FragmentQuery.Create(BoolExpression.CreateNot(query.Condition)));
		}

		// Token: 0x060044C3 RID: 17603 RVA: 0x000F26D0 File Offset: 0x000F08D0
		[Conditional("DEBUG")]
		private void PrintStatistics(RewritingProcessor<Tile<FragmentQuery>> qp)
		{
			int num;
			int num2;
			int num3;
			int num4;
			int num5;
			qp.GetStatistics(out num, out num2, out num3, out num4, out num5);
		}

		// Token: 0x060044C4 RID: 17604 RVA: 0x000F26ED File Offset: 0x000F08ED
		[Conditional("DEBUG")]
		internal void TraceVerbose(string msg, params object[] parameters)
		{
			if (this._config.IsVerboseTracing)
			{
				Helpers.FormatTraceLine(msg, parameters);
			}
		}

		// Token: 0x040018AB RID: 6315
		private readonly MemberPath _extentPath;

		// Token: 0x040018AC RID: 6316
		private readonly MemberDomainMap _domainMap;

		// Token: 0x040018AD RID: 6317
		private readonly ConfigViewGenerator _config;

		// Token: 0x040018AE RID: 6318
		private readonly CqlIdentifiers _identifiers;

		// Token: 0x040018AF RID: 6319
		private readonly ViewgenContext _context;

		// Token: 0x040018B0 RID: 6320
		private readonly RewritingProcessor<Tile<FragmentQuery>> _qp;

		// Token: 0x040018B1 RID: 6321
		private readonly List<MemberPath> _keyAttributes;

		// Token: 0x040018B2 RID: 6322
		private readonly List<FragmentQuery> _fragmentQueries = new List<FragmentQuery>();

		// Token: 0x040018B3 RID: 6323
		private readonly List<Tile<FragmentQuery>> _views = new List<Tile<FragmentQuery>>();

		// Token: 0x040018B4 RID: 6324
		private readonly FragmentQuery _domainQuery;

		// Token: 0x040018B5 RID: 6325
		private readonly EdmType _generatedType;

		// Token: 0x040018B6 RID: 6326
		private readonly HashSet<FragmentQuery> _usedViews = new HashSet<FragmentQuery>();

		// Token: 0x040018B7 RID: 6327
		private List<LeftCellWrapper> _usedCells = new List<LeftCellWrapper>();

		// Token: 0x040018B8 RID: 6328
		private BoolExpression _topLevelWhereClause;

		// Token: 0x040018B9 RID: 6329
		private CellTreeNode _basicView;

		// Token: 0x040018BA RID: 6330
		private Dictionary<MemberPath, CaseStatement> _caseStatements = new Dictionary<MemberPath, CaseStatement>();

		// Token: 0x040018BB RID: 6331
		private readonly ErrorLog _errorLog = new ErrorLog();

		// Token: 0x040018BC RID: 6332
		private readonly ViewGenMode _typesGenerationMode;

		// Token: 0x040018BD RID: 6333
		private static readonly Tile<FragmentQuery> _trueViewSurrogate = QueryRewriter.CreateTile(FragmentQuery.Create(BoolExpression.True));
	}
}
