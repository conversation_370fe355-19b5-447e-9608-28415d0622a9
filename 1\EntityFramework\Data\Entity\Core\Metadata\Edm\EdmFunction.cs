﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004A4 RID: 1188
	public class EdmFunction : EdmType
	{
		// Token: 0x06003A5A RID: 14938 RVA: 0x000BFF95 File Offset: 0x000BE195
		internal EdmFunction(string name, string namespaceName, DataSpace dataSpace)
			: this(name, namespaceName, dataSpace, new EdmFunctionPayload())
		{
		}

		// Token: 0x06003A5B RID: 14939 RVA: 0x000BFFA8 File Offset: 0x000BE1A8
		internal EdmFunction(string name, string namespaceName, DataSpace dataSpace, EdmFunctionPayload payload)
			: base(name, namespaceName, dataSpace)
		{
			this._schemaName = payload.Schema;
			IList<FunctionParameter> list = payload.ReturnParameters ?? new FunctionParameter[0];
			foreach (FunctionParameter functionParameter in list)
			{
				if (functionParameter == null)
				{
					throw new ArgumentException(Strings.ADP_CollectionParameterElementIsNull("ReturnParameters"));
				}
				if (functionParameter.Mode != ParameterMode.ReturnValue)
				{
					throw new ArgumentException(Strings.NonReturnParameterInReturnParameterCollection);
				}
			}
			this._returnParameters = new ReadOnlyMetadataCollection<FunctionParameter>(list.Select((FunctionParameter returnParameter) => SafeLink<EdmFunction>.BindChild<FunctionParameter>(this, FunctionParameter.DeclaringFunctionLinker, returnParameter)).ToList<FunctionParameter>());
			if (payload.IsAggregate != null)
			{
				EdmFunction.SetFunctionAttribute(ref this._functionAttributes, EdmFunction.FunctionAttributes.Aggregate, payload.IsAggregate.Value);
			}
			if (payload.IsBuiltIn != null)
			{
				EdmFunction.SetFunctionAttribute(ref this._functionAttributes, EdmFunction.FunctionAttributes.BuiltIn, payload.IsBuiltIn.Value);
			}
			if (payload.IsNiladic != null)
			{
				EdmFunction.SetFunctionAttribute(ref this._functionAttributes, EdmFunction.FunctionAttributes.NiladicFunction, payload.IsNiladic.Value);
			}
			if (payload.IsComposable != null)
			{
				EdmFunction.SetFunctionAttribute(ref this._functionAttributes, EdmFunction.FunctionAttributes.IsComposable, payload.IsComposable.Value);
			}
			if (payload.IsFromProviderManifest != null)
			{
				EdmFunction.SetFunctionAttribute(ref this._functionAttributes, EdmFunction.FunctionAttributes.IsFromProviderManifest, payload.IsFromProviderManifest.Value);
			}
			if (payload.IsCachedStoreFunction != null)
			{
				EdmFunction.SetFunctionAttribute(ref this._functionAttributes, EdmFunction.FunctionAttributes.IsCachedStoreFunction, payload.IsCachedStoreFunction.Value);
			}
			if (payload.IsFunctionImport != null)
			{
				EdmFunction.SetFunctionAttribute(ref this._functionAttributes, EdmFunction.FunctionAttributes.IsFunctionImport, payload.IsFunctionImport.Value);
			}
			if (payload.ParameterTypeSemantics != null)
			{
				this._parameterTypeSemantics = payload.ParameterTypeSemantics.Value;
			}
			if (payload.StoreFunctionName != null)
			{
				this._storeFunctionNameAttribute = payload.StoreFunctionName;
			}
			if (payload.EntitySets != null)
			{
				if (payload.EntitySets.Count != list.Count)
				{
					throw new ArgumentException(Strings.NumberOfEntitySetsDoesNotMatchNumberOfReturnParameters);
				}
				this._entitySets = new ReadOnlyCollection<EntitySet>(payload.EntitySets);
			}
			else
			{
				if (this._returnParameters.Count > 1)
				{
					throw new ArgumentException(Strings.NullEntitySetsForFunctionReturningMultipleResultSets);
				}
				this._entitySets = new ReadOnlyCollection<EntitySet>(this._returnParameters.Select((FunctionParameter p) => null).ToList<EntitySet>());
			}
			if (payload.CommandText != null)
			{
				this._commandTextAttribute = payload.CommandText;
			}
			if (payload.Parameters != null)
			{
				foreach (FunctionParameter functionParameter2 in payload.Parameters)
				{
					if (functionParameter2 == null)
					{
						throw new ArgumentException(Strings.ADP_CollectionParameterElementIsNull("parameters"));
					}
					if (functionParameter2.Mode == ParameterMode.ReturnValue)
					{
						throw new ArgumentException(Strings.ReturnParameterInInputParameterCollection);
					}
				}
				this._parameters = new SafeLinkCollection<EdmFunction, FunctionParameter>(this, FunctionParameter.DeclaringFunctionLinker, new MetadataCollection<FunctionParameter>(payload.Parameters));
				return;
			}
			this._parameters = new ReadOnlyMetadataCollection<FunctionParameter>(new MetadataCollection<FunctionParameter>());
		}

		// Token: 0x17000B2A RID: 2858
		// (get) Token: 0x06003A5C RID: 14940 RVA: 0x000C030C File Offset: 0x000BE50C
		public override BuiltInTypeKind BuiltInTypeKind
		{
			get
			{
				return BuiltInTypeKind.EdmFunction;
			}
		}

		// Token: 0x17000B2B RID: 2859
		// (get) Token: 0x06003A5D RID: 14941 RVA: 0x000C0310 File Offset: 0x000BE510
		public override string FullName
		{
			get
			{
				return this.NamespaceName + "." + this.Name;
			}
		}

		// Token: 0x17000B2C RID: 2860
		// (get) Token: 0x06003A5E RID: 14942 RVA: 0x000C0328 File Offset: 0x000BE528
		public ReadOnlyMetadataCollection<FunctionParameter> Parameters
		{
			get
			{
				return this._parameters;
			}
		}

		// Token: 0x06003A5F RID: 14943 RVA: 0x000C0330 File Offset: 0x000BE530
		public void AddParameter(FunctionParameter functionParameter)
		{
			Check.NotNull<FunctionParameter>(functionParameter, "functionParameter");
			Util.ThrowIfReadOnly(this);
			if (functionParameter.Mode == ParameterMode.ReturnValue)
			{
				throw new ArgumentException(Strings.ReturnParameterInInputParameterCollection);
			}
			this._parameters.Source.Add(functionParameter);
		}

		// Token: 0x17000B2D RID: 2861
		// (get) Token: 0x06003A60 RID: 14944 RVA: 0x000C0369 File Offset: 0x000BE569
		internal bool HasUserDefinedBody
		{
			get
			{
				return this.IsModelDefinedFunction && !string.IsNullOrEmpty(this.CommandTextAttribute);
			}
		}

		// Token: 0x17000B2E RID: 2862
		// (get) Token: 0x06003A61 RID: 14945 RVA: 0x000C0383 File Offset: 0x000BE583
		[MetadataProperty(BuiltInTypeKind.EntitySet, false)]
		internal EntitySet EntitySet
		{
			get
			{
				if (this._entitySets.Count == 0)
				{
					return null;
				}
				return this._entitySets[0];
			}
		}

		// Token: 0x17000B2F RID: 2863
		// (get) Token: 0x06003A62 RID: 14946 RVA: 0x000C03A0 File Offset: 0x000BE5A0
		[MetadataProperty(BuiltInTypeKind.EntitySet, true)]
		internal ReadOnlyCollection<EntitySet> EntitySets
		{
			get
			{
				return this._entitySets;
			}
		}

		// Token: 0x17000B30 RID: 2864
		// (get) Token: 0x06003A63 RID: 14947 RVA: 0x000C03A8 File Offset: 0x000BE5A8
		[MetadataProperty(BuiltInTypeKind.FunctionParameter, false)]
		public FunctionParameter ReturnParameter
		{
			get
			{
				return this._returnParameters.FirstOrDefault<FunctionParameter>();
			}
		}

		// Token: 0x17000B31 RID: 2865
		// (get) Token: 0x06003A64 RID: 14948 RVA: 0x000C03B5 File Offset: 0x000BE5B5
		[MetadataProperty(BuiltInTypeKind.FunctionParameter, true)]
		public ReadOnlyMetadataCollection<FunctionParameter> ReturnParameters
		{
			get
			{
				return this._returnParameters;
			}
		}

		// Token: 0x17000B32 RID: 2866
		// (get) Token: 0x06003A65 RID: 14949 RVA: 0x000C03BD File Offset: 0x000BE5BD
		// (set) Token: 0x06003A66 RID: 14950 RVA: 0x000C03C5 File Offset: 0x000BE5C5
		[MetadataProperty(PrimitiveTypeKind.String, false)]
		public string StoreFunctionNameAttribute
		{
			get
			{
				return this._storeFunctionNameAttribute;
			}
			set
			{
				Check.NotEmpty(value, "value");
				Util.ThrowIfReadOnly(this);
				this._storeFunctionNameAttribute = value;
			}
		}

		// Token: 0x17000B33 RID: 2867
		// (get) Token: 0x06003A67 RID: 14951 RVA: 0x000C03E0 File Offset: 0x000BE5E0
		internal string FunctionName
		{
			get
			{
				return this.StoreFunctionNameAttribute ?? this.Name;
			}
		}

		// Token: 0x17000B34 RID: 2868
		// (get) Token: 0x06003A68 RID: 14952 RVA: 0x000C03F2 File Offset: 0x000BE5F2
		[MetadataProperty(typeof(ParameterTypeSemantics), false)]
		public ParameterTypeSemantics ParameterTypeSemanticsAttribute
		{
			get
			{
				return this._parameterTypeSemantics;
			}
		}

		// Token: 0x17000B35 RID: 2869
		// (get) Token: 0x06003A69 RID: 14953 RVA: 0x000C03FA File Offset: 0x000BE5FA
		[MetadataProperty(PrimitiveTypeKind.Boolean, false)]
		public bool AggregateAttribute
		{
			get
			{
				return this.GetFunctionAttribute(EdmFunction.FunctionAttributes.Aggregate);
			}
		}

		// Token: 0x17000B36 RID: 2870
		// (get) Token: 0x06003A6A RID: 14954 RVA: 0x000C0403 File Offset: 0x000BE603
		[MetadataProperty(PrimitiveTypeKind.Boolean, false)]
		public virtual bool BuiltInAttribute
		{
			get
			{
				return this.GetFunctionAttribute(EdmFunction.FunctionAttributes.BuiltIn);
			}
		}

		// Token: 0x17000B37 RID: 2871
		// (get) Token: 0x06003A6B RID: 14955 RVA: 0x000C040C File Offset: 0x000BE60C
		[MetadataProperty(PrimitiveTypeKind.Boolean, false)]
		public bool IsFromProviderManifest
		{
			get
			{
				return this.GetFunctionAttribute(EdmFunction.FunctionAttributes.IsFromProviderManifest);
			}
		}

		// Token: 0x17000B38 RID: 2872
		// (get) Token: 0x06003A6C RID: 14956 RVA: 0x000C0416 File Offset: 0x000BE616
		[MetadataProperty(PrimitiveTypeKind.Boolean, false)]
		public bool NiladicFunctionAttribute
		{
			get
			{
				return this.GetFunctionAttribute(EdmFunction.FunctionAttributes.NiladicFunction);
			}
		}

		// Token: 0x17000B39 RID: 2873
		// (get) Token: 0x06003A6D RID: 14957 RVA: 0x000C041F File Offset: 0x000BE61F
		[MetadataProperty(PrimitiveTypeKind.Boolean, false)]
		public bool IsComposableAttribute
		{
			get
			{
				return this.GetFunctionAttribute(EdmFunction.FunctionAttributes.IsComposable);
			}
		}

		// Token: 0x17000B3A RID: 2874
		// (get) Token: 0x06003A6E RID: 14958 RVA: 0x000C0428 File Offset: 0x000BE628
		[MetadataProperty(PrimitiveTypeKind.String, false)]
		public string CommandTextAttribute
		{
			get
			{
				return this._commandTextAttribute;
			}
		}

		// Token: 0x17000B3B RID: 2875
		// (get) Token: 0x06003A6F RID: 14959 RVA: 0x000C0430 File Offset: 0x000BE630
		internal bool IsCachedStoreFunction
		{
			get
			{
				return this.GetFunctionAttribute(EdmFunction.FunctionAttributes.IsCachedStoreFunction);
			}
		}

		// Token: 0x17000B3C RID: 2876
		// (get) Token: 0x06003A70 RID: 14960 RVA: 0x000C043A File Offset: 0x000BE63A
		internal bool IsModelDefinedFunction
		{
			get
			{
				return this.DataSpace == DataSpace.CSpace && !this.IsCachedStoreFunction && !this.IsFromProviderManifest && !this.IsFunctionImport;
			}
		}

		// Token: 0x17000B3D RID: 2877
		// (get) Token: 0x06003A71 RID: 14961 RVA: 0x000C0460 File Offset: 0x000BE660
		internal bool IsFunctionImport
		{
			get
			{
				return this.GetFunctionAttribute(EdmFunction.FunctionAttributes.IsFunctionImport);
			}
		}

		// Token: 0x17000B3E RID: 2878
		// (get) Token: 0x06003A72 RID: 14962 RVA: 0x000C046A File Offset: 0x000BE66A
		// (set) Token: 0x06003A73 RID: 14963 RVA: 0x000C0472 File Offset: 0x000BE672
		[MetadataProperty(PrimitiveTypeKind.String, false)]
		public string Schema
		{
			get
			{
				return this._schemaName;
			}
			set
			{
				Check.NotEmpty(value, "value");
				Util.ThrowIfReadOnly(this);
				this._schemaName = value;
			}
		}

		// Token: 0x06003A74 RID: 14964 RVA: 0x000C0490 File Offset: 0x000BE690
		internal override void SetReadOnly()
		{
			if (!base.IsReadOnly)
			{
				base.SetReadOnly();
				this.Parameters.Source.SetReadOnly();
				foreach (FunctionParameter functionParameter in this.ReturnParameters)
				{
					functionParameter.SetReadOnly();
				}
			}
		}

		// Token: 0x06003A75 RID: 14965 RVA: 0x000C0500 File Offset: 0x000BE700
		internal override void BuildIdentity(StringBuilder builder)
		{
			if (base.CacheIdentity != null)
			{
				builder.Append(base.CacheIdentity);
				return;
			}
			EdmFunction.BuildIdentity<FunctionParameter>(builder, this.FullName, this.Parameters, (FunctionParameter param) => param.TypeUsage, (FunctionParameter param) => param.Mode);
		}

		// Token: 0x06003A76 RID: 14966 RVA: 0x000C0574 File Offset: 0x000BE774
		internal static string BuildIdentity(string functionName, IEnumerable<TypeUsage> functionParameters)
		{
			StringBuilder stringBuilder = new StringBuilder();
			EdmFunction.BuildIdentity<TypeUsage>(stringBuilder, functionName, functionParameters, (TypeUsage param) => param, (TypeUsage param) => ParameterMode.In);
			return stringBuilder.ToString();
		}

		// Token: 0x06003A77 RID: 14967 RVA: 0x000C05D4 File Offset: 0x000BE7D4
		internal static void BuildIdentity<TParameterMetadata>(StringBuilder builder, string functionName, IEnumerable<TParameterMetadata> functionParameters, Func<TParameterMetadata, TypeUsage> getParameterTypeUsage, Func<TParameterMetadata, ParameterMode> getParameterMode)
		{
			builder.Append(functionName);
			builder.Append('(');
			bool flag = true;
			foreach (TParameterMetadata tparameterMetadata in functionParameters)
			{
				if (flag)
				{
					flag = false;
				}
				else
				{
					builder.Append(",");
				}
				builder.Append(Helper.ToString(getParameterMode(tparameterMetadata)));
				builder.Append(' ');
				getParameterTypeUsage(tparameterMetadata).BuildIdentity(builder);
			}
			builder.Append(')');
		}

		// Token: 0x06003A78 RID: 14968 RVA: 0x000C0670 File Offset: 0x000BE870
		private bool GetFunctionAttribute(EdmFunction.FunctionAttributes attribute)
		{
			return attribute == (attribute & this._functionAttributes);
		}

		// Token: 0x06003A79 RID: 14969 RVA: 0x000C067D File Offset: 0x000BE87D
		private static void SetFunctionAttribute(ref EdmFunction.FunctionAttributes field, EdmFunction.FunctionAttributes attribute, bool isSet)
		{
			if (isSet)
			{
				field |= attribute;
				return;
			}
			field ^= field & attribute;
		}

		// Token: 0x06003A7A RID: 14970 RVA: 0x000C0694 File Offset: 0x000BE894
		public static EdmFunction Create(string name, string namespaceName, DataSpace dataSpace, EdmFunctionPayload payload, IEnumerable<MetadataProperty> metadataProperties)
		{
			Check.NotEmpty(name, "name");
			Check.NotEmpty(namespaceName, "namespaceName");
			EdmFunction edmFunction = new EdmFunction(name, namespaceName, dataSpace, payload);
			if (metadataProperties != null)
			{
				edmFunction.AddMetadataProperties(metadataProperties);
			}
			edmFunction.SetReadOnly();
			return edmFunction;
		}

		// Token: 0x04001411 RID: 5137
		private readonly ReadOnlyMetadataCollection<FunctionParameter> _returnParameters;

		// Token: 0x04001412 RID: 5138
		private readonly ReadOnlyMetadataCollection<FunctionParameter> _parameters;

		// Token: 0x04001413 RID: 5139
		private readonly EdmFunction.FunctionAttributes _functionAttributes = EdmFunction.FunctionAttributes.IsComposable;

		// Token: 0x04001414 RID: 5140
		private string _storeFunctionNameAttribute;

		// Token: 0x04001415 RID: 5141
		private readonly ParameterTypeSemantics _parameterTypeSemantics;

		// Token: 0x04001416 RID: 5142
		private readonly string _commandTextAttribute;

		// Token: 0x04001417 RID: 5143
		private string _schemaName;

		// Token: 0x04001418 RID: 5144
		private readonly ReadOnlyCollection<EntitySet> _entitySets;

		// Token: 0x02000ACD RID: 2765
		[Flags]
		private enum FunctionAttributes : byte
		{
			// Token: 0x04002BB4 RID: 11188
			Aggregate = 1,
			// Token: 0x04002BB5 RID: 11189
			BuiltIn = 2,
			// Token: 0x04002BB6 RID: 11190
			NiladicFunction = 4,
			// Token: 0x04002BB7 RID: 11191
			IsComposable = 8,
			// Token: 0x04002BB8 RID: 11192
			IsFromProviderManifest = 16,
			// Token: 0x04002BB9 RID: 11193
			IsCachedStoreFunction = 32,
			// Token: 0x04002BBA RID: 11194
			IsFunctionImport = 64,
			// Token: 0x04002BBB RID: 11195
			Default = 8
		}
	}
}
