﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000006 RID: 6
	public class DevilTreasRankRewardList
	{
		// Token: 0x17000009 RID: 9
		// (get) Token: 0x06000015 RID: 21 RVA: 0x00002103 File Offset: 0x00000303
		// (set) Token: 0x06000016 RID: 22 RVA: 0x0000210B File Offset: 0x0000030B
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int ID { get; set; }

		// Token: 0x1700000A RID: 10
		// (get) Token: 0x06000017 RID: 23 RVA: 0x00002114 File Offset: 0x00000314
		// (set) Token: 0x06000018 RID: 24 RVA: 0x0000211C File Offset: 0x0000031C
		public int RankMin { get; set; }

		// Token: 0x1700000B RID: 11
		// (get) Token: 0x06000019 RID: 25 RVA: 0x00002125 File Offset: 0x00000325
		// (set) Token: 0x0600001A RID: 26 RVA: 0x0000212D File Offset: 0x0000032D
		public int RankMax { get; set; }

		// Token: 0x1700000C RID: 12
		// (get) Token: 0x0600001B RID: 27 RVA: 0x00002136 File Offset: 0x00000336
		// (set) Token: 0x0600001C RID: 28 RVA: 0x0000213E File Offset: 0x0000033E
		public string Desc { get; set; }

		// Token: 0x1700000D RID: 13
		// (get) Token: 0x0600001D RID: 29 RVA: 0x00002147 File Offset: 0x00000347
		// (set) Token: 0x0600001E RID: 30 RVA: 0x0000214F File Offset: 0x0000034F
		public float Percent { get; set; }

		// Token: 0x1700000E RID: 14
		// (get) Token: 0x0600001F RID: 31 RVA: 0x00002158 File Offset: 0x00000358
		// (set) Token: 0x06000020 RID: 32 RVA: 0x00002160 File Offset: 0x00000360
		public int Reward { get; set; }
	}
}
