﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Query.InternalTrees;
using System.Data.Entity.Core.Query.PlanCompiler;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x02000538 RID: 1336
	public sealed class FunctionImportMappingComposable : FunctionImportMapping
	{
		// Token: 0x060041D4 RID: 16852 RVA: 0x000DD300 File Offset: 0x000DB500
		public FunctionImportMappingComposable(EdmFunction functionImport, EdmFunction targetFunction, FunctionImportResultMapping resultMapping, EntityContainerMapping containerMapping)
			: base(Check.NotNull<EdmFunction>(functionImport, "functionImport"), Check.NotNull<EdmFunction>(targetFunction, "targetFunction"))
		{
			Check.NotNull<FunctionImportResultMapping>(resultMapping, "resultMapping");
			Check.NotNull<EntityContainerMapping>(containerMapping, "containerMapping");
			if (!functionImport.IsComposableAttribute)
			{
				throw new ArgumentException(Strings.NonComposableFunctionCannotBeMappedAsComposable("functionImport"));
			}
			if (!targetFunction.IsComposableAttribute)
			{
				throw new ArgumentException(Strings.NonComposableFunctionCannotBeMappedAsComposable("targetFunction"));
			}
			EdmType edmType;
			if (!MetadataHelper.TryGetFunctionImportReturnType<EdmType>(functionImport, 0, out edmType))
			{
				throw new ArgumentException(Strings.InvalidReturnTypeForComposableFunction);
			}
			EdmFunction edmFunction = ((containerMapping.StorageMappingItemCollection != null) ? containerMapping.StorageMappingItemCollection.StoreItemCollection.ConvertToCTypeFunction(targetFunction) : StoreItemCollection.ConvertFunctionSignatureToCType(targetFunction));
			RowType tvfReturnType = TypeHelpers.GetTvfReturnType(edmFunction);
			RowType tvfReturnType2 = TypeHelpers.GetTvfReturnType(targetFunction);
			if (tvfReturnType == null)
			{
				throw new ArgumentException(Strings.Mapping_FunctionImport_ResultMapping_InvalidSType(functionImport.Identity), "functionImport");
			}
			List<EdmSchemaError> list = new List<EdmSchemaError>();
			FunctionImportMappingComposableHelper functionImportMappingComposableHelper = new FunctionImportMappingComposableHelper(containerMapping, string.Empty, list);
			FunctionImportMappingComposable functionImportMappingComposable;
			if (Helper.IsStructuralType(edmType))
			{
				functionImportMappingComposableHelper.TryCreateFunctionImportMappingComposableWithStructuralResult(functionImport, edmFunction, resultMapping.SourceList, tvfReturnType, tvfReturnType2, LineInfo.Empty, out functionImportMappingComposable);
			}
			else
			{
				functionImportMappingComposableHelper.TryCreateFunctionImportMappingComposableWithScalarResult(functionImport, edmFunction, targetFunction, edmType, tvfReturnType, LineInfo.Empty, out functionImportMappingComposable);
			}
			if (functionImportMappingComposable == null)
			{
				throw new InvalidOperationException((list.Count > 0) ? list[0].Message : string.Empty);
			}
			this._containerMapping = functionImportMappingComposable._containerMapping;
			this.m_commandParameters = functionImportMappingComposable.m_commandParameters;
			this.m_structuralTypeMappings = functionImportMappingComposable.m_structuralTypeMappings;
			this.m_targetFunctionKeys = functionImportMappingComposable.m_targetFunctionKeys;
			this._resultMapping = resultMapping;
		}

		// Token: 0x060041D5 RID: 16853 RVA: 0x000DD484 File Offset: 0x000DB684
		internal FunctionImportMappingComposable(EdmFunction functionImport, EdmFunction targetFunction, List<Tuple<StructuralType, List<ConditionPropertyMapping>, List<PropertyMapping>>> structuralTypeMappings)
			: base(functionImport, targetFunction)
		{
			if (!functionImport.IsComposableAttribute)
			{
				throw new ArgumentException(Strings.NonComposableFunctionCannotBeMappedAsComposable("functionImport"));
			}
			if (!targetFunction.IsComposableAttribute)
			{
				throw new ArgumentException(Strings.NonComposableFunctionCannotBeMappedAsComposable("targetFunction"));
			}
			EdmType edmType;
			if (!MetadataHelper.TryGetFunctionImportReturnType<EdmType>(functionImport, 0, out edmType))
			{
				throw new ArgumentException(Strings.InvalidReturnTypeForComposableFunction);
			}
			if (!TypeSemantics.IsScalarType(edmType) && (structuralTypeMappings == null || structuralTypeMappings.Count == 0))
			{
				throw new ArgumentException(Strings.StructuralTypeMappingsMustNotBeNullForFunctionImportsReturningNonScalarValues);
			}
			this.m_structuralTypeMappings = structuralTypeMappings;
		}

		// Token: 0x060041D6 RID: 16854 RVA: 0x000DD504 File Offset: 0x000DB704
		internal FunctionImportMappingComposable(EdmFunction functionImport, EdmFunction targetFunction, List<Tuple<StructuralType, List<ConditionPropertyMapping>, List<PropertyMapping>>> structuralTypeMappings, EdmProperty[] targetFunctionKeys, EntityContainerMapping containerMapping)
			: base(functionImport, targetFunction)
		{
			this._containerMapping = containerMapping;
			this.m_commandParameters = functionImport.Parameters.Select((FunctionParameter p) => TypeHelpers.GetPrimitiveTypeUsageForScalar(p.TypeUsage).Parameter(p.Name)).ToArray<DbParameterReferenceExpression>();
			this.m_structuralTypeMappings = structuralTypeMappings;
			this.m_targetFunctionKeys = targetFunctionKeys;
		}

		// Token: 0x17000D0B RID: 3339
		// (get) Token: 0x060041D7 RID: 16855 RVA: 0x000DD565 File Offset: 0x000DB765
		public FunctionImportResultMapping ResultMapping
		{
			get
			{
				return this._resultMapping;
			}
		}

		// Token: 0x060041D8 RID: 16856 RVA: 0x000DD56D File Offset: 0x000DB76D
		internal override void SetReadOnly()
		{
			MappingItem.SetReadOnly(this._resultMapping);
			base.SetReadOnly();
		}

		// Token: 0x17000D0C RID: 3340
		// (get) Token: 0x060041D9 RID: 16857 RVA: 0x000DD580 File Offset: 0x000DB780
		internal ReadOnlyCollection<Tuple<StructuralType, List<ConditionPropertyMapping>, List<PropertyMapping>>> StructuralTypeMappings
		{
			get
			{
				if (this.m_structuralTypeMappings != null)
				{
					return new ReadOnlyCollection<Tuple<StructuralType, List<ConditionPropertyMapping>, List<PropertyMapping>>>(this.m_structuralTypeMappings);
				}
				return null;
			}
		}

		// Token: 0x17000D0D RID: 3341
		// (get) Token: 0x060041DA RID: 16858 RVA: 0x000DD597 File Offset: 0x000DB797
		internal EdmProperty[] TvfKeys
		{
			get
			{
				return this.m_targetFunctionKeys;
			}
		}

		// Token: 0x060041DB RID: 16859 RVA: 0x000DD5A0 File Offset: 0x000DB7A0
		internal Node GetInternalTree(Command targetIqtCommand, IList<Node> targetIqtArguments)
		{
			if (this.m_internalTreeNode == null)
			{
				DiscriminatorMap discriminatorMap;
				Command command = ITreeGenerator.Generate(this.GenerateFunctionView(out discriminatorMap), discriminatorMap);
				Node root = command.Root;
				PlanCompiler.Assert(root.Op.OpType == OpType.PhysicalProject, "Expected a physical projectOp at the root of the tree - found " + root.Op.OpType.ToString());
				PhysicalProjectOp physicalProjectOp = (PhysicalProjectOp)root.Op;
				Node child = root.Child0;
				command.DisableVarVecEnumCaching();
				Node node = child;
				Var var = physicalProjectOp.Outputs[0];
				if (!Command.EqualTypes(physicalProjectOp.ColumnMap.Type, base.FunctionImport.ReturnParameter.TypeUsage))
				{
					TypeUsage typeUsage = ((CollectionType)base.FunctionImport.ReturnParameter.TypeUsage.EdmType).TypeUsage;
					Node node2 = command.CreateNode(command.CreateVarRefOp(var));
					Node node3 = command.CreateNode(command.CreateSoftCastOp(typeUsage), node2);
					Node node4 = command.CreateVarDefListNode(node3, out var);
					ProjectOp projectOp = command.CreateProjectOp(var);
					node = command.CreateNode(projectOp, node, node4);
				}
				this.m_internalTreeNode = command.BuildCollect(node, var);
			}
			Dictionary<string, Node> dictionary = new Dictionary<string, Node>(this.m_commandParameters.Length);
			for (int i = 0; i < this.m_commandParameters.Length; i++)
			{
				DbParameterReferenceExpression dbParameterReferenceExpression = this.m_commandParameters[i];
				Node node5 = targetIqtArguments[i];
				if (TypeSemantics.IsEnumerationType(node5.Op.Type))
				{
					node5 = targetIqtCommand.CreateNode(targetIqtCommand.CreateSoftCastOp(TypeHelpers.CreateEnumUnderlyingTypeUsage(node5.Op.Type)), node5);
				}
				dictionary.Add(dbParameterReferenceExpression.ParameterName, node5);
			}
			return FunctionImportMappingComposable.FunctionViewOpCopier.Copy(targetIqtCommand, this.m_internalTreeNode, dictionary);
		}

		// Token: 0x060041DC RID: 16860 RVA: 0x000DD750 File Offset: 0x000DB950
		internal DbQueryCommandTree GenerateFunctionView(out DiscriminatorMap discriminatorMap)
		{
			discriminatorMap = null;
			DbExpression dbExpression = base.TargetFunction.Invoke(this.GetParametersForTargetFunctionCall());
			DbExpression dbExpression2;
			if (this.m_structuralTypeMappings != null)
			{
				dbExpression2 = this.GenerateStructuralTypeResultMappingView(dbExpression, out discriminatorMap);
			}
			else
			{
				dbExpression2 = this.GenerateScalarResultMappingView(dbExpression);
			}
			return DbQueryCommandTree.FromValidExpression(this._containerMapping.StorageMappingItemCollection.Workspace, DataSpace.SSpace, dbExpression2, true, false);
		}

		// Token: 0x060041DD RID: 16861 RVA: 0x000DD7A6 File Offset: 0x000DB9A6
		private IEnumerable<DbExpression> GetParametersForTargetFunctionCall()
		{
			using (ReadOnlyMetadataCollection<FunctionParameter>.Enumerator enumerator = base.TargetFunction.Parameters.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					FunctionParameter targetParameter = enumerator.Current;
					FunctionParameter functionParameter = base.FunctionImport.Parameters.Single((FunctionParameter p) => p.Name == targetParameter.Name);
					yield return this.m_commandParameters[base.FunctionImport.Parameters.IndexOf(functionParameter)];
				}
			}
			ReadOnlyMetadataCollection<FunctionParameter>.Enumerator enumerator = default(ReadOnlyMetadataCollection<FunctionParameter>.Enumerator);
			yield break;
			yield break;
		}

		// Token: 0x060041DE RID: 16862 RVA: 0x000DD7B8 File Offset: 0x000DB9B8
		private DbExpression GenerateStructuralTypeResultMappingView(DbExpression storeFunctionInvoke, out DiscriminatorMap discriminatorMap)
		{
			discriminatorMap = null;
			DbExpression dbExpression = storeFunctionInvoke;
			if (this.m_structuralTypeMappings.Count == 1)
			{
				Tuple<StructuralType, List<ConditionPropertyMapping>, List<PropertyMapping>> tuple = this.m_structuralTypeMappings[0];
				StructuralType item = tuple.Item1;
				List<ConditionPropertyMapping> conditions = tuple.Item2;
				List<PropertyMapping> item2 = tuple.Item3;
				if (conditions.Count > 0)
				{
					dbExpression = dbExpression.Where((DbExpression row) => FunctionImportMappingComposable.GenerateStructuralTypeConditionsPredicate(conditions, row));
				}
				DbExpressionBinding dbExpressionBinding = dbExpression.BindAs("row");
				DbExpression dbExpression2 = FunctionImportMappingComposable.GenerateStructuralTypeMappingView(item, item2, dbExpressionBinding.Variable);
				dbExpression = dbExpressionBinding.Project(dbExpression2);
			}
			else
			{
				DbExpressionBinding binding = dbExpression.BindAs("row");
				List<DbExpression> list = this.m_structuralTypeMappings.Select((Tuple<StructuralType, List<ConditionPropertyMapping>, List<PropertyMapping>> m) => FunctionImportMappingComposable.GenerateStructuralTypeConditionsPredicate(m.Item2, binding.Variable)).ToList<DbExpression>();
				dbExpression = binding.Filter(Helpers.BuildBalancedTreeInPlace<DbExpression>(list.ToArray(), (DbExpression prev, DbExpression next) => prev.Or(next)));
				binding = dbExpression.BindAs("row");
				List<DbExpression> list2 = new List<DbExpression>(this.m_structuralTypeMappings.Count);
				foreach (Tuple<StructuralType, List<ConditionPropertyMapping>, List<PropertyMapping>> tuple2 in this.m_structuralTypeMappings)
				{
					StructuralType item3 = tuple2.Item1;
					List<PropertyMapping> item4 = tuple2.Item3;
					list2.Add(FunctionImportMappingComposable.GenerateStructuralTypeMappingView(item3, item4, binding.Variable));
				}
				DbExpression dbExpression3 = DbExpressionBuilder.Case(list.Take(this.m_structuralTypeMappings.Count - 1), list2.Take(this.m_structuralTypeMappings.Count - 1), list2[this.m_structuralTypeMappings.Count - 1]);
				dbExpression = binding.Project(dbExpression3);
				DiscriminatorMap.TryCreateDiscriminatorMap(base.FunctionImport.EntitySet, dbExpression, out discriminatorMap);
			}
			return dbExpression;
		}

		// Token: 0x060041DF RID: 16863 RVA: 0x000DD9B4 File Offset: 0x000DBBB4
		private static DbExpression GenerateStructuralTypeMappingView(StructuralType structuralType, List<PropertyMapping> propertyMappings, DbExpression row)
		{
			List<DbExpression> list = new List<DbExpression>(TypeHelpers.GetAllStructuralMembers(structuralType).Count);
			for (int i = 0; i < propertyMappings.Count; i++)
			{
				PropertyMapping propertyMapping = propertyMappings[i];
				list.Add(FunctionImportMappingComposable.GeneratePropertyMappingView(propertyMapping, row));
			}
			return TypeUsage.Create(structuralType).New(list);
		}

		// Token: 0x060041E0 RID: 16864 RVA: 0x000DDA04 File Offset: 0x000DBC04
		private static DbExpression GenerateStructuralTypeConditionsPredicate(List<ConditionPropertyMapping> conditions, DbExpression row)
		{
			return Helpers.BuildBalancedTreeInPlace<DbExpression>(conditions.Select((ConditionPropertyMapping c) => FunctionImportMappingComposable.GeneratePredicate(c, row)).ToArray<DbExpression>(), (DbExpression prev, DbExpression next) => prev.And(next));
		}

		// Token: 0x060041E1 RID: 16865 RVA: 0x000DDA5C File Offset: 0x000DBC5C
		private static DbExpression GeneratePredicate(ConditionPropertyMapping condition, DbExpression row)
		{
			DbExpression dbExpression = FunctionImportMappingComposable.GenerateColumnRef(row, condition.Column);
			if (condition.IsNull == null)
			{
				return dbExpression.Equal(dbExpression.ResultType.Constant(condition.Value));
			}
			if (!condition.IsNull.Value)
			{
				return dbExpression.IsNull().Not();
			}
			return dbExpression.IsNull();
		}

		// Token: 0x060041E2 RID: 16866 RVA: 0x000DDAC0 File Offset: 0x000DBCC0
		private static DbExpression GeneratePropertyMappingView(PropertyMapping mapping, DbExpression row)
		{
			ScalarPropertyMapping scalarPropertyMapping = (ScalarPropertyMapping)mapping;
			return FunctionImportMappingComposable.GenerateScalarPropertyMappingView(scalarPropertyMapping.Property, scalarPropertyMapping.Column, row);
		}

		// Token: 0x060041E3 RID: 16867 RVA: 0x000DDAE8 File Offset: 0x000DBCE8
		private static DbExpression GenerateScalarPropertyMappingView(EdmProperty edmProperty, EdmProperty columnProperty, DbExpression row)
		{
			DbExpression dbExpression = FunctionImportMappingComposable.GenerateColumnRef(row, columnProperty);
			if (!TypeSemantics.IsEqual(dbExpression.ResultType, edmProperty.TypeUsage))
			{
				dbExpression = dbExpression.CastTo(edmProperty.TypeUsage);
			}
			return dbExpression;
		}

		// Token: 0x060041E4 RID: 16868 RVA: 0x000DDB1E File Offset: 0x000DBD1E
		private static DbExpression GenerateColumnRef(DbExpression row, EdmProperty column)
		{
			RowType rowType = (RowType)row.ResultType.EdmType;
			return row.Property(column.Name);
		}

		// Token: 0x060041E5 RID: 16869 RVA: 0x000DDB40 File Offset: 0x000DBD40
		private DbExpression GenerateScalarResultMappingView(DbExpression storeFunctionInvoke)
		{
			CollectionType functionImportReturnType;
			MetadataHelper.TryGetFunctionImportReturnCollectionType(base.FunctionImport, 0, out functionImportReturnType);
			RowType rowType = (RowType)((CollectionType)storeFunctionInvoke.ResultType.EdmType).TypeUsage.EdmType;
			EdmProperty column = rowType.Properties[0];
			Func<DbExpression, DbExpression> scalarView = delegate(DbExpression row)
			{
				DbPropertyExpression dbPropertyExpression = row.Property(column);
				if (TypeSemantics.IsEqual(functionImportReturnType.TypeUsage, column.TypeUsage))
				{
					return dbPropertyExpression;
				}
				return dbPropertyExpression.CastTo(functionImportReturnType.TypeUsage);
			};
			return storeFunctionInvoke.Select((DbExpression row) => scalarView(row));
		}

		// Token: 0x040016D2 RID: 5842
		private readonly FunctionImportResultMapping _resultMapping;

		// Token: 0x040016D3 RID: 5843
		private readonly EntityContainerMapping _containerMapping;

		// Token: 0x040016D4 RID: 5844
		private readonly DbParameterReferenceExpression[] m_commandParameters;

		// Token: 0x040016D5 RID: 5845
		private readonly List<Tuple<StructuralType, List<ConditionPropertyMapping>, List<PropertyMapping>>> m_structuralTypeMappings;

		// Token: 0x040016D6 RID: 5846
		private readonly EdmProperty[] m_targetFunctionKeys;

		// Token: 0x040016D7 RID: 5847
		private Node m_internalTreeNode;

		// Token: 0x02000B3A RID: 2874
		private sealed class FunctionViewOpCopier : OpCopier
		{
			// Token: 0x06006569 RID: 25961 RVA: 0x0015BF40 File Offset: 0x0015A140
			private FunctionViewOpCopier(Command cmd, Dictionary<string, Node> viewArguments)
				: base(cmd)
			{
				this.m_viewArguments = viewArguments;
			}

			// Token: 0x0600656A RID: 25962 RVA: 0x0015BF50 File Offset: 0x0015A150
			internal static Node Copy(Command cmd, Node viewNode, Dictionary<string, Node> viewArguments)
			{
				return new FunctionImportMappingComposable.FunctionViewOpCopier(cmd, viewArguments).CopyNode(viewNode);
			}

			// Token: 0x0600656B RID: 25963 RVA: 0x0015BF60 File Offset: 0x0015A160
			public override Node Visit(VarRefOp op, Node n)
			{
				Node node;
				if (op.Var.VarType == VarType.Parameter && this.m_viewArguments.TryGetValue(((ParameterVar)op.Var).ParameterName, out node))
				{
					return OpCopier.Copy(this.m_destCmd, node);
				}
				return base.Visit(op, n);
			}

			// Token: 0x04002D4C RID: 11596
			private readonly Dictionary<string, Node> m_viewArguments;
		}
	}
}
