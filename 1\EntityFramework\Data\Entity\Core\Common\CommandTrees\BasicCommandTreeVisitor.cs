﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006A2 RID: 1698
	public abstract class BasicCommandTreeVisitor : BasicExpressionVisitor
	{
		// Token: 0x06004FBF RID: 20415 RVA: 0x001203A7 File Offset: 0x0011E5A7
		protected virtual void VisitSetClause(DbSetClause setClause)
		{
			Check.NotNull<DbSetClause>(setClause, "setClause");
			this.VisitExpression(setClause.Property);
			this.VisitExpression(setClause.Value);
		}

		// Token: 0x06004FC0 RID: 20416 RVA: 0x001203CD File Offset: 0x0011E5CD
		protected virtual void VisitModificationClause(DbModificationClause modificationClause)
		{
			Check.NotNull<DbModificationClause>(modificationClause, "modificationClause");
			this.VisitSetClause((DbSetClause)modificationClause);
		}

		// Token: 0x06004FC1 RID: 20417 RVA: 0x001203E8 File Offset: 0x0011E5E8
		protected virtual void VisitModificationClauses(IList<DbModificationClause> modificationClauses)
		{
			Check.NotNull<IList<DbModificationClause>>(modificationClauses, "modificationClauses");
			for (int i = 0; i < modificationClauses.Count; i++)
			{
				this.VisitModificationClause(modificationClauses[i]);
			}
		}

		// Token: 0x06004FC2 RID: 20418 RVA: 0x00120420 File Offset: 0x0011E620
		public virtual void VisitCommandTree(DbCommandTree commandTree)
		{
			Check.NotNull<DbCommandTree>(commandTree, "commandTree");
			switch (commandTree.CommandTreeKind)
			{
			case DbCommandTreeKind.Query:
				this.VisitQueryCommandTree((DbQueryCommandTree)commandTree);
				return;
			case DbCommandTreeKind.Update:
				this.VisitUpdateCommandTree((DbUpdateCommandTree)commandTree);
				return;
			case DbCommandTreeKind.Insert:
				this.VisitInsertCommandTree((DbInsertCommandTree)commandTree);
				return;
			case DbCommandTreeKind.Delete:
				this.VisitDeleteCommandTree((DbDeleteCommandTree)commandTree);
				return;
			case DbCommandTreeKind.Function:
				this.VisitFunctionCommandTree((DbFunctionCommandTree)commandTree);
				return;
			default:
				throw new NotSupportedException();
			}
		}

		// Token: 0x06004FC3 RID: 20419 RVA: 0x001204A2 File Offset: 0x0011E6A2
		protected virtual void VisitDeleteCommandTree(DbDeleteCommandTree deleteTree)
		{
			Check.NotNull<DbDeleteCommandTree>(deleteTree, "deleteTree");
			this.VisitExpressionBindingPre(deleteTree.Target);
			this.VisitExpression(deleteTree.Predicate);
			this.VisitExpressionBindingPost(deleteTree.Target);
		}

		// Token: 0x06004FC4 RID: 20420 RVA: 0x001204D4 File Offset: 0x0011E6D4
		protected virtual void VisitFunctionCommandTree(DbFunctionCommandTree functionTree)
		{
			Check.NotNull<DbFunctionCommandTree>(functionTree, "functionTree");
		}

		// Token: 0x06004FC5 RID: 20421 RVA: 0x001204E4 File Offset: 0x0011E6E4
		protected virtual void VisitInsertCommandTree(DbInsertCommandTree insertTree)
		{
			Check.NotNull<DbInsertCommandTree>(insertTree, "insertTree");
			this.VisitExpressionBindingPre(insertTree.Target);
			this.VisitModificationClauses(insertTree.SetClauses);
			if (insertTree.Returning != null)
			{
				this.VisitExpression(insertTree.Returning);
			}
			this.VisitExpressionBindingPost(insertTree.Target);
		}

		// Token: 0x06004FC6 RID: 20422 RVA: 0x00120535 File Offset: 0x0011E735
		protected virtual void VisitQueryCommandTree(DbQueryCommandTree queryTree)
		{
			Check.NotNull<DbQueryCommandTree>(queryTree, "queryTree");
			this.VisitExpression(queryTree.Query);
		}

		// Token: 0x06004FC7 RID: 20423 RVA: 0x00120550 File Offset: 0x0011E750
		protected virtual void VisitUpdateCommandTree(DbUpdateCommandTree updateTree)
		{
			Check.NotNull<DbUpdateCommandTree>(updateTree, "updateTree");
			this.VisitExpressionBindingPre(updateTree.Target);
			this.VisitModificationClauses(updateTree.SetClauses);
			this.VisitExpression(updateTree.Predicate);
			if (updateTree.Returning != null)
			{
				this.VisitExpression(updateTree.Returning);
			}
			this.VisitExpressionBindingPost(updateTree.Target);
		}
	}
}
