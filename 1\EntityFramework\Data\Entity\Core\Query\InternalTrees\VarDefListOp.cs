﻿using System;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003F9 RID: 1017
	internal sealed class VarDefListOp : AncillaryOp
	{
		// Token: 0x06002F6D RID: 12141 RVA: 0x00094E0F File Offset: 0x0009300F
		private VarDefListOp()
			: base(OpType.VarDefList)
		{
		}

		// Token: 0x06002F6E RID: 12142 RVA: 0x00094E19 File Offset: 0x00093019
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002F6F RID: 12143 RVA: 0x00094E23 File Offset: 0x00093023
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04001003 RID: 4099
		internal static readonly VarDefListOp Instance = new VarDefListOp();

		// Token: 0x04001004 RID: 4100
		internal static readonly VarDefListOp Pattern = VarDefListOp.Instance;
	}
}
