﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.Utils;
using System.Globalization;
using System.Linq;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x0200053B RID: 1339
	internal sealed class FunctionImportNormalizedEntityTypeMapping
	{
		// Token: 0x060041FC RID: 16892 RVA: 0x000DE99C File Offset: 0x000DCB9C
		internal FunctionImportNormalizedEntityTypeMapping(FunctionImportStructuralTypeMappingKB parent, List<FunctionImportEntityTypeMappingCondition> columnConditions, BitArray impliedEntityTypes)
		{
			this.ColumnConditions = new ReadOnlyCollection<FunctionImportEntityTypeMappingCondition>(columnConditions.ToList<FunctionImportEntityTypeMappingCondition>());
			this.ImpliedEntityTypes = impliedEntityTypes;
			this.ComplementImpliedEntityTypes = new BitArray(this.ImpliedEntityTypes).Not();
		}

		// Token: 0x060041FD RID: 16893 RVA: 0x000DE9D2 File Offset: 0x000DCBD2
		public override string ToString()
		{
			return string.Format(CultureInfo.InvariantCulture, "Values={0}, Types={1}", new object[]
			{
				StringUtil.ToCommaSeparatedString(this.ColumnConditions),
				StringUtil.ToCommaSeparatedString(this.ImpliedEntityTypes)
			});
		}

		// Token: 0x040016DE RID: 5854
		internal readonly ReadOnlyCollection<FunctionImportEntityTypeMappingCondition> ColumnConditions;

		// Token: 0x040016DF RID: 5855
		internal readonly BitArray ImpliedEntityTypes;

		// Token: 0x040016E0 RID: 5856
		internal readonly BitArray ComplementImpliedEntityTypes;
	}
}
