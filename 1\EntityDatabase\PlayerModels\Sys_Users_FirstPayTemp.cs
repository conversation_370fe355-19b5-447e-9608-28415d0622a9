﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x0200002B RID: 43
	public class Sys_Users_FirstPayTemp
	{
		// Token: 0x1700014F RID: 335
		// (get) Token: 0x060002C8 RID: 712 RVA: 0x0000381C File Offset: 0x00001A1C
		// (set) Token: 0x060002C9 RID: 713 RVA: 0x00003824 File Offset: 0x00001A24
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000150 RID: 336
		// (get) Token: 0x060002CA RID: 714 RVA: 0x0000382D File Offset: 0x00001A2D
		// (set) Token: 0x060002CB RID: 715 RVA: 0x00003835 File Offset: 0x00001A35
		public int UserID { get; set; }

		// Token: 0x17000151 RID: 337
		// (get) Token: 0x060002CC RID: 716 RVA: 0x0000383E File Offset: 0x00001A3E
		// (set) Token: 0x060002CD RID: 717 RVA: 0x00003846 File Offset: 0x00001A46
		public int TemplateID { get; set; }

		// Token: 0x17000152 RID: 338
		// (get) Token: 0x060002CE RID: 718 RVA: 0x0000384F File Offset: 0x00001A4F
		// (set) Token: 0x060002CF RID: 719 RVA: 0x00003857 File Offset: 0x00001A57
		public int Count { get; set; }

		// Token: 0x17000153 RID: 339
		// (get) Token: 0x060002D0 RID: 720 RVA: 0x00003860 File Offset: 0x00001A60
		// (set) Token: 0x060002D1 RID: 721 RVA: 0x00003868 File Offset: 0x00001A68
		public int Type { get; set; }
	}
}
