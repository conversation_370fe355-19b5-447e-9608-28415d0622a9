﻿using System;
using System.Collections.Generic;
using System.Reflection;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x02000511 RID: 1297
	internal class MutableAssemblyCacheEntry : AssemblyCacheEntry
	{
		// Token: 0x17000C80 RID: 3200
		// (get) Token: 0x06003FF1 RID: 16369 RVA: 0x000D3C7B File Offset: 0x000D1E7B
		internal override IList<EdmType> TypesInAssembly
		{
			get
			{
				return this._typesInAssembly;
			}
		}

		// Token: 0x17000C81 RID: 3201
		// (get) Token: 0x06003FF2 RID: 16370 RVA: 0x000D3C83 File Offset: 0x000D1E83
		internal override IList<Assembly> ClosureAssemblies
		{
			get
			{
				return this._closureAssemblies;
			}
		}

		// Token: 0x04001652 RID: 5714
		private readonly List<EdmType> _typesInAssembly = new List<EdmType>();

		// Token: 0x04001653 RID: 5715
		private readonly List<Assembly> _closureAssemblies = new List<Assembly>();
	}
}
