﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.PlayerModels
{
	// Token: 0x02000024 RID: 36
	public class Sys_Users_CaveLoot_PlayerInfo
	{
		// Token: 0x17000118 RID: 280
		// (get) Token: 0x06000253 RID: 595 RVA: 0x00003436 File Offset: 0x00001636
		// (set) Token: 0x06000254 RID: 596 RVA: 0x0000343E File Offset: 0x0000163E
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		// Token: 0x17000119 RID: 281
		// (get) Token: 0x06000255 RID: 597 RVA: 0x00003447 File Offset: 0x00001647
		// (set) Token: 0x06000256 RID: 598 RVA: 0x0000344F File Offset: 0x0000164F
		public int UserID { get; set; }

		// Token: 0x1700011A RID: 282
		// (get) Token: 0x06000257 RID: 599 RVA: 0x00003458 File Offset: 0x00001658
		// (set) Token: 0x06000258 RID: 600 RVA: 0x00003460 File Offset: 0x00001660
		public int OpenCount { get; set; }

		// Token: 0x1700011B RID: 283
		// (get) Token: 0x06000259 RID: 601 RVA: 0x00003469 File Offset: 0x00001669
		// (set) Token: 0x0600025A RID: 602 RVA: 0x00003471 File Offset: 0x00001671
		public int TotalScore { get; set; }

		// Token: 0x1700011C RID: 284
		// (get) Token: 0x0600025B RID: 603 RVA: 0x0000347A File Offset: 0x0000167A
		// (set) Token: 0x0600025C RID: 604 RVA: 0x00003482 File Offset: 0x00001682
		public int DailyFreeMines { get; set; }

		// Token: 0x1700011D RID: 285
		// (get) Token: 0x0600025D RID: 605 RVA: 0x0000348B File Offset: 0x0000168B
		// (set) Token: 0x0600025E RID: 606 RVA: 0x00003493 File Offset: 0x00001693
		public int ShopCoin1 { get; set; }

		// Token: 0x1700011E RID: 286
		// (get) Token: 0x0600025F RID: 607 RVA: 0x0000349C File Offset: 0x0000169C
		// (set) Token: 0x06000260 RID: 608 RVA: 0x000034A4 File Offset: 0x000016A4
		public int ShopCoin2 { get; set; }

		// Token: 0x1700011F RID: 287
		// (get) Token: 0x06000261 RID: 609 RVA: 0x000034AD File Offset: 0x000016AD
		// (set) Token: 0x06000262 RID: 610 RVA: 0x000034B5 File Offset: 0x000016B5
		public string RewardList { get; set; }

		// Token: 0x17000120 RID: 288
		// (get) Token: 0x06000263 RID: 611 RVA: 0x000034BE File Offset: 0x000016BE
		// (set) Token: 0x06000264 RID: 612 RVA: 0x000034C6 File Offset: 0x000016C6
		public DateTime LastUpdate { get; set; }
	}
}
