﻿using System;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Mapping.Update.Internal
{
	// Token: 0x020005D0 RID: 1488
	internal class RecordConverter
	{
		// Token: 0x060047E7 RID: 18407 RVA: 0x000FDE4A File Offset: 0x000FC04A
		internal RecordConverter(UpdateTranslator updateTranslator)
		{
			this.m_updateTranslator = updateTranslator;
		}

		// Token: 0x060047E8 RID: 18408 RVA: 0x000FDE59 File Offset: 0x000FC059
		internal PropagatorResult ConvertOriginalValuesToPropagatorResult(IEntityStateEntry stateEntry, ModifiedPropertiesBehavior modifiedPropertiesBehavior)
		{
			return this.ConvertStateEntryToPropagatorResult(stateEntry, false, modifiedPropertiesBehavior);
		}

		// Token: 0x060047E9 RID: 18409 RVA: 0x000FDE64 File Offset: 0x000FC064
		internal PropagatorResult ConvertCurrentValuesToPropagatorResult(IEntityStateEntry stateEntry, ModifiedPropertiesBehavior modifiedPropertiesBehavior)
		{
			return this.ConvertStateEntryToPropagatorResult(stateEntry, true, modifiedPropertiesBehavior);
		}

		// Token: 0x060047EA RID: 18410 RVA: 0x000FDE70 File Offset: 0x000FC070
		private PropagatorResult ConvertStateEntryToPropagatorResult(IEntityStateEntry stateEntry, bool useCurrentValues, ModifiedPropertiesBehavior modifiedPropertiesBehavior)
		{
			PropagatorResult propagatorResult;
			try
			{
				IExtendedDataRecord extendedDataRecord;
				if (!useCurrentValues)
				{
					extendedDataRecord = (IExtendedDataRecord)stateEntry.OriginalValues;
				}
				else
				{
					IExtendedDataRecord currentValues = stateEntry.CurrentValues;
					extendedDataRecord = currentValues;
				}
				IExtendedDataRecord extendedDataRecord2 = extendedDataRecord;
				bool flag = false;
				propagatorResult = ExtractorMetadata.ExtractResultFromRecord(stateEntry, flag, extendedDataRecord2, useCurrentValues, this.m_updateTranslator, modifiedPropertiesBehavior);
			}
			catch (Exception ex)
			{
				if (ex.RequiresContext())
				{
					throw EntityUtil.Update(Strings.Update_ErrorLoadingRecord, ex, new IEntityStateEntry[] { stateEntry });
				}
				throw;
			}
			return propagatorResult;
		}

		// Token: 0x0400198F RID: 6543
		private readonly UpdateTranslator m_updateTranslator;
	}
}
