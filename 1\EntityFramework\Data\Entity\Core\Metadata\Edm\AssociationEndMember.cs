﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Objects.DataClasses;
using System.Data.Entity.Utilities;
using System.Threading;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x02000487 RID: 1159
	public sealed class AssociationEndMember : RelationshipEndMember
	{
		// Token: 0x060039A9 RID: 14761 RVA: 0x000BD03E File Offset: 0x000BB23E
		internal AssociationEndMember(string name, RefType endRefType, RelationshipMultiplicity multiplicity)
			: base(name, endRefType, multiplicity)
		{
		}

		// Token: 0x060039AA RID: 14762 RVA: 0x000BD049 File Offset: 0x000BB249
		internal AssociationEndMember(string name, EntityType entityType)
			: base(name, new RefType(entityType), RelationshipMultiplicity.ZeroOrOne)
		{
		}

		// Token: 0x17000AF5 RID: 2805
		// (get) Token: 0x060039AB RID: 14763 RVA: 0x000BD059 File Offset: 0x000BB259
		public override BuiltInTypeKind BuiltInTypeKind
		{
			get
			{
				return BuiltInTypeKind.AssociationEndMember;
			}
		}

		// Token: 0x17000AF6 RID: 2806
		// (get) Token: 0x060039AC RID: 14764 RVA: 0x000BD05C File Offset: 0x000BB25C
		// (set) Token: 0x060039AD RID: 14765 RVA: 0x000BD064 File Offset: 0x000BB264
		internal Func<RelationshipManager, RelatedEnd, RelatedEnd> GetRelatedEnd
		{
			get
			{
				return this._getRelatedEndMethod;
			}
			set
			{
				Interlocked.CompareExchange<Func<RelationshipManager, RelatedEnd, RelatedEnd>>(ref this._getRelatedEndMethod, value, null);
			}
		}

		// Token: 0x060039AE RID: 14766 RVA: 0x000BD074 File Offset: 0x000BB274
		public static AssociationEndMember Create(string name, RefType endRefType, RelationshipMultiplicity multiplicity, OperationAction deleteAction, IEnumerable<MetadataProperty> metadataProperties)
		{
			Check.NotEmpty(name, "name");
			Check.NotNull<RefType>(endRefType, "endRefType");
			AssociationEndMember associationEndMember = new AssociationEndMember(name, endRefType, multiplicity);
			associationEndMember.DeleteBehavior = deleteAction;
			if (metadataProperties != null)
			{
				associationEndMember.AddMetadataProperties(metadataProperties);
			}
			associationEndMember.SetReadOnly();
			return associationEndMember;
		}

		// Token: 0x04001319 RID: 4889
		private Func<RelationshipManager, RelatedEnd, RelatedEnd> _getRelatedEndMethod;
	}
}
