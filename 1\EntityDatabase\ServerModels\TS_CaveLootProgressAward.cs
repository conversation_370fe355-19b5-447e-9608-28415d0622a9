﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x0200000D RID: 13
	public class TS_CaveLootProgressAward
	{
		// Token: 0x17000042 RID: 66
		// (get) Token: 0x0600008F RID: 143 RVA: 0x00002528 File Offset: 0x00000728
		// (set) Token: 0x06000090 RID: 144 RVA: 0x00002530 File Offset: 0x00000730
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int ID { get; set; }

		// Token: 0x17000043 RID: 67
		// (get) Token: 0x06000091 RID: 145 RVA: 0x00002539 File Offset: 0x00000739
		// (set) Token: 0x06000092 RID: 146 RVA: 0x00002541 File Offset: 0x00000741
		public int Type { get; set; }

		// Token: 0x17000044 RID: 68
		// (get) Token: 0x06000093 RID: 147 RVA: 0x0000254A File Offset: 0x0000074A
		// (set) Token: 0x06000094 RID: 148 RVA: 0x00002552 File Offset: 0x00000752
		public int Number { get; set; }

		// Token: 0x17000045 RID: 69
		// (get) Token: 0x06000095 RID: 149 RVA: 0x0000255B File Offset: 0x0000075B
		// (set) Token: 0x06000096 RID: 150 RVA: 0x00002563 File Offset: 0x00000763
		public int Progress { get; set; }

		// Token: 0x17000046 RID: 70
		// (get) Token: 0x06000097 RID: 151 RVA: 0x0000256C File Offset: 0x0000076C
		// (set) Token: 0x06000098 RID: 152 RVA: 0x00002574 File Offset: 0x00000774
		public string Reward { get; set; }
	}
}
