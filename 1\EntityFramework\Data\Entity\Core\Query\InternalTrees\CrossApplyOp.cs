﻿using System;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x02000397 RID: 919
	internal sealed class CrossApplyOp : ApplyBaseOp
	{
		// Token: 0x06002CD9 RID: 11481 RVA: 0x0008F2EE File Offset: 0x0008D4EE
		private CrossApplyOp()
			: base(OpType.CrossApply)
		{
		}

		// Token: 0x06002CDA RID: 11482 RVA: 0x0008F2F8 File Offset: 0x0008D4F8
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002CDB RID: 11483 RVA: 0x0008F302 File Offset: 0x0008D502
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000F11 RID: 3857
		internal static readonly CrossApplyOp Instance = new CrossApplyOp();

		// Token: 0x04000F12 RID: 3858
		internal static readonly CrossApplyOp Pattern = CrossApplyOp.Instance;
	}
}
