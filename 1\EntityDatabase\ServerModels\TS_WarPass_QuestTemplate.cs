﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x0200001C RID: 28
	public class TS_WarPass_QuestTemplate
	{
		// Token: 0x170000C5 RID: 197
		// (get) Token: 0x060001A4 RID: 420 RVA: 0x00002E62 File Offset: 0x00001062
		// (set) Token: 0x060001A5 RID: 421 RVA: 0x00002E6A File Offset: 0x0000106A
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int QID { get; set; }

		// Token: 0x170000C6 RID: 198
		// (get) Token: 0x060001A6 RID: 422 RVA: 0x00002E73 File Offset: 0x00001073
		// (set) Token: 0x060001A7 RID: 423 RVA: 0x00002E7B File Offset: 0x0000107B
		public int SType { get; set; }

		// Token: 0x170000C7 RID: 199
		// (get) Token: 0x060001A8 RID: 424 RVA: 0x00002E84 File Offset: 0x00001084
		// (set) Token: 0x060001A9 RID: 425 RVA: 0x00002E8C File Offset: 0x0000108C
		public int QType { get; set; }

		// Token: 0x170000C8 RID: 200
		// (get) Token: 0x060001AA RID: 426 RVA: 0x00002E95 File Offset: 0x00001095
		// (set) Token: 0x060001AB RID: 427 RVA: 0x00002E9D File Offset: 0x0000109D
		public int Condition1 { get; set; }

		// Token: 0x170000C9 RID: 201
		// (get) Token: 0x060001AC RID: 428 RVA: 0x00002EA6 File Offset: 0x000010A6
		// (set) Token: 0x060001AD RID: 429 RVA: 0x00002EAE File Offset: 0x000010AE
		public int Condition2 { get; set; }

		// Token: 0x170000CA RID: 202
		// (get) Token: 0x060001AE RID: 430 RVA: 0x00002EB7 File Offset: 0x000010B7
		// (set) Token: 0x060001AF RID: 431 RVA: 0x00002EBF File Offset: 0x000010BF
		[Required]
		[StringLength(500)]
		public string Desc { get; set; }

		// Token: 0x170000CB RID: 203
		// (get) Token: 0x060001B0 RID: 432 RVA: 0x00002EC8 File Offset: 0x000010C8
		// (set) Token: 0x060001B1 RID: 433 RVA: 0x00002ED0 File Offset: 0x000010D0
		public int AddGp { get; set; }

		// Token: 0x170000CC RID: 204
		// (get) Token: 0x060001B2 RID: 434 RVA: 0x00002ED9 File Offset: 0x000010D9
		// (set) Token: 0x060001B3 RID: 435 RVA: 0x00002EE1 File Offset: 0x000010E1
		public int FinishPrice { get; set; }

		// Token: 0x170000CD RID: 205
		// (get) Token: 0x060001B4 RID: 436 RVA: 0x00002EEA File Offset: 0x000010EA
		// (set) Token: 0x060001B5 RID: 437 RVA: 0x00002EF2 File Offset: 0x000010F2
		public int HardLevel { get; set; }
	}
}
