﻿using System;
using System.Data.Common;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Infrastructure.DependencyResolution;
using System.Data.Entity.Infrastructure.Interception;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.EntityClient.Internal
{
	// Token: 0x020005E7 RID: 1511
	internal sealed class EntityProviderServices : DbProviderServices
	{
		// Token: 0x060049F1 RID: 18929 RVA: 0x00105918 File Offset: 0x00103B18
		protected override DbCommandDefinition CreateDbCommandDefinition(DbProviderManifest providerManifest, DbCommandTree commandTree)
		{
			Check.NotNull<DbProviderManifest>(providerManifest, "providerManifest");
			Check.NotNull<DbCommandTree>(commandTree, "commandTree");
			return this.CreateDbCommandDefinition(providerManifest, commandTree, new DbInterceptionContext());
		}

		// Token: 0x060049F2 RID: 18930 RVA: 0x0010593F File Offset: 0x00103B3F
		internal static EntityCommandDefinition CreateCommandDefinition(DbProviderFactory storeProviderFactory, DbCommandTree commandTree, DbInterceptionContext interceptionContext, IDbDependencyResolver resolver = null)
		{
			return new EntityCommandDefinition(storeProviderFactory, commandTree, interceptionContext, resolver, null, null);
		}

		// Token: 0x060049F3 RID: 18931 RVA: 0x0010594C File Offset: 0x00103B4C
		internal override DbCommandDefinition CreateDbCommandDefinition(DbProviderManifest providerManifest, DbCommandTree commandTree, DbInterceptionContext interceptionContext)
		{
			return EntityProviderServices.CreateCommandDefinition(((StoreItemCollection)commandTree.MetadataWorkspace.GetItemCollection(DataSpace.SSpace)).ProviderFactory, commandTree, interceptionContext, null);
		}

		// Token: 0x060049F4 RID: 18932 RVA: 0x0010596C File Offset: 0x00103B6C
		internal override void ValidateDataSpace(DbCommandTree commandTree)
		{
			if (commandTree.DataSpace != DataSpace.CSpace)
			{
				throw new ProviderIncompatibleException(Strings.EntityClient_RequiresNonStoreCommandTree);
			}
		}

		// Token: 0x060049F5 RID: 18933 RVA: 0x00105982 File Offset: 0x00103B82
		public override DbCommandDefinition CreateCommandDefinition(DbCommand prototype)
		{
			Check.NotNull<DbCommand>(prototype, "prototype");
			return ((EntityCommand)prototype).GetCommandDefinition();
		}

		// Token: 0x060049F6 RID: 18934 RVA: 0x0010599C File Offset: 0x00103B9C
		protected override string GetDbProviderManifestToken(DbConnection connection)
		{
			Check.NotNull<DbConnection>(connection, "connection");
			if (connection.GetType() != typeof(EntityConnection))
			{
				throw new ArgumentException(Strings.Mapping_Provider_WrongConnectionType(typeof(EntityConnection)));
			}
			return MetadataItem.EdmProviderManifest.Token;
		}

		// Token: 0x060049F7 RID: 18935 RVA: 0x001059EB File Offset: 0x00103BEB
		protected override DbProviderManifest GetDbProviderManifest(string manifestToken)
		{
			Check.NotNull<string>(manifestToken, "manifestToken");
			return MetadataItem.EdmProviderManifest;
		}

		// Token: 0x04001A16 RID: 6678
		internal static readonly EntityProviderServices Instance = new EntityProviderServices();
	}
}
