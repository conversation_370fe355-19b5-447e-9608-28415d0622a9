﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x0200000E RID: 14
	public class TS_FirstCopy
	{
		// Token: 0x17000047 RID: 71
		// (get) Token: 0x0600009A RID: 154 RVA: 0x00002586 File Offset: 0x00000786
		// (set) Token: 0x0600009B RID: 155 RVA: 0x0000258E File Offset: 0x0000078E
		public string CopyName { get; set; }

		// Token: 0x17000048 RID: 72
		// (get) Token: 0x0600009C RID: 156 RVA: 0x00002597 File Offset: 0x00000797
		// (set) Token: 0x0600009D RID: 157 RVA: 0x0000259F File Offset: 0x0000079F
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int MissionID { get; set; }

		// Token: 0x17000049 RID: 73
		// (get) Token: 0x0600009E RID: 158 RVA: 0x000025A8 File Offset: 0x000007A8
		// (set) Token: 0x0600009F RID: 159 RVA: 0x000025B0 File Offset: 0x000007B0
		public int BossID { get; set; }

		// Token: 0x1700004A RID: 74
		// (get) Token: 0x060000A0 RID: 160 RVA: 0x000025B9 File Offset: 0x000007B9
		// (set) Token: 0x060000A1 RID: 161 RVA: 0x000025C1 File Offset: 0x000007C1
		public int TemplateIDA { get; set; }

		// Token: 0x1700004B RID: 75
		// (get) Token: 0x060000A2 RID: 162 RVA: 0x000025CA File Offset: 0x000007CA
		// (set) Token: 0x060000A3 RID: 163 RVA: 0x000025D2 File Offset: 0x000007D2
		public int ValidDateA { get; set; }

		// Token: 0x1700004C RID: 76
		// (get) Token: 0x060000A4 RID: 164 RVA: 0x000025DB File Offset: 0x000007DB
		// (set) Token: 0x060000A5 RID: 165 RVA: 0x000025E3 File Offset: 0x000007E3
		public int CountA { get; set; }

		// Token: 0x1700004D RID: 77
		// (get) Token: 0x060000A6 RID: 166 RVA: 0x000025EC File Offset: 0x000007EC
		// (set) Token: 0x060000A7 RID: 167 RVA: 0x000025F4 File Offset: 0x000007F4
		public int TemplateIDB { get; set; }

		// Token: 0x1700004E RID: 78
		// (get) Token: 0x060000A8 RID: 168 RVA: 0x000025FD File Offset: 0x000007FD
		// (set) Token: 0x060000A9 RID: 169 RVA: 0x00002605 File Offset: 0x00000805
		public int ValidDateB { get; set; }

		// Token: 0x1700004F RID: 79
		// (get) Token: 0x060000AA RID: 170 RVA: 0x0000260E File Offset: 0x0000080E
		// (set) Token: 0x060000AB RID: 171 RVA: 0x00002616 File Offset: 0x00000816
		public int CountB { get; set; }

		// Token: 0x17000050 RID: 80
		// (get) Token: 0x060000AC RID: 172 RVA: 0x0000261F File Offset: 0x0000081F
		// (set) Token: 0x060000AD RID: 173 RVA: 0x00002627 File Offset: 0x00000827
		public int TemplateIDC { get; set; }

		// Token: 0x17000051 RID: 81
		// (get) Token: 0x060000AE RID: 174 RVA: 0x00002630 File Offset: 0x00000830
		// (set) Token: 0x060000AF RID: 175 RVA: 0x00002638 File Offset: 0x00000838
		public int ValidDateC { get; set; }

		// Token: 0x17000052 RID: 82
		// (get) Token: 0x060000B0 RID: 176 RVA: 0x00002641 File Offset: 0x00000841
		// (set) Token: 0x060000B1 RID: 177 RVA: 0x00002649 File Offset: 0x00000849
		public int CountC { get; set; }
	}
}
