﻿using System;

namespace System.Data.Entity.Core.Mapping
{
	// Token: 0x02000556 RID: 1366
	internal static class ObjectMslConstructs
	{
		// Token: 0x040017E5 RID: 6117
		internal const string MappingElement = "Mapping";

		// Token: 0x040017E6 RID: 6118
		internal const string AliasElement = "Alias";

		// Token: 0x040017E7 RID: 6119
		internal const string AliasKeyAttribute = "Key";

		// Token: 0x040017E8 RID: 6120
		internal const string AliasValueAttribute = "Value";

		// Token: 0x040017E9 RID: 6121
		internal const char IdentitySeperator = ':';
	}
}
