﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000004 RID: 4
	public class DevilTreasItemList
	{
		// Token: 0x17000001 RID: 1
		// (get) Token: 0x06000003 RID: 3 RVA: 0x00002069 File Offset: 0x00000269
		// (set) Token: 0x06000004 RID: 4 RVA: 0x00002071 File Offset: 0x00000271
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int ID { get; set; }

		// Token: 0x17000002 RID: 2
		// (get) Token: 0x06000005 RID: 5 RVA: 0x0000207A File Offset: 0x0000027A
		// (set) Token: 0x06000006 RID: 6 RVA: 0x00002082 File Offset: 0x00000282
		public int Type { get; set; }

		// Token: 0x17000003 RID: 3
		// (get) Token: 0x06000007 RID: 7 RVA: 0x0000208B File Offset: 0x0000028B
		// (set) Token: 0x06000008 RID: 8 RVA: 0x00002093 File Offset: 0x00000293
		public int TemplateID { get; set; }

		// Token: 0x17000004 RID: 4
		// (get) Token: 0x06000009 RID: 9 RVA: 0x0000209C File Offset: 0x0000029C
		// (set) Token: 0x0600000A RID: 10 RVA: 0x000020A4 File Offset: 0x000002A4
		public int Value { get; set; }

		// Token: 0x17000005 RID: 5
		// (get) Token: 0x0600000B RID: 11 RVA: 0x000020AD File Offset: 0x000002AD
		// (set) Token: 0x0600000C RID: 12 RVA: 0x000020B5 File Offset: 0x000002B5
		public int Random { get; set; }
	}
}
