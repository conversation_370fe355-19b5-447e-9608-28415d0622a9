﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Linq;

namespace System.Data.Entity.Core.Common.EntitySql
{
	// Token: 0x02000670 RID: 1648
	internal sealed class TypeResolver
	{
		// Token: 0x06004F00 RID: 20224 RVA: 0x0011E918 File Offset: 0x0011CB18
		internal TypeResolver(Perspective perspective, ParserOptions parserOptions)
		{
			this._perspective = perspective;
			this._parserOptions = parserOptions;
			this._aliasedNamespaces = new Dictionary<string, MetadataNamespace>(parserOptions.NameComparer);
			this._namespaces = new HashSet<MetadataNamespace>(MetadataMember.CreateMetadataMemberNameEqualityComparer(parserOptions.NameComparer));
			this._functionDefinitions = new Dictionary<string, List<InlineFunctionInfo>>(parserOptions.NameComparer);
			this._includeInlineFunctions = true;
			this._resolveLeftMostUnqualifiedNameAsNamespaceOnly = false;
		}

		// Token: 0x17000F35 RID: 3893
		// (get) Token: 0x06004F01 RID: 20225 RVA: 0x0011E97F File Offset: 0x0011CB7F
		internal Perspective Perspective
		{
			get
			{
				return this._perspective;
			}
		}

		// Token: 0x17000F36 RID: 3894
		// (get) Token: 0x06004F02 RID: 20226 RVA: 0x0011E987 File Offset: 0x0011CB87
		internal ICollection<MetadataNamespace> NamespaceImports
		{
			get
			{
				return this._namespaces;
			}
		}

		// Token: 0x17000F37 RID: 3895
		// (get) Token: 0x06004F03 RID: 20227 RVA: 0x0011E98F File Offset: 0x0011CB8F
		internal static TypeUsage StringType
		{
			get
			{
				return MetadataWorkspace.GetCanonicalModelTypeUsage(PrimitiveTypeKind.String);
			}
		}

		// Token: 0x17000F38 RID: 3896
		// (get) Token: 0x06004F04 RID: 20228 RVA: 0x0011E998 File Offset: 0x0011CB98
		internal static TypeUsage BooleanType
		{
			get
			{
				return MetadataWorkspace.GetCanonicalModelTypeUsage(PrimitiveTypeKind.Boolean);
			}
		}

		// Token: 0x17000F39 RID: 3897
		// (get) Token: 0x06004F05 RID: 20229 RVA: 0x0011E9A0 File Offset: 0x0011CBA0
		internal static TypeUsage Int64Type
		{
			get
			{
				return MetadataWorkspace.GetCanonicalModelTypeUsage(PrimitiveTypeKind.Int64);
			}
		}

		// Token: 0x06004F06 RID: 20230 RVA: 0x0011E9AC File Offset: 0x0011CBAC
		internal void AddAliasedNamespaceImport(string alias, MetadataNamespace @namespace, ErrorContext errCtx)
		{
			if (this._aliasedNamespaces.ContainsKey(alias))
			{
				string text = Strings.NamespaceAliasAlreadyUsed(alias);
				throw EntitySqlException.Create(errCtx, text, null);
			}
			this._aliasedNamespaces.Add(alias, @namespace);
		}

		// Token: 0x06004F07 RID: 20231 RVA: 0x0011E9E4 File Offset: 0x0011CBE4
		internal void AddNamespaceImport(MetadataNamespace @namespace, ErrorContext errCtx)
		{
			if (this._namespaces.Contains(@namespace))
			{
				string text = Strings.NamespaceAlreadyImported(@namespace.Name);
				throw EntitySqlException.Create(errCtx, text, null);
			}
			this._namespaces.Add(@namespace);
		}

		// Token: 0x06004F08 RID: 20232 RVA: 0x0011EA24 File Offset: 0x0011CC24
		internal void DeclareInlineFunction(string name, InlineFunctionInfo functionInfo)
		{
			List<InlineFunctionInfo> list;
			if (!this._functionDefinitions.TryGetValue(name, out list))
			{
				list = new List<InlineFunctionInfo>();
				this._functionDefinitions.Add(name, list);
			}
			if (list.Exists((InlineFunctionInfo overload) => overload.Parameters.Select((DbVariableReferenceExpression p) => p.ResultType).SequenceEqual(functionInfo.Parameters.Select((DbVariableReferenceExpression p) => p.ResultType), TypeResolver.TypeUsageStructuralComparer.Instance)))
			{
				ErrorContext errCtx = functionInfo.FunctionDefAst.ErrCtx;
				string text = Strings.DuplicatedInlineFunctionOverload(name);
				throw EntitySqlException.Create(errCtx, text, null);
			}
			list.Add(functionInfo);
		}

		// Token: 0x06004F09 RID: 20233 RVA: 0x0011EAA0 File Offset: 0x0011CCA0
		internal IDisposable EnterFunctionNameResolution(bool includeInlineFunctions)
		{
			bool savedIncludeInlineFunctions = this._includeInlineFunctions;
			this._includeInlineFunctions = includeInlineFunctions;
			return new Disposer(delegate
			{
				this._includeInlineFunctions = savedIncludeInlineFunctions;
			});
		}

		// Token: 0x06004F0A RID: 20234 RVA: 0x0011EAD1 File Offset: 0x0011CCD1
		internal IDisposable EnterBackwardCompatibilityResolution()
		{
			this._resolveLeftMostUnqualifiedNameAsNamespaceOnly = true;
			return new Disposer(delegate
			{
				this._resolveLeftMostUnqualifiedNameAsNamespaceOnly = false;
			});
		}

		// Token: 0x06004F0B RID: 20235 RVA: 0x0011EAEC File Offset: 0x0011CCEC
		internal MetadataMember ResolveMetadataMemberName(string[] name, ErrorContext errCtx)
		{
			MetadataMember metadataMember;
			if (name.Length == 1)
			{
				metadataMember = this.ResolveUnqualifiedName(name[0], false, errCtx);
			}
			else
			{
				metadataMember = this.ResolveFullyQualifiedName(name, name.Length, errCtx);
			}
			return metadataMember;
		}

		// Token: 0x06004F0C RID: 20236 RVA: 0x0011EB1C File Offset: 0x0011CD1C
		internal MetadataMember ResolveMetadataMemberAccess(MetadataMember qualifier, string name, ErrorContext errCtx)
		{
			string fullName = TypeResolver.GetFullName(new string[] { qualifier.Name, name });
			if (qualifier.MetadataMemberClass != MetadataMemberClass.Namespace)
			{
				if (qualifier.MetadataMemberClass == MetadataMemberClass.Type)
				{
					MetadataType metadataType = (MetadataType)qualifier;
					if (TypeSemantics.IsEnumerationType(metadataType.TypeUsage))
					{
						EnumMember enumMember;
						if (this._perspective.TryGetEnumMember((EnumType)metadataType.TypeUsage.EdmType, name, this._parserOptions.NameComparisonCaseInsensitive, out enumMember))
						{
							return new MetadataEnumMember(fullName, metadataType.TypeUsage, enumMember);
						}
						string text = Strings.NotAMemberOfType(name, qualifier.Name);
						throw EntitySqlException.Create(errCtx, text, null);
					}
				}
				string text2 = Strings.InvalidMetadataMemberClassResolution(qualifier.Name, qualifier.MetadataMemberClassName, MetadataNamespace.NamespaceClassName);
				throw EntitySqlException.Create(errCtx, text2, null);
			}
			MetadataType metadataType2;
			if (this.TryGetTypeFromMetadata(fullName, out metadataType2))
			{
				return metadataType2;
			}
			MetadataFunctionGroup metadataFunctionGroup;
			if (this.TryGetFunctionFromMetadata(qualifier.Name, name, out metadataFunctionGroup))
			{
				return metadataFunctionGroup;
			}
			return new MetadataNamespace(fullName);
		}

		// Token: 0x06004F0D RID: 20237 RVA: 0x0011EC04 File Offset: 0x0011CE04
		internal MetadataMember ResolveUnqualifiedName(string name, bool partOfQualifiedName, ErrorContext errCtx)
		{
			bool flag = partOfQualifiedName && this._resolveLeftMostUnqualifiedNameAsNamespaceOnly;
			bool flag2 = !partOfQualifiedName;
			InlineFunctionGroup inlineFunctionGroup;
			if (!flag && flag2 && this.TryGetInlineFunction(name, out inlineFunctionGroup))
			{
				return inlineFunctionGroup;
			}
			MetadataNamespace metadataNamespace;
			if (this._aliasedNamespaces.TryGetValue(name, out metadataNamespace))
			{
				return metadataNamespace;
			}
			if (!flag)
			{
				MetadataType metadataType = null;
				MetadataFunctionGroup metadataFunctionGroup = null;
				if (!this.TryGetTypeFromMetadata(name, out metadataType) && flag2)
				{
					string[] array = name.Split(new char[] { '.' });
					if (array.Length > 1)
					{
						if (array.All((string p) => p.Length > 0))
						{
							string text = array[array.Length - 1];
							string text2 = name.Substring(0, name.Length - text.Length - 1);
							this.TryGetFunctionFromMetadata(text2, text, out metadataFunctionGroup);
						}
					}
				}
				MetadataNamespace metadataNamespace2 = null;
				foreach (MetadataNamespace metadataNamespace3 in this._namespaces)
				{
					string fullName = TypeResolver.GetFullName(new string[] { metadataNamespace3.Name, name });
					MetadataType metadataType2;
					if (this.TryGetTypeFromMetadata(fullName, out metadataType2))
					{
						if (metadataType != null || metadataFunctionGroup != null)
						{
							throw TypeResolver.AmbiguousMetadataMemberName(errCtx, name, metadataNamespace3, metadataNamespace2);
						}
						metadataType = metadataType2;
						metadataNamespace2 = metadataNamespace3;
					}
					MetadataFunctionGroup metadataFunctionGroup2;
					if (flag2 && this.TryGetFunctionFromMetadata(metadataNamespace3.Name, name, out metadataFunctionGroup2))
					{
						if (metadataType != null || metadataFunctionGroup != null)
						{
							throw TypeResolver.AmbiguousMetadataMemberName(errCtx, name, metadataNamespace3, metadataNamespace2);
						}
						metadataFunctionGroup = metadataFunctionGroup2;
						metadataNamespace2 = metadataNamespace3;
					}
				}
				if (metadataType != null)
				{
					return metadataType;
				}
				if (metadataFunctionGroup != null)
				{
					return metadataFunctionGroup;
				}
			}
			return new MetadataNamespace(name);
		}

		// Token: 0x06004F0E RID: 20238 RVA: 0x0011EDB0 File Offset: 0x0011CFB0
		private MetadataMember ResolveFullyQualifiedName(string[] name, int length, ErrorContext errCtx)
		{
			MetadataMember metadataMember;
			if (length == 2)
			{
				metadataMember = this.ResolveUnqualifiedName(name[0], true, errCtx);
			}
			else
			{
				metadataMember = this.ResolveFullyQualifiedName(name, length - 1, errCtx);
			}
			string text = name[length - 1];
			return this.ResolveMetadataMemberAccess(metadataMember, text, errCtx);
		}

		// Token: 0x06004F0F RID: 20239 RVA: 0x0011EDEC File Offset: 0x0011CFEC
		private static Exception AmbiguousMetadataMemberName(ErrorContext errCtx, string name, MetadataNamespace ns1, MetadataNamespace ns2)
		{
			string text = Strings.AmbiguousMetadataMemberName(name, ns1.Name, (ns2 != null) ? ns2.Name : null);
			throw EntitySqlException.Create(errCtx, text, null);
		}

		// Token: 0x06004F10 RID: 20240 RVA: 0x0011EE1C File Offset: 0x0011D01C
		private bool TryGetTypeFromMetadata(string typeFullName, out MetadataType type)
		{
			TypeUsage typeUsage;
			if (this._perspective.TryGetTypeByName(typeFullName, this._parserOptions.NameComparisonCaseInsensitive, out typeUsage))
			{
				type = new MetadataType(typeFullName, typeUsage);
				return true;
			}
			type = null;
			return false;
		}

		// Token: 0x06004F11 RID: 20241 RVA: 0x0011EE54 File Offset: 0x0011D054
		internal bool TryGetFunctionFromMetadata(string namespaceName, string functionName, out MetadataFunctionGroup functionGroup)
		{
			IList<EdmFunction> list;
			if (this._perspective.TryGetFunctionByName(namespaceName, functionName, this._parserOptions.NameComparisonCaseInsensitive, out list))
			{
				functionGroup = new MetadataFunctionGroup(TypeResolver.GetFullName(new string[] { namespaceName, functionName }), list);
				return true;
			}
			functionGroup = null;
			return false;
		}

		// Token: 0x06004F12 RID: 20242 RVA: 0x0011EEA0 File Offset: 0x0011D0A0
		private bool TryGetInlineFunction(string functionName, out InlineFunctionGroup inlineFunctionGroup)
		{
			List<InlineFunctionInfo> list;
			if (this._includeInlineFunctions && this._functionDefinitions.TryGetValue(functionName, out list))
			{
				inlineFunctionGroup = new InlineFunctionGroup(functionName, list);
				return true;
			}
			inlineFunctionGroup = null;
			return false;
		}

		// Token: 0x06004F13 RID: 20243 RVA: 0x0011EED4 File Offset: 0x0011D0D4
		internal static string GetFullName(params string[] names)
		{
			return string.Join(".", names);
		}

		// Token: 0x04001C8A RID: 7306
		private readonly Perspective _perspective;

		// Token: 0x04001C8B RID: 7307
		private readonly ParserOptions _parserOptions;

		// Token: 0x04001C8C RID: 7308
		private readonly Dictionary<string, MetadataNamespace> _aliasedNamespaces;

		// Token: 0x04001C8D RID: 7309
		private readonly HashSet<MetadataNamespace> _namespaces;

		// Token: 0x04001C8E RID: 7310
		private readonly Dictionary<string, List<InlineFunctionInfo>> _functionDefinitions;

		// Token: 0x04001C8F RID: 7311
		private bool _includeInlineFunctions;

		// Token: 0x04001C90 RID: 7312
		private bool _resolveLeftMostUnqualifiedNameAsNamespaceOnly;

		// Token: 0x02000C8D RID: 3213
		private sealed class TypeUsageStructuralComparer : IEqualityComparer<TypeUsage>
		{
			// Token: 0x06006BED RID: 27629 RVA: 0x0016FD3F File Offset: 0x0016DF3F
			private TypeUsageStructuralComparer()
			{
			}

			// Token: 0x17001198 RID: 4504
			// (get) Token: 0x06006BEE RID: 27630 RVA: 0x0016FD47 File Offset: 0x0016DF47
			public static TypeResolver.TypeUsageStructuralComparer Instance
			{
				get
				{
					return TypeResolver.TypeUsageStructuralComparer._instance;
				}
			}

			// Token: 0x06006BEF RID: 27631 RVA: 0x0016FD4E File Offset: 0x0016DF4E
			public bool Equals(TypeUsage x, TypeUsage y)
			{
				return TypeSemantics.IsStructurallyEqual(x, y);
			}

			// Token: 0x06006BF0 RID: 27632 RVA: 0x0016FD57 File Offset: 0x0016DF57
			public int GetHashCode(TypeUsage obj)
			{
				return 0;
			}

			// Token: 0x040031A4 RID: 12708
			private static readonly TypeResolver.TypeUsageStructuralComparer _instance = new TypeResolver.TypeUsageStructuralComparer();
		}
	}
}
