﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000016 RID: 22
	public class TS_Relic_UpgradeTemplate
	{
		// Token: 0x17000092 RID: 146
		// (get) Token: 0x06000138 RID: 312 RVA: 0x00002AC9 File Offset: 0x00000CC9
		// (set) Token: 0x06000139 RID: 313 RVA: 0x00002AD1 File Offset: 0x00000CD1
		[Key]
		public int ID { get; set; }

		// Token: 0x17000093 RID: 147
		// (get) Token: 0x0600013A RID: 314 RVA: 0x00002ADA File Offset: 0x00000CDA
		// (set) Token: 0x0600013B RID: 315 RVA: 0x00002AE2 File Offset: 0x00000CE2
		public int RelicID { get; set; }

		// Token: 0x17000094 RID: 148
		// (get) Token: 0x0600013C RID: 316 RVA: 0x00002AEB File Offset: 0x00000CEB
		// (set) Token: 0x0600013D RID: 317 RVA: 0x00002AF3 File Offset: 0x00000CF3
		public int Level { get; set; }

		// Token: 0x17000095 RID: 149
		// (get) Token: 0x0600013E RID: 318 RVA: 0x00002AFC File Offset: 0x00000CFC
		// (set) Token: 0x0600013F RID: 319 RVA: 0x00002B04 File Offset: 0x00000D04
		public int Type { get; set; }

		// Token: 0x17000096 RID: 150
		// (get) Token: 0x06000140 RID: 320 RVA: 0x00002B0D File Offset: 0x00000D0D
		// (set) Token: 0x06000141 RID: 321 RVA: 0x00002B15 File Offset: 0x00000D15
		public int Data { get; set; }

		// Token: 0x17000097 RID: 151
		// (get) Token: 0x06000142 RID: 322 RVA: 0x00002B1E File Offset: 0x00000D1E
		// (set) Token: 0x06000143 RID: 323 RVA: 0x00002B26 File Offset: 0x00000D26
		public int NeedExp { get; set; }
	}
}
