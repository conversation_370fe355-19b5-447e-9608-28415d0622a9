﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006E5 RID: 1765
	public sealed class DbUnionAllExpression : DbBinaryExpression
	{
		// Token: 0x060051BB RID: 20923 RVA: 0x001235B8 File Offset: 0x001217B8
		internal DbUnionAllExpression(TypeUsage resultType, DbExpression left, DbExpression right)
			: base(DbExpressionKind.UnionAll, resultType, left, right)
		{
		}

		// Token: 0x060051BC RID: 20924 RVA: 0x001235C5 File Offset: 0x001217C5
		public override void Accept(DbExpressionVisitor visitor)
		{
			Check.NotNull<DbExpressionVisitor>(visitor, "visitor");
			visitor.Visit(this);
		}

		// Token: 0x060051BD RID: 20925 RVA: 0x001235DA File Offset: 0x001217DA
		public override TResultType Accept<TResultType>(DbExpressionVisitor<TResultType> visitor)
		{
			Check.NotNull<DbExpressionVisitor<TResultType>>(visitor, "visitor");
			return visitor.Visit(this);
		}
	}
}
