﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005B7 RID: 1463
	internal class ExtentKey : InternalBase
	{
		// Token: 0x06004729 RID: 18217 RVA: 0x000FA851 File Offset: 0x000F8A51
		internal ExtentKey(IEnumerable<MemberPath> keyFields)
		{
			this.m_keyFields = new List<MemberPath>(keyFields);
		}

		// Token: 0x17000E10 RID: 3600
		// (get) Token: 0x0600472A RID: 18218 RVA: 0x000FA865 File Offset: 0x000F8A65
		internal IEnumerable<MemberPath> KeyFields
		{
			get
			{
				return this.m_keyFields;
			}
		}

		// Token: 0x0600472B RID: 18219 RVA: 0x000FA870 File Offset: 0x000F8A70
		internal static List<ExtentKey> GetKeysForEntityType(MemberPath prefix, EntityType entityType)
		{
			ExtentKey primaryKeyForEntityType = ExtentKey.GetPrimaryKeyForEntityType(prefix, entityType);
			return new List<ExtentKey> { primaryKeyForEntityType };
		}

		// Token: 0x0600472C RID: 18220 RVA: 0x000FA894 File Offset: 0x000F8A94
		internal static ExtentKey GetPrimaryKeyForEntityType(MemberPath prefix, EntityType entityType)
		{
			List<MemberPath> list = new List<MemberPath>();
			foreach (EdmMember edmMember in entityType.KeyMembers)
			{
				list.Add(new MemberPath(prefix, edmMember));
			}
			return new ExtentKey(list);
		}

		// Token: 0x0600472D RID: 18221 RVA: 0x000FA8FC File Offset: 0x000F8AFC
		internal static ExtentKey GetKeyForRelationType(MemberPath prefix, AssociationType relationType)
		{
			List<MemberPath> list = new List<MemberPath>();
			foreach (AssociationEndMember associationEndMember in relationType.AssociationEndMembers)
			{
				MemberPath memberPath = new MemberPath(prefix, associationEndMember);
				EntityType entityTypeForEnd = MetadataHelper.GetEntityTypeForEnd(associationEndMember);
				ExtentKey primaryKeyForEntityType = ExtentKey.GetPrimaryKeyForEntityType(memberPath, entityTypeForEnd);
				list.AddRange(primaryKeyForEntityType.KeyFields);
			}
			return new ExtentKey(list);
		}

		// Token: 0x0600472E RID: 18222 RVA: 0x000FA978 File Offset: 0x000F8B78
		internal string ToUserString()
		{
			return StringUtil.ToCommaSeparatedStringSorted(this.m_keyFields);
		}

		// Token: 0x0600472F RID: 18223 RVA: 0x000FA985 File Offset: 0x000F8B85
		internal override void ToCompactString(StringBuilder builder)
		{
			StringUtil.ToCommaSeparatedStringSorted(builder, this.m_keyFields);
		}

		// Token: 0x0400193F RID: 6463
		private readonly List<MemberPath> m_keyFields;
	}
}
