﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x02000516 RID: 1302
	internal class ObjectItemLoadingSessionData
	{
		// Token: 0x17000C88 RID: 3208
		// (get) Token: 0x0600402B RID: 16427 RVA: 0x000D5319 File Offset: 0x000D3519
		internal virtual Dictionary<string, EdmType> TypesInLoading
		{
			get
			{
				return this._typesInLoading;
			}
		}

		// Token: 0x17000C89 RID: 3209
		// (get) Token: 0x0600402C RID: 16428 RVA: 0x000D5321 File Offset: 0x000D3521
		internal Dictionary<Assembly, MutableAssemblyCacheEntry> AssembliesLoaded
		{
			get
			{
				return this._listOfAssembliesLoaded;
			}
		}

		// Token: 0x17000C8A RID: 3210
		// (get) Token: 0x0600402D RID: 16429 RVA: 0x000D5329 File Offset: 0x000D3529
		internal virtual List<EdmItemError> EdmItemErrors
		{
			get
			{
				return this._errors;
			}
		}

		// Token: 0x17000C8B RID: 3211
		// (get) Token: 0x0600402E RID: 16430 RVA: 0x000D5331 File Offset: 0x000D3531
		internal KnownAssembliesSet KnownAssemblies
		{
			get
			{
				return this._knownAssemblies;
			}
		}

		// Token: 0x17000C8C RID: 3212
		// (get) Token: 0x0600402F RID: 16431 RVA: 0x000D5339 File Offset: 0x000D3539
		internal LockedAssemblyCache LockedAssemblyCache
		{
			get
			{
				return this._lockedAssemblyCache;
			}
		}

		// Token: 0x17000C8D RID: 3213
		// (get) Token: 0x06004030 RID: 16432 RVA: 0x000D5341 File Offset: 0x000D3541
		internal EdmItemCollection EdmItemCollection
		{
			get
			{
				return this._edmItemCollection;
			}
		}

		// Token: 0x17000C8E RID: 3214
		// (get) Token: 0x06004031 RID: 16433 RVA: 0x000D5349 File Offset: 0x000D3549
		internal virtual Dictionary<EdmType, EdmType> CspaceToOspace
		{
			get
			{
				return this._cspaceToOspace;
			}
		}

		// Token: 0x17000C8F RID: 3215
		// (get) Token: 0x06004032 RID: 16434 RVA: 0x000D5351 File Offset: 0x000D3551
		// (set) Token: 0x06004033 RID: 16435 RVA: 0x000D5359 File Offset: 0x000D3559
		internal bool ConventionBasedRelationshipsAreLoaded { get; set; }

		// Token: 0x17000C90 RID: 3216
		// (get) Token: 0x06004034 RID: 16436 RVA: 0x000D5362 File Offset: 0x000D3562
		internal virtual LoadMessageLogger LoadMessageLogger
		{
			get
			{
				return this._loadMessageLogger;
			}
		}

		// Token: 0x17000C91 RID: 3217
		// (get) Token: 0x06004035 RID: 16437 RVA: 0x000D536C File Offset: 0x000D356C
		internal Dictionary<string, KeyValuePair<EdmType, int>> ConventionCSpaceTypeNames
		{
			get
			{
				if (this._edmItemCollection != null && this._conventionCSpaceTypeNames == null)
				{
					this._conventionCSpaceTypeNames = new Dictionary<string, KeyValuePair<EdmType, int>>();
					foreach (EdmType edmType in this._edmItemCollection.GetItems<EdmType>())
					{
						if ((edmType is StructuralType && edmType.BuiltInTypeKind != BuiltInTypeKind.AssociationType) || Helper.IsEnumType(edmType))
						{
							KeyValuePair<EdmType, int> keyValuePair;
							if (this._conventionCSpaceTypeNames.TryGetValue(edmType.Name, out keyValuePair))
							{
								this._conventionCSpaceTypeNames[edmType.Name] = new KeyValuePair<EdmType, int>(keyValuePair.Key, keyValuePair.Value + 1);
							}
							else
							{
								keyValuePair = new KeyValuePair<EdmType, int>(edmType, 1);
								this._conventionCSpaceTypeNames.Add(edmType.Name, keyValuePair);
							}
						}
					}
				}
				return this._conventionCSpaceTypeNames;
			}
		}

		// Token: 0x17000C92 RID: 3218
		// (get) Token: 0x06004036 RID: 16438 RVA: 0x000D5450 File Offset: 0x000D3650
		// (set) Token: 0x06004037 RID: 16439 RVA: 0x000D5458 File Offset: 0x000D3658
		internal Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader> ObjectItemAssemblyLoaderFactory
		{
			get
			{
				return this._loaderFactory;
			}
			set
			{
				if (this._loaderFactory != value)
				{
					this._loaderFactory = value;
				}
			}
		}

		// Token: 0x17000C93 RID: 3219
		// (get) Token: 0x06004038 RID: 16440 RVA: 0x000D546F File Offset: 0x000D366F
		internal object LoaderCookie
		{
			get
			{
				if (this._originalLoaderCookie != null)
				{
					return this._originalLoaderCookie;
				}
				return this._loaderFactory;
			}
		}

		// Token: 0x06004039 RID: 16441 RVA: 0x000D5486 File Offset: 0x000D3686
		internal ObjectItemLoadingSessionData()
		{
		}

		// Token: 0x0600403A RID: 16442 RVA: 0x000D54B0 File Offset: 0x000D36B0
		internal ObjectItemLoadingSessionData(KnownAssembliesSet knownAssemblies, LockedAssemblyCache lockedAssemblyCache, EdmItemCollection edmItemCollection, Action<string> logLoadMessage, object loaderCookie)
		{
			this._typesInLoading = new Dictionary<string, EdmType>(StringComparer.Ordinal);
			this._errors = new List<EdmItemError>();
			this._knownAssemblies = knownAssemblies;
			this._lockedAssemblyCache = lockedAssemblyCache;
			this._edmItemCollection = edmItemCollection;
			this._loadMessageLogger = new LoadMessageLogger(logLoadMessage);
			this._cspaceToOspace = new Dictionary<EdmType, EdmType>();
			this._loaderFactory = (Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader>)loaderCookie;
			this._originalLoaderCookie = loaderCookie;
			if (this._loaderFactory == new Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader>(ObjectItemConventionAssemblyLoader.Create) && this._edmItemCollection != null)
			{
				foreach (KnownAssemblyEntry knownAssemblyEntry in this._knownAssemblies.GetEntries(this._loaderFactory, edmItemCollection))
				{
					foreach (EdmType edmType in knownAssemblyEntry.CacheEntry.TypesInAssembly.OfType<EdmType>())
					{
						if (Helper.IsEntityType(edmType))
						{
							ClrEntityType clrEntityType = (ClrEntityType)edmType;
							this._cspaceToOspace.Add(this._edmItemCollection.GetItem<StructuralType>(clrEntityType.CSpaceTypeName), clrEntityType);
						}
						else if (Helper.IsComplexType(edmType))
						{
							ClrComplexType clrComplexType = (ClrComplexType)edmType;
							this._cspaceToOspace.Add(this._edmItemCollection.GetItem<StructuralType>(clrComplexType.CSpaceTypeName), clrComplexType);
						}
						else if (Helper.IsEnumType(edmType))
						{
							ClrEnumType clrEnumType = (ClrEnumType)edmType;
							this._cspaceToOspace.Add(this._edmItemCollection.GetItem<EnumType>(clrEnumType.CSpaceTypeName), clrEnumType);
						}
						else
						{
							this._cspaceToOspace.Add(this._edmItemCollection.GetItem<StructuralType>(edmType.FullName), edmType);
						}
					}
				}
			}
		}

		// Token: 0x0600403B RID: 16443 RVA: 0x000D56A4 File Offset: 0x000D38A4
		internal void RegisterForLevel1PostSessionProcessing(ObjectItemAssemblyLoader loader)
		{
			this._loadersThatNeedLevel1PostSessionProcessing.Add(loader);
		}

		// Token: 0x0600403C RID: 16444 RVA: 0x000D56B3 File Offset: 0x000D38B3
		internal void RegisterForLevel2PostSessionProcessing(ObjectItemAssemblyLoader loader)
		{
			this._loadersThatNeedLevel2PostSessionProcessing.Add(loader);
		}

		// Token: 0x0600403D RID: 16445 RVA: 0x000D56C4 File Offset: 0x000D38C4
		internal void CompleteSession()
		{
			foreach (ObjectItemAssemblyLoader objectItemAssemblyLoader in this._loadersThatNeedLevel1PostSessionProcessing)
			{
				objectItemAssemblyLoader.OnLevel1SessionProcessing();
			}
			foreach (ObjectItemAssemblyLoader objectItemAssemblyLoader2 in this._loadersThatNeedLevel2PostSessionProcessing)
			{
				objectItemAssemblyLoader2.OnLevel2SessionProcessing();
			}
		}

		// Token: 0x0400165B RID: 5723
		private Func<Assembly, ObjectItemLoadingSessionData, ObjectItemAssemblyLoader> _loaderFactory;

		// Token: 0x0400165C RID: 5724
		private readonly Dictionary<string, EdmType> _typesInLoading;

		// Token: 0x0400165D RID: 5725
		private readonly LoadMessageLogger _loadMessageLogger;

		// Token: 0x0400165E RID: 5726
		private readonly List<EdmItemError> _errors;

		// Token: 0x0400165F RID: 5727
		private readonly Dictionary<Assembly, MutableAssemblyCacheEntry> _listOfAssembliesLoaded = new Dictionary<Assembly, MutableAssemblyCacheEntry>();

		// Token: 0x04001660 RID: 5728
		private readonly KnownAssembliesSet _knownAssemblies;

		// Token: 0x04001661 RID: 5729
		private readonly LockedAssemblyCache _lockedAssemblyCache;

		// Token: 0x04001662 RID: 5730
		private readonly HashSet<ObjectItemAssemblyLoader> _loadersThatNeedLevel1PostSessionProcessing = new HashSet<ObjectItemAssemblyLoader>();

		// Token: 0x04001663 RID: 5731
		private readonly HashSet<ObjectItemAssemblyLoader> _loadersThatNeedLevel2PostSessionProcessing = new HashSet<ObjectItemAssemblyLoader>();

		// Token: 0x04001664 RID: 5732
		private readonly EdmItemCollection _edmItemCollection;

		// Token: 0x04001665 RID: 5733
		private Dictionary<string, KeyValuePair<EdmType, int>> _conventionCSpaceTypeNames;

		// Token: 0x04001666 RID: 5734
		private readonly Dictionary<EdmType, EdmType> _cspaceToOspace;

		// Token: 0x04001667 RID: 5735
		private readonly object _originalLoaderCookie;
	}
}
