﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.Internal;
using System.Data.Entity.Core.Common.EntitySql;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Query.InternalTrees;
using System.Data.Entity.Core.Query.PlanCompiler;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration
{
	// Token: 0x0200056A RID: 1386
	internal sealed class GeneratedView : InternalBase
	{
		// Token: 0x060043A8 RID: 17320 RVA: 0x000E9BC0 File Offset: 0x000E7DC0
		internal static GeneratedView CreateGeneratedView(EntitySetBase extent, EdmType type, DbQueryCommandTree commandTree, string eSQL, StorageMappingItemCollection mappingItemCollection, ConfigViewGenerator config)
		{
			DiscriminatorMap discriminatorMap = null;
			if (commandTree != null)
			{
				commandTree = ViewSimplifier.SimplifyView(extent, commandTree);
				if (extent.BuiltInTypeKind == BuiltInTypeKind.EntitySet)
				{
					DiscriminatorMap.TryCreateDiscriminatorMap((EntitySet)extent, commandTree.Query, out discriminatorMap);
				}
			}
			return new GeneratedView(extent, type, commandTree, eSQL, discriminatorMap, mappingItemCollection, config);
		}

		// Token: 0x060043A9 RID: 17321 RVA: 0x000E9C07 File Offset: 0x000E7E07
		internal static GeneratedView CreateGeneratedViewForFKAssociationSet(EntitySetBase extent, EdmType type, DbQueryCommandTree commandTree, StorageMappingItemCollection mappingItemCollection, ConfigViewGenerator config)
		{
			return new GeneratedView(extent, type, commandTree, null, null, mappingItemCollection, config);
		}

		// Token: 0x060043AA RID: 17322 RVA: 0x000E9C18 File Offset: 0x000E7E18
		internal static bool TryParseUserSpecifiedView(EntitySetBaseMapping setMapping, EntityTypeBase type, string eSQL, bool includeSubtypes, StorageMappingItemCollection mappingItemCollection, ConfigViewGenerator config, IList<EdmSchemaError> errors, out GeneratedView generatedView)
		{
			bool flag = false;
			DbQueryCommandTree dbQueryCommandTree;
			DiscriminatorMap discriminatorMap;
			Exception ex;
			if (!GeneratedView.TryParseView(eSQL, true, setMapping.Set, mappingItemCollection, config, out dbQueryCommandTree, out discriminatorMap, out ex))
			{
				EdmSchemaError edmSchemaError = new EdmSchemaError(Strings.Mapping_Invalid_QueryView2(setMapping.Set.Name, ex.Message), 2068, EdmSchemaErrorSeverity.Error, setMapping.EntityContainerMapping.SourceLocation, setMapping.StartLineNumber, setMapping.StartLinePosition, ex);
				errors.Add(edmSchemaError);
				flag = true;
			}
			else
			{
				foreach (EdmSchemaError edmSchemaError2 in ViewValidator.ValidateQueryView(dbQueryCommandTree, setMapping, type, includeSubtypes))
				{
					errors.Add(edmSchemaError2);
					flag = true;
				}
				CollectionType collectionType = dbQueryCommandTree.Query.ResultType.EdmType as CollectionType;
				if (collectionType == null || !setMapping.Set.ElementType.IsAssignableFrom(collectionType.TypeUsage.EdmType))
				{
					EdmSchemaError edmSchemaError3 = new EdmSchemaError(Strings.Mapping_Invalid_QueryView_Type(setMapping.Set.Name), 2069, EdmSchemaErrorSeverity.Error, setMapping.EntityContainerMapping.SourceLocation, setMapping.StartLineNumber, setMapping.StartLinePosition);
					errors.Add(edmSchemaError3);
					flag = true;
				}
			}
			if (!flag)
			{
				generatedView = new GeneratedView(setMapping.Set, type, dbQueryCommandTree, eSQL, discriminatorMap, mappingItemCollection, config);
				return true;
			}
			generatedView = null;
			return false;
		}

		// Token: 0x060043AB RID: 17323 RVA: 0x000E9D70 File Offset: 0x000E7F70
		private GeneratedView(EntitySetBase extent, EdmType type, DbQueryCommandTree commandTree, string eSQL, DiscriminatorMap discriminatorMap, StorageMappingItemCollection mappingItemCollection, ConfigViewGenerator config)
		{
			this.m_extent = extent;
			this.m_type = type;
			this.m_commandTree = commandTree;
			this.m_eSQL = eSQL;
			this.m_discriminatorMap = discriminatorMap;
			this.m_mappingItemCollection = mappingItemCollection;
			this.m_config = config;
			if (this.m_config.IsViewTracing)
			{
				StringBuilder stringBuilder = new StringBuilder(1024);
				this.ToCompactString(stringBuilder);
				Helpers.FormatTraceLine("CQL view for {0}", new object[] { stringBuilder.ToString() });
			}
		}

		// Token: 0x17000D6B RID: 3435
		// (get) Token: 0x060043AC RID: 17324 RVA: 0x000E9DF0 File Offset: 0x000E7FF0
		internal string eSQL
		{
			get
			{
				return this.m_eSQL;
			}
		}

		// Token: 0x060043AD RID: 17325 RVA: 0x000E9DF8 File Offset: 0x000E7FF8
		internal DbQueryCommandTree GetCommandTree()
		{
			if (this.m_commandTree != null)
			{
				return this.m_commandTree;
			}
			Exception ex;
			if (GeneratedView.TryParseView(this.m_eSQL, false, this.m_extent, this.m_mappingItemCollection, this.m_config, out this.m_commandTree, out this.m_discriminatorMap, out ex))
			{
				return this.m_commandTree;
			}
			throw new MappingException(Strings.Mapping_Invalid_QueryView(this.m_extent.Name, ex.Message));
		}

		// Token: 0x060043AE RID: 17326 RVA: 0x000E9E64 File Offset: 0x000E8064
		internal Node GetInternalTree(Command targetIqtCommand)
		{
			if (this.m_internalTreeNode == null)
			{
				Command command = ITreeGenerator.Generate(this.GetCommandTree(), this.m_discriminatorMap);
				PlanCompiler.Assert(command.Root.Op.OpType == OpType.PhysicalProject, "Expected a physical projectOp at the root of the tree - found " + command.Root.Op.OpType.ToString());
				command.DisableVarVecEnumCaching();
				this.m_internalTreeNode = command.Root.Child0;
			}
			return OpCopier.Copy(targetIqtCommand, this.m_internalTreeNode);
		}

		// Token: 0x060043AF RID: 17327 RVA: 0x000E9EF0 File Offset: 0x000E80F0
		private static bool TryParseView(string eSQL, bool isUserSpecified, EntitySetBase extent, StorageMappingItemCollection mappingItemCollection, ConfigViewGenerator config, out DbQueryCommandTree commandTree, out DiscriminatorMap discriminatorMap, out Exception parserException)
		{
			commandTree = null;
			discriminatorMap = null;
			parserException = null;
			config.StartSingleWatch(PerfType.ViewParsing);
			try
			{
				ParserOptions.CompilationMode compilationMode = ParserOptions.CompilationMode.RestrictedViewGenerationMode;
				if (isUserSpecified)
				{
					compilationMode = ParserOptions.CompilationMode.UserViewGenerationMode;
				}
				commandTree = (DbQueryCommandTree)ExternalCalls.CompileView(eSQL, mappingItemCollection, compilationMode);
				commandTree = ViewSimplifier.SimplifyView(extent, commandTree);
				if (extent.BuiltInTypeKind == BuiltInTypeKind.EntitySet)
				{
					DiscriminatorMap.TryCreateDiscriminatorMap((EntitySet)extent, commandTree.Query, out discriminatorMap);
				}
			}
			catch (Exception ex)
			{
				if (!ex.IsCatchableExceptionType())
				{
					throw;
				}
				parserException = ex;
			}
			finally
			{
				config.StopSingleWatch(PerfType.ViewParsing);
			}
			return parserException == null;
		}

		// Token: 0x060043B0 RID: 17328 RVA: 0x000E9F98 File Offset: 0x000E8198
		internal override void ToCompactString(StringBuilder builder)
		{
			bool flag = this.m_type != this.m_extent.ElementType;
			if (flag)
			{
				builder.Append("OFTYPE(");
			}
			builder.AppendFormat("{0}.{1}", this.m_extent.EntityContainer.Name, this.m_extent.Name);
			if (flag)
			{
				builder.Append(", ").Append(this.m_type.Name).Append(')');
			}
			builder.AppendLine(" = ");
			if (!string.IsNullOrEmpty(this.m_eSQL))
			{
				builder.Append(this.m_eSQL);
				return;
			}
			builder.Append(this.m_commandTree.Print());
		}

		// Token: 0x0400182A RID: 6186
		private readonly EntitySetBase m_extent;

		// Token: 0x0400182B RID: 6187
		private readonly EdmType m_type;

		// Token: 0x0400182C RID: 6188
		private DbQueryCommandTree m_commandTree;

		// Token: 0x0400182D RID: 6189
		private readonly string m_eSQL;

		// Token: 0x0400182E RID: 6190
		private Node m_internalTreeNode;

		// Token: 0x0400182F RID: 6191
		private DiscriminatorMap m_discriminatorMap;

		// Token: 0x04001830 RID: 6192
		private readonly StorageMappingItemCollection m_mappingItemCollection;

		// Token: 0x04001831 RID: 6193
		private readonly ConfigViewGenerator m_config;
	}
}
