﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.CommandTrees.Internal;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006BE RID: 1726
	public sealed class DbFunctionCommandTree : DbCommandTree
	{
		// Token: 0x060050E6 RID: 20710 RVA: 0x001217C0 File Offset: 0x0011F9C0
		public DbFunctionCommandTree(MetadataWorkspace metadata, DataSpace dataSpace, EdmFunction edmFunction, TypeUsage resultType, IEnumerable<KeyValuePair<string, TypeUsage>> parameters)
			: base(metadata, dataSpace, true, false)
		{
			Check.NotNull<EdmFunction>(edmFunction, "edmFunction");
			this._edmFunction = edmFunction;
			this._resultType = resultType;
			List<string> list = new List<string>();
			List<TypeUsage> list2 = new List<TypeUsage>();
			if (parameters != null)
			{
				foreach (KeyValuePair<string, TypeUsage> keyValuePair in parameters)
				{
					list.Add(keyValuePair.Key);
					list2.Add(keyValuePair.Value);
				}
			}
			this._parameterNames = new ReadOnlyCollection<string>(list);
			this._parameterTypes = new ReadOnlyCollection<TypeUsage>(list2);
		}

		// Token: 0x17000FB4 RID: 4020
		// (get) Token: 0x060050E7 RID: 20711 RVA: 0x0012186C File Offset: 0x0011FA6C
		public EdmFunction EdmFunction
		{
			get
			{
				return this._edmFunction;
			}
		}

		// Token: 0x17000FB5 RID: 4021
		// (get) Token: 0x060050E8 RID: 20712 RVA: 0x00121874 File Offset: 0x0011FA74
		public TypeUsage ResultType
		{
			get
			{
				return this._resultType;
			}
		}

		// Token: 0x17000FB6 RID: 4022
		// (get) Token: 0x060050E9 RID: 20713 RVA: 0x0012187C File Offset: 0x0011FA7C
		public override DbCommandTreeKind CommandTreeKind
		{
			get
			{
				return DbCommandTreeKind.Function;
			}
		}

		// Token: 0x060050EA RID: 20714 RVA: 0x0012187F File Offset: 0x0011FA7F
		internal override IEnumerable<KeyValuePair<string, TypeUsage>> GetParameters()
		{
			int num;
			for (int idx = 0; idx < this._parameterNames.Count; idx = num + 1)
			{
				yield return new KeyValuePair<string, TypeUsage>(this._parameterNames[idx], this._parameterTypes[idx]);
				num = idx;
			}
			yield break;
		}

		// Token: 0x060050EB RID: 20715 RVA: 0x0012188F File Offset: 0x0011FA8F
		internal override void DumpStructure(ExpressionDumper dumper)
		{
			if (this.EdmFunction != null)
			{
				dumper.Dump(this.EdmFunction);
			}
		}

		// Token: 0x060050EC RID: 20716 RVA: 0x001218A5 File Offset: 0x0011FAA5
		internal override string PrintTree(ExpressionPrinter printer)
		{
			return printer.Print(this);
		}

		// Token: 0x04001D9B RID: 7579
		private readonly EdmFunction _edmFunction;

		// Token: 0x04001D9C RID: 7580
		private readonly TypeUsage _resultType;

		// Token: 0x04001D9D RID: 7581
		private readonly ReadOnlyCollection<string> _parameterNames;

		// Token: 0x04001D9E RID: 7582
		private readonly ReadOnlyCollection<TypeUsage> _parameterTypes;
	}
}
