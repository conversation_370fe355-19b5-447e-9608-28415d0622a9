﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006B5 RID: 1717
	public sealed class DbExceptExpression : DbBinaryExpression
	{
		// Token: 0x06005054 RID: 20564 RVA: 0x00121383 File Offset: 0x0011F583
		internal DbExceptExpression(TypeUsage resultType, DbExpression left, DbExpression right)
			: base(DbExpressionKind.Except, resultType, left, right)
		{
		}

		// Token: 0x06005055 RID: 20565 RVA: 0x00121390 File Offset: 0x0011F590
		public override void Accept(DbExpressionVisitor visitor)
		{
			Check.NotNull<DbExpressionVisitor>(visitor, "visitor");
			visitor.Visit(this);
		}

		// Token: 0x06005056 RID: 20566 RVA: 0x001213A5 File Offset: 0x0011F5A5
		public override TResultType Accept<TResultType>(DbExpressionVisitor<TResultType> visitor)
		{
			Check.NotNull<DbExpressionVisitor<TResultType>>(visitor, "visitor");
			return visitor.Visit(this);
		}
	}
}
