﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.CqlGeneration;
using System.Data.Entity.Core.Mapping.ViewGeneration.QueryRewriting;
using System.Data.Entity.Core.Metadata.Edm;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005A4 RID: 1444
	internal class LeafCellTreeNode : CellTreeNode
	{
		// Token: 0x0600461B RID: 17947 RVA: 0x000F6470 File Offset: 0x000F4670
		internal LeafCellTreeNode(ViewgenContext context, LeftCellWrapper cellWrapper)
			: base(context)
		{
			this.m_cellWrapper = cellWrapper;
			this.m_rightFragmentQuery = FragmentQuery.Create(cellWrapper.OriginalCellNumberString, cellWrapper.CreateRoleBoolean(), cellWrapper.RightCellQuery);
		}

		// Token: 0x0600461C RID: 17948 RVA: 0x000F649D File Offset: 0x000F469D
		internal LeafCellTreeNode(ViewgenContext context, LeftCellWrapper cellWrapper, FragmentQuery rightFragmentQuery)
			: base(context)
		{
			this.m_cellWrapper = cellWrapper;
			this.m_rightFragmentQuery = rightFragmentQuery;
		}

		// Token: 0x17000DD9 RID: 3545
		// (get) Token: 0x0600461D RID: 17949 RVA: 0x000F64B4 File Offset: 0x000F46B4
		internal LeftCellWrapper LeftCellWrapper
		{
			get
			{
				return this.m_cellWrapper;
			}
		}

		// Token: 0x17000DDA RID: 3546
		// (get) Token: 0x0600461E RID: 17950 RVA: 0x000F64BC File Offset: 0x000F46BC
		internal override MemberDomainMap RightDomainMap
		{
			get
			{
				return this.m_cellWrapper.RightDomainMap;
			}
		}

		// Token: 0x17000DDB RID: 3547
		// (get) Token: 0x0600461F RID: 17951 RVA: 0x000F64C9 File Offset: 0x000F46C9
		internal override FragmentQuery LeftFragmentQuery
		{
			get
			{
				return this.m_cellWrapper.FragmentQuery;
			}
		}

		// Token: 0x17000DDC RID: 3548
		// (get) Token: 0x06004620 RID: 17952 RVA: 0x000F64D6 File Offset: 0x000F46D6
		internal override FragmentQuery RightFragmentQuery
		{
			get
			{
				return this.m_rightFragmentQuery;
			}
		}

		// Token: 0x17000DDD RID: 3549
		// (get) Token: 0x06004621 RID: 17953 RVA: 0x000F64DE File Offset: 0x000F46DE
		internal override Set<MemberPath> Attributes
		{
			get
			{
				return this.m_cellWrapper.Attributes;
			}
		}

		// Token: 0x17000DDE RID: 3550
		// (get) Token: 0x06004622 RID: 17954 RVA: 0x000F64EB File Offset: 0x000F46EB
		internal override List<CellTreeNode> Children
		{
			get
			{
				return new List<CellTreeNode>();
			}
		}

		// Token: 0x17000DDF RID: 3551
		// (get) Token: 0x06004623 RID: 17955 RVA: 0x000F64F2 File Offset: 0x000F46F2
		internal override CellTreeOpType OpType
		{
			get
			{
				return CellTreeOpType.Leaf;
			}
		}

		// Token: 0x17000DE0 RID: 3552
		// (get) Token: 0x06004624 RID: 17956 RVA: 0x000F64F5 File Offset: 0x000F46F5
		internal override int NumProjectedSlots
		{
			get
			{
				return this.LeftCellWrapper.RightCellQuery.NumProjectedSlots;
			}
		}

		// Token: 0x17000DE1 RID: 3553
		// (get) Token: 0x06004625 RID: 17957 RVA: 0x000F6507 File Offset: 0x000F4707
		internal override int NumBoolSlots
		{
			get
			{
				return this.LeftCellWrapper.RightCellQuery.NumBoolVars;
			}
		}

		// Token: 0x06004626 RID: 17958 RVA: 0x000F6519 File Offset: 0x000F4719
		internal override TOutput Accept<TInput, TOutput>(CellTreeNode.CellTreeVisitor<TInput, TOutput> visitor, TInput param)
		{
			return visitor.VisitLeaf(this, param);
		}

		// Token: 0x06004627 RID: 17959 RVA: 0x000F6523 File Offset: 0x000F4723
		internal override TOutput Accept<TInput, TOutput>(CellTreeNode.SimpleCellTreeVisitor<TInput, TOutput> visitor, TInput param)
		{
			return visitor.VisitLeaf(this, param);
		}

		// Token: 0x06004628 RID: 17960 RVA: 0x000F6530 File Offset: 0x000F4730
		internal override bool IsProjectedSlot(int slot)
		{
			CellQuery rightCellQuery = this.LeftCellWrapper.RightCellQuery;
			if (base.IsBoolSlot(slot))
			{
				return rightCellQuery.GetBoolVar(base.SlotToBoolIndex(slot)) != null;
			}
			return rightCellQuery.ProjectedSlotAt(slot) != null;
		}

		// Token: 0x06004629 RID: 17961 RVA: 0x000F6570 File Offset: 0x000F4770
		internal override CqlBlock ToCqlBlock(bool[] requiredSlots, CqlIdentifiers identifiers, ref int blockAliasNum, ref List<WithRelationship> withRelationships)
		{
			int num = requiredSlots.Length;
			CellQuery rightCellQuery = this.LeftCellWrapper.RightCellQuery;
			SlotInfo[] array = new SlotInfo[num];
			for (int i = 0; i < rightCellQuery.NumProjectedSlots; i++)
			{
				ProjectedSlot projectedSlot = rightCellQuery.ProjectedSlotAt(i);
				if (requiredSlots[i] && projectedSlot == null)
				{
					ConstantProjectedSlot constantProjectedSlot = new ConstantProjectedSlot(Domain.GetDefaultValueForMemberPath(base.ProjectedSlotMap[i], base.GetLeaves(), base.ViewgenContext.Config));
					rightCellQuery.FixMissingSlotAsDefaultConstant(i, constantProjectedSlot);
					projectedSlot = constantProjectedSlot;
				}
				SlotInfo slotInfo = new SlotInfo(requiredSlots[i], projectedSlot != null, projectedSlot, base.ProjectedSlotMap[i]);
				array[i] = slotInfo;
			}
			for (int j = 0; j < rightCellQuery.NumBoolVars; j++)
			{
				BoolExpression boolVar = rightCellQuery.GetBoolVar(j);
				BooleanProjectedSlot booleanProjectedSlot;
				if (boolVar != null)
				{
					booleanProjectedSlot = new BooleanProjectedSlot(boolVar, identifiers, j);
				}
				else
				{
					booleanProjectedSlot = new BooleanProjectedSlot(BoolExpression.False, identifiers, j);
				}
				int num2 = base.BoolIndexToSlot(j);
				SlotInfo slotInfo2 = new SlotInfo(requiredSlots[num2], boolVar != null, booleanProjectedSlot, null);
				array[num2] = slotInfo2;
			}
			IEnumerable<SlotInfo> enumerable = array;
			if (rightCellQuery.Extent.EntityContainer.DataSpace == DataSpace.SSpace && this.m_cellWrapper.LeftExtent.BuiltInTypeKind == BuiltInTypeKind.EntitySet)
			{
				IEnumerable<AssociationSetMapping> relationshipSetMappingsFor = base.ViewgenContext.EntityContainerMapping.GetRelationshipSetMappingsFor(this.m_cellWrapper.LeftExtent, rightCellQuery.Extent);
				List<SlotInfo> list = new List<SlotInfo>();
				using (IEnumerator<AssociationSetMapping> enumerator = relationshipSetMappingsFor.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						WithRelationship withRelationship;
						if (LeafCellTreeNode.TryGetWithRelationship(enumerator.Current, this.m_cellWrapper.LeftExtent, rightCellQuery.SourceExtentMemberPath, ref list, out withRelationship))
						{
							withRelationships.Add(withRelationship);
							enumerable = array.Concat(list);
						}
					}
				}
			}
			EntitySetBase extent = rightCellQuery.Extent;
			CellQuery.SelectDistinct selectDistinctFlag = rightCellQuery.SelectDistinctFlag;
			SlotInfo[] array2 = enumerable.ToArray<SlotInfo>();
			BoolExpression whereClause = rightCellQuery.WhereClause;
			int num3 = blockAliasNum + 1;
			blockAliasNum = num3;
			return new ExtentCqlBlock(extent, selectDistinctFlag, array2, whereClause, identifiers, num3);
		}

		// Token: 0x0600462A RID: 17962 RVA: 0x000F6754 File Offset: 0x000F4954
		private static bool TryGetWithRelationship(AssociationSetMapping collocatedAssociationSetMap, EntitySetBase thisExtent, MemberPath sRootNode, ref List<SlotInfo> foreignKeySlots, out WithRelationship withRelationship)
		{
			withRelationship = null;
			EndPropertyMapping foreignKeyEndMapFromAssociationMap = LeafCellTreeNode.GetForeignKeyEndMapFromAssociationMap(collocatedAssociationSetMap);
			if (foreignKeyEndMapFromAssociationMap == null || foreignKeyEndMapFromAssociationMap.AssociationEnd.RelationshipMultiplicity == RelationshipMultiplicity.Many)
			{
				return false;
			}
			AssociationEndMember associationEnd = foreignKeyEndMapFromAssociationMap.AssociationEnd;
			AssociationEndMember otherAssociationEnd = MetadataHelper.GetOtherAssociationEnd(associationEnd);
			EntityType entityType = (EntityType)((RefType)associationEnd.TypeUsage.EdmType).ElementType;
			EntityType entityType2 = (EntityType)((RefType)otherAssociationEnd.TypeUsage.EdmType).ElementType;
			AssociationSet associationSet = (AssociationSet)collocatedAssociationSetMap.Set;
			MemberPath memberPath = new MemberPath(associationSet, associationEnd);
			IEnumerable<ScalarPropertyMapping> enumerable = foreignKeyEndMapFromAssociationMap.PropertyMappings.Cast<ScalarPropertyMapping>();
			List<MemberPath> list = new List<MemberPath>();
			using (ReadOnlyMetadataCollection<EdmMember>.Enumerator enumerator = entityType.KeyMembers.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					EdmProperty edmProperty = (EdmProperty)enumerator.Current;
					ScalarPropertyMapping scalarPropertyMapping = enumerable.Where((ScalarPropertyMapping propMap) => propMap.Property.Equals(edmProperty)).First<ScalarPropertyMapping>();
					MemberProjectedSlot memberProjectedSlot = new MemberProjectedSlot(new MemberPath(sRootNode, scalarPropertyMapping.Column));
					MemberPath memberPath2 = new MemberPath(memberPath, edmProperty);
					list.Add(memberPath2);
					foreignKeySlots.Add(new SlotInfo(true, true, memberProjectedSlot, memberPath2));
				}
			}
			if (thisExtent.ElementType.IsAssignableFrom(entityType2))
			{
				withRelationship = new WithRelationship(associationSet, otherAssociationEnd, entityType2, associationEnd, entityType, list);
				return true;
			}
			return false;
		}

		// Token: 0x0600462B RID: 17963 RVA: 0x000F68C0 File Offset: 0x000F4AC0
		private static EndPropertyMapping GetForeignKeyEndMapFromAssociationMap(AssociationSetMapping collocatedAssociationSetMap)
		{
			MappingFragment mappingFragment = collocatedAssociationSetMap.TypeMappings.First<TypeMapping>().MappingFragments.First<MappingFragment>();
			IEnumerable<EdmMember> keyMembers = collocatedAssociationSetMap.StoreEntitySet.ElementType.KeyMembers;
			using (IEnumerator<PropertyMapping> enumerator = mappingFragment.PropertyMappings.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					EndPropertyMapping endMap = (EndPropertyMapping)enumerator.Current;
					if (endMap.StoreProperties.SequenceEqual(keyMembers, EqualityComparer<EdmMember>.Default))
					{
						return (from eMap in mappingFragment.PropertyMappings.OfType<EndPropertyMapping>()
							where !eMap.Equals(endMap)
							select eMap).First<EndPropertyMapping>();
					}
				}
			}
			return null;
		}

		// Token: 0x0600462C RID: 17964 RVA: 0x000F6980 File Offset: 0x000F4B80
		internal override void ToCompactString(StringBuilder stringBuilder)
		{
			this.m_cellWrapper.ToCompactString(stringBuilder);
		}

		// Token: 0x0400190D RID: 6413
		internal static readonly IEqualityComparer<LeafCellTreeNode> EqualityComparer = new LeafCellTreeNode.LeafCellTreeNodeComparer();

		// Token: 0x0400190E RID: 6414
		private readonly LeftCellWrapper m_cellWrapper;

		// Token: 0x0400190F RID: 6415
		private readonly FragmentQuery m_rightFragmentQuery;

		// Token: 0x02000BCE RID: 3022
		private class LeafCellTreeNodeComparer : IEqualityComparer<LeafCellTreeNode>
		{
			// Token: 0x0600682E RID: 26670 RVA: 0x0016265F File Offset: 0x0016085F
			public bool Equals(LeafCellTreeNode left, LeafCellTreeNode right)
			{
				return left == right || (left != null && right != null && left.m_cellWrapper.Equals(right.m_cellWrapper));
			}

			// Token: 0x0600682F RID: 26671 RVA: 0x00162680 File Offset: 0x00160880
			public int GetHashCode(LeafCellTreeNode node)
			{
				return node.m_cellWrapper.GetHashCode();
			}
		}
	}
}
