﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EntityDatabase.ServerModels
{
	// Token: 0x02000005 RID: 5
	public class DevilTreasPointsList
	{
		// Token: 0x17000006 RID: 6
		// (get) Token: 0x0600000E RID: 14 RVA: 0x000020C7 File Offset: 0x000002C7
		// (set) Token: 0x0600000F RID: 15 RVA: 0x000020CF File Offset: 0x000002CF
		[Key]
		[DatabaseGenerated(DatabaseGeneratedOption.None)]
		public int ID { get; set; }

		// Token: 0x17000007 RID: 7
		// (get) Token: 0x06000010 RID: 16 RVA: 0x000020D8 File Offset: 0x000002D8
		// (set) Token: 0x06000011 RID: 17 RVA: 0x000020E0 File Offset: 0x000002E0
		public int Points { get; set; }

		// Token: 0x17000008 RID: 8
		// (get) Token: 0x06000012 RID: 18 RVA: 0x000020E9 File Offset: 0x000002E9
		// (set) Token: 0x06000013 RID: 19 RVA: 0x000020F1 File Offset: 0x000002F1
		public int TemplateID { get; set; }
	}
}
