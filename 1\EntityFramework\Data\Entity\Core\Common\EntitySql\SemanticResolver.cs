﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Common.EntitySql.AST;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;

namespace System.Data.Entity.Core.Common.EntitySql
{
	// Token: 0x0200066B RID: 1643
	internal sealed class SemanticResolver
	{
		// Token: 0x06004EB5 RID: 20149 RVA: 0x0011DADA File Offset: 0x0011BCDA
		internal static SemanticResolver Create(Perspective perspective, ParserOptions parserOptions, IEnumerable<DbParameterReferenceExpression> parameters, IEnumerable<DbVariableReferenceExpression> variables)
		{
			return new SemanticResolver(parserOptions, SemanticResolver.ProcessParameters(parameters, parserOptions), SemanticResolver.ProcessVariables(variables, parserOptions), new TypeResolver(perspective, parserOptions));
		}

		// Token: 0x06004EB6 RID: 20150 RVA: 0x0011DAF7 File Offset: 0x0011BCF7
		internal SemanticResolver CloneForInlineFunctionConversion()
		{
			return new SemanticResolver(this._parserOptions, this._parameters, this._variables, this._typeResolver);
		}

		// Token: 0x06004EB7 RID: 20151 RVA: 0x0011DB18 File Offset: 0x0011BD18
		private SemanticResolver(ParserOptions parserOptions, Dictionary<string, DbParameterReferenceExpression> parameters, Dictionary<string, DbVariableReferenceExpression> variables, TypeResolver typeResolver)
		{
			this._parserOptions = parserOptions;
			this._parameters = parameters;
			this._variables = variables;
			this._typeResolver = typeResolver;
			this._scopeManager = new ScopeManager(this.NameComparer);
			this.EnterScopeRegion();
			foreach (DbVariableReferenceExpression dbVariableReferenceExpression in this._variables.Values)
			{
				this.CurrentScope.Add(dbVariableReferenceExpression.VariableName, new FreeVariableScopeEntry(dbVariableReferenceExpression));
			}
		}

		// Token: 0x06004EB8 RID: 20152 RVA: 0x0011DBC8 File Offset: 0x0011BDC8
		private static Dictionary<string, DbParameterReferenceExpression> ProcessParameters(IEnumerable<DbParameterReferenceExpression> paramDefs, ParserOptions parserOptions)
		{
			Dictionary<string, DbParameterReferenceExpression> dictionary = new Dictionary<string, DbParameterReferenceExpression>(parserOptions.NameComparer);
			if (paramDefs != null)
			{
				foreach (DbParameterReferenceExpression dbParameterReferenceExpression in paramDefs)
				{
					if (dictionary.ContainsKey(dbParameterReferenceExpression.ParameterName))
					{
						throw new EntitySqlException(Strings.MultipleDefinitionsOfParameter(dbParameterReferenceExpression.ParameterName));
					}
					dictionary.Add(dbParameterReferenceExpression.ParameterName, dbParameterReferenceExpression);
				}
			}
			return dictionary;
		}

		// Token: 0x06004EB9 RID: 20153 RVA: 0x0011DC48 File Offset: 0x0011BE48
		private static Dictionary<string, DbVariableReferenceExpression> ProcessVariables(IEnumerable<DbVariableReferenceExpression> varDefs, ParserOptions parserOptions)
		{
			Dictionary<string, DbVariableReferenceExpression> dictionary = new Dictionary<string, DbVariableReferenceExpression>(parserOptions.NameComparer);
			if (varDefs != null)
			{
				foreach (DbVariableReferenceExpression dbVariableReferenceExpression in varDefs)
				{
					if (dictionary.ContainsKey(dbVariableReferenceExpression.VariableName))
					{
						throw new EntitySqlException(Strings.MultipleDefinitionsOfVariable(dbVariableReferenceExpression.VariableName));
					}
					dictionary.Add(dbVariableReferenceExpression.VariableName, dbVariableReferenceExpression);
				}
			}
			return dictionary;
		}

		// Token: 0x17000F25 RID: 3877
		// (get) Token: 0x06004EBA RID: 20154 RVA: 0x0011DCC8 File Offset: 0x0011BEC8
		internal Dictionary<string, DbParameterReferenceExpression> Parameters
		{
			get
			{
				return this._parameters;
			}
		}

		// Token: 0x17000F26 RID: 3878
		// (get) Token: 0x06004EBB RID: 20155 RVA: 0x0011DCD0 File Offset: 0x0011BED0
		internal Dictionary<string, DbVariableReferenceExpression> Variables
		{
			get
			{
				return this._variables;
			}
		}

		// Token: 0x17000F27 RID: 3879
		// (get) Token: 0x06004EBC RID: 20156 RVA: 0x0011DCD8 File Offset: 0x0011BED8
		internal TypeResolver TypeResolver
		{
			get
			{
				return this._typeResolver;
			}
		}

		// Token: 0x17000F28 RID: 3880
		// (get) Token: 0x06004EBD RID: 20157 RVA: 0x0011DCE0 File Offset: 0x0011BEE0
		internal ParserOptions ParserOptions
		{
			get
			{
				return this._parserOptions;
			}
		}

		// Token: 0x17000F29 RID: 3881
		// (get) Token: 0x06004EBE RID: 20158 RVA: 0x0011DCE8 File Offset: 0x0011BEE8
		internal StringComparer NameComparer
		{
			get
			{
				return this._parserOptions.NameComparer;
			}
		}

		// Token: 0x17000F2A RID: 3882
		// (get) Token: 0x06004EBF RID: 20159 RVA: 0x0011DCF5 File Offset: 0x0011BEF5
		internal IEnumerable<ScopeRegion> ScopeRegions
		{
			get
			{
				return this._scopeRegions;
			}
		}

		// Token: 0x17000F2B RID: 3883
		// (get) Token: 0x06004EC0 RID: 20160 RVA: 0x0011DCFD File Offset: 0x0011BEFD
		internal ScopeRegion CurrentScopeRegion
		{
			get
			{
				return this._scopeRegions[this._scopeRegions.Count - 1];
			}
		}

		// Token: 0x17000F2C RID: 3884
		// (get) Token: 0x06004EC1 RID: 20161 RVA: 0x0011DD17 File Offset: 0x0011BF17
		internal Scope CurrentScope
		{
			get
			{
				return this._scopeManager.CurrentScope;
			}
		}

		// Token: 0x17000F2D RID: 3885
		// (get) Token: 0x06004EC2 RID: 20162 RVA: 0x0011DD24 File Offset: 0x0011BF24
		internal int CurrentScopeIndex
		{
			get
			{
				return this._scopeManager.CurrentScopeIndex;
			}
		}

		// Token: 0x17000F2E RID: 3886
		// (get) Token: 0x06004EC3 RID: 20163 RVA: 0x0011DD31 File Offset: 0x0011BF31
		internal GroupAggregateInfo CurrentGroupAggregateInfo
		{
			get
			{
				return this._currentGroupAggregateInfo;
			}
		}

		// Token: 0x06004EC4 RID: 20164 RVA: 0x0011DD3C File Offset: 0x0011BF3C
		private DbExpression GetExpressionFromScopeEntry(ScopeEntry scopeEntry, int scopeIndex, string varName, ErrorContext errCtx)
		{
			DbExpression dbExpression = scopeEntry.GetExpression(varName, errCtx);
			if (this._currentGroupAggregateInfo != null)
			{
				ScopeRegion definingScopeRegion = this.GetDefiningScopeRegion(scopeIndex);
				if (definingScopeRegion.ScopeRegionIndex <= this._currentGroupAggregateInfo.DefiningScopeRegion.ScopeRegionIndex)
				{
					this._currentGroupAggregateInfo.UpdateScopeIndex(scopeIndex, this);
					IGroupExpressionExtendedInfo groupExpressionExtendedInfo = scopeEntry as IGroupExpressionExtendedInfo;
					if (groupExpressionExtendedInfo != null)
					{
						GroupAggregateInfo groupAggregateInfo = this._currentGroupAggregateInfo;
						while (groupAggregateInfo != null && groupAggregateInfo.DefiningScopeRegion.ScopeRegionIndex >= definingScopeRegion.ScopeRegionIndex && groupAggregateInfo.DefiningScopeRegion.ScopeRegionIndex != definingScopeRegion.ScopeRegionIndex)
						{
							groupAggregateInfo = groupAggregateInfo.ContainingAggregate;
						}
						if (groupAggregateInfo == null || groupAggregateInfo.DefiningScopeRegion.ScopeRegionIndex < definingScopeRegion.ScopeRegionIndex)
						{
							groupAggregateInfo = this._currentGroupAggregateInfo;
						}
						switch (groupAggregateInfo.AggregateKind)
						{
						case GroupAggregateKind.Function:
							if (groupExpressionExtendedInfo.GroupVarBasedExpression != null)
							{
								dbExpression = groupExpressionExtendedInfo.GroupVarBasedExpression;
							}
							break;
						case GroupAggregateKind.Partition:
							if (groupExpressionExtendedInfo.GroupAggBasedExpression != null)
							{
								dbExpression = groupExpressionExtendedInfo.GroupAggBasedExpression;
							}
							break;
						}
					}
				}
			}
			return dbExpression;
		}

		// Token: 0x06004EC5 RID: 20165 RVA: 0x0011DE31 File Offset: 0x0011C031
		internal IDisposable EnterIgnoreEntityContainerNameResolution()
		{
			this._ignoreEntityContainerNameResolution = true;
			return new Disposer(delegate
			{
				this._ignoreEntityContainerNameResolution = false;
			});
		}

		// Token: 0x06004EC6 RID: 20166 RVA: 0x0011DE4C File Offset: 0x0011C04C
		internal ExpressionResolution ResolveSimpleName(string name, bool leftHandSideOfMemberAccess, ErrorContext errCtx)
		{
			ScopeEntry scopeEntry;
			int num;
			if (this.TryScopeLookup(name, out scopeEntry, out num))
			{
				if (scopeEntry.EntryKind == ScopeEntryKind.SourceVar && ((SourceScopeEntry)scopeEntry).IsJoinClauseLeftExpr)
				{
					string invalidJoinLeftCorrelation = Strings.InvalidJoinLeftCorrelation;
					throw EntitySqlException.Create(errCtx, invalidJoinLeftCorrelation, null);
				}
				this.SetScopeRegionCorrelationFlag(num);
				return new ValueExpression(this.GetExpressionFromScopeEntry(scopeEntry, num, name, errCtx));
			}
			else
			{
				EntityContainer defaultContainer = this.TypeResolver.Perspective.GetDefaultContainer();
				ExpressionResolution expressionResolution;
				if (defaultContainer != null && this.TryResolveEntityContainerMemberAccess(defaultContainer, name, out expressionResolution))
				{
					return expressionResolution;
				}
				EntityContainer entityContainer;
				if (!this._ignoreEntityContainerNameResolution && this.TypeResolver.Perspective.TryGetEntityContainer(name, this._parserOptions.NameComparisonCaseInsensitive, out entityContainer))
				{
					return new EntityContainerExpression(entityContainer);
				}
				return this.TypeResolver.ResolveUnqualifiedName(name, leftHandSideOfMemberAccess, errCtx);
			}
		}

		// Token: 0x06004EC7 RID: 20167 RVA: 0x0011DF04 File Offset: 0x0011C104
		internal MetadataMember ResolveSimpleFunctionName(string name, ErrorContext errCtx)
		{
			MetadataMember metadataMember = this.TypeResolver.ResolveUnqualifiedName(name, false, errCtx);
			if (metadataMember.MetadataMemberClass == MetadataMemberClass.Namespace)
			{
				EntityContainer defaultContainer = this.TypeResolver.Perspective.GetDefaultContainer();
				ExpressionResolution expressionResolution;
				if (defaultContainer != null && this.TryResolveEntityContainerMemberAccess(defaultContainer, name, out expressionResolution) && expressionResolution.ExpressionClass == ExpressionResolutionClass.MetadataMember)
				{
					metadataMember = (MetadataMember)expressionResolution;
				}
			}
			return metadataMember;
		}

		// Token: 0x06004EC8 RID: 20168 RVA: 0x0011DF5C File Offset: 0x0011C15C
		private bool TryScopeLookup(string key, out ScopeEntry scopeEntry, out int scopeIndex)
		{
			scopeEntry = null;
			scopeIndex = -1;
			for (int i = this.CurrentScopeIndex; i >= 0; i--)
			{
				if (this._scopeManager.GetScopeByIndex(i).TryLookup(key, out scopeEntry))
				{
					scopeIndex = i;
					return true;
				}
			}
			return false;
		}

		// Token: 0x06004EC9 RID: 20169 RVA: 0x0011DF9B File Offset: 0x0011C19B
		internal MetadataMember ResolveMetadataMemberName(string[] name, ErrorContext errCtx)
		{
			return this.TypeResolver.ResolveMetadataMemberName(name, errCtx);
		}

		// Token: 0x06004ECA RID: 20170 RVA: 0x0011DFAC File Offset: 0x0011C1AC
		internal ValueExpression ResolvePropertyAccess(DbExpression valueExpr, string name, ErrorContext errCtx)
		{
			DbExpression dbExpression;
			if (this.TryResolveAsPropertyAccess(valueExpr, name, out dbExpression))
			{
				return new ValueExpression(dbExpression);
			}
			if (this.TryResolveAsRefPropertyAccess(valueExpr, name, errCtx, out dbExpression))
			{
				return new ValueExpression(dbExpression);
			}
			if (TypeSemantics.IsCollectionType(valueExpr.ResultType))
			{
				string text = Strings.NotAMemberOfCollection(name, valueExpr.ResultType.EdmType.FullName);
				throw EntitySqlException.Create(errCtx, text, null);
			}
			string text2 = Strings.NotAMemberOfType(name, valueExpr.ResultType.EdmType.FullName);
			throw EntitySqlException.Create(errCtx, text2, null);
		}

		// Token: 0x06004ECB RID: 20171 RVA: 0x0011E02C File Offset: 0x0011C22C
		private bool TryResolveAsPropertyAccess(DbExpression valueExpr, string name, out DbExpression propertyExpr)
		{
			propertyExpr = null;
			EdmMember edmMember;
			if (Helper.IsStructuralType(valueExpr.ResultType.EdmType) && this.TypeResolver.Perspective.TryGetMember((StructuralType)valueExpr.ResultType.EdmType, name, this._parserOptions.NameComparisonCaseInsensitive, out edmMember))
			{
				propertyExpr = DbExpressionBuilder.CreatePropertyExpressionFromMember(valueExpr, edmMember);
				return true;
			}
			return false;
		}

		// Token: 0x06004ECC RID: 20172 RVA: 0x0011E08C File Offset: 0x0011C28C
		private bool TryResolveAsRefPropertyAccess(DbExpression valueExpr, string name, ErrorContext errCtx, out DbExpression propertyExpr)
		{
			propertyExpr = null;
			if (!TypeSemantics.IsReferenceType(valueExpr.ResultType))
			{
				return false;
			}
			DbExpression dbExpression = valueExpr.Deref();
			TypeUsage resultType = dbExpression.ResultType;
			if (this.TryResolveAsPropertyAccess(dbExpression, name, out propertyExpr))
			{
				return true;
			}
			string text = Strings.InvalidDeRefProperty(name, resultType.EdmType.FullName, valueExpr.ResultType.EdmType.FullName);
			throw EntitySqlException.Create(errCtx, text, null);
		}

		// Token: 0x06004ECD RID: 20173 RVA: 0x0011E0F4 File Offset: 0x0011C2F4
		internal ExpressionResolution ResolveEntityContainerMemberAccess(EntityContainer entityContainer, string name, ErrorContext errCtx)
		{
			ExpressionResolution expressionResolution;
			if (this.TryResolveEntityContainerMemberAccess(entityContainer, name, out expressionResolution))
			{
				return expressionResolution;
			}
			string text = Strings.MemberDoesNotBelongToEntityContainer(name, entityContainer.Name);
			throw EntitySqlException.Create(errCtx, text, null);
		}

		// Token: 0x06004ECE RID: 20174 RVA: 0x0011E124 File Offset: 0x0011C324
		private bool TryResolveEntityContainerMemberAccess(EntityContainer entityContainer, string name, out ExpressionResolution resolution)
		{
			EntitySetBase entitySetBase;
			if (this.TypeResolver.Perspective.TryGetExtent(entityContainer, name, this._parserOptions.NameComparisonCaseInsensitive, out entitySetBase))
			{
				resolution = new ValueExpression(entitySetBase.Scan());
				return true;
			}
			EdmFunction edmFunction;
			if (this.TypeResolver.Perspective.TryGetFunctionImport(entityContainer, name, this._parserOptions.NameComparisonCaseInsensitive, out edmFunction))
			{
				resolution = new MetadataFunctionGroup(edmFunction.FullName, new EdmFunction[] { edmFunction });
				return true;
			}
			resolution = null;
			return false;
		}

		// Token: 0x06004ECF RID: 20175 RVA: 0x0011E19F File Offset: 0x0011C39F
		internal MetadataMember ResolveMetadataMemberAccess(MetadataMember metadataMember, string name, ErrorContext errCtx)
		{
			return this.TypeResolver.ResolveMetadataMemberAccess(metadataMember, name, errCtx);
		}

		// Token: 0x06004ED0 RID: 20176 RVA: 0x0011E1B0 File Offset: 0x0011C3B0
		internal bool TryResolveInternalAggregateName(string name, ErrorContext errCtx, out DbExpression dbExpression)
		{
			ScopeEntry scopeEntry;
			int num;
			if (this.TryScopeLookup(name, out scopeEntry, out num))
			{
				this.SetScopeRegionCorrelationFlag(num);
				dbExpression = scopeEntry.GetExpression(name, errCtx);
				return true;
			}
			dbExpression = null;
			return false;
		}

		// Token: 0x06004ED1 RID: 20177 RVA: 0x0011E1E4 File Offset: 0x0011C3E4
		internal bool TryResolveDotExprAsGroupKeyAlternativeName(DotExpr dotExpr, out ValueExpression groupKeyResolution)
		{
			groupKeyResolution = null;
			string[] array;
			ScopeEntry scopeEntry;
			int num;
			if (this.IsInAnyGroupScope() && dotExpr.IsMultipartIdentifier(out array) && this.TryScopeLookup(TypeResolver.GetFullName(array), out scopeEntry, out num))
			{
				IGetAlternativeName getAlternativeName = scopeEntry as IGetAlternativeName;
				if (getAlternativeName != null && getAlternativeName.AlternativeName != null && array.SequenceEqual(getAlternativeName.AlternativeName, this.NameComparer))
				{
					this.SetScopeRegionCorrelationFlag(num);
					groupKeyResolution = new ValueExpression(this.GetExpressionFromScopeEntry(scopeEntry, num, TypeResolver.GetFullName(array), dotExpr.ErrCtx));
					return true;
				}
			}
			return false;
		}

		// Token: 0x06004ED2 RID: 20178 RVA: 0x0011E264 File Offset: 0x0011C464
		internal string GenerateInternalName(string hint)
		{
			string text = "_##";
			uint namegenCounter = this._namegenCounter;
			this._namegenCounter = namegenCounter + 1U;
			return text + hint + namegenCounter.ToString(CultureInfo.InvariantCulture);
		}

		// Token: 0x06004ED3 RID: 20179 RVA: 0x0011E298 File Offset: 0x0011C498
		private string CreateNewAlias(DbExpression expr)
		{
			DbScanExpression dbScanExpression = expr as DbScanExpression;
			if (dbScanExpression != null)
			{
				return dbScanExpression.Target.Name;
			}
			DbPropertyExpression dbPropertyExpression = expr as DbPropertyExpression;
			if (dbPropertyExpression != null)
			{
				return dbPropertyExpression.Property.Name;
			}
			DbVariableReferenceExpression dbVariableReferenceExpression = expr as DbVariableReferenceExpression;
			if (dbVariableReferenceExpression != null)
			{
				return dbVariableReferenceExpression.VariableName;
			}
			return this.GenerateInternalName(string.Empty);
		}

		// Token: 0x06004ED4 RID: 20180 RVA: 0x0011E2F0 File Offset: 0x0011C4F0
		internal string InferAliasName(AliasedExpr aliasedExpr, DbExpression convertedExpression)
		{
			if (aliasedExpr.Alias != null)
			{
				return aliasedExpr.Alias.Name;
			}
			Identifier identifier = aliasedExpr.Expr as Identifier;
			if (identifier != null)
			{
				return identifier.Name;
			}
			DotExpr dotExpr = aliasedExpr.Expr as DotExpr;
			string[] array;
			if (dotExpr != null && dotExpr.IsMultipartIdentifier(out array))
			{
				return array[array.Length - 1];
			}
			return this.CreateNewAlias(convertedExpression);
		}

		// Token: 0x06004ED5 RID: 20181 RVA: 0x0011E350 File Offset: 0x0011C550
		internal IDisposable EnterScopeRegion()
		{
			this._scopeManager.EnterScope();
			ScopeRegion scopeRegion = new ScopeRegion(this._scopeManager, this.CurrentScopeIndex, this._scopeRegions.Count);
			this._scopeRegions.Add(scopeRegion);
			return new Disposer(delegate
			{
				this.CurrentScopeRegion.GroupAggregateInfos.Each(delegate(GroupAggregateInfo groupAggregateInfo)
				{
					groupAggregateInfo.DetachFromAstNode();
				});
				this.CurrentScopeRegion.RollbackAllScopes();
				this._scopeRegions.Remove(this.CurrentScopeRegion);
			});
		}

		// Token: 0x06004ED6 RID: 20182 RVA: 0x0011E3A2 File Offset: 0x0011C5A2
		internal void RollbackToScope(int scopeIndex)
		{
			this._scopeManager.RollbackToScope(scopeIndex);
		}

		// Token: 0x06004ED7 RID: 20183 RVA: 0x0011E3B0 File Offset: 0x0011C5B0
		internal void EnterScope()
		{
			this._scopeManager.EnterScope();
		}

		// Token: 0x06004ED8 RID: 20184 RVA: 0x0011E3BD File Offset: 0x0011C5BD
		internal void LeaveScope()
		{
			this._scopeManager.LeaveScope();
		}

		// Token: 0x06004ED9 RID: 20185 RVA: 0x0011E3CC File Offset: 0x0011C5CC
		internal bool IsInAnyGroupScope()
		{
			for (int i = 0; i < this._scopeRegions.Count; i++)
			{
				if (this._scopeRegions[i].IsAggregating)
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x06004EDA RID: 20186 RVA: 0x0011E408 File Offset: 0x0011C608
		internal ScopeRegion GetDefiningScopeRegion(int scopeIndex)
		{
			for (int i = this._scopeRegions.Count - 1; i >= 0; i--)
			{
				if (this._scopeRegions[i].ContainsScope(scopeIndex))
				{
					return this._scopeRegions[i];
				}
			}
			return null;
		}

		// Token: 0x06004EDB RID: 20187 RVA: 0x0011E44F File Offset: 0x0011C64F
		private void SetScopeRegionCorrelationFlag(int scopeIndex)
		{
			this.GetDefiningScopeRegion(scopeIndex).WasResolutionCorrelated = true;
		}

		// Token: 0x06004EDC RID: 20188 RVA: 0x0011E45E File Offset: 0x0011C65E
		internal IDisposable EnterFunctionAggregate(MethodExpr methodExpr, ErrorContext errCtx, out FunctionAggregateInfo aggregateInfo)
		{
			aggregateInfo = new FunctionAggregateInfo(methodExpr, errCtx, this._currentGroupAggregateInfo, this.CurrentScopeRegion);
			return this.EnterGroupAggregate(aggregateInfo);
		}

		// Token: 0x06004EDD RID: 20189 RVA: 0x0011E47D File Offset: 0x0011C67D
		internal IDisposable EnterGroupPartition(GroupPartitionExpr groupPartitionExpr, ErrorContext errCtx, out GroupPartitionInfo aggregateInfo)
		{
			aggregateInfo = new GroupPartitionInfo(groupPartitionExpr, errCtx, this._currentGroupAggregateInfo, this.CurrentScopeRegion);
			return this.EnterGroupAggregate(aggregateInfo);
		}

		// Token: 0x06004EDE RID: 20190 RVA: 0x0011E49C File Offset: 0x0011C69C
		internal IDisposable EnterGroupKeyDefinition(GroupAggregateKind aggregateKind, ErrorContext errCtx, out GroupKeyAggregateInfo aggregateInfo)
		{
			aggregateInfo = new GroupKeyAggregateInfo(aggregateKind, errCtx, this._currentGroupAggregateInfo, this.CurrentScopeRegion);
			return this.EnterGroupAggregate(aggregateInfo);
		}

		// Token: 0x06004EDF RID: 20191 RVA: 0x0011E4BC File Offset: 0x0011C6BC
		private IDisposable EnterGroupAggregate(GroupAggregateInfo aggregateInfo)
		{
			this._currentGroupAggregateInfo = aggregateInfo;
			return new Disposer(delegate
			{
				this._currentGroupAggregateInfo = aggregateInfo.ContainingAggregate;
				aggregateInfo.ValidateAndComputeEvaluatingScopeRegion(this);
			});
		}

		// Token: 0x06004EE0 RID: 20192 RVA: 0x0011E4FA File Offset: 0x0011C6FA
		internal static EdmFunction ResolveFunctionOverloads(IList<EdmFunction> functionsMetadata, IList<TypeUsage> argTypes, bool isGroupAggregateFunction, out bool isAmbiguous)
		{
			return FunctionOverloadResolver.ResolveFunctionOverloads(functionsMetadata, argTypes, new Func<TypeUsage, IEnumerable<TypeUsage>>(SemanticResolver.UntypedNullAwareFlattenArgumentType), new Func<TypeUsage, TypeUsage, IEnumerable<TypeUsage>>(SemanticResolver.UntypedNullAwareFlattenParameterType), new Func<TypeUsage, TypeUsage, bool>(SemanticResolver.UntypedNullAwareIsPromotableTo), new Func<TypeUsage, TypeUsage, bool>(SemanticResolver.UntypedNullAwareIsStructurallyEqual), isGroupAggregateFunction, out isAmbiguous);
		}

		// Token: 0x06004EE1 RID: 20193 RVA: 0x0011E538 File Offset: 0x0011C738
		internal static TFunctionMetadata ResolveFunctionOverloads<TFunctionMetadata, TFunctionParameterMetadata>(IList<TFunctionMetadata> functionsMetadata, IList<TypeUsage> argTypes, Func<TFunctionMetadata, IList<TFunctionParameterMetadata>> getSignatureParams, Func<TFunctionParameterMetadata, TypeUsage> getParameterTypeUsage, Func<TFunctionParameterMetadata, ParameterMode> getParameterMode, bool isGroupAggregateFunction, out bool isAmbiguous) where TFunctionMetadata : class
		{
			return FunctionOverloadResolver.ResolveFunctionOverloads<TFunctionMetadata, TFunctionParameterMetadata>(functionsMetadata, argTypes, getSignatureParams, getParameterTypeUsage, getParameterMode, new Func<TypeUsage, IEnumerable<TypeUsage>>(SemanticResolver.UntypedNullAwareFlattenArgumentType), new Func<TypeUsage, TypeUsage, IEnumerable<TypeUsage>>(SemanticResolver.UntypedNullAwareFlattenParameterType), new Func<TypeUsage, TypeUsage, bool>(SemanticResolver.UntypedNullAwareIsPromotableTo), new Func<TypeUsage, TypeUsage, bool>(SemanticResolver.UntypedNullAwareIsStructurallyEqual), isGroupAggregateFunction, out isAmbiguous);
		}

		// Token: 0x06004EE2 RID: 20194 RVA: 0x0011E584 File Offset: 0x0011C784
		private static IEnumerable<TypeUsage> UntypedNullAwareFlattenArgumentType(TypeUsage argType)
		{
			if (argType == null)
			{
				return new TypeUsage[1];
			}
			return TypeSemantics.FlattenType(argType);
		}

		// Token: 0x06004EE3 RID: 20195 RVA: 0x0011E5A4 File Offset: 0x0011C7A4
		private static IEnumerable<TypeUsage> UntypedNullAwareFlattenParameterType(TypeUsage paramType, TypeUsage argType)
		{
			if (argType == null)
			{
				return new TypeUsage[] { paramType };
			}
			return TypeSemantics.FlattenType(paramType);
		}

		// Token: 0x06004EE4 RID: 20196 RVA: 0x0011E5C7 File Offset: 0x0011C7C7
		private static bool UntypedNullAwareIsPromotableTo(TypeUsage fromType, TypeUsage toType)
		{
			if (fromType == null)
			{
				return !Helper.IsCollectionType(toType.EdmType);
			}
			return TypeSemantics.IsPromotableTo(fromType, toType);
		}

		// Token: 0x06004EE5 RID: 20197 RVA: 0x0011E5E2 File Offset: 0x0011C7E2
		private static bool UntypedNullAwareIsStructurallyEqual(TypeUsage fromType, TypeUsage toType)
		{
			if (fromType == null)
			{
				return SemanticResolver.UntypedNullAwareIsPromotableTo(fromType, toType);
			}
			return TypeSemantics.IsStructurallyEqual(fromType, toType);
		}

		// Token: 0x04001C77 RID: 7287
		private readonly ParserOptions _parserOptions;

		// Token: 0x04001C78 RID: 7288
		private readonly Dictionary<string, DbParameterReferenceExpression> _parameters;

		// Token: 0x04001C79 RID: 7289
		private readonly Dictionary<string, DbVariableReferenceExpression> _variables;

		// Token: 0x04001C7A RID: 7290
		private readonly TypeResolver _typeResolver;

		// Token: 0x04001C7B RID: 7291
		private readonly ScopeManager _scopeManager;

		// Token: 0x04001C7C RID: 7292
		private readonly List<ScopeRegion> _scopeRegions = new List<ScopeRegion>();

		// Token: 0x04001C7D RID: 7293
		private bool _ignoreEntityContainerNameResolution;

		// Token: 0x04001C7E RID: 7294
		private GroupAggregateInfo _currentGroupAggregateInfo;

		// Token: 0x04001C7F RID: 7295
		private uint _namegenCounter;
	}
}
