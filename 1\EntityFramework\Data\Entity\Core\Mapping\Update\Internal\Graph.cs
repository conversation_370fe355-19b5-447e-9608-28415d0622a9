﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.Update.Internal
{
	// Token: 0x020005C9 RID: 1481
	internal class Graph<TVertex>
	{
		// Token: 0x060047A7 RID: 18343 RVA: 0x000FCEF8 File Offset: 0x000FB0F8
		internal Graph(IEqualityComparer<TVertex> comparer)
		{
			this.m_comparer = comparer;
			this.m_successorMap = new Dictionary<TVertex, HashSet<TVertex>>(comparer);
			this.m_predecessorCounts = new Dictionary<TVertex, int>(comparer);
			this.m_vertices = new HashSet<TVertex>(comparer);
		}

		// Token: 0x17000E29 RID: 3625
		// (get) Token: 0x060047A8 RID: 18344 RVA: 0x000FCF2B File Offset: 0x000FB12B
		internal IEnumerable<TVertex> Vertices
		{
			get
			{
				return this.m_vertices;
			}
		}

		// Token: 0x17000E2A RID: 3626
		// (get) Token: 0x060047A9 RID: 18345 RVA: 0x000FCF33 File Offset: 0x000FB133
		internal IEnumerable<KeyValuePair<TVertex, TVertex>> Edges
		{
			get
			{
				foreach (KeyValuePair<TVertex, HashSet<TVertex>> successors in this.m_successorMap)
				{
					foreach (TVertex tvertex in successors.Value)
					{
						yield return new KeyValuePair<TVertex, TVertex>(successors.Key, tvertex);
					}
					HashSet<TVertex>.Enumerator enumerator2 = default(HashSet<TVertex>.Enumerator);
					successors = default(KeyValuePair<TVertex, HashSet<TVertex>>);
				}
				Dictionary<TVertex, HashSet<TVertex>>.Enumerator enumerator = default(Dictionary<TVertex, HashSet<TVertex>>.Enumerator);
				yield break;
				yield break;
			}
		}

		// Token: 0x060047AA RID: 18346 RVA: 0x000FCF43 File Offset: 0x000FB143
		internal void AddVertex(TVertex vertex)
		{
			this.m_vertices.Add(vertex);
		}

		// Token: 0x060047AB RID: 18347 RVA: 0x000FCF54 File Offset: 0x000FB154
		internal void AddEdge(TVertex from, TVertex to)
		{
			if (this.m_vertices.Contains(from) && this.m_vertices.Contains(to))
			{
				HashSet<TVertex> hashSet;
				if (!this.m_successorMap.TryGetValue(from, out hashSet))
				{
					hashSet = new HashSet<TVertex>(this.m_comparer);
					this.m_successorMap.Add(from, hashSet);
				}
				if (hashSet.Add(to))
				{
					int num;
					if (!this.m_predecessorCounts.TryGetValue(to, out num))
					{
						num = 1;
					}
					else
					{
						num++;
					}
					this.m_predecessorCounts[to] = num;
				}
			}
		}

		// Token: 0x060047AC RID: 18348 RVA: 0x000FCFD4 File Offset: 0x000FB1D4
		internal bool TryTopologicalSort(out IEnumerable<TVertex> orderedVertices, out IEnumerable<TVertex> remainder)
		{
			SortedSet<TVertex> sortedSet = new SortedSet<TVertex>(Comparer<TVertex>.Default);
			foreach (TVertex tvertex in this.m_vertices)
			{
				int num;
				if (!this.m_predecessorCounts.TryGetValue(tvertex, out num) || num == 0)
				{
					sortedSet.Add(tvertex);
				}
			}
			TVertex[] array = new TVertex[this.m_vertices.Count];
			int num2 = 0;
			while (0 < sortedSet.Count)
			{
				TVertex min = sortedSet.Min;
				sortedSet.Remove(min);
				HashSet<TVertex> hashSet;
				if (this.m_successorMap.TryGetValue(min, out hashSet))
				{
					foreach (TVertex tvertex2 in hashSet)
					{
						int num3 = this.m_predecessorCounts[tvertex2] - 1;
						this.m_predecessorCounts[tvertex2] = num3;
						if (num3 == 0)
						{
							sortedSet.Add(tvertex2);
						}
					}
					this.m_successorMap.Remove(min);
				}
				array[num2++] = min;
				this.m_vertices.Remove(min);
			}
			if (this.m_vertices.Count == 0)
			{
				orderedVertices = array;
				remainder = Enumerable.Empty<TVertex>();
				return true;
			}
			orderedVertices = array.Take(num2);
			remainder = this.m_vertices;
			return false;
		}

		// Token: 0x060047AD RID: 18349 RVA: 0x000FD14C File Offset: 0x000FB34C
		public override string ToString()
		{
			StringBuilder stringBuilder = new StringBuilder();
			foreach (KeyValuePair<TVertex, HashSet<TVertex>> keyValuePair in this.m_successorMap)
			{
				bool flag = true;
				stringBuilder.AppendFormat(CultureInfo.InvariantCulture, "[{0}] --> ", new object[] { keyValuePair.Key });
				foreach (TVertex tvertex in keyValuePair.Value)
				{
					if (flag)
					{
						flag = false;
					}
					else
					{
						stringBuilder.Append(", ");
					}
					stringBuilder.AppendFormat(CultureInfo.InvariantCulture, "[{0}]", new object[] { tvertex });
				}
				stringBuilder.Append("; ");
			}
			return stringBuilder.ToString();
		}

		// Token: 0x04001970 RID: 6512
		private readonly Dictionary<TVertex, HashSet<TVertex>> m_successorMap;

		// Token: 0x04001971 RID: 6513
		private readonly Dictionary<TVertex, int> m_predecessorCounts;

		// Token: 0x04001972 RID: 6514
		private readonly HashSet<TVertex> m_vertices;

		// Token: 0x04001973 RID: 6515
		private readonly IEqualityComparer<TVertex> m_comparer;
	}
}
