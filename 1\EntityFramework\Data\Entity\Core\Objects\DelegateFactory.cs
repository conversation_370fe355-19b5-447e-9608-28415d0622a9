﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects.DataClasses;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace System.Data.Entity.Core.Objects
{
	// Token: 0x02000406 RID: 1030
	internal static class DelegateFactory
	{
		// Token: 0x06003025 RID: 12325 RVA: 0x00096F6C File Offset: 0x0009516C
		internal static Func<object> GetConstructorDelegateForType(ClrComplexType clrType)
		{
			Func<object> func;
			if ((func = clrType.Constructor) == null)
			{
				func = (clrType.Constructor = DelegateFactory.CreateConstructor(clrType.ClrType));
			}
			return func;
		}

		// Token: 0x06003026 RID: 12326 RVA: 0x00096F98 File Offset: 0x00095198
		internal static Func<object> GetConstructorDelegateForType(ClrEntityType clrType)
		{
			Func<object> func;
			if ((func = clrType.Constructor) == null)
			{
				func = (clrType.Constructor = DelegateFactory.CreateConstructor(clrType.ClrType));
			}
			return func;
		}

		// Token: 0x06003027 RID: 12327 RVA: 0x00096FC3 File Offset: 0x000951C3
		internal static object GetValue(EdmProperty property, object target)
		{
			return DelegateFactory.GetGetterDelegateForProperty(property)(target);
		}

		// Token: 0x06003028 RID: 12328 RVA: 0x00096FD4 File Offset: 0x000951D4
		internal static Func<object, object> GetGetterDelegateForProperty(EdmProperty property)
		{
			Func<object, object> func;
			if ((func = property.ValueGetter) == null)
			{
				func = (property.ValueGetter = DelegateFactory.CreatePropertyGetter(property.EntityDeclaringType, property.PropertyInfo));
			}
			return func;
		}

		// Token: 0x06003029 RID: 12329 RVA: 0x00097005 File Offset: 0x00095205
		internal static void SetValue(EdmProperty property, object target, object value)
		{
			DelegateFactory.GetSetterDelegateForProperty(property)(target, value);
		}

		// Token: 0x0600302A RID: 12330 RVA: 0x00097014 File Offset: 0x00095214
		internal static Action<object, object> GetSetterDelegateForProperty(EdmProperty property)
		{
			Action<object, object> action = property.ValueSetter;
			if (action == null)
			{
				action = DelegateFactory.CreatePropertySetter(property.EntityDeclaringType, property.PropertyInfo, property.Nullable);
				property.ValueSetter = action;
			}
			return action;
		}

		// Token: 0x0600302B RID: 12331 RVA: 0x0009704C File Offset: 0x0009524C
		internal static RelatedEnd GetRelatedEnd(RelationshipManager sourceRelationshipManager, AssociationEndMember sourceMember, AssociationEndMember targetMember, RelatedEnd existingRelatedEnd)
		{
			Func<RelationshipManager, RelatedEnd, RelatedEnd> func = sourceMember.GetRelatedEnd;
			if (func == null)
			{
				func = DelegateFactory.CreateGetRelatedEndMethod(sourceMember, targetMember);
				sourceMember.GetRelatedEnd = func;
			}
			return func(sourceRelationshipManager, existingRelatedEnd);
		}

		// Token: 0x0600302C RID: 12332 RVA: 0x0009707C File Offset: 0x0009527C
		internal static Action<object, object> CreateNavigationPropertySetter(Type declaringType, PropertyInfo navigationProperty)
		{
			PropertyInfo propertyInfoForSet = navigationProperty.GetPropertyInfoForSet();
			MethodInfo methodInfo = propertyInfoForSet.Setter();
			if (methodInfo == null)
			{
				throw new InvalidOperationException(Strings.CodeGen_PropertyNoSetter);
			}
			if (methodInfo.IsStatic)
			{
				throw new InvalidOperationException(Strings.CodeGen_PropertyIsStatic);
			}
			if (methodInfo.DeclaringType.IsValueType())
			{
				throw new InvalidOperationException(Strings.CodeGen_PropertyDeclaringTypeIsValueType);
			}
			ParameterExpression parameterExpression;
			ParameterExpression parameterExpression2;
			return Expression.Lambda<Action<object, object>>(Expression.Assign(Expression.Property(Expression.Convert(parameterExpression, declaringType), propertyInfoForSet), Expression.Convert(parameterExpression2, navigationProperty.PropertyType)), new ParameterExpression[] { parameterExpression, parameterExpression2 }).Compile();
		}

		// Token: 0x0600302D RID: 12333 RVA: 0x00097134 File Offset: 0x00095334
		internal static ConstructorInfo GetConstructorForType(Type type)
		{
			ConstructorInfo declaredConstructor = type.GetDeclaredConstructor(new Type[0]);
			if (null == declaredConstructor)
			{
				throw new InvalidOperationException(Strings.CodeGen_ConstructorNoParameterless(type.FullName));
			}
			return declaredConstructor;
		}

		// Token: 0x0600302E RID: 12334 RVA: 0x0009716C File Offset: 0x0009536C
		internal static NewExpression GetNewExpressionForCollectionType(Type type)
		{
			if (type.IsGenericType() && type.GetGenericTypeDefinition() == typeof(HashSet<>))
			{
				return Expression.New(type.GetDeclaredConstructor(new Type[] { typeof(IEqualityComparer<>).MakeGenericType(type.GetGenericArguments()) }), new Expression[] { Expression.New(typeof(ObjectReferenceEqualityComparer)) });
			}
			return Expression.New(DelegateFactory.GetConstructorForType(type));
		}

		// Token: 0x0600302F RID: 12335 RVA: 0x000971E5 File Offset: 0x000953E5
		internal static Func<object> CreateConstructor(Type type)
		{
			DelegateFactory.GetConstructorForType(type);
			return Expression.Lambda<Func<object>>(Expression.New(type), new ParameterExpression[0]).Compile();
		}

		// Token: 0x06003030 RID: 12336 RVA: 0x00097204 File Offset: 0x00095404
		internal static Func<object, object> CreatePropertyGetter(Type entityDeclaringType, PropertyInfo propertyInfo)
		{
			MethodInfo methodInfo = propertyInfo.Getter();
			if (methodInfo == null)
			{
				throw new InvalidOperationException(Strings.CodeGen_PropertyNoGetter);
			}
			if (methodInfo.IsStatic)
			{
				throw new InvalidOperationException(Strings.CodeGen_PropertyIsStatic);
			}
			if (propertyInfo.DeclaringType.IsValueType())
			{
				throw new InvalidOperationException(Strings.CodeGen_PropertyDeclaringTypeIsValueType);
			}
			if (propertyInfo.GetIndexParameters().Any<ParameterInfo>())
			{
				throw new InvalidOperationException(Strings.CodeGen_PropertyIsIndexed);
			}
			Type propertyType = propertyInfo.PropertyType;
			if (propertyType.IsPointer)
			{
				throw new InvalidOperationException(Strings.CodeGen_PropertyUnsupportedType);
			}
			ParameterExpression parameterExpression = Expression.Parameter(typeof(object), "entity");
			Expression expression = Expression.Property(Expression.Convert(parameterExpression, entityDeclaringType), propertyInfo);
			if (propertyType.IsValueType())
			{
				expression = Expression.Convert(expression, typeof(object));
			}
			return Expression.Lambda<Func<object, object>>(expression, new ParameterExpression[] { parameterExpression }).Compile();
		}

		// Token: 0x06003031 RID: 12337 RVA: 0x000972D8 File Offset: 0x000954D8
		internal static Action<object, object> CreatePropertySetter(Type entityDeclaringType, PropertyInfo propertyInfo, bool allowNull)
		{
			PropertyInfo propertyInfo2 = DelegateFactory.ValidateSetterProperty(propertyInfo);
			ParameterExpression parameterExpression = Expression.Parameter(typeof(object), "entity");
			ParameterExpression parameterExpression2 = Expression.Parameter(typeof(object), "target");
			Type propertyType = propertyInfo.PropertyType;
			if (propertyType.IsValueType() && Nullable.GetUnderlyingType(propertyType) == null)
			{
				allowNull = false;
			}
			Expression expression = Expression.TypeIs(parameterExpression2, propertyType);
			if (allowNull)
			{
				expression = Expression.Or(Expression.ReferenceEqual(parameterExpression2, Expression.Constant(null)), expression);
			}
			return Expression.Lambda<Action<object, object>>(Expression.IfThenElse(expression, Expression.Assign(Expression.Property(Expression.Convert(parameterExpression, entityDeclaringType), propertyInfo2), Expression.Convert(parameterExpression2, propertyInfo.PropertyType)), Expression.Call(DelegateFactory._throwSetInvalidValue, parameterExpression2, Expression.Constant(propertyType), Expression.Constant(entityDeclaringType.Name), Expression.Constant(propertyInfo.Name))), new ParameterExpression[] { parameterExpression, parameterExpression2 }).Compile();
		}

		// Token: 0x06003032 RID: 12338 RVA: 0x000973BC File Offset: 0x000955BC
		internal static PropertyInfo ValidateSetterProperty(PropertyInfo propertyInfo)
		{
			PropertyInfo propertyInfoForSet = propertyInfo.GetPropertyInfoForSet();
			MethodInfo methodInfo = propertyInfoForSet.Setter();
			if (methodInfo == null)
			{
				throw new InvalidOperationException(Strings.CodeGen_PropertyNoSetter);
			}
			if (methodInfo.IsStatic)
			{
				throw new InvalidOperationException(Strings.CodeGen_PropertyIsStatic);
			}
			if (propertyInfoForSet.DeclaringType.IsValueType())
			{
				throw new InvalidOperationException(Strings.CodeGen_PropertyDeclaringTypeIsValueType);
			}
			if (propertyInfoForSet.GetIndexParameters().Any<ParameterInfo>())
			{
				throw new InvalidOperationException(Strings.CodeGen_PropertyIsIndexed);
			}
			if (propertyInfoForSet.PropertyType.IsPointer)
			{
				throw new InvalidOperationException(Strings.CodeGen_PropertyUnsupportedType);
			}
			return propertyInfoForSet;
		}

		// Token: 0x06003033 RID: 12339 RVA: 0x00097444 File Offset: 0x00095644
		private static Func<RelationshipManager, RelatedEnd, RelatedEnd> CreateGetRelatedEndMethod(AssociationEndMember sourceMember, AssociationEndMember targetMember)
		{
			EntityType entityTypeForEnd = MetadataHelper.GetEntityTypeForEnd(sourceMember);
			EntityType entityTypeForEnd2 = MetadataHelper.GetEntityTypeForEnd(targetMember);
			NavigationPropertyAccessor navigationPropertyAccessor = MetadataHelper.GetNavigationPropertyAccessor(entityTypeForEnd2, targetMember, sourceMember);
			NavigationPropertyAccessor navigationPropertyAccessor2 = MetadataHelper.GetNavigationPropertyAccessor(entityTypeForEnd, sourceMember, targetMember);
			return (Func<RelationshipManager, RelatedEnd, RelatedEnd>)typeof(DelegateFactory).GetDeclaredMethod("CreateGetRelatedEndMethod", new Type[]
			{
				typeof(AssociationEndMember),
				typeof(AssociationEndMember),
				typeof(NavigationPropertyAccessor),
				typeof(NavigationPropertyAccessor)
			}).MakeGenericMethod(new Type[] { entityTypeForEnd.ClrType, entityTypeForEnd2.ClrType }).Invoke(null, new object[] { sourceMember, targetMember, navigationPropertyAccessor, navigationPropertyAccessor2 });
		}

		// Token: 0x06003034 RID: 12340 RVA: 0x00097500 File Offset: 0x00095700
		private static Func<RelationshipManager, RelatedEnd, RelatedEnd> CreateGetRelatedEndMethod<TSource, TTarget>(AssociationEndMember sourceMember, AssociationEndMember targetMember, NavigationPropertyAccessor sourceAccessor, NavigationPropertyAccessor targetAccessor) where TSource : class where TTarget : class
		{
			RelationshipMultiplicity relationshipMultiplicity = targetMember.RelationshipMultiplicity;
			Func<RelationshipManager, RelatedEnd, RelatedEnd> func;
			if (relationshipMultiplicity > RelationshipMultiplicity.One)
			{
				if (relationshipMultiplicity != RelationshipMultiplicity.Many)
				{
					Type typeFromHandle = typeof(RelationshipMultiplicity);
					throw new ArgumentOutOfRangeException(typeFromHandle.Name, Strings.ADP_InvalidEnumerationValue(typeFromHandle.Name, ((int)targetMember.RelationshipMultiplicity).ToString(CultureInfo.InvariantCulture)));
				}
				func = (RelationshipManager manager, RelatedEnd relatedEnd) => manager.GetRelatedCollection<TSource, TTarget>(sourceMember, targetMember, sourceAccessor, targetAccessor, relatedEnd);
			}
			else
			{
				func = (RelationshipManager manager, RelatedEnd relatedEnd) => manager.GetRelatedReference<TSource, TTarget>(sourceMember, targetMember, sourceAccessor, targetAccessor, relatedEnd);
			}
			return func;
		}

		// Token: 0x04001023 RID: 4131
		private static readonly MethodInfo _throwSetInvalidValue = typeof(EntityUtil).GetDeclaredMethod("ThrowSetInvalidValue", new Type[]
		{
			typeof(object),
			typeof(Type),
			typeof(string),
			typeof(string)
		});
	}
}
