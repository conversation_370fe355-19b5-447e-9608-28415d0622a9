﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Mapping.Update.Internal
{
	// Token: 0x020005D2 RID: 1490
	internal class TableChangeProcessor
	{
		// Token: 0x060047EF RID: 18415 RVA: 0x000FE06E File Offset: 0x000FC26E
		internal TableChangeProcessor(EntitySet table)
		{
			this.m_table = table;
			this.m_keyOrdinals = TableChangeProcessor.InitializeKeyOrdinals(table);
		}

		// Token: 0x060047F0 RID: 18416 RVA: 0x000FE089 File Offset: 0x000FC289
		protected TableChangeProcessor()
		{
		}

		// Token: 0x17000E36 RID: 3638
		// (get) Token: 0x060047F1 RID: 18417 RVA: 0x000FE091 File Offset: 0x000FC291
		internal EntitySet Table
		{
			get
			{
				return this.m_table;
			}
		}

		// Token: 0x17000E37 RID: 3639
		// (get) Token: 0x060047F2 RID: 18418 RVA: 0x000FE099 File Offset: 0x000FC299
		internal int[] KeyOrdinals
		{
			get
			{
				return this.m_keyOrdinals;
			}
		}

		// Token: 0x060047F3 RID: 18419 RVA: 0x000FE0A4 File Offset: 0x000FC2A4
		internal bool IsKeyProperty(int propertyOrdinal)
		{
			foreach (int num in this.m_keyOrdinals)
			{
				if (propertyOrdinal == num)
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x060047F4 RID: 18420 RVA: 0x000FE0D4 File Offset: 0x000FC2D4
		private static int[] InitializeKeyOrdinals(EntitySet table)
		{
			EntityType elementType = table.ElementType;
			IList<EdmMember> keyMembers = elementType.KeyMembers;
			IBaseList<EdmMember> allStructuralMembers = TypeHelpers.GetAllStructuralMembers(elementType);
			int[] array = new int[keyMembers.Count];
			for (int i = 0; i < keyMembers.Count; i++)
			{
				EdmMember edmMember = keyMembers[i];
				array[i] = allStructuralMembers.IndexOf(edmMember);
			}
			return array;
		}

		// Token: 0x060047F5 RID: 18421 RVA: 0x000FE128 File Offset: 0x000FC328
		internal List<UpdateCommand> CompileCommands(ChangeNode changeNode, UpdateCompiler compiler)
		{
			Set<CompositeKey> set = new Set<CompositeKey>(compiler.m_translator.KeyComparer);
			Dictionary<CompositeKey, PropagatorResult> dictionary = this.ProcessKeys(compiler, changeNode.Deleted, set);
			Dictionary<CompositeKey, PropagatorResult> dictionary2 = this.ProcessKeys(compiler, changeNode.Inserted, set);
			List<UpdateCommand> list = new List<UpdateCommand>(dictionary.Count + dictionary2.Count);
			foreach (CompositeKey compositeKey in set)
			{
				PropagatorResult propagatorResult;
				bool flag = dictionary.TryGetValue(compositeKey, out propagatorResult);
				PropagatorResult propagatorResult2;
				bool flag2 = dictionary2.TryGetValue(compositeKey, out propagatorResult2);
				try
				{
					if (!flag)
					{
						list.Add(compiler.BuildInsertCommand(propagatorResult2, this));
					}
					else if (!flag2)
					{
						list.Add(compiler.BuildDeleteCommand(propagatorResult, this));
					}
					else
					{
						UpdateCommand updateCommand = compiler.BuildUpdateCommand(propagatorResult, propagatorResult2, this);
						if (updateCommand != null)
						{
							list.Add(updateCommand);
						}
					}
				}
				catch (Exception ex)
				{
					if (ex.RequiresContext())
					{
						List<IEntityStateEntry> list2 = new List<IEntityStateEntry>();
						if (propagatorResult != null)
						{
							list2.AddRange(SourceInterpreter.GetAllStateEntries(propagatorResult, compiler.m_translator, this.m_table));
						}
						if (propagatorResult2 != null)
						{
							list2.AddRange(SourceInterpreter.GetAllStateEntries(propagatorResult2, compiler.m_translator, this.m_table));
						}
						throw new UpdateException(Strings.Update_GeneralExecutionException, ex, list2.Cast<ObjectStateEntry>().Distinct<ObjectStateEntry>());
					}
					throw;
				}
			}
			return list;
		}

		// Token: 0x060047F6 RID: 18422 RVA: 0x000FE28C File Offset: 0x000FC48C
		private Dictionary<CompositeKey, PropagatorResult> ProcessKeys(UpdateCompiler compiler, List<PropagatorResult> changes, Set<CompositeKey> keys)
		{
			Dictionary<CompositeKey, PropagatorResult> dictionary = new Dictionary<CompositeKey, PropagatorResult>(compiler.m_translator.KeyComparer);
			foreach (PropagatorResult propagatorResult in changes)
			{
				PropagatorResult propagatorResult2 = propagatorResult;
				CompositeKey compositeKey = new CompositeKey(this.GetKeyConstants(propagatorResult2));
				PropagatorResult propagatorResult3;
				if (dictionary.TryGetValue(compositeKey, out propagatorResult3))
				{
					this.DiagnoseKeyCollision(compiler, propagatorResult, compositeKey, propagatorResult3);
				}
				dictionary.Add(compositeKey, propagatorResult2);
				keys.Add(compositeKey);
			}
			return dictionary;
		}

		// Token: 0x060047F7 RID: 18423 RVA: 0x000FE320 File Offset: 0x000FC520
		private void DiagnoseKeyCollision(UpdateCompiler compiler, PropagatorResult change, CompositeKey key, PropagatorResult other)
		{
			KeyManager keyManager = compiler.m_translator.KeyManager;
			CompositeKey compositeKey = new CompositeKey(this.GetKeyConstants(other));
			bool flag = true;
			int num = 0;
			while (flag && num < key.KeyComponents.Length)
			{
				int identifier = key.KeyComponents[num].Identifier;
				int identifier2 = compositeKey.KeyComponents[num].Identifier;
				if (!keyManager.GetPrincipals(identifier).Intersect(keyManager.GetPrincipals(identifier2)).Any<int>())
				{
					flag = false;
				}
				num++;
			}
			if (flag)
			{
				IEnumerable<IEntityStateEntry> enumerable = SourceInterpreter.GetAllStateEntries(change, compiler.m_translator, this.m_table).Concat(SourceInterpreter.GetAllStateEntries(other, compiler.m_translator, this.m_table));
				throw new UpdateException(Strings.Update_DuplicateKeys, null, enumerable.Cast<ObjectStateEntry>().Distinct<ObjectStateEntry>());
			}
			HashSet<IEntityStateEntry> hashSet = null;
			foreach (PropagatorResult propagatorResult in key.KeyComponents.Concat(compositeKey.KeyComponents))
			{
				HashSet<IEntityStateEntry> hashSet2 = new HashSet<IEntityStateEntry>();
				foreach (int num2 in keyManager.GetDependents(propagatorResult.Identifier))
				{
					PropagatorResult propagatorResult2;
					if (keyManager.TryGetIdentifierOwner(num2, out propagatorResult2) && propagatorResult2.StateEntry != null)
					{
						hashSet2.Add(propagatorResult2.StateEntry);
					}
				}
				if (hashSet == null)
				{
					hashSet = new HashSet<IEntityStateEntry>(hashSet2);
				}
				else
				{
					hashSet.IntersectWith(hashSet2);
				}
			}
			throw new UpdateException(Strings.Update_GeneralExecutionException, new ConstraintException(Strings.Update_ReferentialConstraintIntegrityViolation), hashSet.Cast<ObjectStateEntry>().Distinct<ObjectStateEntry>());
		}

		// Token: 0x060047F8 RID: 18424 RVA: 0x000FE4DC File Offset: 0x000FC6DC
		private PropagatorResult[] GetKeyConstants(PropagatorResult row)
		{
			PropagatorResult[] array = new PropagatorResult[this.m_keyOrdinals.Length];
			for (int i = 0; i < this.m_keyOrdinals.Length; i++)
			{
				PropagatorResult memberValue = row.GetMemberValue(this.m_keyOrdinals[i]);
				array[i] = memberValue;
			}
			return array;
		}

		// Token: 0x04001993 RID: 6547
		private readonly EntitySet m_table;

		// Token: 0x04001994 RID: 6548
		private readonly int[] m_keyOrdinals;
	}
}
