﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Text;

namespace System.Data.Entity.Core.Objects
{
	// Token: 0x02000419 RID: 1049
	public class ObjectParameterCollection : ICollection<ObjectParameter>, IEnumerable<ObjectParameter>, IEnumerable
	{
		// Token: 0x06003242 RID: 12866 RVA: 0x000A0828 File Offset: 0x0009EA28
		internal ObjectParameterCollection(ClrPerspective perspective)
		{
			this._perspective = perspective;
			this._parameters = new List<ObjectParameter>();
		}

		// Token: 0x170009B6 RID: 2486
		// (get) Token: 0x06003243 RID: 12867 RVA: 0x000A0842 File Offset: 0x0009EA42
		public int Count
		{
			get
			{
				return this._parameters.Count;
			}
		}

		// Token: 0x170009B7 RID: 2487
		// (get) Token: 0x06003244 RID: 12868 RVA: 0x000A084F File Offset: 0x0009EA4F
		bool ICollection<ObjectParameter>.IsReadOnly
		{
			get
			{
				return this._locked;
			}
		}

		// Token: 0x170009B8 RID: 2488
		public ObjectParameter this[string name]
		{
			get
			{
				int num = this.IndexOf(name);
				if (num == -1)
				{
					throw new ArgumentOutOfRangeException("name", Strings.ObjectParameterCollection_ParameterNameNotFound(name));
				}
				return this._parameters[num];
			}
		}

		// Token: 0x06003246 RID: 12870 RVA: 0x000A0890 File Offset: 0x0009EA90
		public void Add(ObjectParameter item)
		{
			Check.NotNull<ObjectParameter>(item, "item");
			this.CheckUnlocked();
			if (this.Contains(item))
			{
				throw new ArgumentException(Strings.ObjectParameterCollection_ParameterAlreadyExists(item.Name), "item");
			}
			if (this.Contains(item.Name))
			{
				throw new ArgumentException(Strings.ObjectParameterCollection_DuplicateParameterName(item.Name), "item");
			}
			if (!item.ValidateParameterType(this._perspective))
			{
				throw new ArgumentOutOfRangeException("item", Strings.ObjectParameter_InvalidParameterType(item.ParameterType.FullName));
			}
			this._parameters.Add(item);
			this._cacheKey = null;
		}

		// Token: 0x06003247 RID: 12871 RVA: 0x000A092E File Offset: 0x0009EB2E
		public void Clear()
		{
			this.CheckUnlocked();
			this._parameters.Clear();
			this._cacheKey = null;
		}

		// Token: 0x06003248 RID: 12872 RVA: 0x000A0948 File Offset: 0x0009EB48
		public bool Contains(ObjectParameter item)
		{
			Check.NotNull<ObjectParameter>(item, "item");
			return this._parameters.Contains(item);
		}

		// Token: 0x06003249 RID: 12873 RVA: 0x000A0962 File Offset: 0x0009EB62
		public bool Contains(string name)
		{
			Check.NotNull<string>(name, "name");
			return this.IndexOf(name) != -1;
		}

		// Token: 0x0600324A RID: 12874 RVA: 0x000A097D File Offset: 0x0009EB7D
		public void CopyTo(ObjectParameter[] array, int arrayIndex)
		{
			this._parameters.CopyTo(array, arrayIndex);
		}

		// Token: 0x0600324B RID: 12875 RVA: 0x000A098C File Offset: 0x0009EB8C
		public bool Remove(ObjectParameter item)
		{
			Check.NotNull<ObjectParameter>(item, "item");
			this.CheckUnlocked();
			bool flag = this._parameters.Remove(item);
			if (flag)
			{
				this._cacheKey = null;
			}
			return flag;
		}

		// Token: 0x0600324C RID: 12876 RVA: 0x000A09B6 File Offset: 0x0009EBB6
		public virtual IEnumerator<ObjectParameter> GetEnumerator()
		{
			return ((IEnumerable<ObjectParameter>)this._parameters).GetEnumerator();
		}

		// Token: 0x0600324D RID: 12877 RVA: 0x000A09C3 File Offset: 0x0009EBC3
		IEnumerator IEnumerable.GetEnumerator()
		{
			return ((IEnumerable)this._parameters).GetEnumerator();
		}

		// Token: 0x0600324E RID: 12878 RVA: 0x000A09D0 File Offset: 0x0009EBD0
		internal string GetCacheKey()
		{
			if (this._cacheKey == null && this._parameters.Count > 0)
			{
				if (1 == this._parameters.Count)
				{
					ObjectParameter objectParameter = this._parameters[0];
					this._cacheKey = "@@1" + objectParameter.Name + ":" + objectParameter.ParameterType.FullName;
				}
				else
				{
					StringBuilder stringBuilder = new StringBuilder(this._parameters.Count * 20);
					stringBuilder.Append("@@");
					stringBuilder.Append(this._parameters.Count);
					for (int i = 0; i < this._parameters.Count; i++)
					{
						if (i > 0)
						{
							stringBuilder.Append(";");
						}
						ObjectParameter objectParameter2 = this._parameters[i];
						stringBuilder.Append(objectParameter2.Name);
						stringBuilder.Append(":");
						stringBuilder.Append(objectParameter2.ParameterType.FullName);
					}
					this._cacheKey = stringBuilder.ToString();
				}
			}
			return this._cacheKey;
		}

		// Token: 0x0600324F RID: 12879 RVA: 0x000A0AE1 File Offset: 0x0009ECE1
		internal void SetReadOnly(bool isReadOnly)
		{
			this._locked = isReadOnly;
		}

		// Token: 0x06003250 RID: 12880 RVA: 0x000A0AEC File Offset: 0x0009ECEC
		internal static ObjectParameterCollection DeepCopy(ObjectParameterCollection copyParams)
		{
			if (copyParams == null)
			{
				return null;
			}
			ObjectParameterCollection objectParameterCollection = new ObjectParameterCollection(copyParams._perspective);
			foreach (ObjectParameter objectParameter in copyParams)
			{
				objectParameterCollection.Add(objectParameter.ShallowCopy());
			}
			return objectParameterCollection;
		}

		// Token: 0x06003251 RID: 12881 RVA: 0x000A0B4C File Offset: 0x0009ED4C
		private int IndexOf(string name)
		{
			int num = 0;
			foreach (ObjectParameter objectParameter in this._parameters)
			{
				if (string.Compare(name, objectParameter.Name, StringComparison.OrdinalIgnoreCase) == 0)
				{
					return num;
				}
				num++;
			}
			return -1;
		}

		// Token: 0x06003252 RID: 12882 RVA: 0x000A0BB4 File Offset: 0x0009EDB4
		private void CheckUnlocked()
		{
			if (this._locked)
			{
				throw new InvalidOperationException(Strings.ObjectParameterCollection_ParametersLocked);
			}
		}

		// Token: 0x0400107B RID: 4219
		private bool _locked;

		// Token: 0x0400107C RID: 4220
		private readonly List<ObjectParameter> _parameters;

		// Token: 0x0400107D RID: 4221
		private readonly ClrPerspective _perspective;

		// Token: 0x0400107E RID: 4222
		private string _cacheKey;
	}
}
