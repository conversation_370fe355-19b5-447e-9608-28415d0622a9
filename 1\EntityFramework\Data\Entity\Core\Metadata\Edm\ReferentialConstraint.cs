﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004F0 RID: 1264
	public sealed class ReferentialConstraint : MetadataItem
	{
		// Token: 0x06003EE7 RID: 16103 RVA: 0x000D0630 File Offset: 0x000CE830
		public ReferentialConstraint(RelationshipEndMember fromRole, RelationshipEndMember toRole, IEnumerable<EdmProperty> fromProperties, IEnumerable<EdmProperty> toProperties)
		{
			Check.NotNull<RelationshipEndMember>(fromRole, "fromRole");
			Check.NotNull<RelationshipEndMember>(toRole, "toRole");
			Check.NotNull<IEnumerable<EdmProperty>>(fromProperties, "fromProperties");
			Check.NotNull<IEnumerable<EdmProperty>>(toProperties, "toProperties");
			this._fromRole = fromRole;
			this._toRole = toRole;
			this._fromProperties = new ReadOnlyMetadataCollection<EdmProperty>(new MetadataCollection<EdmProperty>(fromProperties));
			this._toProperties = new ReadOnlyMetadataCollection<EdmProperty>(new MetadataCollection<EdmProperty>(toProperties));
		}

		// Token: 0x17000C54 RID: 3156
		// (get) Token: 0x06003EE8 RID: 16104 RVA: 0x000D06A5 File Offset: 0x000CE8A5
		public override BuiltInTypeKind BuiltInTypeKind
		{
			get
			{
				return BuiltInTypeKind.ReferentialConstraint;
			}
		}

		// Token: 0x17000C55 RID: 3157
		// (get) Token: 0x06003EE9 RID: 16105 RVA: 0x000D06A9 File Offset: 0x000CE8A9
		internal override string Identity
		{
			get
			{
				return this.FromRole.Name + "_" + this.ToRole.Name;
			}
		}

		// Token: 0x17000C56 RID: 3158
		// (get) Token: 0x06003EEA RID: 16106 RVA: 0x000D06CB File Offset: 0x000CE8CB
		// (set) Token: 0x06003EEB RID: 16107 RVA: 0x000D06D3 File Offset: 0x000CE8D3
		[MetadataProperty(BuiltInTypeKind.RelationshipEndMember, false)]
		public RelationshipEndMember FromRole
		{
			get
			{
				return this._fromRole;
			}
			set
			{
				Util.ThrowIfReadOnly(this);
				this._fromRole = value;
			}
		}

		// Token: 0x17000C57 RID: 3159
		// (get) Token: 0x06003EEC RID: 16108 RVA: 0x000D06E2 File Offset: 0x000CE8E2
		// (set) Token: 0x06003EED RID: 16109 RVA: 0x000D06EA File Offset: 0x000CE8EA
		[MetadataProperty(BuiltInTypeKind.RelationshipEndMember, false)]
		public RelationshipEndMember ToRole
		{
			get
			{
				return this._toRole;
			}
			set
			{
				Util.ThrowIfReadOnly(this);
				this._toRole = value;
			}
		}

		// Token: 0x17000C58 RID: 3160
		// (get) Token: 0x06003EEE RID: 16110 RVA: 0x000D06F9 File Offset: 0x000CE8F9
		internal AssociationEndMember PrincipalEnd
		{
			get
			{
				return (AssociationEndMember)this.FromRole;
			}
		}

		// Token: 0x17000C59 RID: 3161
		// (get) Token: 0x06003EEF RID: 16111 RVA: 0x000D0706 File Offset: 0x000CE906
		internal AssociationEndMember DependentEnd
		{
			get
			{
				return (AssociationEndMember)this.ToRole;
			}
		}

		// Token: 0x17000C5A RID: 3162
		// (get) Token: 0x06003EF0 RID: 16112 RVA: 0x000D0713 File Offset: 0x000CE913
		[MetadataProperty(BuiltInTypeKind.EdmProperty, true)]
		public ReadOnlyMetadataCollection<EdmProperty> FromProperties
		{
			get
			{
				if (!base.IsReadOnly && this._fromProperties.Count == 0)
				{
					this._fromRole.GetEntityType().KeyMembers.Each(delegate(EdmMember p)
					{
						this._fromProperties.Source.Add((EdmProperty)p);
					});
				}
				return this._fromProperties;
			}
		}

		// Token: 0x17000C5B RID: 3163
		// (get) Token: 0x06003EF1 RID: 16113 RVA: 0x000D0751 File Offset: 0x000CE951
		[MetadataProperty(BuiltInTypeKind.EdmProperty, true)]
		public ReadOnlyMetadataCollection<EdmProperty> ToProperties
		{
			get
			{
				return this._toProperties;
			}
		}

		// Token: 0x06003EF2 RID: 16114 RVA: 0x000D0759 File Offset: 0x000CE959
		public override string ToString()
		{
			return this.FromRole.Name + "_" + this.ToRole.Name;
		}

		// Token: 0x06003EF3 RID: 16115 RVA: 0x000D077C File Offset: 0x000CE97C
		internal override void SetReadOnly()
		{
			if (!base.IsReadOnly)
			{
				this.FromProperties.Source.SetReadOnly();
				this.ToProperties.Source.SetReadOnly();
				base.SetReadOnly();
				RelationshipEndMember fromRole = this.FromRole;
				if (fromRole != null)
				{
					fromRole.SetReadOnly();
				}
				RelationshipEndMember toRole = this.ToRole;
				if (toRole != null)
				{
					toRole.SetReadOnly();
				}
			}
		}

		// Token: 0x06003EF4 RID: 16116 RVA: 0x000D07DC File Offset: 0x000CE9DC
		internal string BuildConstraintExceptionMessage()
		{
			string name = this.FromProperties.First<EdmProperty>().DeclaringType.Name;
			string name2 = this.ToProperties.First<EdmProperty>().DeclaringType.Name;
			StringBuilder stringBuilder = new StringBuilder();
			StringBuilder stringBuilder2 = new StringBuilder();
			for (int i = 0; i < this.FromProperties.Count; i++)
			{
				if (i > 0)
				{
					stringBuilder.Append(", ");
					stringBuilder2.Append(", ");
				}
				stringBuilder.Append(name).Append('.').Append(this.FromProperties[i]);
				stringBuilder2.Append(name2).Append('.').Append(this.ToProperties[i]);
			}
			return Strings.RelationshipManager_InconsistentReferentialConstraintProperties(stringBuilder.ToString(), stringBuilder2.ToString());
		}

		// Token: 0x04001577 RID: 5495
		private RelationshipEndMember _fromRole;

		// Token: 0x04001578 RID: 5496
		private RelationshipEndMember _toRole;

		// Token: 0x04001579 RID: 5497
		private readonly ReadOnlyMetadataCollection<EdmProperty> _fromProperties;

		// Token: 0x0400157A RID: 5498
		private readonly ReadOnlyMetadataCollection<EdmProperty> _toProperties;
	}
}
