﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data.Entity.Core.SchemaObjectModel;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;
using System.Reflection;
using System.Security;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x02000486 RID: 1158
	internal class AspProxy
	{
		// Token: 0x0600399D RID: 14749 RVA: 0x000BCCA4 File Offset: 0x000BAEA4
		internal bool IsAspNetEnvironment()
		{
			if (!this.TryInitializeWebAssembly())
			{
				return false;
			}
			bool flag;
			try
			{
				flag = this.InternalMapWebPath("~") != null;
			}
			catch (SecurityException)
			{
				flag = false;
			}
			catch (Exception ex)
			{
				if (!ex.IsCatchableExceptionType())
				{
					throw;
				}
				flag = false;
			}
			return flag;
		}

		// Token: 0x0600399E RID: 14750 RVA: 0x000BCCFC File Offset: 0x000BAEFC
		public bool TryInitializeWebAssembly()
		{
			if (this._webAssembly != null)
			{
				return true;
			}
			if (this._triedLoadingWebAssembly)
			{
				return false;
			}
			this._triedLoadingWebAssembly = true;
			if (!AspProxy.IsSystemWebLoaded())
			{
				return false;
			}
			try
			{
				this._webAssembly = Assembly.Load("System.Web, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a");
				return this._webAssembly != null;
			}
			catch (Exception ex)
			{
				if (!ex.IsCatchableExceptionType())
				{
					throw;
				}
			}
			return false;
		}

		// Token: 0x0600399F RID: 14751 RVA: 0x000BCD74 File Offset: 0x000BAF74
		public static bool IsSystemWebLoaded()
		{
			try
			{
				return AppDomain.CurrentDomain.GetAssemblies().Any((Assembly a) => a.GetName().Name == "System.Web" && a.GetName().GetPublicKeyToken() != null && a.GetName().GetPublicKeyToken().SequenceEqual(AspProxy._systemWebPublicKeyToken));
			}
			catch
			{
			}
			return false;
		}

		// Token: 0x060039A0 RID: 14752 RVA: 0x000BCDC8 File Offset: 0x000BAFC8
		private void InitializeWebAssembly()
		{
			if (!this.TryInitializeWebAssembly())
			{
				throw new InvalidOperationException(Strings.UnableToDetermineApplicationContext);
			}
		}

		// Token: 0x060039A1 RID: 14753 RVA: 0x000BCDDD File Offset: 0x000BAFDD
		internal string MapWebPath(string path)
		{
			path = this.InternalMapWebPath(path);
			if (path == null)
			{
				throw new InvalidOperationException(Strings.InvalidUseOfWebPath("~"));
			}
			return path;
		}

		// Token: 0x060039A2 RID: 14754 RVA: 0x000BCDFC File Offset: 0x000BAFFC
		internal string InternalMapWebPath(string path)
		{
			this.InitializeWebAssembly();
			string text;
			try
			{
				text = (string)this._webAssembly.GetType("System.Web.Hosting.HostingEnvironment", true).GetDeclaredMethod("MapPath", new Type[] { typeof(string) }).Invoke(null, new object[] { path });
			}
			catch (TargetException ex)
			{
				throw new InvalidOperationException(Strings.UnableToDetermineApplicationContext, ex);
			}
			catch (ArgumentException ex2)
			{
				throw new InvalidOperationException(Strings.UnableToDetermineApplicationContext, ex2);
			}
			catch (TargetInvocationException ex3)
			{
				throw new InvalidOperationException(Strings.UnableToDetermineApplicationContext, ex3);
			}
			catch (TargetParameterCountException ex4)
			{
				throw new InvalidOperationException(Strings.UnableToDetermineApplicationContext, ex4);
			}
			catch (MethodAccessException ex5)
			{
				throw new InvalidOperationException(Strings.UnableToDetermineApplicationContext, ex5);
			}
			catch (MemberAccessException ex6)
			{
				throw new InvalidOperationException(Strings.UnableToDetermineApplicationContext, ex6);
			}
			catch (TypeLoadException ex7)
			{
				throw new InvalidOperationException(Strings.UnableToDetermineApplicationContext, ex7);
			}
			return text;
		}

		// Token: 0x060039A3 RID: 14755 RVA: 0x000BCF14 File Offset: 0x000BB114
		internal bool HasBuildManagerType()
		{
			Type type;
			return this.TryGetBuildManagerType(out type);
		}

		// Token: 0x060039A4 RID: 14756 RVA: 0x000BCF29 File Offset: 0x000BB129
		private bool TryGetBuildManagerType(out Type buildManager)
		{
			this.InitializeWebAssembly();
			buildManager = this._webAssembly.GetType("System.Web.Compilation.BuildManager", false);
			return buildManager != null;
		}

		// Token: 0x060039A5 RID: 14757 RVA: 0x000BCF4C File Offset: 0x000BB14C
		internal IEnumerable<Assembly> GetBuildManagerReferencedAssemblies()
		{
			MethodInfo referencedAssembliesMethod = this.GetReferencedAssembliesMethod();
			if (referencedAssembliesMethod == null)
			{
				return new List<Assembly>();
			}
			IEnumerable<Assembly> enumerable;
			try
			{
				ICollection collection = (ICollection)referencedAssembliesMethod.Invoke(null, null);
				if (collection == null)
				{
					enumerable = new List<Assembly>();
				}
				else
				{
					enumerable = collection.Cast<Assembly>();
				}
			}
			catch (TargetException ex)
			{
				throw new InvalidOperationException(Strings.UnableToDetermineApplicationContext, ex);
			}
			catch (TargetInvocationException ex2)
			{
				throw new InvalidOperationException(Strings.UnableToDetermineApplicationContext, ex2);
			}
			catch (MethodAccessException ex3)
			{
				throw new InvalidOperationException(Strings.UnableToDetermineApplicationContext, ex3);
			}
			return enumerable;
		}

		// Token: 0x060039A6 RID: 14758 RVA: 0x000BCFE8 File Offset: 0x000BB1E8
		internal MethodInfo GetReferencedAssembliesMethod()
		{
			Type type;
			if (!this.TryGetBuildManagerType(out type))
			{
				throw new InvalidOperationException(Strings.UnableToFindReflectedType("System.Web.Compilation.BuildManager", "System.Web, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"));
			}
			return type.GetDeclaredMethod("GetReferencedAssemblies", new Type[0]);
		}

		// Token: 0x04001314 RID: 4884
		private const string BUILD_MANAGER_TYPE_NAME = "System.Web.Compilation.BuildManager";

		// Token: 0x04001315 RID: 4885
		private const string AspNetAssemblyName = "System.Web, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a";

		// Token: 0x04001316 RID: 4886
		private static readonly byte[] _systemWebPublicKeyToken = ScalarType.ConvertToByteArray("b03f5f7f11d50a3a");

		// Token: 0x04001317 RID: 4887
		private Assembly _webAssembly;

		// Token: 0x04001318 RID: 4888
		private bool _triedLoadingWebAssembly;
	}
}
