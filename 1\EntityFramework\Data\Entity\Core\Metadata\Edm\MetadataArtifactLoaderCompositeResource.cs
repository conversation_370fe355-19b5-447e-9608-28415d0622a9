﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Resources;
using System.Globalization;
using System.IO;
using System.Reflection;
using System.Xml;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004D4 RID: 1236
	internal class MetadataArtifactLoaderCompositeResource : MetadataArtifactLoader
	{
		// Token: 0x06003D58 RID: 15704 RVA: 0x000CA238 File Offset: 0x000C8438
		internal MetadataArtifactLoaderCompositeResource(string originalPath, string assemblyName, string resourceName, ICollection<string> uriRegistry, MetadataArtifactAssemblyResolver resolver)
		{
			this._originalPath = originalPath;
			this._children = new ReadOnlyCollection<MetadataArtifactLoaderResource>(MetadataArtifactLoaderCompositeResource.LoadResources(assemblyName, resourceName, uriRegistry, resolver));
		}

		// Token: 0x17000C14 RID: 3092
		// (get) Token: 0x06003D59 RID: 15705 RVA: 0x000CA25D File Offset: 0x000C845D
		public override string Path
		{
			get
			{
				return this._originalPath;
			}
		}

		// Token: 0x17000C15 RID: 3093
		// (get) Token: 0x06003D5A RID: 15706 RVA: 0x000CA265 File Offset: 0x000C8465
		public override bool IsComposite
		{
			get
			{
				return true;
			}
		}

		// Token: 0x06003D5B RID: 15707 RVA: 0x000CA268 File Offset: 0x000C8468
		public override List<string> GetOriginalPaths(DataSpace spaceToGet)
		{
			return this.GetOriginalPaths();
		}

		// Token: 0x06003D5C RID: 15708 RVA: 0x000CA270 File Offset: 0x000C8470
		public override List<string> GetPaths(DataSpace spaceToGet)
		{
			List<string> list = new List<string>();
			foreach (MetadataArtifactLoaderResource metadataArtifactLoaderResource in this._children)
			{
				list.AddRange(metadataArtifactLoaderResource.GetPaths(spaceToGet));
			}
			return list;
		}

		// Token: 0x06003D5D RID: 15709 RVA: 0x000CA2CC File Offset: 0x000C84CC
		public override List<string> GetPaths()
		{
			List<string> list = new List<string>();
			foreach (MetadataArtifactLoaderResource metadataArtifactLoaderResource in this._children)
			{
				list.AddRange(metadataArtifactLoaderResource.GetPaths());
			}
			return list;
		}

		// Token: 0x06003D5E RID: 15710 RVA: 0x000CA328 File Offset: 0x000C8528
		public override List<XmlReader> GetReaders(Dictionary<MetadataArtifactLoader, XmlReader> sourceDictionary)
		{
			List<XmlReader> list = new List<XmlReader>();
			foreach (MetadataArtifactLoaderResource metadataArtifactLoaderResource in this._children)
			{
				list.AddRange(metadataArtifactLoaderResource.GetReaders(sourceDictionary));
			}
			return list;
		}

		// Token: 0x06003D5F RID: 15711 RVA: 0x000CA384 File Offset: 0x000C8584
		public override List<XmlReader> CreateReaders(DataSpace spaceToGet)
		{
			List<XmlReader> list = new List<XmlReader>();
			foreach (MetadataArtifactLoaderResource metadataArtifactLoaderResource in this._children)
			{
				list.AddRange(metadataArtifactLoaderResource.CreateReaders(spaceToGet));
			}
			return list;
		}

		// Token: 0x06003D60 RID: 15712 RVA: 0x000CA3E0 File Offset: 0x000C85E0
		private static List<MetadataArtifactLoaderResource> LoadResources(string assemblyName, string resourceName, ICollection<string> uriRegistry, MetadataArtifactAssemblyResolver resolver)
		{
			List<MetadataArtifactLoaderResource> list = new List<MetadataArtifactLoaderResource>();
			if (assemblyName == MetadataArtifactLoader.wildcard)
			{
				using (IEnumerator<Assembly> enumerator = resolver.GetWildcardAssemblies().GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						Assembly assembly = enumerator.Current;
						if (MetadataArtifactLoaderCompositeResource.AssemblyContainsResource(assembly, ref resourceName))
						{
							MetadataArtifactLoaderCompositeResource.LoadResourcesFromAssembly(assembly, resourceName, uriRegistry, list);
						}
					}
					goto IL_005E;
				}
			}
			MetadataArtifactLoaderCompositeResource.LoadResourcesFromAssembly(MetadataArtifactLoaderCompositeResource.ResolveAssemblyName(assemblyName, resolver), resourceName, uriRegistry, list);
			IL_005E:
			if (resourceName != null && list.Count == 0)
			{
				throw new MetadataException(Strings.UnableToLoadResource);
			}
			return list;
		}

		// Token: 0x06003D61 RID: 15713 RVA: 0x000CA474 File Offset: 0x000C8674
		private static bool AssemblyContainsResource(Assembly assembly, ref string resourceName)
		{
			if (resourceName == null)
			{
				return true;
			}
			foreach (string text in MetadataArtifactLoaderCompositeResource.GetManifestResourceNamesForAssembly(assembly))
			{
				if (string.Equals(resourceName, text, StringComparison.OrdinalIgnoreCase))
				{
					resourceName = text;
					return true;
				}
			}
			return false;
		}

		// Token: 0x06003D62 RID: 15714 RVA: 0x000CA4B1 File Offset: 0x000C86B1
		private static void LoadResourcesFromAssembly(Assembly assembly, string resourceName, ICollection<string> uriRegistry, List<MetadataArtifactLoaderResource> loaders)
		{
			if (resourceName == null)
			{
				MetadataArtifactLoaderCompositeResource.LoadAllResourcesFromAssembly(assembly, uriRegistry, loaders);
				return;
			}
			if (MetadataArtifactLoaderCompositeResource.AssemblyContainsResource(assembly, ref resourceName))
			{
				MetadataArtifactLoaderCompositeResource.CreateAndAddSingleResourceLoader(assembly, resourceName, uriRegistry, loaders);
				return;
			}
			throw new MetadataException(Strings.UnableToLoadResource);
		}

		// Token: 0x06003D63 RID: 15715 RVA: 0x000CA4E0 File Offset: 0x000C86E0
		private static void LoadAllResourcesFromAssembly(Assembly assembly, ICollection<string> uriRegistry, List<MetadataArtifactLoaderResource> loaders)
		{
			foreach (string text in MetadataArtifactLoaderCompositeResource.GetManifestResourceNamesForAssembly(assembly))
			{
				MetadataArtifactLoaderCompositeResource.CreateAndAddSingleResourceLoader(assembly, text, uriRegistry, loaders);
			}
		}

		// Token: 0x06003D64 RID: 15716 RVA: 0x000CA510 File Offset: 0x000C8710
		private static void CreateAndAddSingleResourceLoader(Assembly assembly, string resourceName, ICollection<string> uriRegistry, List<MetadataArtifactLoaderResource> loaders)
		{
			string text = MetadataArtifactLoaderCompositeResource.CreateResPath(assembly, resourceName);
			if (!uriRegistry.Contains(text))
			{
				loaders.Add(new MetadataArtifactLoaderResource(assembly, resourceName, uriRegistry));
			}
		}

		// Token: 0x06003D65 RID: 15717 RVA: 0x000CA53C File Offset: 0x000C873C
		internal static string CreateResPath(Assembly assembly, string resourceName)
		{
			return string.Format(CultureInfo.InvariantCulture, "{0}{1}{2}{3}", new object[]
			{
				MetadataArtifactLoader.resPathPrefix,
				assembly.FullName,
				MetadataArtifactLoader.resPathSeparator,
				resourceName
			});
		}

		// Token: 0x06003D66 RID: 15718 RVA: 0x000CA570 File Offset: 0x000C8770
		internal static string[] GetManifestResourceNamesForAssembly(Assembly assembly)
		{
			if (assembly.IsDynamic)
			{
				return new string[0];
			}
			return assembly.GetManifestResourceNames();
		}

		// Token: 0x06003D67 RID: 15719 RVA: 0x000CA588 File Offset: 0x000C8788
		private static Assembly ResolveAssemblyName(string assemblyName, MetadataArtifactAssemblyResolver resolver)
		{
			AssemblyName assemblyName2 = new AssemblyName(assemblyName);
			Assembly assembly;
			if (!resolver.TryResolveAssemblyReference(assemblyName2, out assembly))
			{
				throw new FileNotFoundException(Strings.UnableToResolveAssembly(assemblyName));
			}
			return assembly;
		}

		// Token: 0x06003D68 RID: 15720 RVA: 0x000CA5B4 File Offset: 0x000C87B4
		internal static MetadataArtifactLoader CreateResourceLoader(string path, MetadataArtifactLoader.ExtensionCheck extensionCheck, string validExtension, ICollection<string> uriRegistry, MetadataArtifactAssemblyResolver resolver)
		{
			string text = null;
			string text2 = null;
			MetadataArtifactLoaderCompositeResource.ParseResourcePath(path, out text, out text2);
			bool flag = text != null && (text2 == null || text.Trim() == MetadataArtifactLoader.wildcard);
			MetadataArtifactLoaderCompositeResource.ValidateExtension(extensionCheck, validExtension, text2);
			if (flag)
			{
				return new MetadataArtifactLoaderCompositeResource(path, text, text2, uriRegistry, resolver);
			}
			return new MetadataArtifactLoaderResource(MetadataArtifactLoaderCompositeResource.ResolveAssemblyName(text, resolver), text2, uriRegistry);
		}

		// Token: 0x06003D69 RID: 15721 RVA: 0x000CA610 File Offset: 0x000C8810
		private static void ValidateExtension(MetadataArtifactLoader.ExtensionCheck extensionCheck, string validExtension, string resourceName)
		{
			if (resourceName == null)
			{
				return;
			}
			if (extensionCheck == MetadataArtifactLoader.ExtensionCheck.Specific)
			{
				MetadataArtifactLoader.CheckArtifactExtension(resourceName, validExtension);
				return;
			}
			if (extensionCheck != MetadataArtifactLoader.ExtensionCheck.All)
			{
				return;
			}
			if (!MetadataArtifactLoader.IsValidArtifact(resourceName))
			{
				throw new MetadataException(Strings.InvalidMetadataPath);
			}
		}

		// Token: 0x06003D6A RID: 15722 RVA: 0x000CA63C File Offset: 0x000C883C
		private static void ParseResourcePath(string path, out string assemblyName, out string resourceName)
		{
			int length = MetadataArtifactLoader.resPathPrefix.Length;
			string[] array = path.Substring(length).Split(new string[]
			{
				MetadataArtifactLoader.resPathSeparator,
				MetadataArtifactLoader.altPathSeparator
			}, StringSplitOptions.RemoveEmptyEntries);
			if (array.Length == 0 || array.Length > 2)
			{
				throw new MetadataException(Strings.InvalidMetadataPath);
			}
			if (array.Length >= 1)
			{
				assemblyName = array[0];
			}
			else
			{
				assemblyName = null;
			}
			if (array.Length == 2)
			{
				resourceName = array[1];
				return;
			}
			resourceName = null;
		}

		// Token: 0x040014F6 RID: 5366
		private readonly ReadOnlyCollection<MetadataArtifactLoaderResource> _children;

		// Token: 0x040014F7 RID: 5367
		private readonly string _originalPath;
	}
}
