﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace System.Data.Entity.Core.Common.CommandTrees.Internal
{
	// Token: 0x020006F2 RID: 1778
	internal class PatternMatchRuleProcessor : DbExpressionRuleProcessingVisitor
	{
		// Token: 0x060052C2 RID: 21186 RVA: 0x00128370 File Offset: 0x00126570
		private PatternMatchRuleProcessor(ReadOnlyCollection<PatternMatchRule> rules)
		{
			this.ruleSet = rules;
		}

		// Token: 0x060052C3 RID: 21187 RVA: 0x0012837F File Offset: 0x0012657F
		private DbExpression Process(DbExpression expression)
		{
			expression = this.VisitExpression(expression);
			return expression;
		}

		// Token: 0x060052C4 RID: 21188 RVA: 0x0012838B File Offset: 0x0012658B
		protected override IEnumerable<DbExpressionRule> GetRules()
		{
			return this.ruleSet;
		}

		// Token: 0x060052C5 RID: 21189 RVA: 0x00128393 File Offset: 0x00126593
		internal static Func<DbExpression, DbExpression> Create(params PatternMatchRule[] rules)
		{
			return new Func<DbExpression, DbExpression>(new PatternMatchRuleProcessor(new ReadOnlyCollection<PatternMatchRule>(rules)).Process);
		}

		// Token: 0x04001DEB RID: 7659
		private readonly ReadOnlyCollection<PatternMatchRule> ruleSet;
	}
}
