﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.Validation;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Resources;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x0200059D RID: 1437
	internal class CellQuery : InternalBase
	{
		// Token: 0x06004599 RID: 17817 RVA: 0x000F491C File Offset: 0x000F2B1C
		internal CellQuery(List<ProjectedSlot> slots, BoolExpression whereClause, MemberPath rootMember, CellQuery.SelectDistinct eliminateDuplicates)
			: this(slots.ToArray(), whereClause, new List<BoolExpression>(), eliminateDuplicates, rootMember)
		{
		}

		// Token: 0x0600459A RID: 17818 RVA: 0x000F4933 File Offset: 0x000F2B33
		internal CellQuery(ProjectedSlot[] projectedSlots, BoolExpression whereClause, List<BoolExpression> boolExprs, CellQuery.SelectDistinct elimDupl, MemberPath rootMember)
		{
			this.m_boolExprs = boolExprs;
			this.m_projectedSlots = projectedSlots;
			this.m_whereClause = whereClause;
			this.m_originalWhereClause = whereClause;
			this.m_selectDistinct = elimDupl;
			this.m_extentMemberPath = rootMember;
		}

		// Token: 0x0600459B RID: 17819 RVA: 0x000F4968 File Offset: 0x000F2B68
		internal CellQuery(CellQuery source)
		{
			this.m_basicCellRelation = source.m_basicCellRelation;
			this.m_boolExprs = source.m_boolExprs;
			this.m_selectDistinct = source.m_selectDistinct;
			this.m_extentMemberPath = source.m_extentMemberPath;
			this.m_originalWhereClause = source.m_originalWhereClause;
			this.m_projectedSlots = source.m_projectedSlots;
			this.m_whereClause = source.m_whereClause;
		}

		// Token: 0x0600459C RID: 17820 RVA: 0x000F49CF File Offset: 0x000F2BCF
		private CellQuery(CellQuery existing, ProjectedSlot[] newSlots)
			: this(newSlots, existing.m_whereClause, existing.m_boolExprs, existing.m_selectDistinct, existing.m_extentMemberPath)
		{
		}

		// Token: 0x17000DBC RID: 3516
		// (get) Token: 0x0600459D RID: 17821 RVA: 0x000F49F0 File Offset: 0x000F2BF0
		internal CellQuery.SelectDistinct SelectDistinctFlag
		{
			get
			{
				return this.m_selectDistinct;
			}
		}

		// Token: 0x17000DBD RID: 3517
		// (get) Token: 0x0600459E RID: 17822 RVA: 0x000F49F8 File Offset: 0x000F2BF8
		internal EntitySetBase Extent
		{
			get
			{
				return this.m_extentMemberPath.Extent;
			}
		}

		// Token: 0x17000DBE RID: 3518
		// (get) Token: 0x0600459F RID: 17823 RVA: 0x000F4A05 File Offset: 0x000F2C05
		internal int NumProjectedSlots
		{
			get
			{
				return this.m_projectedSlots.Length;
			}
		}

		// Token: 0x17000DBF RID: 3519
		// (get) Token: 0x060045A0 RID: 17824 RVA: 0x000F4A0F File Offset: 0x000F2C0F
		internal ProjectedSlot[] ProjectedSlots
		{
			get
			{
				return this.m_projectedSlots;
			}
		}

		// Token: 0x17000DC0 RID: 3520
		// (get) Token: 0x060045A1 RID: 17825 RVA: 0x000F4A17 File Offset: 0x000F2C17
		internal List<BoolExpression> BoolVars
		{
			get
			{
				return this.m_boolExprs;
			}
		}

		// Token: 0x17000DC1 RID: 3521
		// (get) Token: 0x060045A2 RID: 17826 RVA: 0x000F4A1F File Offset: 0x000F2C1F
		internal int NumBoolVars
		{
			get
			{
				return this.m_boolExprs.Count;
			}
		}

		// Token: 0x17000DC2 RID: 3522
		// (get) Token: 0x060045A3 RID: 17827 RVA: 0x000F4A2C File Offset: 0x000F2C2C
		internal BoolExpression WhereClause
		{
			get
			{
				return this.m_whereClause;
			}
		}

		// Token: 0x17000DC3 RID: 3523
		// (get) Token: 0x060045A4 RID: 17828 RVA: 0x000F4A34 File Offset: 0x000F2C34
		internal MemberPath SourceExtentMemberPath
		{
			get
			{
				return this.m_extentMemberPath;
			}
		}

		// Token: 0x17000DC4 RID: 3524
		// (get) Token: 0x060045A5 RID: 17829 RVA: 0x000F4A3C File Offset: 0x000F2C3C
		internal BasicCellRelation BasicCellRelation
		{
			get
			{
				return this.m_basicCellRelation;
			}
		}

		// Token: 0x17000DC5 RID: 3525
		// (get) Token: 0x060045A6 RID: 17830 RVA: 0x000F4A44 File Offset: 0x000F2C44
		internal IEnumerable<MemberRestriction> Conditions
		{
			get
			{
				return this.GetConjunctsFromOriginalWhereClause();
			}
		}

		// Token: 0x060045A7 RID: 17831 RVA: 0x000F4A4C File Offset: 0x000F2C4C
		internal ProjectedSlot ProjectedSlotAt(int slotNum)
		{
			return this.m_projectedSlots[slotNum];
		}

		// Token: 0x060045A8 RID: 17832 RVA: 0x000F4A58 File Offset: 0x000F2C58
		internal ErrorLog.Record CheckForDuplicateFields(CellQuery cQuery, Cell sourceCell)
		{
			KeyToListMap<MemberProjectedSlot, int> keyToListMap = new KeyToListMap<MemberProjectedSlot, int>(ProjectedSlot.EqualityComparer);
			for (int i = 0; i < this.m_projectedSlots.Length; i++)
			{
				MemberProjectedSlot memberProjectedSlot = this.m_projectedSlots[i] as MemberProjectedSlot;
				keyToListMap.Add(memberProjectedSlot, i);
			}
			StringBuilder stringBuilder = null;
			bool flag = false;
			foreach (MemberProjectedSlot memberProjectedSlot2 in keyToListMap.Keys)
			{
				ReadOnlyCollection<int> readOnlyCollection = keyToListMap.ListForKey(memberProjectedSlot2);
				if (readOnlyCollection.Count > 1 && !cQuery.AreSlotsEquivalentViaRefConstraints(readOnlyCollection))
				{
					flag = true;
					if (stringBuilder == null)
					{
						stringBuilder = new StringBuilder(Strings.ViewGen_Duplicate_CProperties(this.Extent.Name));
						stringBuilder.AppendLine();
					}
					StringBuilder stringBuilder2 = new StringBuilder();
					for (int j = 0; j < readOnlyCollection.Count; j++)
					{
						int num = readOnlyCollection[j];
						if (j != 0)
						{
							stringBuilder2.Append(", ");
						}
						MemberProjectedSlot memberProjectedSlot3 = (MemberProjectedSlot)cQuery.m_projectedSlots[num];
						stringBuilder2.Append(memberProjectedSlot3.ToUserString());
					}
					stringBuilder.AppendLine(Strings.ViewGen_Duplicate_CProperties_IsMapped(memberProjectedSlot2.ToUserString(), stringBuilder2.ToString()));
				}
			}
			if (!flag)
			{
				return null;
			}
			return new ErrorLog.Record(ViewGenErrorCode.DuplicateCPropertiesMapped, stringBuilder.ToString(), sourceCell, string.Empty);
		}

		// Token: 0x060045A9 RID: 17833 RVA: 0x000F4BB8 File Offset: 0x000F2DB8
		private bool AreSlotsEquivalentViaRefConstraints(ReadOnlyCollection<int> cSideSlotIndexes)
		{
			if (!(this.Extent is AssociationSet))
			{
				return false;
			}
			if (cSideSlotIndexes.Count > 2)
			{
				return false;
			}
			MemberProjectedSlot memberProjectedSlot = (MemberProjectedSlot)this.m_projectedSlots[cSideSlotIndexes[0]];
			MemberProjectedSlot memberProjectedSlot2 = (MemberProjectedSlot)this.m_projectedSlots[cSideSlotIndexes[1]];
			return memberProjectedSlot.MemberPath.IsEquivalentViaRefConstraint(memberProjectedSlot2.MemberPath);
		}

		// Token: 0x060045AA RID: 17834 RVA: 0x000F4C18 File Offset: 0x000F2E18
		internal ErrorLog.Record CheckForProjectedNotNullSlots(Cell sourceCell, IEnumerable<Cell> associationSets)
		{
			StringBuilder stringBuilder = new StringBuilder();
			bool flag = false;
			foreach (MemberRestriction memberRestriction in this.Conditions)
			{
				if (memberRestriction.Domain.ContainsNotNull() && MemberProjectedSlot.GetSlotForMember(this.m_projectedSlots, memberRestriction.RestrictedMemberSlot.MemberPath) == null)
				{
					bool flag2 = true;
					if (this.Extent is EntitySet)
					{
						bool flag3 = sourceCell.CQuery == this;
						ViewTarget target = (flag3 ? ViewTarget.QueryView : ViewTarget.UpdateView);
						CellQuery cellQuery = (flag3 ? sourceCell.SQuery : sourceCell.CQuery);
						EntitySet rightExtent = cellQuery.Extent as EntitySet;
						if (rightExtent != null)
						{
							IEnumerable<AssociationSet> associationSets2 = (cellQuery.Extent as EntitySet).AssociationSets;
							Func<AssociationSet, bool> func;
							Func<AssociationSet, bool> <>9__0;
							if ((func = <>9__0) == null)
							{
								Func<AssociationSetEnd, bool> <>9__1;
								func = (<>9__0 = delegate(AssociationSet association)
								{
									IEnumerable<AssociationSetEnd> associationSetEnds = association.AssociationSetEnds;
									Func<AssociationSetEnd, bool> func3;
									if ((func3 = <>9__1) == null)
									{
										func3 = (<>9__1 = (AssociationSetEnd end) => end.CorrespondingAssociationEndMember.RelationshipMultiplicity == RelationshipMultiplicity.One && MetadataHelper.GetOppositeEnd(end).EntitySet.EdmEquals(rightExtent));
									}
									return associationSetEnds.Any(func3);
								});
							}
							using (IEnumerator<AssociationSet> enumerator2 = associationSets2.Where(func).GetEnumerator())
							{
								while (enumerator2.MoveNext())
								{
									AssociationSet association = enumerator2.Current;
									Func<Cell, bool> func2;
									Func<Cell, bool> <>9__2;
									if ((func2 = <>9__2) == null)
									{
										func2 = (<>9__2 = (Cell c) => c.GetRightQuery(target).Extent.EdmEquals(association));
									}
									using (IEnumerator<Cell> enumerator3 = associationSets.Where(func2).GetEnumerator())
									{
										while (enumerator3.MoveNext())
										{
											if (MemberProjectedSlot.GetSlotForMember(enumerator3.Current.GetLeftQuery(target).ProjectedSlots, memberRestriction.RestrictedMemberSlot.MemberPath) != null)
											{
												flag2 = false;
											}
										}
									}
								}
							}
						}
					}
					if (flag2)
					{
						stringBuilder.AppendLine(Strings.ViewGen_NotNull_No_Projected_Slot(memberRestriction.RestrictedMemberSlot.MemberPath.PathToString(new bool?(false))));
						flag = true;
					}
				}
			}
			if (!flag)
			{
				return null;
			}
			return new ErrorLog.Record(ViewGenErrorCode.NotNullNoProjectedSlot, stringBuilder.ToString(), sourceCell, string.Empty);
		}

		// Token: 0x060045AB RID: 17835 RVA: 0x000F4E78 File Offset: 0x000F3078
		internal void FixMissingSlotAsDefaultConstant(int slotNumber, ConstantProjectedSlot slot)
		{
			this.m_projectedSlots[slotNumber] = slot;
		}

		// Token: 0x060045AC RID: 17836 RVA: 0x000F4E84 File Offset: 0x000F3084
		internal void CreateFieldAlignedCellQueries(CellQuery otherQuery, MemberProjectionIndex projectedSlotMap, out CellQuery newMainQuery, out CellQuery newOtherQuery)
		{
			int count = projectedSlotMap.Count;
			ProjectedSlot[] array = new ProjectedSlot[count];
			ProjectedSlot[] array2 = new ProjectedSlot[count];
			for (int i = 0; i < this.m_projectedSlots.Length; i++)
			{
				MemberProjectedSlot memberProjectedSlot = this.m_projectedSlots[i] as MemberProjectedSlot;
				int num = projectedSlotMap.IndexOf(memberProjectedSlot.MemberPath);
				array[num] = this.m_projectedSlots[i];
				array2[num] = otherQuery.m_projectedSlots[i];
			}
			newMainQuery = new CellQuery(this, array);
			newOtherQuery = new CellQuery(otherQuery, array2);
		}

		// Token: 0x060045AD RID: 17837 RVA: 0x000F4F00 File Offset: 0x000F3100
		internal Set<MemberPath> GetNonNullSlots()
		{
			Set<MemberPath> set = new Set<MemberPath>(MemberPath.EqualityComparer);
			foreach (ProjectedSlot projectedSlot in this.m_projectedSlots)
			{
				if (projectedSlot != null)
				{
					MemberProjectedSlot memberProjectedSlot = projectedSlot as MemberProjectedSlot;
					set.Add(memberProjectedSlot.MemberPath);
				}
			}
			return set;
		}

		// Token: 0x060045AE RID: 17838 RVA: 0x000F4F4C File Offset: 0x000F314C
		internal ErrorLog.Record VerifyKeysPresent(Cell ownerCell, Func<object, object, string> formatEntitySetMessage, Func<object, object, object, string> formatAssociationSetMessage, ViewGenErrorCode errorCode)
		{
			List<MemberPath> list = new List<MemberPath>(1);
			List<ExtentKey> list2 = new List<ExtentKey>(1);
			if (this.Extent is EntitySet)
			{
				MemberPath memberPath = new MemberPath(this.Extent);
				list.Add(memberPath);
				EntityType entityType = (EntityType)this.Extent.ElementType;
				List<ExtentKey> keysForEntityType = ExtentKey.GetKeysForEntityType(memberPath, entityType);
				list2.Add(keysForEntityType[0]);
			}
			else
			{
				AssociationSet associationSet = (AssociationSet)this.Extent;
				foreach (AssociationSetEnd associationSetEnd in associationSet.AssociationSetEnds)
				{
					AssociationEndMember correspondingAssociationEndMember = associationSetEnd.CorrespondingAssociationEndMember;
					MemberPath memberPath2 = new MemberPath(associationSet, correspondingAssociationEndMember);
					list.Add(memberPath2);
					List<ExtentKey> keysForEntityType2 = ExtentKey.GetKeysForEntityType(memberPath2, MetadataHelper.GetEntityTypeForEnd(correspondingAssociationEndMember));
					list2.Add(keysForEntityType2[0]);
				}
			}
			for (int i = 0; i < list.Count; i++)
			{
				MemberPath memberPath3 = list[i];
				if (MemberProjectedSlot.GetKeySlots(this.GetMemberProjectedSlots(), memberPath3) == null)
				{
					ExtentKey extentKey = list2[i];
					string text2;
					if (this.Extent is EntitySet)
					{
						string text = MemberPath.PropertiesToUserString(extentKey.KeyFields, true);
						text2 = formatEntitySetMessage(text, this.Extent.Name);
					}
					else
					{
						string name = memberPath3.RootEdmMember.Name;
						string text3 = MemberPath.PropertiesToUserString(extentKey.KeyFields, false);
						text2 = formatAssociationSetMessage(text3, name, this.Extent.Name);
					}
					return new ErrorLog.Record(errorCode, text2, ownerCell, string.Empty);
				}
			}
			return null;
		}

		// Token: 0x060045AF RID: 17839 RVA: 0x000F50F0 File Offset: 0x000F32F0
		internal IEnumerable<MemberPath> GetProjectedMembers()
		{
			foreach (MemberProjectedSlot memberProjectedSlot in this.GetMemberProjectedSlots())
			{
				yield return memberProjectedSlot.MemberPath;
			}
			IEnumerator<MemberProjectedSlot> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x060045B0 RID: 17840 RVA: 0x000F5100 File Offset: 0x000F3300
		private IEnumerable<MemberProjectedSlot> GetMemberProjectedSlots()
		{
			ProjectedSlot[] array = this.m_projectedSlots;
			for (int i = 0; i < array.Length; i++)
			{
				MemberProjectedSlot memberProjectedSlot = array[i] as MemberProjectedSlot;
				if (memberProjectedSlot != null)
				{
					yield return memberProjectedSlot;
				}
			}
			array = null;
			yield break;
		}

		// Token: 0x060045B1 RID: 17841 RVA: 0x000F5110 File Offset: 0x000F3310
		internal List<MemberProjectedSlot> GetAllQuerySlots()
		{
			HashSet<MemberProjectedSlot> hashSet = new HashSet<MemberProjectedSlot>(this.GetMemberProjectedSlots());
			hashSet.Add(new MemberProjectedSlot(this.SourceExtentMemberPath));
			foreach (MemberRestriction memberRestriction in this.Conditions)
			{
				hashSet.Add(memberRestriction.RestrictedMemberSlot);
			}
			return new List<MemberProjectedSlot>(hashSet);
		}

		// Token: 0x060045B2 RID: 17842 RVA: 0x000F5188 File Offset: 0x000F3388
		internal int GetProjectedPosition(MemberProjectedSlot slot)
		{
			for (int i = 0; i < this.m_projectedSlots.Length; i++)
			{
				if (ProjectedSlot.EqualityComparer.Equals(slot, this.m_projectedSlots[i]))
				{
					return i;
				}
			}
			return -1;
		}

		// Token: 0x060045B3 RID: 17843 RVA: 0x000F51C0 File Offset: 0x000F33C0
		internal List<int> GetProjectedPositions(MemberPath member)
		{
			List<int> list = new List<int>();
			for (int i = 0; i < this.m_projectedSlots.Length; i++)
			{
				MemberProjectedSlot memberProjectedSlot = this.m_projectedSlots[i] as MemberProjectedSlot;
				if (memberProjectedSlot != null && MemberPath.EqualityComparer.Equals(member, memberProjectedSlot.MemberPath))
				{
					list.Add(i);
				}
			}
			return list;
		}

		// Token: 0x060045B4 RID: 17844 RVA: 0x000F5214 File Offset: 0x000F3414
		internal List<int> GetProjectedPositions(IEnumerable<MemberPath> paths)
		{
			List<int> list = new List<int>();
			foreach (MemberPath memberPath in paths)
			{
				List<int> projectedPositions = this.GetProjectedPositions(memberPath);
				if (projectedPositions.Count == 0)
				{
					return null;
				}
				list.Add(projectedPositions[0]);
			}
			return list;
		}

		// Token: 0x060045B5 RID: 17845 RVA: 0x000F5284 File Offset: 0x000F3484
		internal List<int> GetAssociationEndSlots(AssociationEndMember endMember)
		{
			List<int> list = new List<int>();
			for (int i = 0; i < this.m_projectedSlots.Length; i++)
			{
				MemberProjectedSlot memberProjectedSlot = this.m_projectedSlots[i] as MemberProjectedSlot;
				if (memberProjectedSlot != null && memberProjectedSlot.MemberPath.RootEdmMember.Equals(endMember))
				{
					list.Add(i);
				}
			}
			return list;
		}

		// Token: 0x060045B6 RID: 17846 RVA: 0x000F52D8 File Offset: 0x000F34D8
		internal List<int> GetProjectedPositions(IEnumerable<MemberPath> paths, List<int> slotsToSearchFrom)
		{
			List<int> list = new List<int>();
			foreach (MemberPath memberPath in paths)
			{
				List<int> projectedPositions = this.GetProjectedPositions(memberPath);
				if (projectedPositions.Count == 0)
				{
					return null;
				}
				int num = -1;
				if (projectedPositions.Count > 1)
				{
					for (int i = 0; i < projectedPositions.Count; i++)
					{
						if (slotsToSearchFrom.Contains(projectedPositions[i]))
						{
							num = projectedPositions[i];
						}
					}
					if (num == -1)
					{
						return null;
					}
				}
				else
				{
					num = projectedPositions[0];
				}
				list.Add(num);
			}
			return list;
		}

		// Token: 0x060045B7 RID: 17847 RVA: 0x000F5390 File Offset: 0x000F3590
		internal void UpdateWhereClause(MemberDomainMap domainMap)
		{
			List<BoolExpression> list = new List<BoolExpression>();
			foreach (BoolExpression boolExpression in this.WhereClause.Atoms)
			{
				MemberRestriction memberRestriction = boolExpression.AsLiteral as MemberRestriction;
				IEnumerable<Constant> domain = domainMap.GetDomain(memberRestriction.RestrictedMemberSlot.MemberPath);
				MemberRestriction memberRestriction2 = memberRestriction.CreateCompleteMemberRestriction(domain);
				ScalarRestriction scalarRestriction = memberRestriction as ScalarRestriction;
				bool flag = scalarRestriction != null && !scalarRestriction.Domain.Contains(Constant.Null) && !scalarRestriction.Domain.Contains(Constant.NotNull) && !scalarRestriction.Domain.Contains(Constant.Undefined);
				if (flag)
				{
					domainMap.AddSentinel(memberRestriction2.RestrictedMemberSlot.MemberPath);
				}
				list.Add(BoolExpression.CreateLiteral(memberRestriction2, domainMap));
				if (flag)
				{
					domainMap.RemoveSentinel(memberRestriction2.RestrictedMemberSlot.MemberPath);
				}
			}
			if (list.Count > 0)
			{
				this.m_whereClause = BoolExpression.CreateAnd(list.ToArray());
			}
		}

		// Token: 0x060045B8 RID: 17848 RVA: 0x000F54A8 File Offset: 0x000F36A8
		internal BoolExpression GetBoolVar(int varNum)
		{
			return this.m_boolExprs[varNum];
		}

		// Token: 0x060045B9 RID: 17849 RVA: 0x000F54B8 File Offset: 0x000F36B8
		internal void InitializeBoolExpressions(int numBoolVars, int cellNum)
		{
			this.m_boolExprs = new List<BoolExpression>(numBoolVars);
			for (int i = 0; i < numBoolVars; i++)
			{
				this.m_boolExprs.Add(null);
			}
			this.m_boolExprs[cellNum] = BoolExpression.True;
		}

		// Token: 0x060045BA RID: 17850 RVA: 0x000F54FA File Offset: 0x000F36FA
		internal IEnumerable<MemberRestriction> GetConjunctsFromWhereClause()
		{
			return CellQuery.GetConjunctsFromWhereClause(this.m_whereClause);
		}

		// Token: 0x060045BB RID: 17851 RVA: 0x000F5507 File Offset: 0x000F3707
		internal IEnumerable<MemberRestriction> GetConjunctsFromOriginalWhereClause()
		{
			return CellQuery.GetConjunctsFromWhereClause(this.m_originalWhereClause);
		}

		// Token: 0x060045BC RID: 17852 RVA: 0x000F5514 File Offset: 0x000F3714
		private static IEnumerable<MemberRestriction> GetConjunctsFromWhereClause(BoolExpression whereClause)
		{
			foreach (BoolExpression boolExpression in whereClause.Atoms)
			{
				if (!boolExpression.IsTrue)
				{
					MemberRestriction memberRestriction = boolExpression.AsLiteral as MemberRestriction;
					yield return memberRestriction;
				}
			}
			IEnumerator<BoolExpression> enumerator = null;
			yield break;
			yield break;
		}

		// Token: 0x060045BD RID: 17853 RVA: 0x000F5524 File Offset: 0x000F3724
		internal void GetIdentifiers(CqlIdentifiers identifiers)
		{
			ProjectedSlot[] projectedSlots = this.m_projectedSlots;
			for (int i = 0; i < projectedSlots.Length; i++)
			{
				MemberProjectedSlot memberProjectedSlot = projectedSlots[i] as MemberProjectedSlot;
				if (memberProjectedSlot != null)
				{
					memberProjectedSlot.MemberPath.GetIdentifiers(identifiers);
				}
			}
			this.m_extentMemberPath.GetIdentifiers(identifiers);
		}

		// Token: 0x060045BE RID: 17854 RVA: 0x000F556C File Offset: 0x000F376C
		internal void CreateBasicCellRelation(ViewCellRelation viewCellRelation)
		{
			List<MemberProjectedSlot> allQuerySlots = this.GetAllQuerySlots();
			this.m_basicCellRelation = new BasicCellRelation(this, viewCellRelation, allQuerySlots);
		}

		// Token: 0x060045BF RID: 17855 RVA: 0x000F5590 File Offset: 0x000F3790
		internal override void ToCompactString(StringBuilder stringBuilder)
		{
			List<BoolExpression> boolExprs = this.m_boolExprs;
			int num = 0;
			bool flag = true;
			using (List<BoolExpression>.Enumerator enumerator = boolExprs.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					if (enumerator.Current != null)
					{
						if (!flag)
						{
							stringBuilder.Append(",");
						}
						else
						{
							stringBuilder.Append("[");
						}
						StringUtil.FormatStringBuilder(stringBuilder, "C{0}", new object[] { num });
						flag = false;
					}
					num++;
				}
			}
			if (flag)
			{
				this.ToFullString(stringBuilder);
				return;
			}
			stringBuilder.Append("]");
		}

		// Token: 0x060045C0 RID: 17856 RVA: 0x000F5638 File Offset: 0x000F3838
		internal override void ToFullString(StringBuilder builder)
		{
			builder.Append("SELECT ");
			if (this.m_selectDistinct == CellQuery.SelectDistinct.Yes)
			{
				builder.Append("DISTINCT ");
			}
			StringUtil.ToSeparatedString(builder, this.m_projectedSlots, ", ", "_");
			if (this.m_boolExprs.Count > 0)
			{
				builder.Append(", Bool[");
				StringUtil.ToSeparatedString(builder, this.m_boolExprs, ", ", "_");
				builder.Append("]");
			}
			builder.Append(" FROM ");
			this.m_extentMemberPath.ToFullString(builder);
			if (!this.m_whereClause.IsTrue)
			{
				builder.Append(" WHERE ");
				this.m_whereClause.ToFullString(builder);
			}
		}

		// Token: 0x060045C1 RID: 17857 RVA: 0x000F56F4 File Offset: 0x000F38F4
		public override string ToString()
		{
			return this.ToFullString();
		}

		// Token: 0x040018F5 RID: 6389
		private List<BoolExpression> m_boolExprs;

		// Token: 0x040018F6 RID: 6390
		private readonly ProjectedSlot[] m_projectedSlots;

		// Token: 0x040018F7 RID: 6391
		private BoolExpression m_whereClause;

		// Token: 0x040018F8 RID: 6392
		private readonly BoolExpression m_originalWhereClause;

		// Token: 0x040018F9 RID: 6393
		private readonly CellQuery.SelectDistinct m_selectDistinct;

		// Token: 0x040018FA RID: 6394
		private readonly MemberPath m_extentMemberPath;

		// Token: 0x040018FB RID: 6395
		private BasicCellRelation m_basicCellRelation;

		// Token: 0x02000BB7 RID: 2999
		internal enum SelectDistinct
		{
			// Token: 0x04002E91 RID: 11921
			Yes,
			// Token: 0x04002E92 RID: 11922
			No
		}
	}
}
