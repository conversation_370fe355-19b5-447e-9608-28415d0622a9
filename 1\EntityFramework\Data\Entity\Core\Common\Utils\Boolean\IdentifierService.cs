﻿using System;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.Utils.Boolean
{
	// Token: 0x02000614 RID: 1556
	internal abstract class IdentifierService<T_Identifier>
	{
		// Token: 0x06004BB0 RID: 19376 RVA: 0x0010A464 File Offset: 0x00108664
		private static IdentifierService<T_Identifier> GetIdentifierService()
		{
			Type typeFromHandle = typeof(T_Identifier);
			if (typeFromHandle.IsGenericType() && typeFromHandle.GetGenericTypeDefinition() == typeof(DomainConstraint<, >))
			{
				Type[] genericArguments = typeFromHandle.GetGenericArguments();
				Type type = genericArguments[0];
				Type type2 = genericArguments[1];
				return (IdentifierService<T_Identifier>)Activator.CreateInstance(typeof(IdentifierService<>.DomainConstraintIdentifierService<, >).MakeGenericType(new Type[] { typeFromHandle, type, type2 }));
			}
			return new IdentifierService<T_Identifier>.GenericIdentifierService();
		}

		// Token: 0x06004BB1 RID: 19377 RVA: 0x0010A4D9 File Offset: 0x001086D9
		private IdentifierService()
		{
		}

		// Token: 0x06004BB2 RID: 19378
		internal abstract Literal<T_Identifier> NegateLiteral(Literal<T_Identifier> literal);

		// Token: 0x06004BB3 RID: 19379
		internal abstract ConversionContext<T_Identifier> CreateConversionContext();

		// Token: 0x06004BB4 RID: 19380
		internal abstract BoolExpr<T_Identifier> LocalSimplify(BoolExpr<T_Identifier> expression);

		// Token: 0x04001A77 RID: 6775
		internal static readonly IdentifierService<T_Identifier> Instance = IdentifierService<T_Identifier>.GetIdentifierService();

		// Token: 0x02000C50 RID: 3152
		private class GenericIdentifierService : IdentifierService<T_Identifier>
		{
			// Token: 0x06006AA3 RID: 27299 RVA: 0x0016BAB7 File Offset: 0x00169CB7
			internal override Literal<T_Identifier> NegateLiteral(Literal<T_Identifier> literal)
			{
				return new Literal<T_Identifier>(literal.Term, !literal.IsTermPositive);
			}

			// Token: 0x06006AA4 RID: 27300 RVA: 0x0016BACD File Offset: 0x00169CCD
			internal override ConversionContext<T_Identifier> CreateConversionContext()
			{
				return new GenericConversionContext<T_Identifier>();
			}

			// Token: 0x06006AA5 RID: 27301 RVA: 0x0016BAD4 File Offset: 0x00169CD4
			internal override BoolExpr<T_Identifier> LocalSimplify(BoolExpr<T_Identifier> expression)
			{
				return expression.Accept<BoolExpr<T_Identifier>>(Simplifier<T_Identifier>.Instance);
			}
		}

		// Token: 0x02000C51 RID: 3153
		private class DomainConstraintIdentifierService<T_Variable, T_Element> : IdentifierService<DomainConstraint<T_Variable, T_Element>>
		{
			// Token: 0x06006AA7 RID: 27303 RVA: 0x0016BAE9 File Offset: 0x00169CE9
			internal override Literal<DomainConstraint<T_Variable, T_Element>> NegateLiteral(Literal<DomainConstraint<T_Variable, T_Element>> literal)
			{
				return new Literal<DomainConstraint<T_Variable, T_Element>>(new TermExpr<DomainConstraint<T_Variable, T_Element>>(literal.Term.Identifier.InvertDomainConstraint()), literal.IsTermPositive);
			}

			// Token: 0x06006AA8 RID: 27304 RVA: 0x0016BB0B File Offset: 0x00169D0B
			internal override ConversionContext<DomainConstraint<T_Variable, T_Element>> CreateConversionContext()
			{
				return new DomainConstraintConversionContext<T_Variable, T_Element>();
			}

			// Token: 0x06006AA9 RID: 27305 RVA: 0x0016BB12 File Offset: 0x00169D12
			internal override BoolExpr<DomainConstraint<T_Variable, T_Element>> LocalSimplify(BoolExpr<DomainConstraint<T_Variable, T_Element>> expression)
			{
				expression = NegationPusher.EliminateNot<T_Variable, T_Element>(expression);
				return expression.Accept<BoolExpr<DomainConstraint<T_Variable, T_Element>>>(Simplifier<DomainConstraint<T_Variable, T_Element>>.Instance);
			}
		}
	}
}
