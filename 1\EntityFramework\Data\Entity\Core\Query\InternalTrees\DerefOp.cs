﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x02000399 RID: 921
	internal sealed class DerefOp : ScalarOp
	{
		// Token: 0x06002CE2 RID: 11490 RVA: 0x0008F359 File Offset: 0x0008D559
		internal DerefOp(TypeUsage type)
			: base(OpType.Deref, type)
		{
		}

		// Token: 0x06002CE3 RID: 11491 RVA: 0x0008F364 File Offset: 0x0008D564
		private DerefOp()
			: base(OpType.Deref)
		{
		}

		// Token: 0x170008D1 RID: 2257
		// (get) Token: 0x06002CE4 RID: 11492 RVA: 0x0008F36E File Offset: 0x0008D56E
		internal override int Arity
		{
			get
			{
				return 1;
			}
		}

		// Token: 0x06002CE5 RID: 11493 RVA: 0x0008F371 File Offset: 0x0008D571
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002CE6 RID: 11494 RVA: 0x0008F37B File Offset: 0x0008D57B
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000F15 RID: 3861
		internal static readonly DerefOp Pattern = new DerefOp();
	}
}
