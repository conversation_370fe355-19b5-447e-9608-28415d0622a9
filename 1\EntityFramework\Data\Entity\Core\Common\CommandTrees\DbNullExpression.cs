﻿using System;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006D2 RID: 1746
	public sealed class DbNullExpression : DbExpression
	{
		// Token: 0x0600515E RID: 20830 RVA: 0x00122DE3 File Offset: 0x00120FE3
		internal DbNullExpression(TypeUsage type)
			: base(DbExpressionKind.Null, type, true)
		{
		}

		// Token: 0x0600515F RID: 20831 RVA: 0x00122DEF File Offset: 0x00120FEF
		public override void Accept(DbExpressionVisitor visitor)
		{
			Check.NotNull<DbExpressionVisitor>(visitor, "visitor");
			visitor.Visit(this);
		}

		// Token: 0x06005160 RID: 20832 RVA: 0x00122E04 File Offset: 0x00121004
		public override TResultType Accept<TResultType>(DbExpressionVisitor<TResultType> visitor)
		{
			Check.NotNull<DbExpressionVisitor<TResultType>>(visitor, "visitor");
			return visitor.Visit(this);
		}
	}
}
