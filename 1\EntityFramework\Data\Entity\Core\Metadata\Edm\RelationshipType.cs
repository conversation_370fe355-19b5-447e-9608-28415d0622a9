﻿using System;
using System.Threading;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004F6 RID: 1270
	public abstract class RelationshipType : EntityTypeBase
	{
		// Token: 0x06003F08 RID: 16136 RVA: 0x000D0AB0 File Offset: 0x000CECB0
		internal RelationshipType(string name, string namespaceName, DataSpace dataSpace)
			: base(name, namespaceName, dataSpace)
		{
		}

		// Token: 0x17000C62 RID: 3170
		// (get) Token: 0x06003F09 RID: 16137 RVA: 0x000D0ABC File Offset: 0x000CECBC
		public ReadOnlyMetadataCollection<RelationshipEndMember> RelationshipEndMembers
		{
			get
			{
				if (this._relationshipEndMembers == null)
				{
					FilteredReadOnlyMetadataCollection<RelationshipEndMember, EdmMember> filteredReadOnlyMetadataCollection = new FilteredReadOnlyMetadataCollection<RelationshipEndMember, EdmMember>(base.Members, new Predicate<EdmMember>(Helper.IsRelationshipEndMember));
					Interlocked.CompareExchange<ReadOnlyMetadataCollection<RelationshipEndMember>>(ref this._relationshipEndMembers, filteredReadOnlyMetadataCollection, null);
				}
				return this._relationshipEndMembers;
			}
		}

		// Token: 0x04001582 RID: 5506
		private ReadOnlyMetadataCollection<RelationshipEndMember> _relationshipEndMembers;
	}
}
