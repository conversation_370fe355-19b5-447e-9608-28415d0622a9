﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Resources;
using System.IO;
using System.Xml;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004D1 RID: 1233
	internal abstract class MetadataArtifactLoader
	{
		// Token: 0x17000C0B RID: 3083
		// (get) Token: 0x06003D25 RID: 15653
		public abstract string Path { get; }

		// Token: 0x06003D26 RID: 15654 RVA: 0x000C9719 File Offset: 0x000C7919
		public static MetadataArtifactLoader Create(string path, MetadataArtifactLoader.ExtensionCheck extensionCheck, string validExtension, ICollection<string> uriRegistry)
		{
			return MetadataArtifactLoader.Create(path, extensionCheck, validExtension, uriRegistry, new DefaultAssemblyResolver());
		}

		// Token: 0x06003D27 RID: 15655 RVA: 0x000C972C File Offset: 0x000C792C
		internal static MetadataArtifactLoader Create(string path, MetadataArtifactLoader.ExtensionCheck extensionCheck, string validExtension, ICollection<string> uriRegistry, MetadataArtifactAssemblyResolver resolver)
		{
			if (MetadataArtifactLoader.PathStartsWithResPrefix(path))
			{
				return MetadataArtifactLoaderCompositeResource.CreateResourceLoader(path, extensionCheck, validExtension, uriRegistry, resolver);
			}
			string text = MetadataArtifactLoader.NormalizeFilePaths(path);
			if (Directory.Exists(text))
			{
				return new MetadataArtifactLoaderCompositeFile(text, uriRegistry);
			}
			if (File.Exists(text))
			{
				if (extensionCheck != MetadataArtifactLoader.ExtensionCheck.Specific)
				{
					if (extensionCheck == MetadataArtifactLoader.ExtensionCheck.All)
					{
						if (!MetadataArtifactLoader.IsValidArtifact(text))
						{
							throw new MetadataException(Strings.InvalidMetadataPath);
						}
					}
				}
				else
				{
					MetadataArtifactLoader.CheckArtifactExtension(text, validExtension);
				}
				return new MetadataArtifactLoaderFile(text, uriRegistry);
			}
			throw new MetadataException(Strings.InvalidMetadataPath);
		}

		// Token: 0x06003D28 RID: 15656 RVA: 0x000C97A4 File Offset: 0x000C79A4
		public static MetadataArtifactLoader Create(List<MetadataArtifactLoader> allCollections)
		{
			return new MetadataArtifactLoaderComposite(allCollections);
		}

		// Token: 0x06003D29 RID: 15657 RVA: 0x000C97AC File Offset: 0x000C79AC
		public static MetadataArtifactLoader CreateCompositeFromFilePaths(IEnumerable<string> filePaths, string validExtension)
		{
			return MetadataArtifactLoader.CreateCompositeFromFilePaths(filePaths, validExtension, new DefaultAssemblyResolver());
		}

		// Token: 0x06003D2A RID: 15658 RVA: 0x000C97BC File Offset: 0x000C79BC
		internal static MetadataArtifactLoader CreateCompositeFromFilePaths(IEnumerable<string> filePaths, string validExtension, MetadataArtifactAssemblyResolver resolver)
		{
			MetadataArtifactLoader.ExtensionCheck extensionCheck;
			if (string.IsNullOrEmpty(validExtension))
			{
				extensionCheck = MetadataArtifactLoader.ExtensionCheck.All;
			}
			else
			{
				extensionCheck = MetadataArtifactLoader.ExtensionCheck.Specific;
			}
			List<MetadataArtifactLoader> list = new List<MetadataArtifactLoader>();
			HashSet<string> hashSet = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
			foreach (string text in filePaths)
			{
				if (string.IsNullOrEmpty(text))
				{
					throw new MetadataException(Strings.NotValidInputPath, new ArgumentException(Strings.ADP_CollectionParameterElementIsNullOrEmpty("filePaths")));
				}
				string text2 = text.Trim();
				if (text2.Length > 0)
				{
					list.Add(MetadataArtifactLoader.Create(text2, extensionCheck, validExtension, hashSet, resolver));
				}
			}
			return MetadataArtifactLoader.Create(list);
		}

		// Token: 0x06003D2B RID: 15659 RVA: 0x000C9868 File Offset: 0x000C7A68
		public static MetadataArtifactLoader CreateCompositeFromXmlReaders(IEnumerable<XmlReader> xmlReaders)
		{
			List<MetadataArtifactLoader> list = new List<MetadataArtifactLoader>();
			foreach (XmlReader xmlReader in xmlReaders)
			{
				if (xmlReader == null)
				{
					throw new ArgumentException(Strings.ADP_CollectionParameterElementIsNull("xmlReaders"));
				}
				list.Add(new MetadataArtifactLoaderXmlReaderWrapper(xmlReader));
			}
			return MetadataArtifactLoader.Create(list);
		}

		// Token: 0x06003D2C RID: 15660 RVA: 0x000C98D4 File Offset: 0x000C7AD4
		internal static void CheckArtifactExtension(string path, string validExtension)
		{
			string extension = MetadataArtifactLoader.GetExtension(path);
			if (!extension.Equals(validExtension, StringComparison.OrdinalIgnoreCase))
			{
				throw new MetadataException(Strings.InvalidFileExtension(path, extension, validExtension));
			}
		}

		// Token: 0x06003D2D RID: 15661 RVA: 0x000C9900 File Offset: 0x000C7B00
		public virtual List<string> GetOriginalPaths()
		{
			return new List<string>(new string[] { this.Path });
		}

		// Token: 0x06003D2E RID: 15662 RVA: 0x000C9918 File Offset: 0x000C7B18
		public virtual List<string> GetOriginalPaths(DataSpace spaceToGet)
		{
			List<string> list = new List<string>();
			if (MetadataArtifactLoader.IsArtifactOfDataSpace(this.Path, spaceToGet))
			{
				list.Add(this.Path);
			}
			return list;
		}

		// Token: 0x17000C0C RID: 3084
		// (get) Token: 0x06003D2F RID: 15663 RVA: 0x000C9946 File Offset: 0x000C7B46
		public virtual bool IsComposite
		{
			get
			{
				return false;
			}
		}

		// Token: 0x06003D30 RID: 15664
		public abstract List<string> GetPaths();

		// Token: 0x06003D31 RID: 15665
		public abstract List<string> GetPaths(DataSpace spaceToGet);

		// Token: 0x06003D32 RID: 15666 RVA: 0x000C9949 File Offset: 0x000C7B49
		public List<XmlReader> GetReaders()
		{
			return this.GetReaders(null);
		}

		// Token: 0x06003D33 RID: 15667
		public abstract List<XmlReader> GetReaders(Dictionary<MetadataArtifactLoader, XmlReader> sourceDictionary);

		// Token: 0x06003D34 RID: 15668
		public abstract List<XmlReader> CreateReaders(DataSpace spaceToGet);

		// Token: 0x06003D35 RID: 15669 RVA: 0x000C9952 File Offset: 0x000C7B52
		internal static bool PathStartsWithResPrefix(string path)
		{
			return path.StartsWith(MetadataArtifactLoader.resPathPrefix, StringComparison.OrdinalIgnoreCase);
		}

		// Token: 0x06003D36 RID: 15670 RVA: 0x000C9960 File Offset: 0x000C7B60
		protected static bool IsCSpaceArtifact(string resource)
		{
			string extension = MetadataArtifactLoader.GetExtension(resource);
			return !string.IsNullOrEmpty(extension) && string.Compare(extension, ".csdl", StringComparison.OrdinalIgnoreCase) == 0;
		}

		// Token: 0x06003D37 RID: 15671 RVA: 0x000C9990 File Offset: 0x000C7B90
		protected static bool IsSSpaceArtifact(string resource)
		{
			string extension = MetadataArtifactLoader.GetExtension(resource);
			return !string.IsNullOrEmpty(extension) && string.Compare(extension, ".ssdl", StringComparison.OrdinalIgnoreCase) == 0;
		}

		// Token: 0x06003D38 RID: 15672 RVA: 0x000C99C0 File Offset: 0x000C7BC0
		protected static bool IsCSSpaceArtifact(string resource)
		{
			string extension = MetadataArtifactLoader.GetExtension(resource);
			return !string.IsNullOrEmpty(extension) && string.Compare(extension, ".msl", StringComparison.OrdinalIgnoreCase) == 0;
		}

		// Token: 0x06003D39 RID: 15673 RVA: 0x000C99F0 File Offset: 0x000C7BF0
		private static string GetExtension(string resource)
		{
			if (string.IsNullOrEmpty(resource))
			{
				return string.Empty;
			}
			int num = resource.LastIndexOf('.');
			if (num < 0)
			{
				return string.Empty;
			}
			return resource.Substring(num);
		}

		// Token: 0x06003D3A RID: 15674 RVA: 0x000C9A28 File Offset: 0x000C7C28
		internal static bool IsValidArtifact(string resource)
		{
			string extension = MetadataArtifactLoader.GetExtension(resource);
			return !string.IsNullOrEmpty(extension) && (string.Compare(extension, ".csdl", StringComparison.OrdinalIgnoreCase) == 0 || string.Compare(extension, ".ssdl", StringComparison.OrdinalIgnoreCase) == 0 || string.Compare(extension, ".msl", StringComparison.OrdinalIgnoreCase) == 0);
		}

		// Token: 0x06003D3B RID: 15675 RVA: 0x000C9A73 File Offset: 0x000C7C73
		protected static bool IsArtifactOfDataSpace(string resource, DataSpace dataSpace)
		{
			if (dataSpace == DataSpace.CSpace)
			{
				return MetadataArtifactLoader.IsCSpaceArtifact(resource);
			}
			if (dataSpace == DataSpace.SSpace)
			{
				return MetadataArtifactLoader.IsSSpaceArtifact(resource);
			}
			return dataSpace == DataSpace.CSSpace && MetadataArtifactLoader.IsCSSpaceArtifact(resource);
		}

		// Token: 0x06003D3C RID: 15676 RVA: 0x000C9A98 File Offset: 0x000C7C98
		internal static string NormalizeFilePaths(string path)
		{
			bool flag = true;
			if (!string.IsNullOrEmpty(path))
			{
				path = path.Trim();
				if (path.StartsWith("~", StringComparison.Ordinal))
				{
					path = new AspProxy().MapWebPath(path);
					flag = false;
				}
				if (path.Length == 2 && path[1] == global::System.IO.Path.VolumeSeparatorChar)
				{
					path += global::System.IO.Path.DirectorySeparatorChar.ToString();
				}
				else
				{
					string text = DbProviderServices.ExpandDataDirectory(path);
					if (!path.Equals(text, StringComparison.Ordinal))
					{
						path = text;
						flag = false;
					}
				}
			}
			try
			{
				if (flag)
				{
					path = global::System.IO.Path.GetFullPath(path);
				}
			}
			catch (ArgumentException ex)
			{
				throw new MetadataException(Strings.NotValidInputPath, ex);
			}
			catch (NotSupportedException ex2)
			{
				throw new MetadataException(Strings.NotValidInputPath, ex2);
			}
			catch (PathTooLongException)
			{
				throw new MetadataException(Strings.NotValidInputPath);
			}
			return path;
		}

		// Token: 0x040014EC RID: 5356
		protected static readonly string resPathPrefix = "res://";

		// Token: 0x040014ED RID: 5357
		protected static readonly string resPathSeparator = "/";

		// Token: 0x040014EE RID: 5358
		protected static readonly string altPathSeparator = "\\";

		// Token: 0x040014EF RID: 5359
		protected static readonly string wildcard = "*";

		// Token: 0x02000AF4 RID: 2804
		public enum ExtensionCheck
		{
			// Token: 0x04002C5A RID: 11354
			None,
			// Token: 0x04002C5B RID: 11355
			Specific,
			// Token: 0x04002C5C RID: 11356
			All
		}
	}
}
