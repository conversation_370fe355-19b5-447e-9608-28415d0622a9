﻿using System;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003CA RID: 970
	internal sealed class OuterApplyOp : ApplyBaseOp
	{
		// Token: 0x06002E8C RID: 11916 RVA: 0x00093BFA File Offset: 0x00091DFA
		private OuterApplyOp()
			: base(OpType.OuterApply)
		{
		}

		// Token: 0x06002E8D RID: 11917 RVA: 0x00093C04 File Offset: 0x00091E04
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002E8E RID: 11918 RVA: 0x00093C0E File Offset: 0x00091E0E
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000FB4 RID: 4020
		internal static readonly OuterApplyOp Instance = new OuterApplyOp();

		// Token: 0x04000FB5 RID: 4021
		internal static readonly OuterApplyOp Pattern = OuterApplyOp.Instance;
	}
}
