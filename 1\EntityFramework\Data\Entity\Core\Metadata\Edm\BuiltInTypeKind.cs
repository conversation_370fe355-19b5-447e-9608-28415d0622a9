﻿using System;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x0200048B RID: 1163
	public enum BuiltInTypeKind
	{
		// Token: 0x04001323 RID: 4899
		AssociationEndMember,
		// Token: 0x04001324 RID: 4900
		AssociationSetEnd,
		// Token: 0x04001325 RID: 4901
		AssociationSet,
		// Token: 0x04001326 RID: 4902
		AssociationType,
		// Token: 0x04001327 RID: 4903
		EntitySetBase,
		// Token: 0x04001328 RID: 4904
		EntityTypeBase,
		// Token: 0x04001329 RID: 4905
		CollectionType,
		// Token: 0x0400132A RID: 4906
		CollectionKind,
		// Token: 0x0400132B RID: 4907
		ComplexType,
		// Token: 0x0400132C RID: 4908
		Documentation,
		// Token: 0x0400132D RID: 4909
		OperationAction,
		// Token: 0x0400132E RID: 4910
		EdmType,
		// Token: 0x0400132F RID: 4911
		EntityContainer,
		// Token: 0x04001330 RID: 4912
		EntitySet,
		// Token: 0x04001331 RID: 4913
		EntityType,
		// Token: 0x04001332 RID: 4914
		EnumType,
		// Token: 0x04001333 RID: 4915
		EnumMember,
		// Token: 0x04001334 RID: 4916
		Facet,
		// Token: 0x04001335 RID: 4917
		EdmFunction,
		// Token: 0x04001336 RID: 4918
		FunctionParameter,
		// Token: 0x04001337 RID: 4919
		GlobalItem,
		// Token: 0x04001338 RID: 4920
		MetadataProperty,
		// Token: 0x04001339 RID: 4921
		NavigationProperty,
		// Token: 0x0400133A RID: 4922
		MetadataItem,
		// Token: 0x0400133B RID: 4923
		EdmMember,
		// Token: 0x0400133C RID: 4924
		ParameterMode,
		// Token: 0x0400133D RID: 4925
		PrimitiveType,
		// Token: 0x0400133E RID: 4926
		PrimitiveTypeKind,
		// Token: 0x0400133F RID: 4927
		EdmProperty,
		// Token: 0x04001340 RID: 4928
		ProviderManifest,
		// Token: 0x04001341 RID: 4929
		ReferentialConstraint,
		// Token: 0x04001342 RID: 4930
		RefType,
		// Token: 0x04001343 RID: 4931
		RelationshipEndMember,
		// Token: 0x04001344 RID: 4932
		RelationshipMultiplicity,
		// Token: 0x04001345 RID: 4933
		RelationshipSet,
		// Token: 0x04001346 RID: 4934
		RelationshipType,
		// Token: 0x04001347 RID: 4935
		RowType,
		// Token: 0x04001348 RID: 4936
		SimpleType,
		// Token: 0x04001349 RID: 4937
		StructuralType,
		// Token: 0x0400134A RID: 4938
		TypeUsage
	}
}
