﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;

namespace System.Data.Entity.Core.Objects.ELinq
{
	// Token: 0x02000461 RID: 1121
	internal sealed class Funcletizer
	{
		// Token: 0x06003748 RID: 14152 RVA: 0x000B2734 File Offset: 0x000B0934
		private Funcletizer(Funcletizer.Mode mode, ObjectContext rootContext, ParameterExpression rootContextParameter, ReadOnlyCollection<ParameterExpression> compiledQueryParameters)
		{
			this._mode = mode;
			this._rootContext = rootContext;
			this._rootContextParameter = rootContextParameter;
			this._compiledQueryParameters = compiledQueryParameters;
			if (this._rootContextParameter != null && this._rootContext != null)
			{
				this._rootContextExpression = Expression.Constant(this._rootContext);
			}
		}

		// Token: 0x06003749 RID: 14153 RVA: 0x000B2790 File Offset: 0x000B0990
		internal static Funcletizer CreateCompiledQueryEvaluationFuncletizer(ObjectContext rootContext, ParameterExpression rootContextParameter, ReadOnlyCollection<ParameterExpression> compiledQueryParameters)
		{
			return new Funcletizer(Funcletizer.Mode.CompiledQueryEvaluation, rootContext, rootContextParameter, compiledQueryParameters);
		}

		// Token: 0x0600374A RID: 14154 RVA: 0x000B279B File Offset: 0x000B099B
		internal static Funcletizer CreateCompiledQueryLockdownFuncletizer()
		{
			return new Funcletizer(Funcletizer.Mode.CompiledQueryLockdown, null, null, null);
		}

		// Token: 0x0600374B RID: 14155 RVA: 0x000B27A6 File Offset: 0x000B09A6
		internal static Funcletizer CreateQueryFuncletizer(ObjectContext rootContext)
		{
			return new Funcletizer(Funcletizer.Mode.ConventionalQuery, rootContext, null, null);
		}

		// Token: 0x17000A9F RID: 2719
		// (get) Token: 0x0600374C RID: 14156 RVA: 0x000B27B1 File Offset: 0x000B09B1
		internal ObjectContext RootContext
		{
			get
			{
				return this._rootContext;
			}
		}

		// Token: 0x17000AA0 RID: 2720
		// (get) Token: 0x0600374D RID: 14157 RVA: 0x000B27B9 File Offset: 0x000B09B9
		internal ParameterExpression RootContextParameter
		{
			get
			{
				return this._rootContextParameter;
			}
		}

		// Token: 0x17000AA1 RID: 2721
		// (get) Token: 0x0600374E RID: 14158 RVA: 0x000B27C1 File Offset: 0x000B09C1
		internal ConstantExpression RootContextExpression
		{
			get
			{
				return this._rootContextExpression;
			}
		}

		// Token: 0x17000AA2 RID: 2722
		// (get) Token: 0x0600374F RID: 14159 RVA: 0x000B27C9 File Offset: 0x000B09C9
		internal bool IsCompiledQuery
		{
			get
			{
				return this._mode == Funcletizer.Mode.CompiledQueryEvaluation || this._mode == Funcletizer.Mode.CompiledQueryLockdown;
			}
		}

		// Token: 0x06003750 RID: 14160 RVA: 0x000B27E0 File Offset: 0x000B09E0
		internal Expression Funcletize(Expression expression, out Func<bool> recompileRequired)
		{
			expression = this.ReplaceRootContextParameter(expression);
			Func<Expression, bool> func;
			Func<Expression, bool> func2;
			if (this._mode == Funcletizer.Mode.CompiledQueryEvaluation)
			{
				func = Funcletizer.Nominate(expression, new Func<Expression, bool>(this.IsClosureExpression));
				func2 = Funcletizer.Nominate(expression, new Func<Expression, bool>(this.IsCompiledQueryParameterVariable));
			}
			else if (this._mode == Funcletizer.Mode.CompiledQueryLockdown)
			{
				func = Funcletizer.Nominate(expression, new Func<Expression, bool>(this.IsClosureExpression));
				func2 = (Expression exp) => false;
			}
			else
			{
				func = Funcletizer.Nominate(expression, new Func<Expression, bool>(this.IsImmutable));
				func2 = Funcletizer.Nominate(expression, new Func<Expression, bool>(this.IsClosureExpression));
			}
			Funcletizer.FuncletizingVisitor funcletizingVisitor = new Funcletizer.FuncletizingVisitor(this, func, func2);
			Expression expression2 = funcletizingVisitor.Visit(expression);
			recompileRequired = funcletizingVisitor.GetRecompileRequiredFunction();
			return expression2;
		}

		// Token: 0x06003751 RID: 14161 RVA: 0x000B28A2 File Offset: 0x000B0AA2
		private Expression ReplaceRootContextParameter(Expression expression)
		{
			if (this._rootContextExpression != null)
			{
				return EntityExpressionVisitor.Visit(expression, delegate(Expression exp, Func<Expression, Expression> baseVisit)
				{
					if (exp != this._rootContextParameter)
					{
						return baseVisit(exp);
					}
					return this._rootContextExpression;
				});
			}
			return expression;
		}

		// Token: 0x06003752 RID: 14162 RVA: 0x000B28C0 File Offset: 0x000B0AC0
		private static Func<Expression, bool> Nominate(Expression expression, Func<Expression, bool> localCriterion)
		{
			HashSet<Expression> candidates = new HashSet<Expression>();
			bool cannotBeNominated = false;
			Func<Expression, Func<Expression, Expression>, Expression> func = delegate(Expression exp, Func<Expression, Expression> baseVisit)
			{
				if (exp != null)
				{
					bool cannotBeNominated2 = cannotBeNominated;
					cannotBeNominated = false;
					baseVisit(exp);
					if (!cannotBeNominated)
					{
						if (localCriterion(exp))
						{
							candidates.Add(exp);
						}
						else
						{
							cannotBeNominated = true;
						}
					}
					cannotBeNominated = cannotBeNominated || cannotBeNominated2;
				}
				return exp;
			};
			EntityExpressionVisitor.Visit(expression, func);
			return new Func<Expression, bool>(candidates.Contains);
		}

		// Token: 0x06003753 RID: 14163 RVA: 0x000B2914 File Offset: 0x000B0B14
		private bool IsImmutable(Expression expression)
		{
			if (expression == null)
			{
				return false;
			}
			ExpressionType nodeType = expression.NodeType;
			if (nodeType <= ExpressionType.Convert)
			{
				if (nodeType == ExpressionType.Constant)
				{
					return true;
				}
				if (nodeType == ExpressionType.Convert)
				{
					return true;
				}
			}
			else
			{
				if (nodeType == ExpressionType.New)
				{
					PrimitiveType primitiveType;
					return ClrProviderManifest.Instance.TryGetPrimitiveType(TypeSystem.GetNonNullableType(expression.Type), out primitiveType);
				}
				if (nodeType == ExpressionType.NewArrayInit)
				{
					return typeof(byte[]) == expression.Type;
				}
			}
			return false;
		}

		// Token: 0x06003754 RID: 14164 RVA: 0x000B2984 File Offset: 0x000B0B84
		private bool IsClosureExpression(Expression expression)
		{
			if (expression == null)
			{
				return false;
			}
			if (this.IsImmutable(expression))
			{
				return true;
			}
			if (ExpressionType.MemberAccess == expression.NodeType)
			{
				MemberExpression memberExpression = (MemberExpression)expression;
				return memberExpression.Member.MemberType != MemberTypes.Property || ExpressionConverter.CanFuncletizePropertyInfo((PropertyInfo)memberExpression.Member);
			}
			return false;
		}

		// Token: 0x06003755 RID: 14165 RVA: 0x000B29D8 File Offset: 0x000B0BD8
		private bool IsCompiledQueryParameterVariable(Expression expression)
		{
			if (expression == null)
			{
				return false;
			}
			if (this.IsClosureExpression(expression))
			{
				return true;
			}
			if (ExpressionType.Parameter == expression.NodeType)
			{
				ParameterExpression parameterExpression = (ParameterExpression)expression;
				return this._compiledQueryParameters.Contains(parameterExpression);
			}
			return false;
		}

		// Token: 0x06003756 RID: 14166 RVA: 0x000B2A14 File Offset: 0x000B0C14
		private bool TryGetTypeUsageForTerminal(Expression expression, out TypeUsage typeUsage)
		{
			Type type = expression.Type;
			if (this._rootContext.Perspective.TryGetTypeByName(TypeSystem.GetNonNullableType(type).FullNameWithNesting(), false, out typeUsage) && TypeSemantics.IsScalarType(typeUsage))
			{
				if (expression.NodeType == ExpressionType.Convert)
				{
					type = ((UnaryExpression)expression).Operand.Type;
				}
				if (type.IsValueType && Nullable.GetUnderlyingType(type) == null && TypeSemantics.IsNullable(typeUsage))
				{
					typeUsage = typeUsage.ShallowCopy(new FacetValues
					{
						Nullable = new bool?(false)
					});
				}
				return true;
			}
			typeUsage = null;
			return false;
		}

		// Token: 0x06003757 RID: 14167 RVA: 0x000B2AB0 File Offset: 0x000B0CB0
		internal string GenerateParameterName()
		{
			IFormatProvider invariantCulture = CultureInfo.InvariantCulture;
			string text = "{0}{1}";
			object[] array = new object[2];
			array[0] = "p__linq__";
			int num = 1;
			long parameterNumber = this._parameterNumber;
			this._parameterNumber = parameterNumber + 1L;
			array[num] = parameterNumber;
			return string.Format(invariantCulture, text, array);
		}

		// Token: 0x0400120B RID: 4619
		private readonly ParameterExpression _rootContextParameter;

		// Token: 0x0400120C RID: 4620
		private readonly ObjectContext _rootContext;

		// Token: 0x0400120D RID: 4621
		private readonly ConstantExpression _rootContextExpression;

		// Token: 0x0400120E RID: 4622
		private readonly ReadOnlyCollection<ParameterExpression> _compiledQueryParameters;

		// Token: 0x0400120F RID: 4623
		private readonly Funcletizer.Mode _mode;

		// Token: 0x04001210 RID: 4624
		private readonly HashSet<Expression> _linqExpressionStack = new HashSet<Expression>();

		// Token: 0x04001211 RID: 4625
		private const string s_parameterPrefix = "p__linq__";

		// Token: 0x04001212 RID: 4626
		private long _parameterNumber;

		// Token: 0x02000AA1 RID: 2721
		private enum Mode
		{
			// Token: 0x04002B41 RID: 11073
			CompiledQueryLockdown,
			// Token: 0x04002B42 RID: 11074
			CompiledQueryEvaluation,
			// Token: 0x04002B43 RID: 11075
			ConventionalQuery
		}

		// Token: 0x02000AA2 RID: 2722
		private sealed class FuncletizingVisitor : EntityExpressionVisitor
		{
			// Token: 0x06006291 RID: 25233 RVA: 0x0015560B File Offset: 0x0015380B
			internal FuncletizingVisitor(Funcletizer funcletizer, Func<Expression, bool> isClientConstant, Func<Expression, bool> isClientVariable)
			{
				this._funcletizer = funcletizer;
				this._isClientConstant = isClientConstant;
				this._isClientVariable = isClientVariable;
			}

			// Token: 0x06006292 RID: 25234 RVA: 0x00155633 File Offset: 0x00153833
			internal Func<bool> GetRecompileRequiredFunction()
			{
				ReadOnlyCollection<Func<bool>> recompileRequiredDelegates = new ReadOnlyCollection<Func<bool>>(this._recompileRequiredDelegates);
				return () => recompileRequiredDelegates.Any((Func<bool> d) => d());
			}

			// Token: 0x06006293 RID: 25235 RVA: 0x00155658 File Offset: 0x00153858
			internal override Expression Visit(Expression exp)
			{
				if (exp != null)
				{
					if (!this._funcletizer._linqExpressionStack.Add(exp))
					{
						throw new InvalidOperationException(Strings.ELinq_CycleDetected);
					}
					try
					{
						if (this._isClientConstant(exp))
						{
							return this.InlineValue(exp, false);
						}
						if (!this._isClientVariable(exp))
						{
							return base.Visit(exp);
						}
						TypeUsage typeUsage;
						if (this._funcletizer.TryGetTypeUsageForTerminal(exp, out typeUsage))
						{
							return new QueryParameterExpression(typeUsage.Parameter(this._funcletizer.GenerateParameterName()), exp, this._funcletizer._compiledQueryParameters);
						}
						if (this._funcletizer.IsCompiledQuery)
						{
							throw Funcletizer.FuncletizingVisitor.InvalidCompiledQueryParameterException(exp);
						}
						return this.InlineValue(exp, true);
					}
					finally
					{
						this._funcletizer._linqExpressionStack.Remove(exp);
					}
				}
				return base.Visit(exp);
			}

			// Token: 0x06006294 RID: 25236 RVA: 0x00155740 File Offset: 0x00153940
			private static NotSupportedException InvalidCompiledQueryParameterException(Expression expression)
			{
				ParameterExpression parameterExpression;
				if (expression.NodeType == ExpressionType.Parameter)
				{
					parameterExpression = (ParameterExpression)expression;
				}
				else
				{
					HashSet<ParameterExpression> parameters = new HashSet<ParameterExpression>();
					EntityExpressionVisitor.Visit(expression, delegate(Expression exp, Func<Expression, Expression> baseVisit)
					{
						if (exp != null && exp.NodeType == ExpressionType.Parameter)
						{
							parameters.Add((ParameterExpression)exp);
						}
						return baseVisit(exp);
					});
					if (parameters.Count != 1)
					{
						return new NotSupportedException(Strings.CompiledELinq_UnsupportedParameterTypes(expression.Type.FullName));
					}
					parameterExpression = parameters.Single<ParameterExpression>();
				}
				if (parameterExpression.Type.Equals(expression.Type))
				{
					return new NotSupportedException(Strings.CompiledELinq_UnsupportedNamedParameterType(parameterExpression.Name, parameterExpression.Type.FullName));
				}
				return new NotSupportedException(Strings.CompiledELinq_UnsupportedNamedParameterUseAsType(parameterExpression.Name, expression.Type.FullName));
			}

			// Token: 0x06006295 RID: 25237 RVA: 0x001557FE File Offset: 0x001539FE
			private static Func<object> CompileExpression(Expression expression)
			{
				return Expression.Lambda<Func<object>>(TypeSystem.EnsureType(expression, typeof(object)), new ParameterExpression[0]).Compile();
			}

			// Token: 0x06006296 RID: 25238 RVA: 0x00155820 File Offset: 0x00153A20
			private Expression InlineValue(Expression expression, bool recompileOnChange)
			{
				Func<object> func = null;
				object obj = null;
				if (expression.NodeType == ExpressionType.Constant)
				{
					obj = ((ConstantExpression)expression).Value;
				}
				else
				{
					bool flag = false;
					if (expression.NodeType == ExpressionType.Convert)
					{
						UnaryExpression unaryExpression = (UnaryExpression)expression;
						if (!recompileOnChange && unaryExpression.Operand.NodeType == ExpressionType.Constant && typeof(IQueryable).IsAssignableFrom(unaryExpression.Operand.Type))
						{
							obj = ((ConstantExpression)unaryExpression.Operand).Value;
							flag = true;
						}
					}
					if (!flag)
					{
						func = Funcletizer.FuncletizingVisitor.CompileExpression(expression);
						obj = func();
					}
				}
				ObjectQuery objectQuery = (obj as IQueryable).TryGetObjectQuery();
				Expression expression2;
				if (objectQuery != null)
				{
					expression2 = this.InlineObjectQuery(objectQuery, objectQuery.GetType());
				}
				else
				{
					LambdaExpression lambdaExpression = obj as LambdaExpression;
					if (lambdaExpression != null)
					{
						expression2 = this.InlineExpression(Expression.Quote(lambdaExpression));
					}
					else
					{
						expression2 = ((expression.NodeType == ExpressionType.Constant) ? expression : Expression.Constant(obj, expression.Type));
					}
				}
				if (recompileOnChange)
				{
					this.AddRecompileRequiredDelegates(func, obj);
				}
				return expression2;
			}

			// Token: 0x06006297 RID: 25239 RVA: 0x00155918 File Offset: 0x00153B18
			private void AddRecompileRequiredDelegates(Func<object> getValue, object value)
			{
				Funcletizer.FuncletizingVisitor.<>c__DisplayClass10_0 CS$<>8__locals1 = new Funcletizer.FuncletizingVisitor.<>c__DisplayClass10_0();
				CS$<>8__locals1.getValue = getValue;
				CS$<>8__locals1.value = value;
				CS$<>8__locals1.originalQuery = (CS$<>8__locals1.value as IQueryable).TryGetObjectQuery();
				if (CS$<>8__locals1.originalQuery == null)
				{
					if (CS$<>8__locals1.getValue != null)
					{
						this._recompileRequiredDelegates.Add(() => CS$<>8__locals1.value != CS$<>8__locals1.getValue());
					}
					return;
				}
				MergeOption? originalMergeOption = CS$<>8__locals1.originalQuery.QueryState.UserSpecifiedMergeOption;
				if (CS$<>8__locals1.getValue == null)
				{
					this._recompileRequiredDelegates.Add(delegate
					{
						MergeOption? userSpecifiedMergeOption = CS$<>8__locals1.originalQuery.QueryState.UserSpecifiedMergeOption;
						MergeOption? originalMergeOption3 = originalMergeOption;
						return !((userSpecifiedMergeOption.GetValueOrDefault() == originalMergeOption3.GetValueOrDefault()) & (userSpecifiedMergeOption != null == (originalMergeOption3 != null)));
					});
					return;
				}
				this._recompileRequiredDelegates.Add(delegate
				{
					ObjectQuery objectQuery = (CS$<>8__locals1.getValue() as IQueryable).TryGetObjectQuery();
					if (CS$<>8__locals1.originalQuery == objectQuery)
					{
						MergeOption? userSpecifiedMergeOption2 = objectQuery.QueryState.UserSpecifiedMergeOption;
						MergeOption? originalMergeOption2 = originalMergeOption;
						return !((userSpecifiedMergeOption2.GetValueOrDefault() == originalMergeOption2.GetValueOrDefault()) & (userSpecifiedMergeOption2 != null == (originalMergeOption2 != null)));
					}
					return true;
				});
			}

			// Token: 0x06006298 RID: 25240 RVA: 0x001559DC File Offset: 0x00153BDC
			private Expression InlineObjectQuery(ObjectQuery inlineQuery, Type expressionType)
			{
				Expression expression;
				if (this._funcletizer._mode == Funcletizer.Mode.CompiledQueryLockdown)
				{
					expression = Expression.Constant(inlineQuery, expressionType);
				}
				else
				{
					if (this._funcletizer._rootContext != inlineQuery.QueryState.ObjectContext)
					{
						throw new NotSupportedException(Strings.ELinq_UnsupportedDifferentContexts);
					}
					expression = inlineQuery.GetExpression();
					if (!(inlineQuery.QueryState is EntitySqlQueryState))
					{
						expression = this.InlineExpression(expression);
					}
					expression = TypeSystem.EnsureType(expression, expressionType);
				}
				return expression;
			}

			// Token: 0x06006299 RID: 25241 RVA: 0x00155A48 File Offset: 0x00153C48
			private Expression InlineExpression(Expression exp)
			{
				Func<bool> func;
				exp = this._funcletizer.Funcletize(exp, out func);
				if (!this._funcletizer.IsCompiledQuery)
				{
					this._recompileRequiredDelegates.Add(func);
				}
				return exp;
			}

			// Token: 0x04002B44 RID: 11076
			private readonly Funcletizer _funcletizer;

			// Token: 0x04002B45 RID: 11077
			private readonly Func<Expression, bool> _isClientConstant;

			// Token: 0x04002B46 RID: 11078
			private readonly Func<Expression, bool> _isClientVariable;

			// Token: 0x04002B47 RID: 11079
			private readonly List<Func<bool>> _recompileRequiredDelegates = new List<Func<bool>>();
		}
	}
}
