﻿using System;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x0200038B RID: 907
	internal abstract class ColumnMapVisitorWithResults<TResultType, TArgType>
	{
		// Token: 0x06002C08 RID: 11272 RVA: 0x0008E09C File Offset: 0x0008C29C
		protected EntityIdentity VisitEntityIdentity(EntityIdentity entityIdentity, TArgType arg)
		{
			DiscriminatedEntityIdentity discriminatedEntityIdentity = entityIdentity as DiscriminatedEntityIdentity;
			if (discriminatedEntityIdentity != null)
			{
				return this.VisitEntityIdentity(discriminatedEntityIdentity, arg);
			}
			return this.VisitEntityIdentity((SimpleEntityIdentity)entityIdentity, arg);
		}

		// Token: 0x06002C09 RID: 11273 RVA: 0x0008E0C9 File Offset: 0x0008C2C9
		protected virtual EntityIdentity VisitEntityIdentity(DiscriminatedEntityIdentity entityIdentity, TArgType arg)
		{
			return entityIdentity;
		}

		// Token: 0x06002C0A RID: 11274 RVA: 0x0008E0CC File Offset: 0x0008C2CC
		protected virtual EntityIdentity VisitEntityIdentity(SimpleEntityIdentity entityIdentity, TArgType arg)
		{
			return entityIdentity;
		}

		// Token: 0x06002C0B RID: 11275
		internal abstract TResultType Visit(ComplexTypeColumnMap columnMap, TArgType arg);

		// Token: 0x06002C0C RID: 11276
		internal abstract TResultType Visit(DiscriminatedCollectionColumnMap columnMap, TArgType arg);

		// Token: 0x06002C0D RID: 11277
		internal abstract TResultType Visit(EntityColumnMap columnMap, TArgType arg);

		// Token: 0x06002C0E RID: 11278
		internal abstract TResultType Visit(SimplePolymorphicColumnMap columnMap, TArgType arg);

		// Token: 0x06002C0F RID: 11279
		internal abstract TResultType Visit(RecordColumnMap columnMap, TArgType arg);

		// Token: 0x06002C10 RID: 11280
		internal abstract TResultType Visit(RefColumnMap columnMap, TArgType arg);

		// Token: 0x06002C11 RID: 11281
		internal abstract TResultType Visit(ScalarColumnMap columnMap, TArgType arg);

		// Token: 0x06002C12 RID: 11282
		internal abstract TResultType Visit(SimpleCollectionColumnMap columnMap, TArgType arg);

		// Token: 0x06002C13 RID: 11283
		internal abstract TResultType Visit(VarRefColumnMap columnMap, TArgType arg);

		// Token: 0x06002C14 RID: 11284
		internal abstract TResultType Visit(MultipleDiscriminatorPolymorphicColumnMap columnMap, TArgType arg);
	}
}
