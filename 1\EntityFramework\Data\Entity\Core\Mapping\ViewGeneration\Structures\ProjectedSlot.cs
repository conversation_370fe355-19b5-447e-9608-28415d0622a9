﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.CqlGeneration;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005AE RID: 1454
	internal abstract class ProjectedSlot : InternalBase, IEquatable<ProjectedSlot>
	{
		// Token: 0x060046E0 RID: 18144 RVA: 0x000F9935 File Offset: 0x000F7B35
		protected virtual bool IsEqualTo(ProjectedSlot right)
		{
			return base.Equals(right);
		}

		// Token: 0x060046E1 RID: 18145 RVA: 0x000F993E File Offset: 0x000F7B3E
		protected virtual int GetHash()
		{
			return base.GetHashCode();
		}

		// Token: 0x060046E2 RID: 18146 RVA: 0x000F9946 File Offset: 0x000F7B46
		public bool Equals(ProjectedSlot right)
		{
			return ProjectedSlot.EqualityComparer.Equals(this, right);
		}

		// Token: 0x060046E3 RID: 18147 RVA: 0x000F9954 File Offset: 0x000F7B54
		public override bool Equals(object obj)
		{
			ProjectedSlot projectedSlot = obj as ProjectedSlot;
			return obj != null && this.Equals(projectedSlot);
		}

		// Token: 0x060046E4 RID: 18148 RVA: 0x000F9974 File Offset: 0x000F7B74
		public override int GetHashCode()
		{
			return ProjectedSlot.EqualityComparer.GetHashCode(this);
		}

		// Token: 0x060046E5 RID: 18149 RVA: 0x000F9981 File Offset: 0x000F7B81
		internal virtual ProjectedSlot DeepQualify(CqlBlock block)
		{
			return new QualifiedSlot(block, this);
		}

		// Token: 0x060046E6 RID: 18150 RVA: 0x000F998A File Offset: 0x000F7B8A
		internal virtual string GetCqlFieldAlias(MemberPath outputMember)
		{
			return outputMember.CqlFieldAlias;
		}

		// Token: 0x060046E7 RID: 18151
		internal abstract StringBuilder AsEsql(StringBuilder builder, MemberPath outputMember, string blockAlias, int indentLevel);

		// Token: 0x060046E8 RID: 18152
		internal abstract DbExpression AsCqt(DbExpression row, MemberPath outputMember);

		// Token: 0x060046E9 RID: 18153 RVA: 0x000F9994 File Offset: 0x000F7B94
		internal static bool TryMergeRemapSlots(ProjectedSlot[] slots1, ProjectedSlot[] slots2, out ProjectedSlot[] result)
		{
			ProjectedSlot[] array;
			if (!ProjectedSlot.TryMergeSlots(slots1, slots2, out array))
			{
				result = null;
				return false;
			}
			result = array;
			return true;
		}

		// Token: 0x060046EA RID: 18154 RVA: 0x000F99B8 File Offset: 0x000F7BB8
		private static bool TryMergeSlots(ProjectedSlot[] slots1, ProjectedSlot[] slots2, out ProjectedSlot[] slots)
		{
			slots = new ProjectedSlot[slots1.Length];
			for (int i = 0; i < slots.Length; i++)
			{
				ProjectedSlot projectedSlot = slots1[i];
				ProjectedSlot projectedSlot2 = slots2[i];
				if (projectedSlot == null)
				{
					slots[i] = projectedSlot2;
				}
				else if (projectedSlot2 == null)
				{
					slots[i] = projectedSlot;
				}
				else
				{
					MemberProjectedSlot memberProjectedSlot = projectedSlot as MemberProjectedSlot;
					MemberProjectedSlot memberProjectedSlot2 = projectedSlot2 as MemberProjectedSlot;
					if (memberProjectedSlot != null && memberProjectedSlot2 != null && !ProjectedSlot.EqualityComparer.Equals(memberProjectedSlot, memberProjectedSlot2))
					{
						return false;
					}
					ProjectedSlot projectedSlot3 = ((memberProjectedSlot != null) ? projectedSlot : projectedSlot2);
					slots[i] = projectedSlot3;
				}
			}
			return true;
		}

		// Token: 0x04001931 RID: 6449
		internal static readonly IEqualityComparer<ProjectedSlot> EqualityComparer = new ProjectedSlot.Comparer();

		// Token: 0x02000BE1 RID: 3041
		private sealed class Comparer : IEqualityComparer<ProjectedSlot>
		{
			// Token: 0x06006884 RID: 26756 RVA: 0x0016351C File Offset: 0x0016171C
			public bool Equals(ProjectedSlot left, ProjectedSlot right)
			{
				return left == right || (left != null && right != null && left.IsEqualTo(right));
			}

			// Token: 0x06006885 RID: 26757 RVA: 0x00163533 File Offset: 0x00161733
			public int GetHashCode(ProjectedSlot key)
			{
				return key.GetHash();
			}
		}
	}
}
