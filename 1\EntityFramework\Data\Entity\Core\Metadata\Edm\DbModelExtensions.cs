﻿using System;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x0200049F RID: 1183
	[Obsolete("ConceptualModel and StoreModel are now available as properties directly on DbModel.")]
	public static class DbModelExtensions
	{
		// Token: 0x06003A44 RID: 14916 RVA: 0x000BFBF6 File Offset: 0x000BDDF6
		[Obsolete("ConceptualModel is now available as a property directly on DbModel.")]
		public static EdmModel GetConceptualModel(this IEdmModelAdapter model)
		{
			Check.NotNull<IEdmModelAdapter>(model, "model");
			return model.ConceptualModel;
		}

		// Token: 0x06003A45 RID: 14917 RVA: 0x000BFC0A File Offset: 0x000BDE0A
		[Obsolete("StoreModel is now available as a property directly on DbModel.")]
		public static EdmModel GetStoreModel(this IEdmModelAdapter model)
		{
			Check.NotNull<IEdmModelAdapter>(model, "model");
			return model.StoreModel;
		}
	}
}
