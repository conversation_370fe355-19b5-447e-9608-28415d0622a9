﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Query.InternalTrees;

namespace System.Data.Entity.Core.Query.PlanCompiler
{
	// Token: 0x0200036B RID: 875
	internal class StructuredTypeNullabilityAnalyzer : ColumnMapVisitor<HashSet<string>>
	{
		// Token: 0x06002A7E RID: 10878 RVA: 0x0008A879 File Offset: 0x00088A79
		internal override void Visit(VarRefColumnMap columnMap, HashSet<string> typesNeedingNullSentinel)
		{
			StructuredTypeNullabilityAnalyzer.AddTypeNeedingNullSentinel(typesNeedingNullSentinel, columnMap.Type);
			base.Visit(columnMap, typesNeedingNullSentinel);
		}

		// Token: 0x06002A7F RID: 10879 RVA: 0x0008A890 File Offset: 0x00088A90
		private static void AddTypeNeedingNullSentinel(HashSet<string> typesNeedingNullSentinel, TypeUsage typeUsage)
		{
			if (TypeSemantics.IsCollectionType(typeUsage))
			{
				StructuredTypeNullabilityAnalyzer.AddTypeNeedingNullSentinel(typesNeedingNullSentinel, TypeHelpers.GetElementTypeUsage(typeUsage));
				return;
			}
			if (TypeSemantics.IsRowType(typeUsage) || TypeSemantics.IsComplexType(typeUsage))
			{
				StructuredTypeNullabilityAnalyzer.MarkAsNeedingNullSentinel(typesNeedingNullSentinel, typeUsage);
			}
			foreach (object obj in TypeHelpers.GetAllStructuralMembers(typeUsage))
			{
				EdmMember edmMember = (EdmMember)obj;
				StructuredTypeNullabilityAnalyzer.AddTypeNeedingNullSentinel(typesNeedingNullSentinel, edmMember.TypeUsage);
			}
		}

		// Token: 0x06002A80 RID: 10880 RVA: 0x0008A91C File Offset: 0x00088B1C
		internal static void MarkAsNeedingNullSentinel(HashSet<string> typesNeedingNullSentinel, TypeUsage typeUsage)
		{
			typesNeedingNullSentinel.Add(typeUsage.EdmType.Identity);
		}

		// Token: 0x04000EA5 RID: 3749
		internal static StructuredTypeNullabilityAnalyzer Instance = new StructuredTypeNullabilityAnalyzer();
	}
}
