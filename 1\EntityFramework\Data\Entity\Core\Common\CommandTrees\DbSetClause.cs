﻿using System;
using System.Data.Entity.Core.Common.CommandTrees.Internal;
using System.Data.Entity.Core.Common.Utils;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006DF RID: 1759
	public sealed class DbSetClause : DbModificationClause
	{
		// Token: 0x060051A1 RID: 20897 RVA: 0x00123371 File Offset: 0x00121571
		internal DbSetClause(DbExpression targetProperty, DbExpression sourceValue)
		{
			this._prop = targetProperty;
			this._val = sourceValue;
		}

		// Token: 0x17000FF0 RID: 4080
		// (get) Token: 0x060051A2 RID: 20898 RVA: 0x00123387 File Offset: 0x00121587
		public DbExpression Property
		{
			get
			{
				return this._prop;
			}
		}

		// Token: 0x17000FF1 RID: 4081
		// (get) Token: 0x060051A3 RID: 20899 RVA: 0x0012338F File Offset: 0x0012158F
		public DbExpression Value
		{
			get
			{
				return this._val;
			}
		}

		// Token: 0x060051A4 RID: 20900 RVA: 0x00123398 File Offset: 0x00121598
		internal override void DumpStructure(ExpressionDumper dumper)
		{
			dumper.Begin("DbSetClause");
			if (this.Property != null)
			{
				dumper.Dump(this.Property, "Property");
			}
			if (this.Value != null)
			{
				dumper.Dump(this.Value, "Value");
			}
			dumper.End("DbSetClause");
		}

		// Token: 0x060051A5 RID: 20901 RVA: 0x001233F0 File Offset: 0x001215F0
		internal override TreeNode Print(DbExpressionVisitor<TreeNode> visitor)
		{
			TreeNode treeNode = new TreeNode("DbSetClause", new TreeNode[0]);
			if (this.Property != null)
			{
				treeNode.Children.Add(new TreeNode("Property", new TreeNode[] { this.Property.Accept<TreeNode>(visitor) }));
			}
			if (this.Value != null)
			{
				treeNode.Children.Add(new TreeNode("Value", new TreeNode[] { this.Value.Accept<TreeNode>(visitor) }));
			}
			return treeNode;
		}

		// Token: 0x04001DD1 RID: 7633
		private readonly DbExpression _prop;

		// Token: 0x04001DD2 RID: 7634
		private readonly DbExpression _val;
	}
}
