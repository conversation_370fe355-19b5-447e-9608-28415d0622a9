﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.EntitySql;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Metadata.Edm.Provider;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;
using System.Linq;

namespace System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder
{
	// Token: 0x020006F8 RID: 1784
	public static class EdmFunctions
	{
		// Token: 0x060053A1 RID: 21409 RVA: 0x0012C01C File Offset: 0x0012A21C
		private static EdmFunction ResolveCanonicalFunction(string functionName, TypeUsage[] argumentTypes)
		{
			List<EdmFunction> list = new List<EdmFunction>(from func in EdmProviderManifest.Instance.GetStoreFunctions()
				where string.Equals(func.Name, functionName, StringComparison.Ordinal)
				select func);
			EdmFunction edmFunction = null;
			bool flag = false;
			if (list.Count > 0)
			{
				edmFunction = FunctionOverloadResolver.ResolveFunctionOverloads(list, argumentTypes, false, out flag);
				if (flag)
				{
					throw new ArgumentException(Strings.Cqt_Function_CanonicalFunction_AmbiguousMatch(functionName));
				}
			}
			if (edmFunction == null)
			{
				throw new ArgumentException(Strings.Cqt_Function_CanonicalFunction_NotFound(functionName));
			}
			return edmFunction;
		}

		// Token: 0x060053A2 RID: 21410 RVA: 0x0012C098 File Offset: 0x0012A298
		internal static DbFunctionExpression InvokeCanonicalFunction(string functionName, params DbExpression[] arguments)
		{
			TypeUsage[] array = new TypeUsage[arguments.Length];
			for (int i = 0; i < arguments.Length; i++)
			{
				array[i] = arguments[i].ResultType;
			}
			return EdmFunctions.ResolveCanonicalFunction(functionName, array).Invoke(arguments);
		}

		// Token: 0x060053A3 RID: 21411 RVA: 0x0012C0D4 File Offset: 0x0012A2D4
		public static DbFunctionExpression Average(this DbExpression collection)
		{
			Check.NotNull<DbExpression>(collection, "collection");
			return EdmFunctions.InvokeCanonicalFunction("Avg", new DbExpression[] { collection });
		}

		// Token: 0x060053A4 RID: 21412 RVA: 0x0012C0F6 File Offset: 0x0012A2F6
		public static DbFunctionExpression Count(this DbExpression collection)
		{
			Check.NotNull<DbExpression>(collection, "collection");
			return EdmFunctions.InvokeCanonicalFunction("Count", new DbExpression[] { collection });
		}

		// Token: 0x060053A5 RID: 21413 RVA: 0x0012C118 File Offset: 0x0012A318
		public static DbFunctionExpression LongCount(this DbExpression collection)
		{
			Check.NotNull<DbExpression>(collection, "collection");
			return EdmFunctions.InvokeCanonicalFunction("BigCount", new DbExpression[] { collection });
		}

		// Token: 0x060053A6 RID: 21414 RVA: 0x0012C13A File Offset: 0x0012A33A
		public static DbFunctionExpression Max(this DbExpression collection)
		{
			Check.NotNull<DbExpression>(collection, "collection");
			return EdmFunctions.InvokeCanonicalFunction("Max", new DbExpression[] { collection });
		}

		// Token: 0x060053A7 RID: 21415 RVA: 0x0012C15C File Offset: 0x0012A35C
		public static DbFunctionExpression Min(this DbExpression collection)
		{
			Check.NotNull<DbExpression>(collection, "collection");
			return EdmFunctions.InvokeCanonicalFunction("Min", new DbExpression[] { collection });
		}

		// Token: 0x060053A8 RID: 21416 RVA: 0x0012C17E File Offset: 0x0012A37E
		public static DbFunctionExpression Sum(this DbExpression collection)
		{
			Check.NotNull<DbExpression>(collection, "collection");
			return EdmFunctions.InvokeCanonicalFunction("Sum", new DbExpression[] { collection });
		}

		// Token: 0x060053A9 RID: 21417 RVA: 0x0012C1A0 File Offset: 0x0012A3A0
		public static DbFunctionExpression StDev(this DbExpression collection)
		{
			Check.NotNull<DbExpression>(collection, "collection");
			return EdmFunctions.InvokeCanonicalFunction("StDev", new DbExpression[] { collection });
		}

		// Token: 0x060053AA RID: 21418 RVA: 0x0012C1C2 File Offset: 0x0012A3C2
		public static DbFunctionExpression StDevP(this DbExpression collection)
		{
			Check.NotNull<DbExpression>(collection, "collection");
			return EdmFunctions.InvokeCanonicalFunction("StDevP", new DbExpression[] { collection });
		}

		// Token: 0x060053AB RID: 21419 RVA: 0x0012C1E4 File Offset: 0x0012A3E4
		public static DbFunctionExpression Var(this DbExpression collection)
		{
			Check.NotNull<DbExpression>(collection, "collection");
			return EdmFunctions.InvokeCanonicalFunction("Var", new DbExpression[] { collection });
		}

		// Token: 0x060053AC RID: 21420 RVA: 0x0012C206 File Offset: 0x0012A406
		public static DbFunctionExpression VarP(this DbExpression collection)
		{
			Check.NotNull<DbExpression>(collection, "collection");
			return EdmFunctions.InvokeCanonicalFunction("VarP", new DbExpression[] { collection });
		}

		// Token: 0x060053AD RID: 21421 RVA: 0x0012C228 File Offset: 0x0012A428
		public static DbFunctionExpression Concat(this DbExpression string1, DbExpression string2)
		{
			Check.NotNull<DbExpression>(string1, "string1");
			Check.NotNull<DbExpression>(string2, "string2");
			return EdmFunctions.InvokeCanonicalFunction("Concat", new DbExpression[] { string1, string2 });
		}

		// Token: 0x060053AE RID: 21422 RVA: 0x0012C25A File Offset: 0x0012A45A
		public static DbExpression Contains(this DbExpression searchedString, DbExpression searchedForString)
		{
			Check.NotNull<DbExpression>(searchedString, "searchedString");
			Check.NotNull<DbExpression>(searchedForString, "searchedForString");
			return EdmFunctions.InvokeCanonicalFunction("Contains", new DbExpression[] { searchedString, searchedForString });
		}

		// Token: 0x060053AF RID: 21423 RVA: 0x0012C28C File Offset: 0x0012A48C
		public static DbFunctionExpression EndsWith(this DbExpression stringArgument, DbExpression suffix)
		{
			Check.NotNull<DbExpression>(stringArgument, "stringArgument");
			Check.NotNull<DbExpression>(suffix, "suffix");
			return EdmFunctions.InvokeCanonicalFunction("EndsWith", new DbExpression[] { stringArgument, suffix });
		}

		// Token: 0x060053B0 RID: 21424 RVA: 0x0012C2BE File Offset: 0x0012A4BE
		public static DbFunctionExpression IndexOf(this DbExpression searchString, DbExpression stringToFind)
		{
			Check.NotNull<DbExpression>(searchString, "searchString");
			Check.NotNull<DbExpression>(stringToFind, "stringToFind");
			return EdmFunctions.InvokeCanonicalFunction("IndexOf", new DbExpression[] { stringToFind, searchString });
		}

		// Token: 0x060053B1 RID: 21425 RVA: 0x0012C2F0 File Offset: 0x0012A4F0
		public static DbFunctionExpression Left(this DbExpression stringArgument, DbExpression length)
		{
			Check.NotNull<DbExpression>(stringArgument, "stringArgument");
			Check.NotNull<DbExpression>(length, "length");
			return EdmFunctions.InvokeCanonicalFunction("Left", new DbExpression[] { stringArgument, length });
		}

		// Token: 0x060053B2 RID: 21426 RVA: 0x0012C322 File Offset: 0x0012A522
		public static DbFunctionExpression Length(this DbExpression stringArgument)
		{
			Check.NotNull<DbExpression>(stringArgument, "stringArgument");
			return EdmFunctions.InvokeCanonicalFunction("Length", new DbExpression[] { stringArgument });
		}

		// Token: 0x060053B3 RID: 21427 RVA: 0x0012C344 File Offset: 0x0012A544
		public static DbFunctionExpression Replace(this DbExpression stringArgument, DbExpression toReplace, DbExpression replacement)
		{
			Check.NotNull<DbExpression>(stringArgument, "stringArgument");
			Check.NotNull<DbExpression>(toReplace, "toReplace");
			Check.NotNull<DbExpression>(replacement, "replacement");
			return EdmFunctions.InvokeCanonicalFunction("Replace", new DbExpression[] { stringArgument, toReplace, replacement });
		}

		// Token: 0x060053B4 RID: 21428 RVA: 0x0012C391 File Offset: 0x0012A591
		public static DbFunctionExpression Reverse(this DbExpression stringArgument)
		{
			Check.NotNull<DbExpression>(stringArgument, "stringArgument");
			return EdmFunctions.InvokeCanonicalFunction("Reverse", new DbExpression[] { stringArgument });
		}

		// Token: 0x060053B5 RID: 21429 RVA: 0x0012C3B3 File Offset: 0x0012A5B3
		public static DbFunctionExpression Right(this DbExpression stringArgument, DbExpression length)
		{
			Check.NotNull<DbExpression>(stringArgument, "stringArgument");
			Check.NotNull<DbExpression>(length, "length");
			return EdmFunctions.InvokeCanonicalFunction("Right", new DbExpression[] { stringArgument, length });
		}

		// Token: 0x060053B6 RID: 21430 RVA: 0x0012C3E5 File Offset: 0x0012A5E5
		public static DbFunctionExpression StartsWith(this DbExpression stringArgument, DbExpression prefix)
		{
			Check.NotNull<DbExpression>(stringArgument, "stringArgument");
			Check.NotNull<DbExpression>(prefix, "prefix");
			return EdmFunctions.InvokeCanonicalFunction("StartsWith", new DbExpression[] { stringArgument, prefix });
		}

		// Token: 0x060053B7 RID: 21431 RVA: 0x0012C418 File Offset: 0x0012A618
		public static DbFunctionExpression Substring(this DbExpression stringArgument, DbExpression start, DbExpression length)
		{
			Check.NotNull<DbExpression>(stringArgument, "stringArgument");
			Check.NotNull<DbExpression>(start, "start");
			Check.NotNull<DbExpression>(length, "length");
			return EdmFunctions.InvokeCanonicalFunction("Substring", new DbExpression[] { stringArgument, start, length });
		}

		// Token: 0x060053B8 RID: 21432 RVA: 0x0012C465 File Offset: 0x0012A665
		public static DbFunctionExpression ToLower(this DbExpression stringArgument)
		{
			Check.NotNull<DbExpression>(stringArgument, "stringArgument");
			return EdmFunctions.InvokeCanonicalFunction("ToLower", new DbExpression[] { stringArgument });
		}

		// Token: 0x060053B9 RID: 21433 RVA: 0x0012C487 File Offset: 0x0012A687
		public static DbFunctionExpression ToUpper(this DbExpression stringArgument)
		{
			Check.NotNull<DbExpression>(stringArgument, "stringArgument");
			return EdmFunctions.InvokeCanonicalFunction("ToUpper", new DbExpression[] { stringArgument });
		}

		// Token: 0x060053BA RID: 21434 RVA: 0x0012C4A9 File Offset: 0x0012A6A9
		public static DbFunctionExpression Trim(this DbExpression stringArgument)
		{
			Check.NotNull<DbExpression>(stringArgument, "stringArgument");
			return EdmFunctions.InvokeCanonicalFunction("Trim", new DbExpression[] { stringArgument });
		}

		// Token: 0x060053BB RID: 21435 RVA: 0x0012C4CB File Offset: 0x0012A6CB
		public static DbFunctionExpression TrimEnd(this DbExpression stringArgument)
		{
			Check.NotNull<DbExpression>(stringArgument, "stringArgument");
			return EdmFunctions.InvokeCanonicalFunction("RTrim", new DbExpression[] { stringArgument });
		}

		// Token: 0x060053BC RID: 21436 RVA: 0x0012C4ED File Offset: 0x0012A6ED
		public static DbFunctionExpression TrimStart(this DbExpression stringArgument)
		{
			Check.NotNull<DbExpression>(stringArgument, "stringArgument");
			return EdmFunctions.InvokeCanonicalFunction("LTrim", new DbExpression[] { stringArgument });
		}

		// Token: 0x060053BD RID: 21437 RVA: 0x0012C50F File Offset: 0x0012A70F
		public static DbFunctionExpression Year(this DbExpression dateValue)
		{
			Check.NotNull<DbExpression>(dateValue, "dateValue");
			return EdmFunctions.InvokeCanonicalFunction("Year", new DbExpression[] { dateValue });
		}

		// Token: 0x060053BE RID: 21438 RVA: 0x0012C531 File Offset: 0x0012A731
		public static DbFunctionExpression Month(this DbExpression dateValue)
		{
			Check.NotNull<DbExpression>(dateValue, "dateValue");
			return EdmFunctions.InvokeCanonicalFunction("Month", new DbExpression[] { dateValue });
		}

		// Token: 0x060053BF RID: 21439 RVA: 0x0012C553 File Offset: 0x0012A753
		public static DbFunctionExpression Day(this DbExpression dateValue)
		{
			Check.NotNull<DbExpression>(dateValue, "dateValue");
			return EdmFunctions.InvokeCanonicalFunction("Day", new DbExpression[] { dateValue });
		}

		// Token: 0x060053C0 RID: 21440 RVA: 0x0012C575 File Offset: 0x0012A775
		public static DbFunctionExpression DayOfYear(this DbExpression dateValue)
		{
			Check.NotNull<DbExpression>(dateValue, "dateValue");
			return EdmFunctions.InvokeCanonicalFunction("DayOfYear", new DbExpression[] { dateValue });
		}

		// Token: 0x060053C1 RID: 21441 RVA: 0x0012C597 File Offset: 0x0012A797
		public static DbFunctionExpression Hour(this DbExpression timeValue)
		{
			Check.NotNull<DbExpression>(timeValue, "timeValue");
			return EdmFunctions.InvokeCanonicalFunction("Hour", new DbExpression[] { timeValue });
		}

		// Token: 0x060053C2 RID: 21442 RVA: 0x0012C5B9 File Offset: 0x0012A7B9
		public static DbFunctionExpression Minute(this DbExpression timeValue)
		{
			Check.NotNull<DbExpression>(timeValue, "timeValue");
			return EdmFunctions.InvokeCanonicalFunction("Minute", new DbExpression[] { timeValue });
		}

		// Token: 0x060053C3 RID: 21443 RVA: 0x0012C5DB File Offset: 0x0012A7DB
		public static DbFunctionExpression Second(this DbExpression timeValue)
		{
			Check.NotNull<DbExpression>(timeValue, "timeValue");
			return EdmFunctions.InvokeCanonicalFunction("Second", new DbExpression[] { timeValue });
		}

		// Token: 0x060053C4 RID: 21444 RVA: 0x0012C5FD File Offset: 0x0012A7FD
		public static DbFunctionExpression Millisecond(this DbExpression timeValue)
		{
			Check.NotNull<DbExpression>(timeValue, "timeValue");
			return EdmFunctions.InvokeCanonicalFunction("Millisecond", new DbExpression[] { timeValue });
		}

		// Token: 0x060053C5 RID: 21445 RVA: 0x0012C61F File Offset: 0x0012A81F
		public static DbFunctionExpression GetTotalOffsetMinutes(this DbExpression dateTimeOffsetArgument)
		{
			Check.NotNull<DbExpression>(dateTimeOffsetArgument, "dateTimeOffsetArgument");
			return EdmFunctions.InvokeCanonicalFunction("GetTotalOffsetMinutes", new DbExpression[] { dateTimeOffsetArgument });
		}

		// Token: 0x060053C6 RID: 21446 RVA: 0x0012C641 File Offset: 0x0012A841
		public static DbFunctionExpression LocalDateTime(this DbExpression dateTimeOffsetArgument)
		{
			Check.NotNull<DbExpression>(dateTimeOffsetArgument, "dateTimeOffsetArgument");
			return EdmFunctions.InvokeCanonicalFunction("LocalDateTime", new DbExpression[] { dateTimeOffsetArgument });
		}

		// Token: 0x060053C7 RID: 21447 RVA: 0x0012C663 File Offset: 0x0012A863
		public static DbFunctionExpression UtcDateTime(this DbExpression dateTimeOffsetArgument)
		{
			Check.NotNull<DbExpression>(dateTimeOffsetArgument, "dateTimeOffsetArgument");
			return EdmFunctions.InvokeCanonicalFunction("UtcDateTime", new DbExpression[] { dateTimeOffsetArgument });
		}

		// Token: 0x060053C8 RID: 21448 RVA: 0x0012C685 File Offset: 0x0012A885
		public static DbFunctionExpression CurrentDateTime()
		{
			return EdmFunctions.InvokeCanonicalFunction("CurrentDateTime", new DbExpression[0]);
		}

		// Token: 0x060053C9 RID: 21449 RVA: 0x0012C697 File Offset: 0x0012A897
		public static DbFunctionExpression CurrentDateTimeOffset()
		{
			return EdmFunctions.InvokeCanonicalFunction("CurrentDateTimeOffset", new DbExpression[0]);
		}

		// Token: 0x060053CA RID: 21450 RVA: 0x0012C6A9 File Offset: 0x0012A8A9
		public static DbFunctionExpression CurrentUtcDateTime()
		{
			return EdmFunctions.InvokeCanonicalFunction("CurrentUtcDateTime", new DbExpression[0]);
		}

		// Token: 0x060053CB RID: 21451 RVA: 0x0012C6BB File Offset: 0x0012A8BB
		public static DbFunctionExpression TruncateTime(this DbExpression dateValue)
		{
			Check.NotNull<DbExpression>(dateValue, "dateValue");
			return EdmFunctions.InvokeCanonicalFunction("TruncateTime", new DbExpression[] { dateValue });
		}

		// Token: 0x060053CC RID: 21452 RVA: 0x0012C6E0 File Offset: 0x0012A8E0
		public static DbFunctionExpression CreateDateTime(DbExpression year, DbExpression month, DbExpression day, DbExpression hour, DbExpression minute, DbExpression second)
		{
			Check.NotNull<DbExpression>(year, "year");
			Check.NotNull<DbExpression>(month, "month");
			Check.NotNull<DbExpression>(day, "day");
			Check.NotNull<DbExpression>(hour, "hour");
			Check.NotNull<DbExpression>(minute, "minute");
			Check.NotNull<DbExpression>(second, "second");
			return EdmFunctions.InvokeCanonicalFunction("CreateDateTime", new DbExpression[] { year, month, day, hour, minute, second });
		}

		// Token: 0x060053CD RID: 21453 RVA: 0x0012C764 File Offset: 0x0012A964
		public static DbFunctionExpression CreateDateTimeOffset(DbExpression year, DbExpression month, DbExpression day, DbExpression hour, DbExpression minute, DbExpression second, DbExpression timeZoneOffset)
		{
			Check.NotNull<DbExpression>(year, "year");
			Check.NotNull<DbExpression>(month, "month");
			Check.NotNull<DbExpression>(day, "day");
			Check.NotNull<DbExpression>(hour, "hour");
			Check.NotNull<DbExpression>(minute, "minute");
			Check.NotNull<DbExpression>(second, "second");
			Check.NotNull<DbExpression>(timeZoneOffset, "timeZoneOffset");
			return EdmFunctions.InvokeCanonicalFunction("CreateDateTimeOffset", new DbExpression[] { year, month, day, hour, minute, second, timeZoneOffset });
		}

		// Token: 0x060053CE RID: 21454 RVA: 0x0012C7F8 File Offset: 0x0012A9F8
		public static DbFunctionExpression CreateTime(DbExpression hour, DbExpression minute, DbExpression second)
		{
			Check.NotNull<DbExpression>(hour, "hour");
			Check.NotNull<DbExpression>(minute, "minute");
			Check.NotNull<DbExpression>(second, "second");
			return EdmFunctions.InvokeCanonicalFunction("CreateTime", new DbExpression[] { hour, minute, second });
		}

		// Token: 0x060053CF RID: 21455 RVA: 0x0012C845 File Offset: 0x0012AA45
		public static DbFunctionExpression AddYears(this DbExpression dateValue, DbExpression addValue)
		{
			Check.NotNull<DbExpression>(dateValue, "dateValue");
			Check.NotNull<DbExpression>(addValue, "addValue");
			return EdmFunctions.InvokeCanonicalFunction("AddYears", new DbExpression[] { dateValue, addValue });
		}

		// Token: 0x060053D0 RID: 21456 RVA: 0x0012C877 File Offset: 0x0012AA77
		public static DbFunctionExpression AddMonths(this DbExpression dateValue, DbExpression addValue)
		{
			Check.NotNull<DbExpression>(dateValue, "dateValue");
			Check.NotNull<DbExpression>(addValue, "addValue");
			return EdmFunctions.InvokeCanonicalFunction("AddMonths", new DbExpression[] { dateValue, addValue });
		}

		// Token: 0x060053D1 RID: 21457 RVA: 0x0012C8A9 File Offset: 0x0012AAA9
		public static DbFunctionExpression AddDays(this DbExpression dateValue, DbExpression addValue)
		{
			Check.NotNull<DbExpression>(dateValue, "dateValue");
			Check.NotNull<DbExpression>(addValue, "addValue");
			return EdmFunctions.InvokeCanonicalFunction("AddDays", new DbExpression[] { dateValue, addValue });
		}

		// Token: 0x060053D2 RID: 21458 RVA: 0x0012C8DB File Offset: 0x0012AADB
		public static DbFunctionExpression AddHours(this DbExpression timeValue, DbExpression addValue)
		{
			Check.NotNull<DbExpression>(timeValue, "timeValue");
			Check.NotNull<DbExpression>(addValue, "addValue");
			return EdmFunctions.InvokeCanonicalFunction("AddHours", new DbExpression[] { timeValue, addValue });
		}

		// Token: 0x060053D3 RID: 21459 RVA: 0x0012C90D File Offset: 0x0012AB0D
		public static DbFunctionExpression AddMinutes(this DbExpression timeValue, DbExpression addValue)
		{
			Check.NotNull<DbExpression>(timeValue, "timeValue");
			Check.NotNull<DbExpression>(addValue, "addValue");
			return EdmFunctions.InvokeCanonicalFunction("AddMinutes", new DbExpression[] { timeValue, addValue });
		}

		// Token: 0x060053D4 RID: 21460 RVA: 0x0012C93F File Offset: 0x0012AB3F
		public static DbFunctionExpression AddSeconds(this DbExpression timeValue, DbExpression addValue)
		{
			Check.NotNull<DbExpression>(timeValue, "timeValue");
			Check.NotNull<DbExpression>(addValue, "addValue");
			return EdmFunctions.InvokeCanonicalFunction("AddSeconds", new DbExpression[] { timeValue, addValue });
		}

		// Token: 0x060053D5 RID: 21461 RVA: 0x0012C971 File Offset: 0x0012AB71
		public static DbFunctionExpression AddMilliseconds(this DbExpression timeValue, DbExpression addValue)
		{
			Check.NotNull<DbExpression>(timeValue, "timeValue");
			Check.NotNull<DbExpression>(addValue, "addValue");
			return EdmFunctions.InvokeCanonicalFunction("AddMilliseconds", new DbExpression[] { timeValue, addValue });
		}

		// Token: 0x060053D6 RID: 21462 RVA: 0x0012C9A3 File Offset: 0x0012ABA3
		public static DbFunctionExpression AddMicroseconds(this DbExpression timeValue, DbExpression addValue)
		{
			Check.NotNull<DbExpression>(timeValue, "timeValue");
			Check.NotNull<DbExpression>(addValue, "addValue");
			return EdmFunctions.InvokeCanonicalFunction("AddMicroseconds", new DbExpression[] { timeValue, addValue });
		}

		// Token: 0x060053D7 RID: 21463 RVA: 0x0012C9D5 File Offset: 0x0012ABD5
		public static DbFunctionExpression AddNanoseconds(this DbExpression timeValue, DbExpression addValue)
		{
			Check.NotNull<DbExpression>(timeValue, "timeValue");
			Check.NotNull<DbExpression>(addValue, "addValue");
			return EdmFunctions.InvokeCanonicalFunction("AddNanoseconds", new DbExpression[] { timeValue, addValue });
		}

		// Token: 0x060053D8 RID: 21464 RVA: 0x0012CA07 File Offset: 0x0012AC07
		public static DbFunctionExpression DiffYears(this DbExpression dateValue1, DbExpression dateValue2)
		{
			Check.NotNull<DbExpression>(dateValue1, "dateValue1");
			Check.NotNull<DbExpression>(dateValue2, "dateValue2");
			return EdmFunctions.InvokeCanonicalFunction("DiffYears", new DbExpression[] { dateValue1, dateValue2 });
		}

		// Token: 0x060053D9 RID: 21465 RVA: 0x0012CA39 File Offset: 0x0012AC39
		public static DbFunctionExpression DiffMonths(this DbExpression dateValue1, DbExpression dateValue2)
		{
			Check.NotNull<DbExpression>(dateValue1, "dateValue1");
			Check.NotNull<DbExpression>(dateValue2, "dateValue2");
			return EdmFunctions.InvokeCanonicalFunction("DiffMonths", new DbExpression[] { dateValue1, dateValue2 });
		}

		// Token: 0x060053DA RID: 21466 RVA: 0x0012CA6B File Offset: 0x0012AC6B
		public static DbFunctionExpression DiffDays(this DbExpression dateValue1, DbExpression dateValue2)
		{
			Check.NotNull<DbExpression>(dateValue1, "dateValue1");
			Check.NotNull<DbExpression>(dateValue2, "dateValue2");
			return EdmFunctions.InvokeCanonicalFunction("DiffDays", new DbExpression[] { dateValue1, dateValue2 });
		}

		// Token: 0x060053DB RID: 21467 RVA: 0x0012CA9D File Offset: 0x0012AC9D
		public static DbFunctionExpression DiffHours(this DbExpression timeValue1, DbExpression timeValue2)
		{
			Check.NotNull<DbExpression>(timeValue1, "timeValue1");
			Check.NotNull<DbExpression>(timeValue2, "timeValue2");
			return EdmFunctions.InvokeCanonicalFunction("DiffHours", new DbExpression[] { timeValue1, timeValue2 });
		}

		// Token: 0x060053DC RID: 21468 RVA: 0x0012CACF File Offset: 0x0012ACCF
		public static DbFunctionExpression DiffMinutes(this DbExpression timeValue1, DbExpression timeValue2)
		{
			Check.NotNull<DbExpression>(timeValue1, "timeValue1");
			Check.NotNull<DbExpression>(timeValue2, "timeValue2");
			return EdmFunctions.InvokeCanonicalFunction("DiffMinutes", new DbExpression[] { timeValue1, timeValue2 });
		}

		// Token: 0x060053DD RID: 21469 RVA: 0x0012CB01 File Offset: 0x0012AD01
		public static DbFunctionExpression DiffSeconds(this DbExpression timeValue1, DbExpression timeValue2)
		{
			Check.NotNull<DbExpression>(timeValue1, "timeValue1");
			Check.NotNull<DbExpression>(timeValue2, "timeValue2");
			return EdmFunctions.InvokeCanonicalFunction("DiffSeconds", new DbExpression[] { timeValue1, timeValue2 });
		}

		// Token: 0x060053DE RID: 21470 RVA: 0x0012CB33 File Offset: 0x0012AD33
		public static DbFunctionExpression DiffMilliseconds(this DbExpression timeValue1, DbExpression timeValue2)
		{
			Check.NotNull<DbExpression>(timeValue1, "timeValue1");
			Check.NotNull<DbExpression>(timeValue2, "timeValue2");
			return EdmFunctions.InvokeCanonicalFunction("DiffMilliseconds", new DbExpression[] { timeValue1, timeValue2 });
		}

		// Token: 0x060053DF RID: 21471 RVA: 0x0012CB65 File Offset: 0x0012AD65
		public static DbFunctionExpression DiffMicroseconds(this DbExpression timeValue1, DbExpression timeValue2)
		{
			Check.NotNull<DbExpression>(timeValue1, "timeValue1");
			Check.NotNull<DbExpression>(timeValue2, "timeValue2");
			return EdmFunctions.InvokeCanonicalFunction("DiffMicroseconds", new DbExpression[] { timeValue1, timeValue2 });
		}

		// Token: 0x060053E0 RID: 21472 RVA: 0x0012CB97 File Offset: 0x0012AD97
		public static DbFunctionExpression DiffNanoseconds(this DbExpression timeValue1, DbExpression timeValue2)
		{
			Check.NotNull<DbExpression>(timeValue1, "timeValue1");
			Check.NotNull<DbExpression>(timeValue2, "timeValue2");
			return EdmFunctions.InvokeCanonicalFunction("DiffNanoseconds", new DbExpression[] { timeValue1, timeValue2 });
		}

		// Token: 0x060053E1 RID: 21473 RVA: 0x0012CBC9 File Offset: 0x0012ADC9
		public static DbFunctionExpression Round(this DbExpression value)
		{
			Check.NotNull<DbExpression>(value, "value");
			return EdmFunctions.InvokeCanonicalFunction("Round", new DbExpression[] { value });
		}

		// Token: 0x060053E2 RID: 21474 RVA: 0x0012CBEB File Offset: 0x0012ADEB
		public static DbFunctionExpression Round(this DbExpression value, DbExpression digits)
		{
			Check.NotNull<DbExpression>(value, "value");
			Check.NotNull<DbExpression>(digits, "digits");
			return EdmFunctions.InvokeCanonicalFunction("Round", new DbExpression[] { value, digits });
		}

		// Token: 0x060053E3 RID: 21475 RVA: 0x0012CC1D File Offset: 0x0012AE1D
		public static DbFunctionExpression Floor(this DbExpression value)
		{
			Check.NotNull<DbExpression>(value, "value");
			return EdmFunctions.InvokeCanonicalFunction("Floor", new DbExpression[] { value });
		}

		// Token: 0x060053E4 RID: 21476 RVA: 0x0012CC3F File Offset: 0x0012AE3F
		public static DbFunctionExpression Ceiling(this DbExpression value)
		{
			Check.NotNull<DbExpression>(value, "value");
			return EdmFunctions.InvokeCanonicalFunction("Ceiling", new DbExpression[] { value });
		}

		// Token: 0x060053E5 RID: 21477 RVA: 0x0012CC61 File Offset: 0x0012AE61
		public static DbFunctionExpression Abs(this DbExpression value)
		{
			Check.NotNull<DbExpression>(value, "value");
			return EdmFunctions.InvokeCanonicalFunction("Abs", new DbExpression[] { value });
		}

		// Token: 0x060053E6 RID: 21478 RVA: 0x0012CC83 File Offset: 0x0012AE83
		public static DbFunctionExpression Truncate(this DbExpression value, DbExpression digits)
		{
			Check.NotNull<DbExpression>(value, "value");
			Check.NotNull<DbExpression>(digits, "digits");
			return EdmFunctions.InvokeCanonicalFunction("Truncate", new DbExpression[] { value, digits });
		}

		// Token: 0x060053E7 RID: 21479 RVA: 0x0012CCB5 File Offset: 0x0012AEB5
		public static DbFunctionExpression Power(this DbExpression baseArgument, DbExpression exponent)
		{
			Check.NotNull<DbExpression>(baseArgument, "baseArgument");
			Check.NotNull<DbExpression>(exponent, "exponent");
			return EdmFunctions.InvokeCanonicalFunction("Power", new DbExpression[] { baseArgument, exponent });
		}

		// Token: 0x060053E8 RID: 21480 RVA: 0x0012CCE7 File Offset: 0x0012AEE7
		public static DbFunctionExpression BitwiseAnd(this DbExpression value1, DbExpression value2)
		{
			Check.NotNull<DbExpression>(value1, "value1");
			Check.NotNull<DbExpression>(value2, "value2");
			return EdmFunctions.InvokeCanonicalFunction("BitwiseAnd", new DbExpression[] { value1, value2 });
		}

		// Token: 0x060053E9 RID: 21481 RVA: 0x0012CD19 File Offset: 0x0012AF19
		public static DbFunctionExpression BitwiseOr(this DbExpression value1, DbExpression value2)
		{
			Check.NotNull<DbExpression>(value1, "value1");
			Check.NotNull<DbExpression>(value2, "value2");
			return EdmFunctions.InvokeCanonicalFunction("BitwiseOr", new DbExpression[] { value1, value2 });
		}

		// Token: 0x060053EA RID: 21482 RVA: 0x0012CD4B File Offset: 0x0012AF4B
		public static DbFunctionExpression BitwiseNot(this DbExpression value)
		{
			Check.NotNull<DbExpression>(value, "value");
			return EdmFunctions.InvokeCanonicalFunction("BitwiseNot", new DbExpression[] { value });
		}

		// Token: 0x060053EB RID: 21483 RVA: 0x0012CD6D File Offset: 0x0012AF6D
		public static DbFunctionExpression BitwiseXor(this DbExpression value1, DbExpression value2)
		{
			Check.NotNull<DbExpression>(value1, "value1");
			Check.NotNull<DbExpression>(value2, "value2");
			return EdmFunctions.InvokeCanonicalFunction("BitwiseXor", new DbExpression[] { value1, value2 });
		}

		// Token: 0x060053EC RID: 21484 RVA: 0x0012CD9F File Offset: 0x0012AF9F
		public static DbFunctionExpression NewGuid()
		{
			return EdmFunctions.InvokeCanonicalFunction("NewGuid", new DbExpression[0]);
		}
	}
}
