﻿using System;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003A7 RID: 935
	internal sealed class FullOuterJoinOp : JoinBaseOp
	{
		// Token: 0x06002D5D RID: 11613 RVA: 0x00090E8B File Offset: 0x0008F08B
		private FullOuterJoinOp()
			: base(OpType.FullOuterJoin)
		{
		}

		// Token: 0x06002D5E RID: 11614 RVA: 0x00090E95 File Offset: 0x0008F095
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002D5F RID: 11615 RVA: 0x00090E9F File Offset: 0x0008F09F
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000F31 RID: 3889
		internal static readonly FullOuterJoinOp Instance = new FullOuterJoinOp();

		// Token: 0x04000F32 RID: 3890
		internal static readonly FullOuterJoinOp Pattern = FullOuterJoinOp.Instance;
	}
}
