﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Mapping.ViewGeneration.CqlGeneration;
using System.Data.Entity.Core.Metadata.Edm;
using System.Linq;
using System.Text;

namespace System.Data.Entity.Core.Mapping.ViewGeneration.Structures
{
	// Token: 0x020005A8 RID: 1448
	internal sealed class MemberPath : InternalBase, IEquatable<MemberPath>
	{
		// Token: 0x06004661 RID: 18017 RVA: 0x000F76BB File Offset: 0x000F58BB
		internal MemberPath(EntitySetBase extent, IEnumerable<EdmMember> path)
		{
			this.m_extent = extent;
			this.m_path = path.ToList<EdmMember>();
		}

		// Token: 0x06004662 RID: 18018 RVA: 0x000F76D6 File Offset: 0x000F58D6
		internal MemberPath(EntitySetBase extent)
			: this(extent, Enumerable.Empty<EdmMember>())
		{
		}

		// Token: 0x06004663 RID: 18019 RVA: 0x000F76E4 File Offset: 0x000F58E4
		internal MemberPath(EntitySetBase extent, EdmMember member)
			: this(extent, Enumerable.Repeat<EdmMember>(member, 1))
		{
		}

		// Token: 0x06004664 RID: 18020 RVA: 0x000F76F4 File Offset: 0x000F58F4
		internal MemberPath(MemberPath prefix, EdmMember last)
		{
			this.m_extent = prefix.m_extent;
			this.m_path = new List<EdmMember>(prefix.m_path);
			this.m_path.Add(last);
		}

		// Token: 0x17000DF1 RID: 3569
		// (get) Token: 0x06004665 RID: 18021 RVA: 0x000F7725 File Offset: 0x000F5925
		internal EdmMember RootEdmMember
		{
			get
			{
				if (this.m_path.Count <= 0)
				{
					return null;
				}
				return this.m_path[0];
			}
		}

		// Token: 0x17000DF2 RID: 3570
		// (get) Token: 0x06004666 RID: 18022 RVA: 0x000F7743 File Offset: 0x000F5943
		internal EdmMember LeafEdmMember
		{
			get
			{
				if (this.m_path.Count <= 0)
				{
					return null;
				}
				return this.m_path[this.m_path.Count - 1];
			}
		}

		// Token: 0x17000DF3 RID: 3571
		// (get) Token: 0x06004667 RID: 18023 RVA: 0x000F776D File Offset: 0x000F596D
		internal string LeafName
		{
			get
			{
				if (this.m_path.Count == 0)
				{
					return this.m_extent.Name;
				}
				return this.LeafEdmMember.Name;
			}
		}

		// Token: 0x17000DF4 RID: 3572
		// (get) Token: 0x06004668 RID: 18024 RVA: 0x000F7793 File Offset: 0x000F5993
		internal bool IsComputed
		{
			get
			{
				return this.m_path.Count != 0 && this.RootEdmMember.IsStoreGeneratedComputed;
			}
		}

		// Token: 0x17000DF5 RID: 3573
		// (get) Token: 0x06004669 RID: 18025 RVA: 0x000F77B0 File Offset: 0x000F59B0
		internal object DefaultValue
		{
			get
			{
				if (this.m_path.Count == 0)
				{
					return null;
				}
				Facet facet;
				if (this.LeafEdmMember.TypeUsage.Facets.TryGetValue("DefaultValue", false, out facet))
				{
					return facet.Value;
				}
				return null;
			}
		}

		// Token: 0x17000DF6 RID: 3574
		// (get) Token: 0x0600466A RID: 18026 RVA: 0x000F77F3 File Offset: 0x000F59F3
		internal bool IsPartOfKey
		{
			get
			{
				return this.m_path.Count != 0 && MetadataHelper.IsPartOfEntityTypeKey(this.LeafEdmMember);
			}
		}

		// Token: 0x17000DF7 RID: 3575
		// (get) Token: 0x0600466B RID: 18027 RVA: 0x000F780F File Offset: 0x000F5A0F
		internal bool IsNullable
		{
			get
			{
				return this.m_path.Count != 0 && MetadataHelper.IsMemberNullable(this.LeafEdmMember);
			}
		}

		// Token: 0x17000DF8 RID: 3576
		// (get) Token: 0x0600466C RID: 18028 RVA: 0x000F782C File Offset: 0x000F5A2C
		internal EntitySet EntitySet
		{
			get
			{
				if (this.m_path.Count == 0)
				{
					return this.m_extent as EntitySet;
				}
				if (this.m_path.Count == 1)
				{
					AssociationEndMember associationEndMember = this.RootEdmMember as AssociationEndMember;
					if (associationEndMember != null)
					{
						return MetadataHelper.GetEntitySetAtEnd((AssociationSet)this.m_extent, associationEndMember);
					}
				}
				return null;
			}
		}

		// Token: 0x17000DF9 RID: 3577
		// (get) Token: 0x0600466D RID: 18029 RVA: 0x000F7882 File Offset: 0x000F5A82
		internal EntitySetBase Extent
		{
			get
			{
				return this.m_extent;
			}
		}

		// Token: 0x17000DFA RID: 3578
		// (get) Token: 0x0600466E RID: 18030 RVA: 0x000F788A File Offset: 0x000F5A8A
		internal EdmType EdmType
		{
			get
			{
				if (this.m_path.Count > 0)
				{
					return this.LeafEdmMember.TypeUsage.EdmType;
				}
				return this.m_extent.ElementType;
			}
		}

		// Token: 0x17000DFB RID: 3579
		// (get) Token: 0x0600466F RID: 18031 RVA: 0x000F78B8 File Offset: 0x000F5AB8
		internal string CqlFieldAlias
		{
			get
			{
				string text = this.PathToString(new bool?(true));
				if (!text.Contains("_"))
				{
					text = text.Replace('.', '_');
				}
				StringBuilder stringBuilder = new StringBuilder();
				CqlWriter.AppendEscapedName(stringBuilder, text);
				return stringBuilder.ToString();
			}
		}

		// Token: 0x06004670 RID: 18032 RVA: 0x000F78FC File Offset: 0x000F5AFC
		internal bool IsAlwaysDefined(Dictionary<EntityType, Set<EntityType>> inheritanceGraph)
		{
			if (this.m_path.Count == 0)
			{
				return true;
			}
			EdmMember edmMember = this.m_path.Last<EdmMember>();
			for (int i = 0; i < this.m_path.Count - 1; i++)
			{
				if (MetadataHelper.IsMemberNullable(this.m_path[i]))
				{
					return false;
				}
			}
			if (this.m_path[0].DeclaringType is AssociationType)
			{
				return true;
			}
			EntityType entityType = this.m_extent.ElementType as EntityType;
			if (entityType == null)
			{
				return true;
			}
			EntityType entityType2 = this.m_path[0].DeclaringType as EntityType;
			EntityType entityType3 = entityType2.BaseType as EntityType;
			return entityType.EdmEquals(entityType2) || MetadataHelper.IsParentOf(entityType2, entityType) || entityType3 == null || ((entityType3.Abstract || MetadataHelper.DoesMemberExist(entityType3, edmMember)) && !MemberPath.RecurseToFindMemberAbsentInConcreteType(entityType3, entityType2, edmMember, entityType, inheritanceGraph));
		}

		// Token: 0x06004671 RID: 18033 RVA: 0x000F79E0 File Offset: 0x000F5BE0
		private static bool RecurseToFindMemberAbsentInConcreteType(EntityType current, EntityType avoidEdge, EdmMember member, EntityType entitySetType, Dictionary<EntityType, Set<EntityType>> inheritanceGraph)
		{
			IEnumerable<EntityType> enumerable = inheritanceGraph[current];
			Func<EntityType, bool> <>9__0;
			Func<EntityType, bool> func;
			if ((func = <>9__0) == null)
			{
				func = (<>9__0 = (EntityType type) => !type.EdmEquals(avoidEdge));
			}
			foreach (EntityType entityType in enumerable.Where(func))
			{
				if (entitySetType.BaseType == null || !entitySetType.BaseType.EdmEquals(entityType))
				{
					if (!entityType.Abstract && !MetadataHelper.DoesMemberExist(entityType, member))
					{
						return true;
					}
					if (MemberPath.RecurseToFindMemberAbsentInConcreteType(entityType, current, member, entitySetType, inheritanceGraph))
					{
						return true;
					}
				}
			}
			return false;
		}

		// Token: 0x06004672 RID: 18034 RVA: 0x000F7A9C File Offset: 0x000F5C9C
		internal void GetIdentifiers(CqlIdentifiers identifiers)
		{
			identifiers.AddIdentifier(this.m_extent.Name);
			identifiers.AddIdentifier(this.m_extent.ElementType.Name);
			foreach (EdmMember edmMember in this.m_path)
			{
				identifiers.AddIdentifier(edmMember.Name);
			}
		}

		// Token: 0x06004673 RID: 18035 RVA: 0x000F7B1C File Offset: 0x000F5D1C
		internal static bool AreAllMembersNullable(IEnumerable<MemberPath> members)
		{
			foreach (MemberPath memberPath in members)
			{
				if (memberPath.m_path.Count == 0)
				{
					return false;
				}
				if (!memberPath.IsNullable)
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x06004674 RID: 18036 RVA: 0x000F7B80 File Offset: 0x000F5D80
		internal static string PropertiesToUserString(IEnumerable<MemberPath> members, bool fullPath)
		{
			bool flag = true;
			StringBuilder stringBuilder = new StringBuilder();
			foreach (MemberPath memberPath in members)
			{
				if (!flag)
				{
					stringBuilder.Append(", ");
				}
				flag = false;
				if (fullPath)
				{
					stringBuilder.Append(memberPath.PathToString(new bool?(false)));
				}
				else
				{
					stringBuilder.Append(memberPath.LeafName);
				}
			}
			return stringBuilder.ToString();
		}

		// Token: 0x06004675 RID: 18037 RVA: 0x000F7C08 File Offset: 0x000F5E08
		internal StringBuilder AsEsql(StringBuilder inputBuilder, string blockAlias)
		{
			StringBuilder builder = new StringBuilder();
			CqlWriter.AppendEscapedName(builder, blockAlias);
			this.AsCql(delegate(string memberName)
			{
				builder.Append('.');
				CqlWriter.AppendEscapedName(builder, memberName);
			}, delegate
			{
				builder.Insert(0, "Key(");
				builder.Append(")");
			}, delegate(StructuralType treatAsType)
			{
				builder.Insert(0, "TREAT(");
				builder.Append(" AS ");
				CqlWriter.AppendEscapedTypeName(builder, treatAsType);
				builder.Append(')');
			});
			inputBuilder.Append(builder);
			return inputBuilder;
		}

		// Token: 0x06004676 RID: 18038 RVA: 0x000F7C6C File Offset: 0x000F5E6C
		internal DbExpression AsCqt(DbExpression row)
		{
			this.AsCql(delegate(string memberName)
			{
				row = row.Property(memberName);
			}, delegate
			{
				row = row.GetRefKey();
			}, delegate(StructuralType treatAsType)
			{
				TypeUsage typeUsage = TypeUsage.Create(treatAsType);
				row = row.TreatAs(typeUsage);
			});
			return row;
		}

		// Token: 0x06004677 RID: 18039 RVA: 0x000F7CB8 File Offset: 0x000F5EB8
		internal void AsCql(Action<string> accessMember, Action getKey, Action<StructuralType> treatAs)
		{
			EdmType edmType = this.m_extent.ElementType;
			foreach (EdmMember edmMember in this.m_path)
			{
				RefType refType;
				StructuralType structuralType;
				if (Helper.IsRefType(edmType))
				{
					refType = (RefType)edmType;
					structuralType = refType.ElementType;
				}
				else
				{
					refType = null;
					structuralType = (StructuralType)edmType;
				}
				bool flag = MetadataHelper.DoesMemberExist(structuralType, edmMember);
				if (refType != null)
				{
					getKey();
				}
				else if (!flag)
				{
					treatAs(edmMember.DeclaringType);
				}
				accessMember(edmMember.Name);
				edmType = edmMember.TypeUsage.EdmType;
			}
		}

		// Token: 0x06004678 RID: 18040 RVA: 0x000F7D74 File Offset: 0x000F5F74
		public bool Equals(MemberPath right)
		{
			return MemberPath.EqualityComparer.Equals(this, right);
		}

		// Token: 0x06004679 RID: 18041 RVA: 0x000F7D84 File Offset: 0x000F5F84
		public override bool Equals(object obj)
		{
			MemberPath memberPath = obj as MemberPath;
			return obj != null && this.Equals(memberPath);
		}

		// Token: 0x0600467A RID: 18042 RVA: 0x000F7DA4 File Offset: 0x000F5FA4
		public override int GetHashCode()
		{
			return MemberPath.EqualityComparer.GetHashCode(this);
		}

		// Token: 0x0600467B RID: 18043 RVA: 0x000F7DB1 File Offset: 0x000F5FB1
		internal bool IsScalarType()
		{
			return this.EdmType.BuiltInTypeKind == BuiltInTypeKind.PrimitiveType || this.EdmType.BuiltInTypeKind == BuiltInTypeKind.EnumType;
		}

		// Token: 0x0600467C RID: 18044 RVA: 0x000F7DD4 File Offset: 0x000F5FD4
		internal static IEnumerable<MemberPath> GetKeyMembers(EntitySetBase extent, MemberDomainMap domainMap)
		{
			MemberPath memberPath = new MemberPath(extent);
			return new List<MemberPath>(memberPath.GetMembers(memberPath.Extent.ElementType, null, null, new bool?(true), domainMap));
		}

		// Token: 0x0600467D RID: 18045 RVA: 0x000F7E15 File Offset: 0x000F6015
		internal IEnumerable<MemberPath> GetMembers(EdmType edmType, bool? isScalar, bool? isConditional, bool? isPartOfKey, MemberDomainMap domainMap)
		{
			StructuralType structuralType = (StructuralType)edmType;
			foreach (EdmMember edmMember in structuralType.Members)
			{
				if (edmMember is AssociationEndMember)
				{
					foreach (MemberPath memberPath in new MemberPath(this, edmMember).GetMembers(((RefType)edmMember.TypeUsage.EdmType).ElementType, isScalar, isConditional, new bool?(true), domainMap))
					{
						yield return memberPath;
					}
					IEnumerator<MemberPath> enumerator2 = null;
				}
				bool flag = MetadataHelper.IsNonRefSimpleMember(edmMember);
				if (isScalar == null)
				{
					goto IL_0160;
				}
				bool? flag2 = isScalar;
				bool flag3 = flag;
				if ((flag2.GetValueOrDefault() == flag3) & (flag2 != null))
				{
					goto IL_0160;
				}
				IL_0212:
				edmMember = null;
				continue;
				IL_0160:
				EdmProperty edmProperty = edmMember as EdmProperty;
				if (edmProperty != null)
				{
					bool flag4 = MetadataHelper.IsPartOfEntityTypeKey(edmProperty);
					if (isPartOfKey != null)
					{
						flag2 = isPartOfKey;
						flag3 = flag4;
						if (!((flag2.GetValueOrDefault() == flag3) & (flag2 != null)))
						{
							goto IL_0212;
						}
					}
					MemberPath memberPath2 = new MemberPath(this, edmProperty);
					bool flag5 = domainMap.IsConditionMember(memberPath2);
					if (isConditional != null)
					{
						flag2 = isConditional;
						flag3 = flag5;
						if (!((flag2.GetValueOrDefault() == flag3) & (flag2 != null)))
						{
							goto IL_0212;
						}
					}
					yield return memberPath2;
					goto IL_0212;
				}
				goto IL_0212;
			}
			ReadOnlyMetadataCollection<EdmMember>.Enumerator enumerator = default(ReadOnlyMetadataCollection<EdmMember>.Enumerator);
			yield break;
			yield break;
		}

		// Token: 0x0600467E RID: 18046 RVA: 0x000F7E4C File Offset: 0x000F604C
		internal bool IsEquivalentViaRefConstraint(MemberPath path1)
		{
			if (this.EdmType is EntityTypeBase || path1.EdmType is EntityTypeBase || !MetadataHelper.IsNonRefSimpleMember(this.LeafEdmMember) || !MetadataHelper.IsNonRefSimpleMember(path1.LeafEdmMember))
			{
				return false;
			}
			AssociationSet associationSet = this.Extent as AssociationSet;
			AssociationSet associationSet2 = path1.Extent as AssociationSet;
			EntitySet entitySet = this.Extent as EntitySet;
			EntitySet entitySet2 = path1.Extent as EntitySet;
			bool flag = false;
			if (associationSet != null && associationSet2 != null)
			{
				if (!associationSet.Equals(associationSet2))
				{
					return false;
				}
				flag = MemberPath.AreAssociationEndPathsEquivalentViaRefConstraint(this, path1, associationSet);
			}
			else
			{
				if (entitySet != null && entitySet2 != null)
				{
					using (List<AssociationSet>.Enumerator enumerator = MetadataHelper.GetAssociationsForEntitySets(entitySet, entitySet2).GetEnumerator())
					{
						while (enumerator.MoveNext())
						{
							AssociationSet associationSet3 = enumerator.Current;
							MemberPath correspondingAssociationPath = this.GetCorrespondingAssociationPath(associationSet3);
							MemberPath correspondingAssociationPath2 = path1.GetCorrespondingAssociationPath(associationSet3);
							if (MemberPath.AreAssociationEndPathsEquivalentViaRefConstraint(correspondingAssociationPath, correspondingAssociationPath2, associationSet3))
							{
								flag = true;
								break;
							}
						}
						return flag;
					}
				}
				AssociationSet associationSet4 = ((associationSet != null) ? associationSet : associationSet2);
				MemberPath memberPath = ((this.Extent is AssociationSet) ? this : path1);
				MemberPath correspondingAssociationPath3 = ((this.Extent is EntitySet) ? this : path1).GetCorrespondingAssociationPath(associationSet4);
				flag = correspondingAssociationPath3 != null && MemberPath.AreAssociationEndPathsEquivalentViaRefConstraint(memberPath, correspondingAssociationPath3, associationSet4);
			}
			return flag;
		}

		// Token: 0x0600467F RID: 18047 RVA: 0x000F7FA4 File Offset: 0x000F61A4
		private static bool AreAssociationEndPathsEquivalentViaRefConstraint(MemberPath assocPath0, MemberPath assocPath1, AssociationSet assocSet)
		{
			AssociationEndMember associationEndMember = assocPath0.RootEdmMember as AssociationEndMember;
			AssociationEndMember associationEndMember2 = assocPath1.RootEdmMember as AssociationEndMember;
			EdmProperty edmProperty = assocPath0.LeafEdmMember as EdmProperty;
			EdmProperty edmProperty2 = assocPath1.LeafEdmMember as EdmProperty;
			if (associationEndMember == null || associationEndMember2 == null || edmProperty == null || edmProperty2 == null)
			{
				return false;
			}
			AssociationType elementType = assocSet.ElementType;
			bool flag = false;
			foreach (ReferentialConstraint referentialConstraint in elementType.ReferentialConstraints)
			{
				bool flag2 = associationEndMember.Name == referentialConstraint.FromRole.Name && associationEndMember2.Name == referentialConstraint.ToRole.Name;
				bool flag3 = associationEndMember2.Name == referentialConstraint.FromRole.Name && associationEndMember.Name == referentialConstraint.ToRole.Name;
				if (flag2 || flag3)
				{
					ReadOnlyMetadataCollection<EdmProperty> readOnlyMetadataCollection = (flag2 ? referentialConstraint.FromProperties : referentialConstraint.ToProperties);
					ReadOnlyMetadataCollection<EdmProperty> readOnlyMetadataCollection2 = (flag2 ? referentialConstraint.ToProperties : referentialConstraint.FromProperties);
					int num = readOnlyMetadataCollection.IndexOf(edmProperty);
					int num2 = readOnlyMetadataCollection2.IndexOf(edmProperty2);
					if (num == num2 && num != -1)
					{
						flag = true;
						break;
					}
				}
			}
			return flag;
		}

		// Token: 0x06004680 RID: 18048 RVA: 0x000F8100 File Offset: 0x000F6300
		private MemberPath GetCorrespondingAssociationPath(AssociationSet assocSet)
		{
			AssociationEndMember someEndForEntitySet = MetadataHelper.GetSomeEndForEntitySet(assocSet, this.m_extent);
			if (someEndForEntitySet == null)
			{
				return null;
			}
			List<EdmMember> list = new List<EdmMember>();
			list.Add(someEndForEntitySet);
			list.AddRange(this.m_path);
			return new MemberPath(assocSet, list);
		}

		// Token: 0x06004681 RID: 18049 RVA: 0x000F8140 File Offset: 0x000F6340
		internal EntitySet GetScopeOfRelationEnd()
		{
			if (this.m_path.Count == 0)
			{
				return null;
			}
			AssociationEndMember associationEndMember = this.LeafEdmMember as AssociationEndMember;
			if (associationEndMember == null)
			{
				return null;
			}
			return MetadataHelper.GetEntitySetAtEnd((AssociationSet)this.m_extent, associationEndMember);
		}

		// Token: 0x06004682 RID: 18050 RVA: 0x000F8180 File Offset: 0x000F6380
		internal string PathToString(bool? forAlias)
		{
			StringBuilder stringBuilder = new StringBuilder();
			if (forAlias != null)
			{
				bool? flag = forAlias;
				bool flag2 = true;
				if ((flag.GetValueOrDefault() == flag2) & (flag != null))
				{
					if (this.m_path.Count == 0)
					{
						return this.m_extent.ElementType.Name;
					}
					stringBuilder.Append(this.m_path[0].DeclaringType.Name);
				}
				else
				{
					stringBuilder.Append(this.m_extent.Name);
				}
			}
			for (int i = 0; i < this.m_path.Count; i++)
			{
				stringBuilder.Append('.');
				stringBuilder.Append(this.m_path[i].Name);
			}
			return stringBuilder.ToString();
		}

		// Token: 0x06004683 RID: 18051 RVA: 0x000F8240 File Offset: 0x000F6440
		internal override void ToCompactString(StringBuilder builder)
		{
			builder.Append(this.PathToString(new bool?(false)));
		}

		// Token: 0x06004684 RID: 18052 RVA: 0x000F8258 File Offset: 0x000F6458
		internal void ToCompactString(StringBuilder builder, string instanceToken)
		{
			builder.Append(instanceToken + this.PathToString(null));
		}

		// Token: 0x04001922 RID: 6434
		private readonly EntitySetBase m_extent;

		// Token: 0x04001923 RID: 6435
		private readonly List<EdmMember> m_path;

		// Token: 0x04001924 RID: 6436
		internal static readonly IEqualityComparer<MemberPath> EqualityComparer = new MemberPath.Comparer();

		// Token: 0x02000BDA RID: 3034
		private sealed class Comparer : IEqualityComparer<MemberPath>
		{
			// Token: 0x06006865 RID: 26725 RVA: 0x00162DB4 File Offset: 0x00160FB4
			public bool Equals(MemberPath left, MemberPath right)
			{
				if (left == right)
				{
					return true;
				}
				if (left == null || right == null)
				{
					return false;
				}
				if (!left.m_extent.Equals(right.m_extent) || left.m_path.Count != right.m_path.Count)
				{
					return false;
				}
				for (int i = 0; i < left.m_path.Count; i++)
				{
					if (!left.m_path[i].Equals(right.m_path[i]))
					{
						return false;
					}
				}
				return true;
			}

			// Token: 0x06006866 RID: 26726 RVA: 0x00162E34 File Offset: 0x00161034
			public int GetHashCode(MemberPath key)
			{
				int num = key.m_extent.GetHashCode();
				foreach (EdmMember edmMember in key.m_path)
				{
					num ^= edmMember.GetHashCode();
				}
				return num;
			}
		}
	}
}
