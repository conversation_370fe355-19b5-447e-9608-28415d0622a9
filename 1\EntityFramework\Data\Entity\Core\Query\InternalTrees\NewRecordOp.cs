﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Metadata.Edm;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003BF RID: 959
	internal sealed class NewRecordOp : ScalarOp
	{
		// Token: 0x06002DE2 RID: 11746 RVA: 0x00091613 File Offset: 0x0008F813
		internal NewRecordOp(TypeUsage type)
			: base(OpType.NewRecord, type)
		{
			this.m_fields = new List<EdmProperty>(TypeHelpers.GetEdmType<RowType>(type).Properties);
		}

		// Token: 0x06002DE3 RID: 11747 RVA: 0x00091634 File Offset: 0x0008F834
		internal NewRecordOp(TypeUsage type, List<EdmProperty> fields)
			: base(OpType.NewRecord, type)
		{
			this.m_fields = fields;
		}

		// Token: 0x06002DE4 RID: 11748 RVA: 0x00091646 File Offset: 0x0008F846
		private NewRecordOp()
			: base(OpType.NewRecord)
		{
		}

		// Token: 0x06002DE5 RID: 11749 RVA: 0x00091650 File Offset: 0x0008F850
		internal bool GetFieldPosition(EdmProperty field, out int fieldPosition)
		{
			fieldPosition = 0;
			for (int i = 0; i < this.m_fields.Count; i++)
			{
				if (this.m_fields[i] == field)
				{
					fieldPosition = i;
					return true;
				}
			}
			return false;
		}

		// Token: 0x17000906 RID: 2310
		// (get) Token: 0x06002DE6 RID: 11750 RVA: 0x0009168B File Offset: 0x0008F88B
		internal List<EdmProperty> Properties
		{
			get
			{
				return this.m_fields;
			}
		}

		// Token: 0x06002DE7 RID: 11751 RVA: 0x00091693 File Offset: 0x0008F893
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002DE8 RID: 11752 RVA: 0x0009169D File Offset: 0x0008F89D
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000F58 RID: 3928
		private readonly List<EdmProperty> m_fields;

		// Token: 0x04000F59 RID: 3929
		internal static readonly NewRecordOp Pattern = new NewRecordOp();
	}
}
