﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common.CommandTrees.ExpressionBuilder;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Utilities;
using System.Reflection;

namespace System.Data.Entity.Core.Common.CommandTrees
{
	// Token: 0x020006CA RID: 1738
	public sealed class DbLambda
	{
		// Token: 0x06005124 RID: 20772 RVA: 0x00121C7D File Offset: 0x0011FE7D
		internal DbLambda(ReadOnlyCollection<DbVariableReferenceExpression> variables, DbExpression bodyExp)
		{
			this._variables = variables;
			this._body = bodyExp;
		}

		// Token: 0x17000FCE RID: 4046
		// (get) Token: 0x06005125 RID: 20773 RVA: 0x00121C93 File Offset: 0x0011FE93
		public DbExpression Body
		{
			get
			{
				return this._body;
			}
		}

		// Token: 0x17000FCF RID: 4047
		// (get) Token: 0x06005126 RID: 20774 RVA: 0x00121C9B File Offset: 0x0011FE9B
		public IList<DbVariableReferenceExpression> Variables
		{
			get
			{
				return this._variables;
			}
		}

		// Token: 0x06005127 RID: 20775 RVA: 0x00121CA3 File Offset: 0x0011FEA3
		public static DbLambda Create(DbExpression body, IEnumerable<DbVariableReferenceExpression> variables)
		{
			return DbExpressionBuilder.Lambda(body, variables);
		}

		// Token: 0x06005128 RID: 20776 RVA: 0x00121CAC File Offset: 0x0011FEAC
		public static DbLambda Create(DbExpression body, params DbVariableReferenceExpression[] variables)
		{
			return DbExpressionBuilder.Lambda(body, variables);
		}

		// Token: 0x06005129 RID: 20777 RVA: 0x00121CB8 File Offset: 0x0011FEB8
		public static DbLambda Create(TypeUsage argument1Type, Func<DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<Func<DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[] { argument1Type });
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0]), array);
		}

		// Token: 0x0600512A RID: 20778 RVA: 0x00121D04 File Offset: 0x0011FF04
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, Func<DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[] { argument1Type, argument2Type });
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1]), array);
		}

		// Token: 0x0600512B RID: 20779 RVA: 0x00121D64 File Offset: 0x0011FF64
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, Func<DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[] { argument1Type, argument2Type, argument3Type });
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2]), array);
		}

		// Token: 0x0600512C RID: 20780 RVA: 0x00121DD4 File Offset: 0x0011FFD4
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, TypeUsage argument4Type, Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<TypeUsage>(argument4Type, "argument4Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[] { argument1Type, argument2Type, argument3Type, argument4Type });
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2], array[3]), array);
		}

		// Token: 0x0600512D RID: 20781 RVA: 0x00121E5C File Offset: 0x0012005C
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, TypeUsage argument4Type, TypeUsage argument5Type, Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<TypeUsage>(argument4Type, "argument4Type");
			Check.NotNull<TypeUsage>(argument5Type, "argument5Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[] { argument1Type, argument2Type, argument3Type, argument4Type, argument5Type });
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2], array[3], array[4]), array);
		}

		// Token: 0x0600512E RID: 20782 RVA: 0x00121EF8 File Offset: 0x001200F8
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, TypeUsage argument4Type, TypeUsage argument5Type, TypeUsage argument6Type, Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<TypeUsage>(argument4Type, "argument4Type");
			Check.NotNull<TypeUsage>(argument5Type, "argument5Type");
			Check.NotNull<TypeUsage>(argument6Type, "argument6Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[] { argument1Type, argument2Type, argument3Type, argument4Type, argument5Type, argument6Type });
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2], array[3], array[4], array[5]), array);
		}

		// Token: 0x0600512F RID: 20783 RVA: 0x00121FA8 File Offset: 0x001201A8
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, TypeUsage argument4Type, TypeUsage argument5Type, TypeUsage argument6Type, TypeUsage argument7Type, Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<TypeUsage>(argument4Type, "argument4Type");
			Check.NotNull<TypeUsage>(argument5Type, "argument5Type");
			Check.NotNull<TypeUsage>(argument6Type, "argument6Type");
			Check.NotNull<TypeUsage>(argument7Type, "argument7Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[] { argument1Type, argument2Type, argument3Type, argument4Type, argument5Type, argument6Type, argument7Type });
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2], array[3], array[4], array[5], array[6]), array);
		}

		// Token: 0x06005130 RID: 20784 RVA: 0x00122070 File Offset: 0x00120270
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, TypeUsage argument4Type, TypeUsage argument5Type, TypeUsage argument6Type, TypeUsage argument7Type, TypeUsage argument8Type, Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<TypeUsage>(argument4Type, "argument4Type");
			Check.NotNull<TypeUsage>(argument5Type, "argument5Type");
			Check.NotNull<TypeUsage>(argument6Type, "argument6Type");
			Check.NotNull<TypeUsage>(argument7Type, "argument7Type");
			Check.NotNull<TypeUsage>(argument8Type, "argument8Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[] { argument1Type, argument2Type, argument3Type, argument4Type, argument5Type, argument6Type, argument7Type, argument8Type });
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2], array[3], array[4], array[5], array[6], array[7]), array);
		}

		// Token: 0x06005131 RID: 20785 RVA: 0x0012214C File Offset: 0x0012034C
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, TypeUsage argument4Type, TypeUsage argument5Type, TypeUsage argument6Type, TypeUsage argument7Type, TypeUsage argument8Type, TypeUsage argument9Type, Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<TypeUsage>(argument4Type, "argument4Type");
			Check.NotNull<TypeUsage>(argument5Type, "argument5Type");
			Check.NotNull<TypeUsage>(argument6Type, "argument6Type");
			Check.NotNull<TypeUsage>(argument7Type, "argument7Type");
			Check.NotNull<TypeUsage>(argument8Type, "argument8Type");
			Check.NotNull<TypeUsage>(argument9Type, "argument9Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[] { argument1Type, argument2Type, argument3Type, argument4Type, argument5Type, argument6Type, argument7Type, argument8Type, argument9Type });
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2], array[3], array[4], array[5], array[6], array[7], array[8]), array);
		}

		// Token: 0x06005132 RID: 20786 RVA: 0x0012223C File Offset: 0x0012043C
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, TypeUsage argument4Type, TypeUsage argument5Type, TypeUsage argument6Type, TypeUsage argument7Type, TypeUsage argument8Type, TypeUsage argument9Type, TypeUsage argument10Type, Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<TypeUsage>(argument4Type, "argument4Type");
			Check.NotNull<TypeUsage>(argument5Type, "argument5Type");
			Check.NotNull<TypeUsage>(argument6Type, "argument6Type");
			Check.NotNull<TypeUsage>(argument7Type, "argument7Type");
			Check.NotNull<TypeUsage>(argument8Type, "argument8Type");
			Check.NotNull<TypeUsage>(argument9Type, "argument9Type");
			Check.NotNull<TypeUsage>(argument10Type, "argument10Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[] { argument1Type, argument2Type, argument3Type, argument4Type, argument5Type, argument6Type, argument7Type, argument8Type, argument9Type, argument10Type });
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2], array[3], array[4], array[5], array[6], array[7], array[8], array[9]), array);
		}

		// Token: 0x06005133 RID: 20787 RVA: 0x00122344 File Offset: 0x00120544
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, TypeUsage argument4Type, TypeUsage argument5Type, TypeUsage argument6Type, TypeUsage argument7Type, TypeUsage argument8Type, TypeUsage argument9Type, TypeUsage argument10Type, TypeUsage argument11Type, Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<TypeUsage>(argument4Type, "argument4Type");
			Check.NotNull<TypeUsage>(argument5Type, "argument5Type");
			Check.NotNull<TypeUsage>(argument6Type, "argument6Type");
			Check.NotNull<TypeUsage>(argument7Type, "argument7Type");
			Check.NotNull<TypeUsage>(argument8Type, "argument8Type");
			Check.NotNull<TypeUsage>(argument9Type, "argument9Type");
			Check.NotNull<TypeUsage>(argument10Type, "argument10Type");
			Check.NotNull<TypeUsage>(argument11Type, "argument11Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[]
			{
				argument1Type, argument2Type, argument3Type, argument4Type, argument5Type, argument6Type, argument7Type, argument8Type, argument9Type, argument10Type,
				argument11Type
			});
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2], array[3], array[4], array[5], array[6], array[7], array[8], array[9], array[10]), array);
		}

		// Token: 0x06005134 RID: 20788 RVA: 0x00122464 File Offset: 0x00120664
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, TypeUsage argument4Type, TypeUsage argument5Type, TypeUsage argument6Type, TypeUsage argument7Type, TypeUsage argument8Type, TypeUsage argument9Type, TypeUsage argument10Type, TypeUsage argument11Type, TypeUsage argument12Type, Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<TypeUsage>(argument4Type, "argument4Type");
			Check.NotNull<TypeUsage>(argument5Type, "argument5Type");
			Check.NotNull<TypeUsage>(argument6Type, "argument6Type");
			Check.NotNull<TypeUsage>(argument7Type, "argument7Type");
			Check.NotNull<TypeUsage>(argument8Type, "argument8Type");
			Check.NotNull<TypeUsage>(argument9Type, "argument9Type");
			Check.NotNull<TypeUsage>(argument10Type, "argument10Type");
			Check.NotNull<TypeUsage>(argument11Type, "argument11Type");
			Check.NotNull<TypeUsage>(argument12Type, "argument12Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[]
			{
				argument1Type, argument2Type, argument3Type, argument4Type, argument5Type, argument6Type, argument7Type, argument8Type, argument9Type, argument10Type,
				argument11Type, argument12Type
			});
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2], array[3], array[4], array[5], array[6], array[7], array[8], array[9], array[10], array[11]), array);
		}

		// Token: 0x06005135 RID: 20789 RVA: 0x0012259C File Offset: 0x0012079C
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, TypeUsage argument4Type, TypeUsage argument5Type, TypeUsage argument6Type, TypeUsage argument7Type, TypeUsage argument8Type, TypeUsage argument9Type, TypeUsage argument10Type, TypeUsage argument11Type, TypeUsage argument12Type, TypeUsage argument13Type, Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<TypeUsage>(argument4Type, "argument4Type");
			Check.NotNull<TypeUsage>(argument5Type, "argument5Type");
			Check.NotNull<TypeUsage>(argument6Type, "argument6Type");
			Check.NotNull<TypeUsage>(argument7Type, "argument7Type");
			Check.NotNull<TypeUsage>(argument8Type, "argument8Type");
			Check.NotNull<TypeUsage>(argument9Type, "argument9Type");
			Check.NotNull<TypeUsage>(argument10Type, "argument10Type");
			Check.NotNull<TypeUsage>(argument11Type, "argument11Type");
			Check.NotNull<TypeUsage>(argument12Type, "argument12Type");
			Check.NotNull<TypeUsage>(argument13Type, "argument13Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[]
			{
				argument1Type, argument2Type, argument3Type, argument4Type, argument5Type, argument6Type, argument7Type, argument8Type, argument9Type, argument10Type,
				argument11Type, argument12Type, argument13Type
			});
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2], array[3], array[4], array[5], array[6], array[7], array[8], array[9], array[10], array[11], array[12]), array);
		}

		// Token: 0x06005136 RID: 20790 RVA: 0x001226E8 File Offset: 0x001208E8
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, TypeUsage argument4Type, TypeUsage argument5Type, TypeUsage argument6Type, TypeUsage argument7Type, TypeUsage argument8Type, TypeUsage argument9Type, TypeUsage argument10Type, TypeUsage argument11Type, TypeUsage argument12Type, TypeUsage argument13Type, TypeUsage argument14Type, Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<TypeUsage>(argument4Type, "argument4Type");
			Check.NotNull<TypeUsage>(argument5Type, "argument5Type");
			Check.NotNull<TypeUsage>(argument6Type, "argument6Type");
			Check.NotNull<TypeUsage>(argument7Type, "argument7Type");
			Check.NotNull<TypeUsage>(argument8Type, "argument8Type");
			Check.NotNull<TypeUsage>(argument9Type, "argument9Type");
			Check.NotNull<TypeUsage>(argument10Type, "argument10Type");
			Check.NotNull<TypeUsage>(argument11Type, "argument11Type");
			Check.NotNull<TypeUsage>(argument12Type, "argument12Type");
			Check.NotNull<TypeUsage>(argument13Type, "argument13Type");
			Check.NotNull<TypeUsage>(argument14Type, "argument14Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[]
			{
				argument1Type, argument2Type, argument3Type, argument4Type, argument5Type, argument6Type, argument7Type, argument8Type, argument9Type, argument10Type,
				argument11Type, argument12Type, argument13Type, argument14Type
			});
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2], array[3], array[4], array[5], array[6], array[7], array[8], array[9], array[10], array[11], array[12], array[13]), array);
		}

		// Token: 0x06005137 RID: 20791 RVA: 0x0012284C File Offset: 0x00120A4C
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, TypeUsage argument4Type, TypeUsage argument5Type, TypeUsage argument6Type, TypeUsage argument7Type, TypeUsage argument8Type, TypeUsage argument9Type, TypeUsage argument10Type, TypeUsage argument11Type, TypeUsage argument12Type, TypeUsage argument13Type, TypeUsage argument14Type, TypeUsage argument15Type, Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<TypeUsage>(argument4Type, "argument4Type");
			Check.NotNull<TypeUsage>(argument5Type, "argument5Type");
			Check.NotNull<TypeUsage>(argument6Type, "argument6Type");
			Check.NotNull<TypeUsage>(argument7Type, "argument7Type");
			Check.NotNull<TypeUsage>(argument8Type, "argument8Type");
			Check.NotNull<TypeUsage>(argument9Type, "argument9Type");
			Check.NotNull<TypeUsage>(argument10Type, "argument10Type");
			Check.NotNull<TypeUsage>(argument11Type, "argument11Type");
			Check.NotNull<TypeUsage>(argument12Type, "argument12Type");
			Check.NotNull<TypeUsage>(argument13Type, "argument13Type");
			Check.NotNull<TypeUsage>(argument14Type, "argument14Type");
			Check.NotNull<TypeUsage>(argument15Type, "argument15Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[]
			{
				argument1Type, argument2Type, argument3Type, argument4Type, argument5Type, argument6Type, argument7Type, argument8Type, argument9Type, argument10Type,
				argument11Type, argument12Type, argument13Type, argument14Type, argument15Type
			});
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2], array[3], array[4], array[5], array[6], array[7], array[8], array[9], array[10], array[11], array[12], array[13], array[14]), array);
		}

		// Token: 0x06005138 RID: 20792 RVA: 0x001229C8 File Offset: 0x00120BC8
		public static DbLambda Create(TypeUsage argument1Type, TypeUsage argument2Type, TypeUsage argument3Type, TypeUsage argument4Type, TypeUsage argument5Type, TypeUsage argument6Type, TypeUsage argument7Type, TypeUsage argument8Type, TypeUsage argument9Type, TypeUsage argument10Type, TypeUsage argument11Type, TypeUsage argument12Type, TypeUsage argument13Type, TypeUsage argument14Type, TypeUsage argument15Type, TypeUsage argument16Type, Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression> lambdaFunction)
		{
			Check.NotNull<TypeUsage>(argument1Type, "argument1Type");
			Check.NotNull<TypeUsage>(argument2Type, "argument2Type");
			Check.NotNull<TypeUsage>(argument3Type, "argument3Type");
			Check.NotNull<TypeUsage>(argument4Type, "argument4Type");
			Check.NotNull<TypeUsage>(argument5Type, "argument5Type");
			Check.NotNull<TypeUsage>(argument6Type, "argument6Type");
			Check.NotNull<TypeUsage>(argument7Type, "argument7Type");
			Check.NotNull<TypeUsage>(argument8Type, "argument8Type");
			Check.NotNull<TypeUsage>(argument9Type, "argument9Type");
			Check.NotNull<TypeUsage>(argument10Type, "argument10Type");
			Check.NotNull<TypeUsage>(argument11Type, "argument11Type");
			Check.NotNull<TypeUsage>(argument12Type, "argument12Type");
			Check.NotNull<TypeUsage>(argument13Type, "argument13Type");
			Check.NotNull<TypeUsage>(argument14Type, "argument14Type");
			Check.NotNull<TypeUsage>(argument15Type, "argument15Type");
			Check.NotNull<TypeUsage>(argument16Type, "argument16Type");
			Check.NotNull<Func<DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression, DbExpression>>(lambdaFunction, "lambdaFunction");
			DbVariableReferenceExpression[] array = DbLambda.CreateVariables(lambdaFunction.Method, new TypeUsage[]
			{
				argument1Type, argument2Type, argument3Type, argument4Type, argument5Type, argument6Type, argument7Type, argument8Type, argument9Type, argument10Type,
				argument11Type, argument12Type, argument13Type, argument14Type, argument15Type, argument16Type
			});
			return DbExpressionBuilder.Lambda(lambdaFunction(array[0], array[1], array[2], array[3], array[4], array[5], array[6], array[7], array[8], array[9], array[10], array[11], array[12], array[13], array[14], array[15]), array);
		}

		// Token: 0x06005139 RID: 20793 RVA: 0x00122B5C File Offset: 0x00120D5C
		private static DbVariableReferenceExpression[] CreateVariables(MethodInfo lambdaMethod, params TypeUsage[] argumentTypes)
		{
			string[] array = DbExpressionBuilder.ExtractAliases(lambdaMethod);
			DbVariableReferenceExpression[] array2 = new DbVariableReferenceExpression[argumentTypes.Length];
			for (int i = 0; i < array.Length; i++)
			{
				array2[i] = argumentTypes[i].Variable(array[i]);
			}
			return array2;
		}

		// Token: 0x04001DB0 RID: 7600
		private readonly ReadOnlyCollection<DbVariableReferenceExpression> _variables;

		// Token: 0x04001DB1 RID: 7601
		private readonly DbExpression _body;
	}
}
