﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Data.Entity.Core.Common;
using System.Data.Entity.Core.Common.CommandTrees;
using System.Data.Entity.Core.Common.Utils;
using System.Data.Entity.Core.Metadata.Edm;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Hierarchy;
using System.Data.Entity.Resources;
using System.Data.Entity.Spatial;
using System.Data.Entity.Utilities;
using System.Globalization;
using System.Linq;

namespace System.Data.Entity.Core.Mapping.Update.Internal
{
	// Token: 0x020005CD RID: 1485
	internal class Propagator : UpdateExpressionVisitor<ChangeNode>
	{
		// Token: 0x060047C1 RID: 18369 RVA: 0x000FD807 File Offset: 0x000FBA07
		private Propagator(UpdateTranslator parent, EntitySet table)
		{
			this.m_updateTranslator = parent;
			this.m_table = table;
		}

		// Token: 0x17000E2B RID: 3627
		// (get) Token: 0x060047C2 RID: 18370 RVA: 0x000FD81D File Offset: 0x000FBA1D
		internal UpdateTranslator UpdateTranslator
		{
			get
			{
				return this.m_updateTranslator;
			}
		}

		// Token: 0x17000E2C RID: 3628
		// (get) Token: 0x060047C3 RID: 18371 RVA: 0x000FD825 File Offset: 0x000FBA25
		protected override string VisitorName
		{
			get
			{
				return Propagator._visitorName;
			}
		}

		// Token: 0x060047C4 RID: 18372 RVA: 0x000FD82C File Offset: 0x000FBA2C
		internal static ChangeNode Propagate(UpdateTranslator parent, EntitySet table, DbQueryCommandTree umView)
		{
			DbExpressionVisitor<ChangeNode> dbExpressionVisitor = new Propagator(parent, table);
			return umView.Query.Accept<ChangeNode>(dbExpressionVisitor);
		}

		// Token: 0x060047C5 RID: 18373 RVA: 0x000FD84D File Offset: 0x000FBA4D
		private static ChangeNode BuildChangeNode(DbExpression node)
		{
			return new ChangeNode(MetadataHelper.GetElementType(node.ResultType));
		}

		// Token: 0x060047C6 RID: 18374 RVA: 0x000FD85F File Offset: 0x000FBA5F
		public override ChangeNode Visit(DbCrossJoinExpression node)
		{
			Check.NotNull<DbCrossJoinExpression>(node, "node");
			throw new NotSupportedException(Strings.Update_UnsupportedJoinType(node.ExpressionKind));
		}

		// Token: 0x060047C7 RID: 18375 RVA: 0x000FD884 File Offset: 0x000FBA84
		public override ChangeNode Visit(DbJoinExpression node)
		{
			Check.NotNull<DbJoinExpression>(node, "node");
			if (DbExpressionKind.InnerJoin != node.ExpressionKind && DbExpressionKind.LeftOuterJoin != node.ExpressionKind)
			{
				throw new NotSupportedException(Strings.Update_UnsupportedJoinType(node.ExpressionKind));
			}
			DbExpression expression = node.Left.Expression;
			DbExpression expression2 = node.Right.Expression;
			ChangeNode changeNode = this.Visit(expression);
			ChangeNode changeNode2 = this.Visit(expression2);
			return new Propagator.JoinPropagator(changeNode, changeNode2, node, this).Propagate();
		}

		// Token: 0x060047C8 RID: 18376 RVA: 0x000FD8FC File Offset: 0x000FBAFC
		public override ChangeNode Visit(DbUnionAllExpression node)
		{
			Check.NotNull<DbUnionAllExpression>(node, "node");
			ChangeNode changeNode = Propagator.BuildChangeNode(node);
			ChangeNode changeNode2 = this.Visit(node.Left);
			ChangeNode changeNode3 = this.Visit(node.Right);
			changeNode.Inserted.AddRange(changeNode2.Inserted);
			changeNode.Inserted.AddRange(changeNode3.Inserted);
			changeNode.Deleted.AddRange(changeNode2.Deleted);
			changeNode.Deleted.AddRange(changeNode3.Deleted);
			changeNode.Placeholder = changeNode2.Placeholder;
			return changeNode;
		}

		// Token: 0x060047C9 RID: 18377 RVA: 0x000FD988 File Offset: 0x000FBB88
		public override ChangeNode Visit(DbProjectExpression node)
		{
			Check.NotNull<DbProjectExpression>(node, "node");
			ChangeNode changeNode = Propagator.BuildChangeNode(node);
			ChangeNode changeNode2 = this.Visit(node.Input.Expression);
			foreach (PropagatorResult propagatorResult in changeNode2.Inserted)
			{
				changeNode.Inserted.Add(Propagator.Project(node, propagatorResult, changeNode.ElementType));
			}
			foreach (PropagatorResult propagatorResult2 in changeNode2.Deleted)
			{
				changeNode.Deleted.Add(Propagator.Project(node, propagatorResult2, changeNode.ElementType));
			}
			changeNode.Placeholder = Propagator.Project(node, changeNode2.Placeholder, changeNode.ElementType);
			return changeNode;
		}

		// Token: 0x060047CA RID: 18378 RVA: 0x000FDA80 File Offset: 0x000FBC80
		private static PropagatorResult Project(DbProjectExpression node, PropagatorResult row, TypeUsage resultType)
		{
			DbNewInstanceExpression dbNewInstanceExpression = node.Projection as DbNewInstanceExpression;
			if (dbNewInstanceExpression == null)
			{
				throw new NotSupportedException(Strings.Update_UnsupportedProjection(node.Projection.ExpressionKind));
			}
			PropagatorResult[] array = new PropagatorResult[dbNewInstanceExpression.Arguments.Count];
			for (int i = 0; i < array.Length; i++)
			{
				array[i] = Propagator.Evaluator.Evaluate(dbNewInstanceExpression.Arguments[i], row);
			}
			return PropagatorResult.CreateStructuralValue(array, (StructuralType)resultType.EdmType, false);
		}

		// Token: 0x060047CB RID: 18379 RVA: 0x000FDB00 File Offset: 0x000FBD00
		public override ChangeNode Visit(DbFilterExpression node)
		{
			Check.NotNull<DbFilterExpression>(node, "node");
			ChangeNode changeNode = Propagator.BuildChangeNode(node);
			ChangeNode changeNode2 = this.Visit(node.Input.Expression);
			changeNode.Inserted.AddRange(Propagator.Evaluator.Filter(node.Predicate, changeNode2.Inserted));
			changeNode.Deleted.AddRange(Propagator.Evaluator.Filter(node.Predicate, changeNode2.Deleted));
			changeNode.Placeholder = changeNode2.Placeholder;
			return changeNode;
		}

		// Token: 0x060047CC RID: 18380 RVA: 0x000FDB78 File Offset: 0x000FBD78
		public override ChangeNode Visit(DbScanExpression node)
		{
			Check.NotNull<DbScanExpression>(node, "node");
			EntitySetBase target = node.Target;
			ChangeNode extentModifications = this.UpdateTranslator.GetExtentModifications(target);
			if (extentModifications.Placeholder == null)
			{
				extentModifications.Placeholder = Propagator.ExtentPlaceholderCreator.CreatePlaceholder(target);
			}
			return extentModifications;
		}

		// Token: 0x04001983 RID: 6531
		private readonly UpdateTranslator m_updateTranslator;

		// Token: 0x04001984 RID: 6532
		private readonly EntitySet m_table;

		// Token: 0x04001985 RID: 6533
		private static readonly string _visitorName = typeof(Propagator).FullName;

		// Token: 0x02000C05 RID: 3077
		private class Evaluator : UpdateExpressionVisitor<PropagatorResult>
		{
			// Token: 0x06006919 RID: 26905 RVA: 0x0016614F File Offset: 0x0016434F
			private Evaluator(PropagatorResult row)
			{
				this.m_row = row;
			}

			// Token: 0x17001145 RID: 4421
			// (get) Token: 0x0600691A RID: 26906 RVA: 0x0016615E File Offset: 0x0016435E
			protected override string VisitorName
			{
				get
				{
					return Propagator.Evaluator._visitorName;
				}
			}

			// Token: 0x0600691B RID: 26907 RVA: 0x00166165 File Offset: 0x00164365
			internal static IEnumerable<PropagatorResult> Filter(DbExpression predicate, IEnumerable<PropagatorResult> rows)
			{
				foreach (PropagatorResult propagatorResult in rows)
				{
					if (Propagator.Evaluator.EvaluatePredicate(predicate, propagatorResult))
					{
						yield return propagatorResult;
					}
				}
				IEnumerator<PropagatorResult> enumerator = null;
				yield break;
				yield break;
			}

			// Token: 0x0600691C RID: 26908 RVA: 0x0016617C File Offset: 0x0016437C
			internal static bool EvaluatePredicate(DbExpression predicate, PropagatorResult row)
			{
				Propagator.Evaluator evaluator = new Propagator.Evaluator(row);
				return Propagator.Evaluator.ConvertResultToBool(predicate.Accept<PropagatorResult>(evaluator)).GetValueOrDefault();
			}

			// Token: 0x0600691D RID: 26909 RVA: 0x001661A4 File Offset: 0x001643A4
			internal static PropagatorResult Evaluate(DbExpression node, PropagatorResult row)
			{
				DbExpressionVisitor<PropagatorResult> dbExpressionVisitor = new Propagator.Evaluator(row);
				return node.Accept<PropagatorResult>(dbExpressionVisitor);
			}

			// Token: 0x0600691E RID: 26910 RVA: 0x001661C0 File Offset: 0x001643C0
			private static bool? ConvertResultToBool(PropagatorResult result)
			{
				if (result.IsNull)
				{
					return null;
				}
				return new bool?((bool)result.GetSimpleValue());
			}

			// Token: 0x0600691F RID: 26911 RVA: 0x001661F0 File Offset: 0x001643F0
			private static PropagatorResult ConvertBoolToResult(bool? booleanValue, params PropagatorResult[] inputs)
			{
				object obj;
				if (booleanValue != null)
				{
					obj = booleanValue.Value;
				}
				else
				{
					obj = null;
				}
				return PropagatorResult.CreateSimpleValue(Propagator.Evaluator.PropagateUnknownAndPreserveFlags(null, inputs), obj);
			}

			// Token: 0x06006920 RID: 26912 RVA: 0x00166224 File Offset: 0x00164424
			public override PropagatorResult Visit(DbIsOfExpression predicate)
			{
				Check.NotNull<DbIsOfExpression>(predicate, "predicate");
				if (DbExpressionKind.IsOfOnly != predicate.ExpressionKind)
				{
					throw base.ConstructNotSupportedException(predicate);
				}
				PropagatorResult propagatorResult = this.Visit(predicate.Argument);
				bool flag = !propagatorResult.IsNull && propagatorResult.StructuralType.EdmEquals(predicate.OfType.EdmType);
				return Propagator.Evaluator.ConvertBoolToResult(new bool?(flag), new PropagatorResult[] { propagatorResult });
			}

			// Token: 0x06006921 RID: 26913 RVA: 0x00166294 File Offset: 0x00164494
			public override PropagatorResult Visit(DbComparisonExpression predicate)
			{
				Check.NotNull<DbComparisonExpression>(predicate, "predicate");
				if (DbExpressionKind.Equals == predicate.ExpressionKind)
				{
					PropagatorResult propagatorResult = this.Visit(predicate.Left);
					PropagatorResult propagatorResult2 = this.Visit(predicate.Right);
					bool? flag;
					if (propagatorResult.IsNull || propagatorResult2.IsNull)
					{
						flag = null;
					}
					else
					{
						object simpleValue = propagatorResult.GetSimpleValue();
						object simpleValue2 = propagatorResult2.GetSimpleValue();
						flag = new bool?(ByValueEqualityComparer.Default.Equals(simpleValue, simpleValue2));
					}
					return Propagator.Evaluator.ConvertBoolToResult(flag, new PropagatorResult[] { propagatorResult, propagatorResult2 });
				}
				throw base.ConstructNotSupportedException(predicate);
			}

			// Token: 0x06006922 RID: 26914 RVA: 0x0016632C File Offset: 0x0016452C
			public override PropagatorResult Visit(DbAndExpression predicate)
			{
				Check.NotNull<DbAndExpression>(predicate, "predicate");
				PropagatorResult propagatorResult = this.Visit(predicate.Left);
				PropagatorResult propagatorResult2 = this.Visit(predicate.Right);
				bool? flag = Propagator.Evaluator.ConvertResultToBool(propagatorResult);
				bool? flag2 = Propagator.Evaluator.ConvertResultToBool(propagatorResult2);
				if ((flag != null && !flag.Value && Propagator.Evaluator.PreservedAndKnown(propagatorResult)) || (flag2 != null && !flag2.Value && Propagator.Evaluator.PreservedAndKnown(propagatorResult2)))
				{
					return Propagator.Evaluator.CreatePerservedAndKnownResult(false);
				}
				return Propagator.Evaluator.ConvertBoolToResult(flag.And(flag2), new PropagatorResult[] { propagatorResult, propagatorResult2 });
			}

			// Token: 0x06006923 RID: 26915 RVA: 0x001663C8 File Offset: 0x001645C8
			public override PropagatorResult Visit(DbOrExpression predicate)
			{
				Check.NotNull<DbOrExpression>(predicate, "predicate");
				PropagatorResult propagatorResult = this.Visit(predicate.Left);
				PropagatorResult propagatorResult2 = this.Visit(predicate.Right);
				bool? flag = Propagator.Evaluator.ConvertResultToBool(propagatorResult);
				bool? flag2 = Propagator.Evaluator.ConvertResultToBool(propagatorResult2);
				if ((flag != null && flag.Value && Propagator.Evaluator.PreservedAndKnown(propagatorResult)) || (flag2 != null && flag2.Value && Propagator.Evaluator.PreservedAndKnown(propagatorResult2)))
				{
					return Propagator.Evaluator.CreatePerservedAndKnownResult(true);
				}
				return Propagator.Evaluator.ConvertBoolToResult(flag.Or(flag2), new PropagatorResult[] { propagatorResult, propagatorResult2 });
			}

			// Token: 0x06006924 RID: 26916 RVA: 0x00166463 File Offset: 0x00164663
			private static PropagatorResult CreatePerservedAndKnownResult(object value)
			{
				return PropagatorResult.CreateSimpleValue(PropagatorFlags.Preserve, value);
			}

			// Token: 0x06006925 RID: 26917 RVA: 0x0016646C File Offset: 0x0016466C
			private static bool PreservedAndKnown(PropagatorResult result)
			{
				return PropagatorFlags.Preserve == (result.PropagatorFlags & (PropagatorFlags.Preserve | PropagatorFlags.Unknown));
			}

			// Token: 0x06006926 RID: 26918 RVA: 0x0016647C File Offset: 0x0016467C
			public override PropagatorResult Visit(DbNotExpression predicate)
			{
				Check.NotNull<DbNotExpression>(predicate, "predicate");
				PropagatorResult propagatorResult = this.Visit(predicate.Argument);
				return Propagator.Evaluator.ConvertBoolToResult(Propagator.Evaluator.ConvertResultToBool(propagatorResult).Not(), new PropagatorResult[] { propagatorResult });
			}

			// Token: 0x06006927 RID: 26919 RVA: 0x001664BC File Offset: 0x001646BC
			public override PropagatorResult Visit(DbCaseExpression node)
			{
				Check.NotNull<DbCaseExpression>(node, "node");
				int num = -1;
				int num2 = 0;
				List<PropagatorResult> list = new List<PropagatorResult>();
				foreach (DbExpression dbExpression in node.When)
				{
					PropagatorResult propagatorResult = this.Visit(dbExpression);
					list.Add(propagatorResult);
					if (Propagator.Evaluator.ConvertResultToBool(propagatorResult).GetValueOrDefault())
					{
						num = num2;
						break;
					}
					num2++;
				}
				PropagatorResult propagatorResult2;
				if (-1 == num)
				{
					propagatorResult2 = this.Visit(node.Else);
				}
				else
				{
					propagatorResult2 = this.Visit(node.Then[num]);
				}
				list.Add(propagatorResult2);
				PropagatorFlags propagatorFlags = Propagator.Evaluator.PropagateUnknownAndPreserveFlags(propagatorResult2, list);
				return propagatorResult2.ReplicateResultWithNewFlags(propagatorFlags);
			}

			// Token: 0x06006928 RID: 26920 RVA: 0x00166588 File Offset: 0x00164788
			public override PropagatorResult Visit(DbVariableReferenceExpression node)
			{
				Check.NotNull<DbVariableReferenceExpression>(node, "node");
				return this.m_row;
			}

			// Token: 0x06006929 RID: 26921 RVA: 0x0016659C File Offset: 0x0016479C
			public override PropagatorResult Visit(DbPropertyExpression node)
			{
				Check.NotNull<DbPropertyExpression>(node, "node");
				PropagatorResult propagatorResult = this.Visit(node.Instance);
				PropagatorResult propagatorResult2;
				if (propagatorResult.IsNull)
				{
					propagatorResult2 = PropagatorResult.CreateSimpleValue(propagatorResult.PropagatorFlags, null);
				}
				else
				{
					propagatorResult2 = propagatorResult.GetMemberValue(node.Property);
				}
				return propagatorResult2;
			}

			// Token: 0x0600692A RID: 26922 RVA: 0x001665E7 File Offset: 0x001647E7
			public override PropagatorResult Visit(DbConstantExpression node)
			{
				Check.NotNull<DbConstantExpression>(node, "node");
				return PropagatorResult.CreateSimpleValue(PropagatorFlags.Preserve, node.Value);
			}

			// Token: 0x0600692B RID: 26923 RVA: 0x00166601 File Offset: 0x00164801
			public override PropagatorResult Visit(DbRefKeyExpression node)
			{
				Check.NotNull<DbRefKeyExpression>(node, "node");
				return this.Visit(node.Argument);
			}

			// Token: 0x0600692C RID: 26924 RVA: 0x0016661B File Offset: 0x0016481B
			public override PropagatorResult Visit(DbNullExpression node)
			{
				Check.NotNull<DbNullExpression>(node, "node");
				return PropagatorResult.CreateSimpleValue(PropagatorFlags.Preserve, null);
			}

			// Token: 0x0600692D RID: 26925 RVA: 0x00166630 File Offset: 0x00164830
			public override PropagatorResult Visit(DbTreatExpression node)
			{
				Check.NotNull<DbTreatExpression>(node, "node");
				PropagatorResult propagatorResult = this.Visit(node.Argument);
				if (MetadataHelper.IsSuperTypeOf(node.ResultType.EdmType, propagatorResult.StructuralType))
				{
					return propagatorResult;
				}
				return PropagatorResult.CreateSimpleValue(propagatorResult.PropagatorFlags, null);
			}

			// Token: 0x0600692E RID: 26926 RVA: 0x0016667C File Offset: 0x0016487C
			public override PropagatorResult Visit(DbCastExpression node)
			{
				Check.NotNull<DbCastExpression>(node, "node");
				PropagatorResult propagatorResult = this.Visit(node.Argument);
				TypeUsage resultType = node.ResultType;
				if (!propagatorResult.IsSimple || BuiltInTypeKind.PrimitiveType != resultType.EdmType.BuiltInTypeKind)
				{
					throw new NotSupportedException(Strings.Update_UnsupportedCastArgument(resultType.EdmType.Name));
				}
				object obj;
				if (propagatorResult.IsNull)
				{
					obj = null;
				}
				else
				{
					try
					{
						obj = Propagator.Evaluator.Cast(propagatorResult.GetSimpleValue(), ((PrimitiveType)resultType.EdmType).ClrEquivalentType);
					}
					catch
					{
						throw;
					}
				}
				return propagatorResult.ReplicateResultWithNewValue(obj);
			}

			// Token: 0x0600692F RID: 26927 RVA: 0x0016671C File Offset: 0x0016491C
			private static object Cast(object value, Type clrPrimitiveType)
			{
				IFormatProvider invariantCulture = CultureInfo.InvariantCulture;
				if (value == null || value == DBNull.Value || value.GetType() == clrPrimitiveType)
				{
					return value;
				}
				if (value is DateTime && clrPrimitiveType == typeof(DateTimeOffset))
				{
					return new DateTimeOffset(((DateTime)value).Ticks, TimeSpan.Zero);
				}
				return Convert.ChangeType(value, clrPrimitiveType, invariantCulture);
			}

			// Token: 0x06006930 RID: 26928 RVA: 0x0016678C File Offset: 0x0016498C
			public override PropagatorResult Visit(DbIsNullExpression node)
			{
				Check.NotNull<DbIsNullExpression>(node, "node");
				PropagatorResult propagatorResult = this.Visit(node.Argument);
				return Propagator.Evaluator.ConvertBoolToResult(new bool?(propagatorResult.IsNull), new PropagatorResult[] { propagatorResult });
			}

			// Token: 0x06006931 RID: 26929 RVA: 0x001667CC File Offset: 0x001649CC
			private static PropagatorFlags PropagateUnknownAndPreserveFlags(PropagatorResult result, IEnumerable<PropagatorResult> inputs)
			{
				bool flag = false;
				bool flag2 = true;
				bool flag3 = true;
				foreach (PropagatorResult propagatorResult in inputs)
				{
					flag3 = false;
					PropagatorFlags propagatorFlags = propagatorResult.PropagatorFlags;
					if ((PropagatorFlags.Unknown & propagatorFlags) != PropagatorFlags.NoFlags)
					{
						flag = true;
					}
					if ((PropagatorFlags.Preserve & propagatorFlags) == PropagatorFlags.NoFlags)
					{
						flag2 = false;
					}
				}
				if (flag3)
				{
					flag2 = false;
				}
				if (result != null)
				{
					PropagatorFlags propagatorFlags2 = result.PropagatorFlags;
					if (flag)
					{
						propagatorFlags2 |= PropagatorFlags.Unknown;
					}
					if (!flag2)
					{
						propagatorFlags2 &= ~PropagatorFlags.Preserve;
					}
					return propagatorFlags2;
				}
				PropagatorFlags propagatorFlags3 = PropagatorFlags.NoFlags;
				if (flag)
				{
					propagatorFlags3 |= PropagatorFlags.Unknown;
				}
				if (flag2)
				{
					propagatorFlags3 |= PropagatorFlags.Preserve;
				}
				return propagatorFlags3;
			}

			// Token: 0x04002F9F RID: 12191
			private readonly PropagatorResult m_row;

			// Token: 0x04002FA0 RID: 12192
			private static readonly string _visitorName = typeof(Propagator.Evaluator).FullName;
		}

		// Token: 0x02000C06 RID: 3078
		internal class ExtentPlaceholderCreator
		{
			// Token: 0x06006933 RID: 26931 RVA: 0x00166884 File Offset: 0x00164A84
			private static Dictionary<PrimitiveTypeKind, object> InitializeTypeDefaultMap()
			{
				Dictionary<PrimitiveTypeKind, object> dictionary = new Dictionary<PrimitiveTypeKind, object>(EqualityComparer<PrimitiveTypeKind>.Default);
				dictionary[PrimitiveTypeKind.Binary] = new byte[0];
				dictionary[PrimitiveTypeKind.Boolean] = false;
				dictionary[PrimitiveTypeKind.Byte] = 0;
				dictionary[PrimitiveTypeKind.DateTime] = default(DateTime);
				dictionary[PrimitiveTypeKind.Time] = default(TimeSpan);
				dictionary[PrimitiveTypeKind.DateTimeOffset] = default(DateTimeOffset);
				dictionary[PrimitiveTypeKind.Decimal] = 0m;
				dictionary[PrimitiveTypeKind.Double] = 0.0;
				dictionary[PrimitiveTypeKind.Guid] = default(Guid);
				dictionary[PrimitiveTypeKind.Int16] = 0;
				dictionary[PrimitiveTypeKind.Int32] = 0;
				dictionary[PrimitiveTypeKind.Int64] = 0L;
				dictionary[PrimitiveTypeKind.Single] = 0f;
				dictionary[PrimitiveTypeKind.SByte] = 0;
				dictionary[PrimitiveTypeKind.String] = string.Empty;
				dictionary[PrimitiveTypeKind.HierarchyId] = HierarchyId.GetRoot();
				return dictionary;
			}

			// Token: 0x06006934 RID: 26932 RVA: 0x001669A4 File Offset: 0x00164BA4
			private static Dictionary<PrimitiveTypeKind, object> InitializeSpatialTypeDefaultMap()
			{
				Dictionary<PrimitiveTypeKind, object> dictionary = new Dictionary<PrimitiveTypeKind, object>(EqualityComparer<PrimitiveTypeKind>.Default);
				dictionary[PrimitiveTypeKind.Geometry] = DbGeometry.FromText("POINT EMPTY");
				dictionary[PrimitiveTypeKind.GeometryPoint] = DbGeometry.FromText("POINT EMPTY");
				dictionary[PrimitiveTypeKind.GeometryLineString] = DbGeometry.FromText("LINESTRING EMPTY");
				dictionary[PrimitiveTypeKind.GeometryPolygon] = DbGeometry.FromText("POLYGON EMPTY");
				dictionary[PrimitiveTypeKind.GeometryMultiPoint] = DbGeometry.FromText("MULTIPOINT EMPTY");
				dictionary[PrimitiveTypeKind.GeometryMultiLineString] = DbGeometry.FromText("MULTILINESTRING EMPTY");
				dictionary[PrimitiveTypeKind.GeometryMultiPolygon] = DbGeometry.FromText("MULTIPOLYGON EMPTY");
				dictionary[PrimitiveTypeKind.GeometryCollection] = DbGeometry.FromText("GEOMETRYCOLLECTION EMPTY");
				dictionary[PrimitiveTypeKind.Geography] = DbGeography.FromText("POINT EMPTY");
				dictionary[PrimitiveTypeKind.GeographyPoint] = DbGeography.FromText("POINT EMPTY");
				dictionary[PrimitiveTypeKind.GeographyLineString] = DbGeography.FromText("LINESTRING EMPTY");
				dictionary[PrimitiveTypeKind.GeographyPolygon] = DbGeography.FromText("POLYGON EMPTY");
				dictionary[PrimitiveTypeKind.GeographyMultiPoint] = DbGeography.FromText("MULTIPOINT EMPTY");
				dictionary[PrimitiveTypeKind.GeographyMultiLineString] = DbGeography.FromText("MULTILINESTRING EMPTY");
				dictionary[PrimitiveTypeKind.GeographyMultiPolygon] = DbGeography.FromText("MULTIPOLYGON EMPTY");
				dictionary[PrimitiveTypeKind.GeographyCollection] = DbGeography.FromText("GEOMETRYCOLLECTION EMPTY");
				return dictionary;
			}

			// Token: 0x06006935 RID: 26933 RVA: 0x00166ADC File Offset: 0x00164CDC
			private static bool TryGetDefaultValue(PrimitiveType primitiveType, out object defaultValue)
			{
				PrimitiveTypeKind primitiveTypeKind = primitiveType.PrimitiveTypeKind;
				if (!Helper.IsSpatialType(primitiveType))
				{
					return Propagator.ExtentPlaceholderCreator._typeDefaultMap.TryGetValue(primitiveTypeKind, out defaultValue);
				}
				return Propagator.ExtentPlaceholderCreator._spatialTypeDefaultMap.Value.TryGetValue(primitiveTypeKind, out defaultValue);
			}

			// Token: 0x06006936 RID: 26934 RVA: 0x00166B18 File Offset: 0x00164D18
			internal static PropagatorResult CreatePlaceholder(EntitySetBase extent)
			{
				Propagator.ExtentPlaceholderCreator extentPlaceholderCreator = new Propagator.ExtentPlaceholderCreator();
				AssociationSet associationSet = extent as AssociationSet;
				if (associationSet != null)
				{
					return extentPlaceholderCreator.CreateAssociationSetPlaceholder(associationSet);
				}
				EntitySet entitySet = extent as EntitySet;
				if (entitySet != null)
				{
					return extentPlaceholderCreator.CreateEntitySetPlaceholder(entitySet);
				}
				throw new NotSupportedException(Strings.Update_UnsupportedExtentType(extent.Name, extent.GetType().Name));
			}

			// Token: 0x06006937 RID: 26935 RVA: 0x00166B6C File Offset: 0x00164D6C
			private PropagatorResult CreateEntitySetPlaceholder(EntitySet entitySet)
			{
				ReadOnlyMetadataCollection<EdmProperty> properties = entitySet.ElementType.Properties;
				PropagatorResult[] array = new PropagatorResult[properties.Count];
				for (int i = 0; i < properties.Count; i++)
				{
					PropagatorResult propagatorResult = this.CreateMemberPlaceholder(properties[i]);
					array[i] = propagatorResult;
				}
				return PropagatorResult.CreateStructuralValue(array, entitySet.ElementType, false);
			}

			// Token: 0x06006938 RID: 26936 RVA: 0x00166BC4 File Offset: 0x00164DC4
			private PropagatorResult CreateAssociationSetPlaceholder(AssociationSet associationSet)
			{
				ReadOnlyMetadataCollection<AssociationEndMember> associationEndMembers = associationSet.ElementType.AssociationEndMembers;
				PropagatorResult[] array = new PropagatorResult[associationEndMembers.Count];
				for (int i = 0; i < associationEndMembers.Count; i++)
				{
					EntityType entityType = (EntityType)((RefType)associationEndMembers[i].TypeUsage.EdmType).ElementType;
					PropagatorResult[] array2 = new PropagatorResult[entityType.KeyMembers.Count];
					for (int j = 0; j < entityType.KeyMembers.Count; j++)
					{
						EdmMember edmMember = entityType.KeyMembers[j];
						PropagatorResult propagatorResult = this.CreateMemberPlaceholder(edmMember);
						array2[j] = propagatorResult;
					}
					RowType keyRowType = entityType.GetKeyRowType();
					PropagatorResult propagatorResult2 = PropagatorResult.CreateStructuralValue(array2, keyRowType, false);
					array[i] = propagatorResult2;
				}
				return PropagatorResult.CreateStructuralValue(array, associationSet.ElementType, false);
			}

			// Token: 0x06006939 RID: 26937 RVA: 0x00166C93 File Offset: 0x00164E93
			private PropagatorResult CreateMemberPlaceholder(EdmMember member)
			{
				return this.Visit(member);
			}

			// Token: 0x0600693A RID: 26938 RVA: 0x00166C9C File Offset: 0x00164E9C
			internal PropagatorResult Visit(EdmMember node)
			{
				TypeUsage modelTypeUsage = Helper.GetModelTypeUsage(node);
				PropagatorResult propagatorResult;
				if (Helper.IsScalarType(modelTypeUsage.EdmType))
				{
					Propagator.ExtentPlaceholderCreator.GetPropagatorResultForPrimitiveType(Helper.AsPrimitive(modelTypeUsage.EdmType), out propagatorResult);
				}
				else
				{
					StructuralType structuralType = (StructuralType)modelTypeUsage.EdmType;
					IBaseList<EdmMember> allStructuralMembers = TypeHelpers.GetAllStructuralMembers(structuralType);
					PropagatorResult[] array = new PropagatorResult[allStructuralMembers.Count];
					for (int i = 0; i < allStructuralMembers.Count; i++)
					{
						array[i] = this.Visit(allStructuralMembers[i]);
					}
					propagatorResult = PropagatorResult.CreateStructuralValue(array, structuralType, false);
				}
				return propagatorResult;
			}

			// Token: 0x0600693B RID: 26939 RVA: 0x00166D24 File Offset: 0x00164F24
			internal static void GetPropagatorResultForPrimitiveType(PrimitiveType primitiveType, out PropagatorResult result)
			{
				object obj;
				if (!Propagator.ExtentPlaceholderCreator.TryGetDefaultValue(primitiveType, out obj))
				{
					obj = 0;
				}
				result = PropagatorResult.CreateSimpleValue(PropagatorFlags.NoFlags, obj);
			}

			// Token: 0x04002FA1 RID: 12193
			private static readonly Dictionary<PrimitiveTypeKind, object> _typeDefaultMap = Propagator.ExtentPlaceholderCreator.InitializeTypeDefaultMap();

			// Token: 0x04002FA2 RID: 12194
			private static readonly Lazy<Dictionary<PrimitiveTypeKind, object>> _spatialTypeDefaultMap = new Lazy<Dictionary<PrimitiveTypeKind, object>>(new Func<Dictionary<PrimitiveTypeKind, object>>(Propagator.ExtentPlaceholderCreator.InitializeSpatialTypeDefaultMap));
		}

		// Token: 0x02000C07 RID: 3079
		private class JoinPropagator
		{
			// Token: 0x0600693E RID: 26942 RVA: 0x00166D78 File Offset: 0x00164F78
			internal JoinPropagator(ChangeNode left, ChangeNode right, DbJoinExpression node, Propagator parent)
			{
				this.m_left = left;
				this.m_right = right;
				this.m_joinExpression = node;
				this.m_parent = parent;
				if (DbExpressionKind.InnerJoin == this.m_joinExpression.ExpressionKind)
				{
					this.m_insertRules = Propagator.JoinPropagator._innerJoinInsertRules;
					this.m_deleteRules = Propagator.JoinPropagator._innerJoinDeleteRules;
				}
				else
				{
					this.m_insertRules = Propagator.JoinPropagator._leftOuterJoinInsertRules;
					this.m_deleteRules = Propagator.JoinPropagator._leftOuterJoinDeleteRules;
				}
				Propagator.JoinPropagator.JoinConditionVisitor.GetKeySelectors(node.JoinCondition, out this.m_leftKeySelectors, out this.m_rightKeySelectors);
				this.m_leftPlaceholderKey = Propagator.JoinPropagator.ExtractKey(this.m_left.Placeholder, this.m_leftKeySelectors);
				this.m_rightPlaceholderKey = Propagator.JoinPropagator.ExtractKey(this.m_right.Placeholder, this.m_rightKeySelectors);
			}

			// Token: 0x0600693F RID: 26943 RVA: 0x00166E34 File Offset: 0x00165034
			static JoinPropagator()
			{
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.LeftInsert | Propagator.JoinPropagator.Ops.LeftDelete | Propagator.JoinPropagator.Ops.RightInsert | Propagator.JoinPropagator.Ops.RightDelete, Propagator.JoinPropagator.Ops.LeftInsertJoinRightInsert, Propagator.JoinPropagator.Ops.LeftDeleteJoinRightDelete, Propagator.JoinPropagator.Ops.LeftInsertJoinRightInsert, Propagator.JoinPropagator.Ops.LeftDeleteJoinRightDelete);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.LeftDeleteJoinRightDelete, Propagator.JoinPropagator.Ops.Nothing, Propagator.JoinPropagator.Ops.LeftDeleteJoinRightDelete, Propagator.JoinPropagator.Ops.Nothing, Propagator.JoinPropagator.Ops.LeftDeleteJoinRightDelete);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.LeftInsertJoinRightInsert, Propagator.JoinPropagator.Ops.LeftInsertJoinRightInsert, Propagator.JoinPropagator.Ops.Nothing, Propagator.JoinPropagator.Ops.LeftInsertJoinRightInsert, Propagator.JoinPropagator.Ops.Nothing);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.LeftUpdate, Propagator.JoinPropagator.Ops.LeftInsertUnknownExtended, Propagator.JoinPropagator.Ops.LeftDeleteUnknownExtended, Propagator.JoinPropagator.Ops.LeftInsertUnknownExtended, Propagator.JoinPropagator.Ops.LeftDeleteUnknownExtended);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.RightUpdate, Propagator.JoinPropagator.Ops.RightInsertUnknownExtended, Propagator.JoinPropagator.Ops.RightDeleteUnknownExtended, Propagator.JoinPropagator.Ops.RightInsertUnknownExtended, Propagator.JoinPropagator.Ops.RightDeleteUnknownExtended);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.LeftInsert | Propagator.JoinPropagator.Ops.LeftDelete | Propagator.JoinPropagator.Ops.RightDelete, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.LeftInsertNullModifiedExtended, Propagator.JoinPropagator.Ops.LeftDeleteJoinRightDelete);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.LeftInsert | Propagator.JoinPropagator.Ops.LeftDelete | Propagator.JoinPropagator.Ops.RightInsert, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.LeftInsertJoinRightInsert, Propagator.JoinPropagator.Ops.LeftDeleteNullModifiedExtended);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.LeftDelete, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Nothing, Propagator.JoinPropagator.Ops.LeftDeleteNullPreserveExtended);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.LeftInsert, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.LeftInsertNullModifiedExtended, Propagator.JoinPropagator.Ops.Nothing);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.RightDelete, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.LeftUnknownNullModifiedExtended, Propagator.JoinPropagator.Ops.RightDeleteUnknownExtended);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.RightInsert, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.RightInsertUnknownExtended, Propagator.JoinPropagator.Ops.LeftUnknownNullModifiedExtended);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.LeftDelete | Propagator.JoinPropagator.Ops.RightInsert | Propagator.JoinPropagator.Ops.RightDelete, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.LeftDelete | Propagator.JoinPropagator.Ops.RightInsert, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.LeftInsert | Propagator.JoinPropagator.Ops.RightInsert | Propagator.JoinPropagator.Ops.RightDelete, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported);
				Propagator.JoinPropagator.InitializeRule(Propagator.JoinPropagator.Ops.LeftInsert | Propagator.JoinPropagator.Ops.RightDelete, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported, Propagator.JoinPropagator.Ops.Unsupported);
			}

			// Token: 0x06006940 RID: 26944 RVA: 0x00166FBD File Offset: 0x001651BD
			private static void InitializeRule(Propagator.JoinPropagator.Ops input, Propagator.JoinPropagator.Ops joinInsert, Propagator.JoinPropagator.Ops joinDelete, Propagator.JoinPropagator.Ops lojInsert, Propagator.JoinPropagator.Ops lojDelete)
			{
				Propagator.JoinPropagator._innerJoinInsertRules.Add(input, joinInsert);
				Propagator.JoinPropagator._innerJoinDeleteRules.Add(input, joinDelete);
				Propagator.JoinPropagator._leftOuterJoinInsertRules.Add(input, lojInsert);
				Propagator.JoinPropagator._leftOuterJoinDeleteRules.Add(input, lojDelete);
			}

			// Token: 0x06006941 RID: 26945 RVA: 0x00166FF0 File Offset: 0x001651F0
			internal ChangeNode Propagate()
			{
				ChangeNode changeNode = Propagator.BuildChangeNode(this.m_joinExpression);
				Dictionary<CompositeKey, Tuple<CompositeKey, PropagatorResult>> dictionary = this.ProcessKeys(this.m_left.Deleted, this.m_leftKeySelectors);
				Dictionary<CompositeKey, Tuple<CompositeKey, PropagatorResult>> dictionary2 = this.ProcessKeys(this.m_left.Inserted, this.m_leftKeySelectors);
				Dictionary<CompositeKey, Tuple<CompositeKey, PropagatorResult>> dictionary3 = this.ProcessKeys(this.m_right.Deleted, this.m_rightKeySelectors);
				Dictionary<CompositeKey, Tuple<CompositeKey, PropagatorResult>> dictionary4 = this.ProcessKeys(this.m_right.Inserted, this.m_rightKeySelectors);
				foreach (CompositeKey compositeKey in dictionary.Keys.Concat(dictionary2.Keys).Concat(dictionary3.Keys).Concat(dictionary4.Keys)
					.Distinct(this.m_parent.UpdateTranslator.KeyComparer))
				{
					this.Propagate(compositeKey, changeNode, dictionary, dictionary2, dictionary3, dictionary4);
				}
				changeNode.Placeholder = this.CreateResultTuple(Tuple.Create<CompositeKey, PropagatorResult>(null, this.m_left.Placeholder), Tuple.Create<CompositeKey, PropagatorResult>(null, this.m_right.Placeholder), changeNode);
				return changeNode;
			}

			// Token: 0x06006942 RID: 26946 RVA: 0x00167120 File Offset: 0x00165320
			private void Propagate(CompositeKey key, ChangeNode result, Dictionary<CompositeKey, Tuple<CompositeKey, PropagatorResult>> leftDeletes, Dictionary<CompositeKey, Tuple<CompositeKey, PropagatorResult>> leftInserts, Dictionary<CompositeKey, Tuple<CompositeKey, PropagatorResult>> rightDeletes, Dictionary<CompositeKey, Tuple<CompositeKey, PropagatorResult>> rightInserts)
			{
				Tuple<CompositeKey, PropagatorResult> tuple = null;
				Tuple<CompositeKey, PropagatorResult> tuple2 = null;
				Tuple<CompositeKey, PropagatorResult> tuple3 = null;
				Tuple<CompositeKey, PropagatorResult> tuple4 = null;
				Propagator.JoinPropagator.Ops ops = Propagator.JoinPropagator.Ops.Nothing;
				if (leftInserts.TryGetValue(key, out tuple))
				{
					ops |= Propagator.JoinPropagator.Ops.LeftInsert;
				}
				if (leftDeletes.TryGetValue(key, out tuple2))
				{
					ops |= Propagator.JoinPropagator.Ops.LeftDelete;
				}
				if (rightInserts.TryGetValue(key, out tuple3))
				{
					ops |= Propagator.JoinPropagator.Ops.RightInsert;
				}
				if (rightDeletes.TryGetValue(key, out tuple4))
				{
					ops |= Propagator.JoinPropagator.Ops.RightDelete;
				}
				Propagator.JoinPropagator.Ops ops2 = this.m_insertRules[ops];
				Propagator.JoinPropagator.Ops ops3 = this.m_deleteRules[ops];
				if (Propagator.JoinPropagator.Ops.Unsupported == ops2 || Propagator.JoinPropagator.Ops.Unsupported == ops3)
				{
					List<IEntityStateEntry> stateEntries = new List<IEntityStateEntry>();
					Action<Tuple<CompositeKey, PropagatorResult>> action = delegate(Tuple<CompositeKey, PropagatorResult> r)
					{
						if (r != null)
						{
							stateEntries.AddRange(SourceInterpreter.GetAllStateEntries(r.Item2, this.m_parent.m_updateTranslator, this.m_parent.m_table));
						}
					};
					action(tuple);
					action(tuple2);
					action(tuple3);
					action(tuple4);
					throw new UpdateException(Strings.Update_InvalidChanges, null, stateEntries.Cast<ObjectStateEntry>().Distinct<ObjectStateEntry>());
				}
				if ((Propagator.JoinPropagator.Ops.LeftUnknown & ops2) != Propagator.JoinPropagator.Ops.Nothing)
				{
					tuple = Tuple.Create<CompositeKey, PropagatorResult>(key, this.LeftPlaceholder(key, Propagator.JoinPropagator.PopulateMode.Unknown));
				}
				if ((Propagator.JoinPropagator.Ops.LeftUnknown & ops3) != Propagator.JoinPropagator.Ops.Nothing)
				{
					tuple2 = Tuple.Create<CompositeKey, PropagatorResult>(key, this.LeftPlaceholder(key, Propagator.JoinPropagator.PopulateMode.Unknown));
				}
				if ((Propagator.JoinPropagator.Ops.RightNullModified & ops2) != Propagator.JoinPropagator.Ops.Nothing)
				{
					tuple3 = Tuple.Create<CompositeKey, PropagatorResult>(key, this.RightPlaceholder(key, Propagator.JoinPropagator.PopulateMode.NullModified));
				}
				else if ((Propagator.JoinPropagator.Ops.RightNullPreserve & ops2) != Propagator.JoinPropagator.Ops.Nothing)
				{
					tuple3 = Tuple.Create<CompositeKey, PropagatorResult>(key, this.RightPlaceholder(key, Propagator.JoinPropagator.PopulateMode.NullPreserve));
				}
				else if ((Propagator.JoinPropagator.Ops.RightUnknown & ops2) != Propagator.JoinPropagator.Ops.Nothing)
				{
					tuple3 = Tuple.Create<CompositeKey, PropagatorResult>(key, this.RightPlaceholder(key, Propagator.JoinPropagator.PopulateMode.Unknown));
				}
				if ((Propagator.JoinPropagator.Ops.RightNullModified & ops3) != Propagator.JoinPropagator.Ops.Nothing)
				{
					tuple4 = Tuple.Create<CompositeKey, PropagatorResult>(key, this.RightPlaceholder(key, Propagator.JoinPropagator.PopulateMode.NullModified));
				}
				else if ((Propagator.JoinPropagator.Ops.RightNullPreserve & ops3) != Propagator.JoinPropagator.Ops.Nothing)
				{
					tuple4 = Tuple.Create<CompositeKey, PropagatorResult>(key, this.RightPlaceholder(key, Propagator.JoinPropagator.PopulateMode.NullPreserve));
				}
				else if ((Propagator.JoinPropagator.Ops.RightUnknown & ops3) != Propagator.JoinPropagator.Ops.Nothing)
				{
					tuple4 = Tuple.Create<CompositeKey, PropagatorResult>(key, this.RightPlaceholder(key, Propagator.JoinPropagator.PopulateMode.Unknown));
				}
				if (tuple != null && tuple3 != null)
				{
					result.Inserted.Add(this.CreateResultTuple(tuple, tuple3, result));
				}
				if (tuple2 != null && tuple4 != null)
				{
					result.Deleted.Add(this.CreateResultTuple(tuple2, tuple4, result));
				}
			}

			// Token: 0x06006943 RID: 26947 RVA: 0x00167310 File Offset: 0x00165510
			private PropagatorResult CreateResultTuple(Tuple<CompositeKey, PropagatorResult> left, Tuple<CompositeKey, PropagatorResult> right, ChangeNode result)
			{
				CompositeKey item = left.Item1;
				CompositeKey item2 = right.Item1;
				Dictionary<PropagatorResult, PropagatorResult> map = null;
				if (item != null && item2 != null && item != item2)
				{
					CompositeKey compositeKey = item.Merge(this.m_parent.m_updateTranslator.KeyManager, item2);
					map = new Dictionary<PropagatorResult, PropagatorResult>();
					for (int i = 0; i < item.KeyComponents.Length; i++)
					{
						map[item.KeyComponents[i]] = compositeKey.KeyComponents[i];
						map[item2.KeyComponents[i]] = compositeKey.KeyComponents[i];
					}
				}
				PropagatorResult propagatorResult = PropagatorResult.CreateStructuralValue(new PropagatorResult[] { left.Item2, right.Item2 }, (StructuralType)result.ElementType.EdmType, false);
				if (map != null)
				{
					PropagatorResult replacement;
					propagatorResult = propagatorResult.Replace(delegate(PropagatorResult original)
					{
						if (!map.TryGetValue(original, out replacement))
						{
							return original;
						}
						return replacement;
					});
				}
				return propagatorResult;
			}

			// Token: 0x06006944 RID: 26948 RVA: 0x00167404 File Offset: 0x00165604
			private PropagatorResult LeftPlaceholder(CompositeKey key, Propagator.JoinPropagator.PopulateMode mode)
			{
				return Propagator.JoinPropagator.PlaceholderPopulator.Populate(this.m_left.Placeholder, key, this.m_leftPlaceholderKey, mode);
			}

			// Token: 0x06006945 RID: 26949 RVA: 0x0016741E File Offset: 0x0016561E
			private PropagatorResult RightPlaceholder(CompositeKey key, Propagator.JoinPropagator.PopulateMode mode)
			{
				return Propagator.JoinPropagator.PlaceholderPopulator.Populate(this.m_right.Placeholder, key, this.m_rightPlaceholderKey, mode);
			}

			// Token: 0x06006946 RID: 26950 RVA: 0x00167438 File Offset: 0x00165638
			private Dictionary<CompositeKey, Tuple<CompositeKey, PropagatorResult>> ProcessKeys(IEnumerable<PropagatorResult> instances, ReadOnlyCollection<DbExpression> keySelectors)
			{
				Dictionary<CompositeKey, Tuple<CompositeKey, PropagatorResult>> dictionary = new Dictionary<CompositeKey, Tuple<CompositeKey, PropagatorResult>>(this.m_parent.UpdateTranslator.KeyComparer);
				foreach (PropagatorResult propagatorResult in instances)
				{
					CompositeKey compositeKey = Propagator.JoinPropagator.ExtractKey(propagatorResult, keySelectors);
					dictionary[compositeKey] = Tuple.Create<CompositeKey, PropagatorResult>(compositeKey, propagatorResult);
				}
				return dictionary;
			}

			// Token: 0x06006947 RID: 26951 RVA: 0x001674A8 File Offset: 0x001656A8
			private static CompositeKey ExtractKey(PropagatorResult change, ReadOnlyCollection<DbExpression> keySelectors)
			{
				PropagatorResult[] array = new PropagatorResult[keySelectors.Count];
				for (int i = 0; i < keySelectors.Count; i++)
				{
					PropagatorResult propagatorResult = Propagator.Evaluator.Evaluate(keySelectors[i], change);
					array[i] = propagatorResult;
				}
				return new CompositeKey(array);
			}

			// Token: 0x04002FA3 RID: 12195
			private static readonly Dictionary<Propagator.JoinPropagator.Ops, Propagator.JoinPropagator.Ops> _innerJoinInsertRules = new Dictionary<Propagator.JoinPropagator.Ops, Propagator.JoinPropagator.Ops>(EqualityComparer<Propagator.JoinPropagator.Ops>.Default);

			// Token: 0x04002FA4 RID: 12196
			private static readonly Dictionary<Propagator.JoinPropagator.Ops, Propagator.JoinPropagator.Ops> _innerJoinDeleteRules = new Dictionary<Propagator.JoinPropagator.Ops, Propagator.JoinPropagator.Ops>(EqualityComparer<Propagator.JoinPropagator.Ops>.Default);

			// Token: 0x04002FA5 RID: 12197
			private static readonly Dictionary<Propagator.JoinPropagator.Ops, Propagator.JoinPropagator.Ops> _leftOuterJoinInsertRules = new Dictionary<Propagator.JoinPropagator.Ops, Propagator.JoinPropagator.Ops>(EqualityComparer<Propagator.JoinPropagator.Ops>.Default);

			// Token: 0x04002FA6 RID: 12198
			private static readonly Dictionary<Propagator.JoinPropagator.Ops, Propagator.JoinPropagator.Ops> _leftOuterJoinDeleteRules = new Dictionary<Propagator.JoinPropagator.Ops, Propagator.JoinPropagator.Ops>(EqualityComparer<Propagator.JoinPropagator.Ops>.Default);

			// Token: 0x04002FA7 RID: 12199
			private readonly DbJoinExpression m_joinExpression;

			// Token: 0x04002FA8 RID: 12200
			private readonly Propagator m_parent;

			// Token: 0x04002FA9 RID: 12201
			private readonly Dictionary<Propagator.JoinPropagator.Ops, Propagator.JoinPropagator.Ops> m_insertRules;

			// Token: 0x04002FAA RID: 12202
			private readonly Dictionary<Propagator.JoinPropagator.Ops, Propagator.JoinPropagator.Ops> m_deleteRules;

			// Token: 0x04002FAB RID: 12203
			private readonly ReadOnlyCollection<DbExpression> m_leftKeySelectors;

			// Token: 0x04002FAC RID: 12204
			private readonly ReadOnlyCollection<DbExpression> m_rightKeySelectors;

			// Token: 0x04002FAD RID: 12205
			private readonly ChangeNode m_left;

			// Token: 0x04002FAE RID: 12206
			private readonly ChangeNode m_right;

			// Token: 0x04002FAF RID: 12207
			private readonly CompositeKey m_leftPlaceholderKey;

			// Token: 0x04002FB0 RID: 12208
			private readonly CompositeKey m_rightPlaceholderKey;

			// Token: 0x02000D8E RID: 3470
			[Flags]
			private enum Ops : uint
			{
				// Token: 0x04003381 RID: 13185
				Nothing = 0U,
				// Token: 0x04003382 RID: 13186
				LeftInsert = 1U,
				// Token: 0x04003383 RID: 13187
				LeftDelete = 2U,
				// Token: 0x04003384 RID: 13188
				RightInsert = 4U,
				// Token: 0x04003385 RID: 13189
				RightDelete = 8U,
				// Token: 0x04003386 RID: 13190
				LeftUnknown = 32U,
				// Token: 0x04003387 RID: 13191
				RightNullModified = 128U,
				// Token: 0x04003388 RID: 13192
				RightNullPreserve = 256U,
				// Token: 0x04003389 RID: 13193
				RightUnknown = 512U,
				// Token: 0x0400338A RID: 13194
				LeftUpdate = 3U,
				// Token: 0x0400338B RID: 13195
				RightUpdate = 12U,
				// Token: 0x0400338C RID: 13196
				Unsupported = 4096U,
				// Token: 0x0400338D RID: 13197
				LeftInsertJoinRightInsert = 5U,
				// Token: 0x0400338E RID: 13198
				LeftDeleteJoinRightDelete = 10U,
				// Token: 0x0400338F RID: 13199
				LeftInsertNullModifiedExtended = 129U,
				// Token: 0x04003390 RID: 13200
				LeftInsertNullPreserveExtended = 257U,
				// Token: 0x04003391 RID: 13201
				LeftInsertUnknownExtended = 513U,
				// Token: 0x04003392 RID: 13202
				LeftDeleteNullModifiedExtended = 130U,
				// Token: 0x04003393 RID: 13203
				LeftDeleteNullPreserveExtended = 258U,
				// Token: 0x04003394 RID: 13204
				LeftDeleteUnknownExtended = 514U,
				// Token: 0x04003395 RID: 13205
				LeftUnknownNullModifiedExtended = 160U,
				// Token: 0x04003396 RID: 13206
				LeftUnknownNullPreserveExtended = 288U,
				// Token: 0x04003397 RID: 13207
				RightInsertUnknownExtended = 36U,
				// Token: 0x04003398 RID: 13208
				RightDeleteUnknownExtended = 40U
			}

			// Token: 0x02000D8F RID: 3471
			private class JoinConditionVisitor : UpdateExpressionVisitor<object>
			{
				// Token: 0x06006FAA RID: 28586 RVA: 0x0017D437 File Offset: 0x0017B637
				private JoinConditionVisitor()
				{
					this.m_leftKeySelectors = new List<DbExpression>();
					this.m_rightKeySelectors = new List<DbExpression>();
				}

				// Token: 0x170011D0 RID: 4560
				// (get) Token: 0x06006FAB RID: 28587 RVA: 0x0017D455 File Offset: 0x0017B655
				protected override string VisitorName
				{
					get
					{
						return Propagator.JoinPropagator.JoinConditionVisitor._visitorName;
					}
				}

				// Token: 0x06006FAC RID: 28588 RVA: 0x0017D45C File Offset: 0x0017B65C
				internal static void GetKeySelectors(DbExpression joinCondition, out ReadOnlyCollection<DbExpression> leftKeySelectors, out ReadOnlyCollection<DbExpression> rightKeySelectors)
				{
					Propagator.JoinPropagator.JoinConditionVisitor joinConditionVisitor = new Propagator.JoinPropagator.JoinConditionVisitor();
					joinCondition.Accept<object>(joinConditionVisitor);
					leftKeySelectors = new ReadOnlyCollection<DbExpression>(joinConditionVisitor.m_leftKeySelectors);
					rightKeySelectors = new ReadOnlyCollection<DbExpression>(joinConditionVisitor.m_rightKeySelectors);
				}

				// Token: 0x06006FAD RID: 28589 RVA: 0x0017D491 File Offset: 0x0017B691
				public override object Visit(DbAndExpression node)
				{
					Check.NotNull<DbAndExpression>(node, "node");
					this.Visit(node.Left);
					this.Visit(node.Right);
					return null;
				}

				// Token: 0x06006FAE RID: 28590 RVA: 0x0017D4BC File Offset: 0x0017B6BC
				public override object Visit(DbComparisonExpression node)
				{
					Check.NotNull<DbComparisonExpression>(node, "node");
					if (DbExpressionKind.Equals == node.ExpressionKind)
					{
						this.m_leftKeySelectors.Add(node.Left);
						this.m_rightKeySelectors.Add(node.Right);
						return null;
					}
					throw base.ConstructNotSupportedException(node);
				}

				// Token: 0x04003399 RID: 13209
				private readonly List<DbExpression> m_leftKeySelectors;

				// Token: 0x0400339A RID: 13210
				private readonly List<DbExpression> m_rightKeySelectors;

				// Token: 0x0400339B RID: 13211
				private static readonly string _visitorName = typeof(Propagator.JoinPropagator.JoinConditionVisitor).FullName;
			}

			// Token: 0x02000D90 RID: 3472
			private enum PopulateMode
			{
				// Token: 0x0400339D RID: 13213
				NullModified,
				// Token: 0x0400339E RID: 13214
				NullPreserve,
				// Token: 0x0400339F RID: 13215
				Unknown
			}

			// Token: 0x02000D91 RID: 3473
			private static class PlaceholderPopulator
			{
				// Token: 0x06006FB0 RID: 28592 RVA: 0x0017D520 File Offset: 0x0017B720
				internal static PropagatorResult Populate(PropagatorResult placeholder, CompositeKey key, CompositeKey placeholderKey, Propagator.JoinPropagator.PopulateMode mode)
				{
					bool isNull = mode == Propagator.JoinPropagator.PopulateMode.NullModified || mode == Propagator.JoinPropagator.PopulateMode.NullPreserve;
					bool flag = mode == Propagator.JoinPropagator.PopulateMode.NullPreserve || mode == Propagator.JoinPropagator.PopulateMode.Unknown;
					PropagatorFlags flags = PropagatorFlags.NoFlags;
					if (!isNull)
					{
						flags |= PropagatorFlags.Unknown;
					}
					if (flag)
					{
						flags |= PropagatorFlags.Preserve;
					}
					return placeholder.Replace(delegate(PropagatorResult node)
					{
						int num = -1;
						for (int i = 0; i < placeholderKey.KeyComponents.Length; i++)
						{
							if (placeholderKey.KeyComponents[i] == node)
							{
								num = i;
								break;
							}
						}
						if (num != -1)
						{
							return key.KeyComponents[num];
						}
						object obj = (isNull ? null : node.GetSimpleValue());
						return PropagatorResult.CreateSimpleValue(flags, obj);
					});
				}
			}
		}
	}
}
