﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Core.SchemaObjectModel;
using System.Data.Entity.Resources;
using System.IO;
using System.Reflection;
using System.Xml;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004D6 RID: 1238
	internal class MetadataArtifactLoaderResource : MetadataArtifactLoader, IComparable
	{
		// Token: 0x06003D75 RID: 15733 RVA: 0x000CA828 File Offset: 0x000C8A28
		internal MetadataArtifactLoaderResource(Assembly assembly, string resourceName, ICollection<string> uriRegistry)
		{
			this._assembly = assembly;
			this._resourceName = resourceName;
			string text = MetadataArtifactLoaderCompositeResource.CreateResPath(this._assembly, this._resourceName);
			this._alreadyLoaded = uriRegistry.Contains(text);
			if (!this._alreadyLoaded)
			{
				uriRegistry.Add(text);
			}
		}

		// Token: 0x17000C17 RID: 3095
		// (get) Token: 0x06003D76 RID: 15734 RVA: 0x000CA877 File Offset: 0x000C8A77
		public override string Path
		{
			get
			{
				return MetadataArtifactLoaderCompositeResource.CreateResPath(this._assembly, this._resourceName);
			}
		}

		// Token: 0x06003D77 RID: 15735 RVA: 0x000CA88C File Offset: 0x000C8A8C
		public int CompareTo(object obj)
		{
			MetadataArtifactLoaderResource metadataArtifactLoaderResource = obj as MetadataArtifactLoaderResource;
			if (metadataArtifactLoaderResource != null)
			{
				return string.Compare(this.Path, metadataArtifactLoaderResource.Path, StringComparison.OrdinalIgnoreCase);
			}
			return -1;
		}

		// Token: 0x06003D78 RID: 15736 RVA: 0x000CA8B7 File Offset: 0x000C8AB7
		public override bool Equals(object obj)
		{
			return this.CompareTo(obj) == 0;
		}

		// Token: 0x06003D79 RID: 15737 RVA: 0x000CA8C3 File Offset: 0x000C8AC3
		public override int GetHashCode()
		{
			return this.Path.GetHashCode();
		}

		// Token: 0x06003D7A RID: 15738 RVA: 0x000CA8D0 File Offset: 0x000C8AD0
		public override List<string> GetPaths(DataSpace spaceToGet)
		{
			List<string> list = new List<string>();
			if (!this._alreadyLoaded && MetadataArtifactLoader.IsArtifactOfDataSpace(this.Path, spaceToGet))
			{
				list.Add(this.Path);
			}
			return list;
		}

		// Token: 0x06003D7B RID: 15739 RVA: 0x000CA908 File Offset: 0x000C8B08
		public override List<string> GetPaths()
		{
			List<string> list = new List<string>();
			if (!this._alreadyLoaded)
			{
				list.Add(this.Path);
			}
			return list;
		}

		// Token: 0x06003D7C RID: 15740 RVA: 0x000CA930 File Offset: 0x000C8B30
		public override List<XmlReader> GetReaders(Dictionary<MetadataArtifactLoader, XmlReader> sourceDictionary)
		{
			List<XmlReader> list = new List<XmlReader>();
			if (!this._alreadyLoaded)
			{
				XmlReader xmlReader = this.CreateReader();
				list.Add(xmlReader);
				if (sourceDictionary != null)
				{
					sourceDictionary.Add(this, xmlReader);
				}
			}
			return list;
		}

		// Token: 0x06003D7D RID: 15741 RVA: 0x000CA968 File Offset: 0x000C8B68
		private XmlReader CreateReader()
		{
			Stream stream = this.LoadResource();
			XmlReaderSettings xmlReaderSettings = Schema.CreateEdmStandardXmlReaderSettings();
			xmlReaderSettings.CloseInput = true;
			xmlReaderSettings.ConformanceLevel = ConformanceLevel.Document;
			return XmlReader.Create(stream, xmlReaderSettings);
		}

		// Token: 0x06003D7E RID: 15742 RVA: 0x000CA998 File Offset: 0x000C8B98
		public override List<XmlReader> CreateReaders(DataSpace spaceToGet)
		{
			List<XmlReader> list = new List<XmlReader>();
			if (!this._alreadyLoaded && MetadataArtifactLoader.IsArtifactOfDataSpace(this.Path, spaceToGet))
			{
				XmlReader xmlReader = this.CreateReader();
				list.Add(xmlReader);
			}
			return list;
		}

		// Token: 0x06003D7F RID: 15743 RVA: 0x000CA9D0 File Offset: 0x000C8BD0
		private Stream LoadResource()
		{
			Stream stream;
			if (this.TryCreateResourceStream(out stream))
			{
				return stream;
			}
			throw new MetadataException(Strings.UnableToLoadResource);
		}

		// Token: 0x06003D80 RID: 15744 RVA: 0x000CA9F3 File Offset: 0x000C8BF3
		private bool TryCreateResourceStream(out Stream resourceStream)
		{
			resourceStream = this._assembly.GetManifestResourceStream(this._resourceName);
			return resourceStream != null;
		}

		// Token: 0x040014FA RID: 5370
		private readonly bool _alreadyLoaded;

		// Token: 0x040014FB RID: 5371
		private readonly Assembly _assembly;

		// Token: 0x040014FC RID: 5372
		private readonly string _resourceName;
	}
}
