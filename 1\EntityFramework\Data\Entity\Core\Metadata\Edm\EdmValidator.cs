﻿using System;
using System.Collections.Generic;
using System.Data.Entity.Resources;
using System.Data.Entity.Utilities;

namespace System.Data.Entity.Core.Metadata.Edm
{
	// Token: 0x020004B6 RID: 1206
	internal class EdmValidator
	{
		// Token: 0x17000B9A RID: 2970
		// (get) Token: 0x06003B88 RID: 15240 RVA: 0x000C393E File Offset: 0x000C1B3E
		// (set) Token: 0x06003B89 RID: 15241 RVA: 0x000C3946 File Offset: 0x000C1B46
		internal bool SkipReadOnlyItems { get; set; }

		// Token: 0x06003B8A RID: 15242 RVA: 0x000C3950 File Offset: 0x000C1B50
		public void Validate<T>(IEnumerable<T> items, List<EdmItemError> ospaceErrors) where T : EdmType
		{
			Check.NotNull<IEnumerable<T>>(items, "items");
			Check.NotNull<IEnumerable<T>>(items, "items");
			HashSet<MetadataItem> hashSet = new HashSet<MetadataItem>();
			foreach (T t in items)
			{
				MetadataItem metadataItem = t;
				this.InternalValidate(metadataItem, ospaceErrors, hashSet);
			}
		}

		// Token: 0x06003B8B RID: 15243 RVA: 0x000C39C0 File Offset: 0x000C1BC0
		protected virtual void OnValidationError(ValidationErrorEventArgs e)
		{
		}

		// Token: 0x06003B8C RID: 15244 RVA: 0x000C39C4 File Offset: 0x000C1BC4
		private void AddError(List<EdmItemError> errors, EdmItemError newError)
		{
			ValidationErrorEventArgs validationErrorEventArgs = new ValidationErrorEventArgs(newError);
			this.OnValidationError(validationErrorEventArgs);
			errors.Add(validationErrorEventArgs.ValidationError);
		}

		// Token: 0x06003B8D RID: 15245 RVA: 0x000C39EB File Offset: 0x000C1BEB
		protected virtual IEnumerable<EdmItemError> CustomValidate(MetadataItem item)
		{
			return null;
		}

		// Token: 0x06003B8E RID: 15246 RVA: 0x000C39F0 File Offset: 0x000C1BF0
		private void InternalValidate(MetadataItem item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			if ((item.IsReadOnly && this.SkipReadOnlyItems) || validatedItems.Contains(item))
			{
				return;
			}
			validatedItems.Add(item);
			if (string.IsNullOrEmpty(item.Identity))
			{
				this.AddError(errors, new EdmItemError(Strings.Validator_EmptyIdentity));
			}
			switch (item.BuiltInTypeKind)
			{
			case BuiltInTypeKind.CollectionType:
				this.ValidateCollectionType((CollectionType)item, errors, validatedItems);
				break;
			case BuiltInTypeKind.ComplexType:
				this.ValidateComplexType((ComplexType)item, errors, validatedItems);
				break;
			case BuiltInTypeKind.EntityType:
				this.ValidateEntityType((EntityType)item, errors, validatedItems);
				break;
			case BuiltInTypeKind.Facet:
				this.ValidateFacet((Facet)item, errors, validatedItems);
				break;
			case BuiltInTypeKind.MetadataProperty:
				this.ValidateMetadataProperty((MetadataProperty)item, errors, validatedItems);
				break;
			case BuiltInTypeKind.NavigationProperty:
				this.ValidateNavigationProperty((NavigationProperty)item, errors, validatedItems);
				break;
			case BuiltInTypeKind.PrimitiveType:
				this.ValidatePrimitiveType((PrimitiveType)item, errors, validatedItems);
				break;
			case BuiltInTypeKind.EdmProperty:
				this.ValidateEdmProperty((EdmProperty)item, errors, validatedItems);
				break;
			case BuiltInTypeKind.RefType:
				this.ValidateRefType((RefType)item, errors, validatedItems);
				break;
			case BuiltInTypeKind.TypeUsage:
				this.ValidateTypeUsage((TypeUsage)item, errors, validatedItems);
				break;
			}
			IEnumerable<EdmItemError> enumerable = this.CustomValidate(item);
			if (enumerable != null)
			{
				errors.AddRange(enumerable);
			}
		}

		// Token: 0x06003B8F RID: 15247 RVA: 0x000C3B90 File Offset: 0x000C1D90
		private void ValidateCollectionType(CollectionType item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			this.ValidateEdmType(item, errors, validatedItems);
			if (item.BaseType != null)
			{
				this.AddError(errors, new EdmItemError(Strings.Validator_CollectionTypesCannotHaveBaseType));
			}
			if (item.TypeUsage == null)
			{
				this.AddError(errors, new EdmItemError(Strings.Validator_CollectionHasNoTypeUsage));
				return;
			}
			this.InternalValidate(item.TypeUsage, errors, validatedItems);
		}

		// Token: 0x06003B90 RID: 15248 RVA: 0x000C3BE7 File Offset: 0x000C1DE7
		private void ValidateComplexType(ComplexType item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			this.ValidateStructuralType(item, errors, validatedItems);
		}

		// Token: 0x06003B91 RID: 15249 RVA: 0x000C3BF4 File Offset: 0x000C1DF4
		private void ValidateEdmType(EdmType item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			this.ValidateItem(item, errors, validatedItems);
			if (string.IsNullOrEmpty(item.Name))
			{
				this.AddError(errors, new EdmItemError(Strings.Validator_TypeHasNoName));
			}
			if (item.NamespaceName == null || (item.DataSpace != DataSpace.OSpace && string.Empty == item.NamespaceName))
			{
				this.AddError(errors, new EdmItemError(Strings.Validator_TypeHasNoNamespace));
			}
			if (item.BaseType != null)
			{
				this.InternalValidate(item.BaseType, errors, validatedItems);
			}
		}

		// Token: 0x06003B92 RID: 15250 RVA: 0x000C3C74 File Offset: 0x000C1E74
		private void ValidateEntityType(EntityType item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			if (item.BaseType == null)
			{
				if (item.KeyMembers.Count < 1)
				{
					this.AddError(errors, new EdmItemError(Strings.Validator_NoKeyMembers(item.FullName)));
				}
				else
				{
					foreach (EdmMember edmMember in item.KeyMembers)
					{
						EdmProperty edmProperty = (EdmProperty)edmMember;
						if (edmProperty.Nullable)
						{
							this.AddError(errors, new EdmItemError(Strings.Validator_NullableEntityKeyProperty(edmProperty.Name, item.FullName)));
						}
					}
				}
			}
			this.ValidateStructuralType(item, errors, validatedItems);
		}

		// Token: 0x06003B93 RID: 15251 RVA: 0x000C3D28 File Offset: 0x000C1F28
		private void ValidateFacet(Facet item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			this.ValidateItem(item, errors, validatedItems);
			if (string.IsNullOrEmpty(item.Name))
			{
				this.AddError(errors, new EdmItemError(Strings.Validator_FacetHasNoName));
			}
			if (item.FacetType == null)
			{
				this.AddError(errors, new EdmItemError(Strings.Validator_FacetTypeIsNull));
				return;
			}
			this.InternalValidate(item.FacetType, errors, validatedItems);
		}

		// Token: 0x06003B94 RID: 15252 RVA: 0x000C3D84 File Offset: 0x000C1F84
		private void ValidateItem(MetadataItem item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			if (item.RawMetadataProperties != null)
			{
				foreach (MetadataProperty metadataProperty in item.MetadataProperties)
				{
					this.InternalValidate(metadataProperty, errors, validatedItems);
				}
			}
		}

		// Token: 0x06003B95 RID: 15253 RVA: 0x000C3DE4 File Offset: 0x000C1FE4
		private void ValidateEdmMember(EdmMember item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			this.ValidateItem(item, errors, validatedItems);
			if (string.IsNullOrEmpty(item.Name))
			{
				this.AddError(errors, new EdmItemError(Strings.Validator_MemberHasNoName));
			}
			if (item.DeclaringType == null)
			{
				this.AddError(errors, new EdmItemError(Strings.Validator_MemberHasNullDeclaringType));
			}
			else
			{
				this.InternalValidate(item.DeclaringType, errors, validatedItems);
			}
			if (item.TypeUsage == null)
			{
				this.AddError(errors, new EdmItemError(Strings.Validator_MemberHasNullTypeUsage));
				return;
			}
			this.InternalValidate(item.TypeUsage, errors, validatedItems);
		}

		// Token: 0x06003B96 RID: 15254 RVA: 0x000C3E6C File Offset: 0x000C206C
		private void ValidateMetadataProperty(MetadataProperty item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			if (item.PropertyKind == PropertyKind.Extended)
			{
				this.ValidateItem(item, errors, validatedItems);
				if (string.IsNullOrEmpty(item.Name))
				{
					this.AddError(errors, new EdmItemError(Strings.Validator_MetadataPropertyHasNoName));
				}
				if (item.TypeUsage == null)
				{
					this.AddError(errors, new EdmItemError(Strings.Validator_ItemAttributeHasNullTypeUsage));
					return;
				}
				this.InternalValidate(item.TypeUsage, errors, validatedItems);
			}
		}

		// Token: 0x06003B97 RID: 15255 RVA: 0x000C3ED1 File Offset: 0x000C20D1
		private void ValidateNavigationProperty(NavigationProperty item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			this.ValidateEdmMember(item, errors, validatedItems);
		}

		// Token: 0x06003B98 RID: 15256 RVA: 0x000C3EDC File Offset: 0x000C20DC
		private void ValidatePrimitiveType(PrimitiveType item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			this.ValidateSimpleType(item, errors, validatedItems);
		}

		// Token: 0x06003B99 RID: 15257 RVA: 0x000C3EE7 File Offset: 0x000C20E7
		private void ValidateEdmProperty(EdmProperty item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			this.ValidateEdmMember(item, errors, validatedItems);
		}

		// Token: 0x06003B9A RID: 15258 RVA: 0x000C3EF4 File Offset: 0x000C20F4
		private void ValidateRefType(RefType item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			this.ValidateEdmType(item, errors, validatedItems);
			if (item.BaseType != null)
			{
				this.AddError(errors, new EdmItemError(Strings.Validator_RefTypesCannotHaveBaseType));
			}
			if (item.ElementType == null)
			{
				this.AddError(errors, new EdmItemError(Strings.Validator_RefTypeHasNullEntityType));
				return;
			}
			this.InternalValidate(item.ElementType, errors, validatedItems);
		}

		// Token: 0x06003B9B RID: 15259 RVA: 0x000C3F4B File Offset: 0x000C214B
		private void ValidateSimpleType(SimpleType item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			this.ValidateEdmType(item, errors, validatedItems);
		}

		// Token: 0x06003B9C RID: 15260 RVA: 0x000C3F58 File Offset: 0x000C2158
		private void ValidateStructuralType(StructuralType item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			this.ValidateEdmType(item, errors, validatedItems);
			Dictionary<string, EdmMember> dictionary = new Dictionary<string, EdmMember>();
			foreach (EdmMember edmMember in item.Members)
			{
				EdmMember edmMember2 = null;
				if (dictionary.TryGetValue(edmMember.Name, out edmMember2))
				{
					this.AddError(errors, new EdmItemError(Strings.Validator_BaseTypeHasMemberOfSameName));
				}
				else
				{
					dictionary.Add(edmMember.Name, edmMember);
				}
				this.InternalValidate(edmMember, errors, validatedItems);
			}
		}

		// Token: 0x06003B9D RID: 15261 RVA: 0x000C3FF0 File Offset: 0x000C21F0
		private void ValidateTypeUsage(TypeUsage item, List<EdmItemError> errors, HashSet<MetadataItem> validatedItems)
		{
			this.ValidateItem(item, errors, validatedItems);
			if (item.EdmType == null)
			{
				this.AddError(errors, new EdmItemError(Strings.Validator_TypeUsageHasNullEdmType));
			}
			else
			{
				this.InternalValidate(item.EdmType, errors, validatedItems);
			}
			foreach (Facet facet in item.Facets)
			{
				this.InternalValidate(facet, errors, validatedItems);
			}
		}
	}
}
