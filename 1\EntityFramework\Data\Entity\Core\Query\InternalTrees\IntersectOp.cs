﻿using System;
using System.Diagnostics;

namespace System.Data.Entity.Core.Query.InternalTrees
{
	// Token: 0x020003B0 RID: 944
	internal sealed class IntersectOp : SetOp
	{
		// Token: 0x06002D90 RID: 11664 RVA: 0x000910D7 File Offset: 0x0008F2D7
		private IntersectOp()
			: base(OpType.Intersect)
		{
		}

		// Token: 0x06002D91 RID: 11665 RVA: 0x000910E1 File Offset: 0x0008F2E1
		internal IntersectOp(VarVec outputs, VarMap left, VarMap right)
			: base(OpType.Intersect, outputs, left, right)
		{
		}

		// Token: 0x06002D92 RID: 11666 RVA: 0x000910EE File Offset: 0x0008F2EE
		[DebuggerNonUserCode]
		internal override void Accept(BasicOpVisitor v, Node n)
		{
			v.Visit(this, n);
		}

		// Token: 0x06002D93 RID: 11667 RVA: 0x000910F8 File Offset: 0x0008F2F8
		[DebuggerNonUserCode]
		internal override TResultType Accept<TResultType>(BasicOpVisitorOfT<TResultType> v, Node n)
		{
			return v.Visit(this, n);
		}

		// Token: 0x04000F3F RID: 3903
		internal static readonly IntersectOp Pattern = new IntersectOp();
	}
}
